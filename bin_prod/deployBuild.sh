#!/bin/sh

cd ../ && mvn clean package -Dmaven.test.skip=true

# channel-inst-center build
echo "package channel-inst-center Sart!"

cd start/target

mkdir channel-inst-center-client-build

cp start.jar channel-inst-center-client-build

cp ../../bin_prod/deploy.sh channel-inst-center-client-build

mkdir channel-inst-center-client-build/awslogs

tar zcvf root.tar.gz channel-inst-center-client-build

cd ../../

echo "package channel-inst-center done!"