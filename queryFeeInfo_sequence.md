# queryFeeInfo方法时序图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Facade as InstNewInfoQueryFacadeImpl
    participant Val<PERSON><PERSON> as ValidationUtils
    participant Assembler as instContractRequestAssembler
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as paramsCheck方法
    participant ContractService as instContractQueryService
    participant MidMapping as instContractMidMappingRepository
    participant ContractCache as instNewContractCacheManager
    participant FunnelService as funnelModelMatchService
    participant ErrorNotify as channelErrInfoNotify
    participant Redis as stringRedisTemplate
    
    Client->>+Facade: queryFeeInfo(request)
    
    Note over Facade: 1. 参数校验阶段
    Facade->>+Validator: validate(request)
    Validator-->>-Facade: 校验通过/抛出异常
    
    Facade->>+Assembler: convertRequest2Response(request)
    Assembler-->>-Facade: InstInfoQueryResponse
    
    Facade->>+ParamChecker: paramsCheck(request)
    ParamChecker->>ParamChecker: 检查targetOrg和cardOrg
    ParamChecker->>ParamChecker: 检查bizType是否为I/O
    ParamChecker->>ParamChecker: 处理兜底数据替换
    ParamChecker->>ParamChecker: 设置默认mcc值
    ParamChecker-->>-Facade: 校验完成
    
    Note over Facade: 2. 查询合同信息
    Facade->>+ContractService: queryActiveContract(mid, bizType, transactionTime)
    ContractService->>+MidMapping: queryMappingByMid(mid, bizType)
    MidMapping-->>-ContractService: InstContractMidMappingPO
    ContractService->>+ContractCache: getEffectiveInstVersionContract(contractNo, transactionTime)
    ContractCache-->>-ContractService: InstContractVersionInfoPO
    ContractService->>ContractService: 转换为领域对象
    ContractService-->>-Facade: InstContractVersionInfo
    
    Facade->>Facade: 设置合同号和版本到响应对象
    
    Note over Facade: 3. 查询标准产品
    Facade->>+ContractService: queryStandardProduct(standardProducts, paymentMethodType, targetOrg, cardOrg)
    ContractService->>ContractService: preciseMatchingStandardProduct(精准匹配)
    alt 精准匹配成功
        ContractService-->>Facade: InstContractStandardProduct
    else 精准匹配失败
        ContractService->>ContractService: fuzzyMatchingStandardProduct(模糊匹配)
        alt 模糊匹配成功
            ContractService-->>Facade: InstContractStandardProduct
        else 模糊匹配失败
            ContractService-->>-Facade: 抛出异常: 标准产品没找到
        end
    end
    
    Note over Facade: 4. 查询原始产品
    Facade->>+ContractService: queryOriginProductByNo(originProductNo, contractInfo)
    ContractService->>ContractService: 从原始产品列表中过滤匹配
    ContractService-->>-Facade: InstContractOriginProduct
    
    Note over Facade: 5. 查询费用信息
    Facade->>+ContractService: queryFeeByOriProduct(originProduct, request)
    ContractService->>ContractService: 构建FunnelMatchItem
    ContractService->>+FunnelService: matchFee(funnelMatchItem, contractFeeItems)
    FunnelService-->>-ContractService: InstContractFeeItem
    ContractService-->>-Facade: InstContractFeeItem
    
    Note over Facade: 6. 查询结算信息(仅入款)
    alt bizType == "I"
        Facade->>+ContractService: querySettleByOriProduct(originProduct, payCurrency, channelMerchantCode)
        ContractService->>ContractService: preciseMatchingSettle(精准匹配)
        alt 精准匹配成功
            ContractService-->>Facade: InstContractSettlementItem
        else 精准匹配失败
            ContractService->>ContractService: fuzzyMatchingSettle(模糊匹配)
            alt 模糊匹配成功
                ContractService-->>Facade: InstContractSettlementItem
            else 模糊匹配失败
                ContractService-->>-Facade: 抛出异常
            end
        end
    else 结算信息查询异常
        Facade->>Facade: 记录警告日志
        Facade->>+ErrorNotify: channelErrInfoNotify(standardProduct, instCode, mid, currency)
        ErrorNotify->>+Redis: 异步存储错误信息
        Redis-->>-ErrorNotify: 存储完成
        ErrorNotify-->>-Facade: 通知完成
        Facade->>Facade: settleItem = null
    end
    
    Note over Facade: 7. 组装响应
    Facade->>+Facade: assemblerResponse(feeItem, settleItem, contractInfo, response)
    Facade->>Facade: 处理费用配置映射
    Facade->>Facade: 补全争议费用配置
    Facade->>Facade: 设置费用、税费、结算配置
    Facade->>Facade: 设置计算精度和舍入模式
    Facade-->>-Facade: 组装完成
    
    Facade-->>-Client: Result<InstInfoQueryResponse>
    
    Note over Facade: 异常处理
    alt BizException
        Facade-->>Client: ResultUtils.bizExceptionFail(e)
    else BusinessException/IllegalArgumentException  
        Facade-->>Client: ResultUtils.bizException(e)
    else 其他Exception
        Facade-->>Client: ResultUtils.unknownFail()
    end
```

## 时序图说明

### 主要参与者
- **Client**: 调用方客户端
- **Facade**: InstNewInfoQueryFacadeImpl主要业务逻辑层
- **Validator**: ValidationUtils参数校验工具
- **Assembler**: 请求响应转换器
- **ContractService**: 合同查询服务
- **MidMapping**: MID映射数据访问层
- **ContractCache**: 合同缓存管理器
- **FunnelService**: 漏斗模型匹配服务
- **ErrorNotify**: 错误通知服务
- **Redis**: Redis缓存

### 关键交互流程

1. **参数校验阶段**: 使用Hibernate Validator和自定义业务校验
2. **合同查询**: 通过MID映射查找合同，再通过缓存获取生效版本
3. **产品匹配**: 先精准匹配后模糊匹配的策略
4. **费用查询**: 使用漏斗模型进行多维度匹配
5. **结算查询**: 仅对入款业务查询，支持异常容错
6. **错误处理**: 异步通知机制，不影响主流程
7. **响应组装**: 统一组装各类配置信息

### 异常处理机制
- **BizException**: 业务异常，返回具体错误信息
- **BusinessException/IllegalArgumentException**: 参数或业务逻辑异常
- **其他Exception**: 未知异常，返回通用错误信息
- **结算查询异常**: 不影响费用查询，异步记录错误信息
