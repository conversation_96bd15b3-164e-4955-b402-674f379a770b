<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountDao">

    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity">
        <id property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="instCode" column="inst_code" jdbcType="VARCHAR"/>
        <result property="entity" column="entity" jdbcType="VARCHAR"/>
        <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
        <result property="accountType" column="account_type" jdbcType="VARCHAR"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="accountAlias" column="account_alias" jdbcType="VARCHAR"/>
        <result property="accountCategory" column="account_category" jdbcType="VARCHAR"/>
        <result property="accountNature" column="account_nature" jdbcType="VARCHAR"/>
        <result property="iban" column="iban" jdbcType="VARCHAR"/>
        <result property="swiftCode" column="swift_code" jdbcType="VARCHAR"/>
        <result property="accountOpeningTime" column="account_opening_time" jdbcType="TIMESTAMP"/>
        <result property="authorizedOfficer" column="authorized_officer" jdbcType="VARCHAR"/>
        <result property="bankOperator" column="bank_operator" jdbcType="VARCHAR"/>
        <result property="fileList" column="file_list" jdbcType="VARCHAR"/>
        <result property="useType" column="use_type" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
        <result property="scenes" column="scenes" jdbcType="VARCHAR"/>
        <result property="isSupportSubAccount" column="is_support_sub_account" jdbcType="CHAR"/>
        <result property="supportSubAccountType" column="support_sub_account_type" jdbcType="VARCHAR"/>
        <result property="isSupportPreApply" column="is_support_pre_apply" jdbcType="CHAR"/>
        <result property="subAccountMode" column="sub_account_mode" jdbcType="VARCHAR"/>
        <result property="subAccountRule" column="sub_account_rule" jdbcType="VARCHAR"/>
        <result property="subAccountLimit" column="sub_account_limit" jdbcType="BIGINT"/>
        <result property="country" column="country" jdbcType="CHAR"/>
        <result property="currency" column="currency" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="priority" column="priority" jdbcType="INTEGER"/>
        <result property="weight" column="weight" jdbcType="INTEGER"/>
        <result property="isSupportCustomName" column="is_support_custom_name" jdbcType="CHAR"/>
        <result property="isNeedActivation" column="is_need_activation" jdbcType="CHAR"/>
        <result property="activationMode" column="activation_mode" jdbcType="VARCHAR"/>
        <result property="isSupportCloseAccount" column="is_support_close_account" jdbcType="CHAR"/>
        <result property="closeMode" column="close_mode" jdbcType="VARCHAR"/>
        <result property="instMid" column="inst_mid" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="financialAttribute" column="financial_attribute" jdbcType="VARCHAR"/>
        <result property="bankAddress" column="bank_address" jdbcType="VARCHAR"/>
        <result property="bankAddressJson" column="bank_address_json" jdbcType="VARCHAR"/>
        <result property="accountJson" column="account_json" jdbcType="VARCHAR"/>
        <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
        <collection property="accountExtList" javaType="java.util.List" notNullColumn="clear_system"
                    ofType="com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountExtEntity">
            <result property="clearSystem" column="clear_system" jdbcType="VARCHAR"/>
            <result property="extKey" column="ext_key" jdbcType="VARCHAR"/>
            <result property="extValue" column="ext_value" jdbcType="VARCHAR"/>
        </collection>
        <collection property="subAccount" javaType="java.util.List" resultMap="InstSubFundsAccountMap" ofType="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity"/>
    </resultMap>

    <resultMap id="InstSubFundsAccountMap"
               type="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity">
        <id property="subAccountId" column="sub_account_id" jdbcType="VARCHAR"/>
        <result property="businessKey" column="business_key" jdbcType="VARCHAR"/>
        <result property="accountId" column="f_account_id" jdbcType="VARCHAR"/>
        <result property="bucketId" column="bucket_id" jdbcType="BIGINT"/>
        <result property="numberSegmentId" column="number_segment_id" jdbcType="VARCHAR"/>
        <result property="numberSegmentNo" column="number_segment_no" jdbcType="VARCHAR"/>
        <result property="subUseType" column="sub_use_type" jdbcType="VARCHAR"/>
        <result property="subAccountNo" column="sub_account_no" jdbcType="VARCHAR"/>
        <result property="subAccountName" column="sub_account_name" jdbcType="VARCHAR"/>
        <result property="bSubAccountNo" column="b_sub_account_no" jdbcType="VARCHAR"/>
        <result property="merchantNo" column="merchant_no" jdbcType="VARCHAR"/>
        <result property="subMerchantNo" column="sub_merchant_no" jdbcType="VARCHAR"/>
        <result property="status" column="sub_status" jdbcType="INTEGER"/>
        <result property="scenes" column="sub_scenes" jdbcType="VARCHAR"/>
        <result property="accountJson" column="sub_account_json" jdbcType="VARCHAR"/>
        <result property="requestBody" column="request_body" jdbcType="VARCHAR"/>
        <result property="utcCreate" column="sub_utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="sub_utc_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        account.account_id
        ,account.inst_code,account.entity,
        account.account_no,account.account_type,account.account_name,
        account.use_type,account.biz_type,account.scenes,account.is_support_sub_account,account.support_sub_account_type,account.is_support_pre_apply,account.sub_account_mode,
        account.sub_account_rule,account.country,account.currency,
        account.status,account.priority,account.weight,account.sub_account_limit,
        account.is_support_custom_name,account.is_need_activation,account.activation_mode,account.is_support_close_account,account.close_mode,
        account.inst_mid,account.bank_name,account.bank_address,account.bank_address_json,
        account.account_json,account.account_alias,account.account_category,account.account_nature,account.iban,
        account.swift_code,account.account_opening_time,account.authorized_officer,account.bank_operator,account.file_list,
        account.financial_attribute,
        account.utc_create,account.utc_modified,account.is_deleted,
        ext.id,ext.account_id,ext.clear_system,ext.ext_key,ext.ext_value,ext.utc_create,ext.utc_modified
    </sql>

    <sql id="Base_Sub_Column_List">
        subAccount.sub_account_id,subAccount.business_key,subAccount.account_id as f_account_id,
        subAccount.number_segment_id,subAccount.number_segment_no,subAccount.sub_use_type,subAccount.sub_account_no,
        subAccount.sub_account_name,subAccount.b_sub_account_no,subAccount.merchant_no,subAccount.sub_merchant_no,subAccount.scenes as sub_scenes,subAccount.status as sub_status,subAccount.bucket_id,
        subAccount.account_json as sub_account_json,subAccount.request_body,subAccount.utc_create as sub_utc_create,subAccount.utc_modified as sub_utc_modified
    </sql>

    <select id="selectByQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_funds_account account left join tb_inst_funds_account_ext ext on account.account_id =
        ext.account_id
        <where>
            account.is_deleted = 'N'
            <if test="accountId != null and accountId != ''">
                and account.account_id = #{accountId,jdbcType=VARCHAR}
            </if>
            <if test="entity != null and entity != ''">
                and account.entity = #{entity,jdbcType=CHAR}
            </if>
            <if test="instCode != null and instCode != ''">
                and account.inst_code = #{instCode,jdbcType=VARCHAR}
            </if>
            <if test="accountNo != null and accountNo != ''">
                and account.account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
            <if test="accountType != null and accountType != ''">
                and account.account_type = #{accountType,jdbcType=VARCHAR}
            </if>
            <if test="accountAlias != null and accountAlias != ''">
                and account.account_alias = #{accountAlias,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and account.status = #{status,jdbcType=CHAR}
            </if>
            <if test="country != null and country.size() == 1">
                and account.country =
                <foreach collection="country" index="index" item="country" open="" separator="," close="">
                    #{country}
                </foreach>
            </if>
            <if test="country != null and country.size() > 1">
                and account.country in
                <foreach collection="country" index="index" item="country" open="(" separator="," close=")">
                    #{country}
                </foreach>
            </if>
            <if test="useType != null and useType != ''">
                and find_in_set(#{useType,jdbcType=VARCHAR},account.use_type)
            </if>
            <if test="bizType != null and bizType != ''">
                and find_in_set(#{bizType,jdbcType=VARCHAR},account.biz_type)
            </if>
            <if test="currency != null and currency != ''">
                and account.currency = #{currency,jdbcType=CHAR}
            </if>
            <if test="isNeedActivation != null and isNeedActivation != ''">
                and account.is_need_activation = #{isNeedActivation,jdbcType=CHAR}
            </if>
            <if test="isSupportSubAccount != null and isSupportSubAccount != ''">
                and account.is_support_sub_account = #{isSupportSubAccount,jdbcType=CHAR}
            </if>
        </where>
        order by account.priority desc
    </select>


    <select id="selectAccountAndSubByRequest" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <if test="subAccountQuery != null">
            ,<include refid="Base_Sub_Column_List"/>
        </if>
        from tb_inst_funds_account account 
        left join tb_inst_funds_account_ext ext on account.account_id = ext.account_id
        <if test="subAccountQuery != null">
            left join tb_inst_sub_funds_account subAccount on account.account_id = subAccount.account_id
        </if>
        <where>
            account.is_deleted = 'N'
            <if test="accountIds != null and accountIds.size() > 0">
                and account.account_id in
                <foreach collection="accountIds" index="index" item="accountId" open="(" separator="," close=")">
                    #{accountId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="instCode != null and instCode != ''">
                and account.inst_code = #{instCode,jdbcType=VARCHAR}
            </if>
            <if test="entity != null and entity != ''">
                and account.entity = #{entity,jdbcType=CHAR}
            </if>
            <if test="accountNo != null and accountNo != ''">
                and account.account_no = #{accountNo,jdbcType=VARCHAR}
            </if>
            <if test="accountType != null and accountType != ''">
                and account.account_type = #{accountType,jdbcType=VARCHAR}
            </if>
            <if test="accountName != null and accountName != ''">
                and account.account_name = #{accountName,jdbcType=VARCHAR}
            </if>
            <if test="useType != null and useType != ''">
                and find_in_set(#{useType,jdbcType=VARCHAR},account.use_type)
            </if>
            <if test="scenes != null and scenes != ''">
                and find_in_set(#{scenes,jdbcType=VARCHAR},account.scenes)
            </if>
            <if test="isSupportSubAccount != null and isSupportSubAccount != ''">
                and account.is_support_sub_account = #{isSupportSubAccount,jdbcType=CHAR}
            </if>
            <if test="isSupportPreApply != null and isSupportPreApply != ''">
                and account.is_support_pre_apply = #{isSupportPreApply,jdbcType=CHAR}
            </if>
            <if test="subAccountMode != null and subAccountMode != ''">
                and account.sub_account_mode = #{subAccountMode,jdbcType=CHAR}
            </if>
            <if test="country != null and country != ''">
                and account.country = #{country,jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != ''">
                and account.currency = #{currency,jdbcType=CHAR}
            </if>
            <if test="isSupportCustomName != null and isSupportCustomName != ''">
                and account.is_support_custom_name = #{isSupportCustomName,jdbcType=CHAR}
            </if>
            <if test="isNeedActivation != null and isNeedActivation != ''">
                and account.is_need_activation = #{isNeedActivation,jdbcType=CHAR}
            </if>
            <if test="activationMode != null and activationMode != ''">
                and account.activation_mode = #{activationMode,jdbcType=CHAR}
            </if>
            <if test="instMid != null and instMid != ''">
                and account.inst_mid = #{instMid,jdbcType=CHAR}
            </if>
            <if test="iban != null and iban != ''">
                and account.iban = #{iban,jdbcType=VARCHAR}
            </if>
            <if test="swiftCode != null and swiftCode != ''">
                and account.swift_code = #{swiftCode,jdbcType=VARCHAR}
            </if>
            <if test="accountAlias != null and accountAlias != ''">
                and account.account_alias = #{accountAlias,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and account.status = #{status,jdbcType=CHAR}
            </if>
            <if test="subAccountQuery !=null">
                <if test="subAccountQuery.subAccountId != null and subAccountQuery.subAccountId != ''">
                    and subAccount.sub_account_id = #{subAccountQuery.subAccountId,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.businessKey != null and subAccountQuery.businessKey != ''">
                    and subAccount.business_key = #{subAccountQuery.businessKey,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.accountId != null and subAccountQuery.accountId != ''">
                    and subAccount.account_id = #{subAccountQuery.accountId,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.bucketId != null">
                    and subAccount.bucket_id = #{subAccountQuery.bucketId,jdbcType=BIGINT}
                </if>
                <if test="subAccountQuery.numberSegmentId != null and subAccountQuery.numberSegmentId != ''">
                    and subAccount.number_segment_id = #{subAccountQuery.numberSegmentId,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.numberSegmentNo != null and subAccountQuery.numberSegmentNo != ''">
                    and subAccount.number_segment_no = #{subAccountQuery.numberSegmentNo,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.subUseType != null and subAccountQuery.subUseType != ''">
                    and subAccount.sub_use_type = #{subAccountQuery.subUseType,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.subAccountNo != null and subAccountQuery.subAccountNo != ''">
                    and subAccount.sub_account_no = #{subAccountQuery.subAccountNo,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.subAccountName != null and subAccountQuery.subAccountName != ''">
                    and subAccount.sub_account_name = #{subAccountQuery.subAccountName,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.bSubAccountNo != null and subAccountQuery.bSubAccountNo != ''">
                    and subAccount.b_sub_account_no = #{subAccountQuery.bSubAccountNo,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.merchantNo != null and subAccountQuery.merchantNo != ''">
                    and subAccount.merchant_no = #{subAccountQuery.merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.subMerchantNo != null and subAccountQuery.subMerchantNo != ''">
                    and subAccount.sub_merchant_no = #{subAccountQuery.subMerchantNo,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.status != null">
                    and subAccount.status = #{subAccountQuery.status,jdbcType=INTEGER}
                </if>
                <if test="subAccountQuery.scenes != null and subAccountQuery.scenes != ''">
                    and subAccount.scenes = #{subAccountQuery.scenes,jdbcType=VARCHAR}
                </if>
                <if test="subAccountQuery.utcCreate != null">
                    and subAccount.utc_create = #{subAccountQuery.utcCreate,jdbcType=TIMESTAMP}
                </if>
                <if test="subAccountQuery.beginCreateTime != null">
                    and subAccount.utc_create >= #{subAccountQuery.beginCreateTime,jdbcType=TIMESTAMP}
                </if>
                <if test="subAccountQuery.endCreateTime != null">
                    <![CDATA[ and subAccount.utc_create <= #{subAccountQuery.endCreateTime,jdbcType=TIMESTAMP}]]>
                </if>
            </if>
        </where>
        order by account.account_id
        <if test="subAccountQuery !=null">
            ,subAccount.utc_create desc
        </if>
        limit 500
    </select>

</mapper>
