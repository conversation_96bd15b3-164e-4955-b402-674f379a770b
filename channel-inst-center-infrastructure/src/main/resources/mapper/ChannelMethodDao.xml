<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.ChannelMethodDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.ChannelMethodEntity">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
        <result column="channel_method_code" jdbcType="VARCHAR" property="channelMethodCode" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="country" jdbcType="CHAR" property="country" />
        <result column="currency" jdbcType="CHAR" property="currency" />
        <result column="target_org" jdbcType="VARCHAR" property="targetOrg" />
        <result column="payment_tool" jdbcType="VARCHAR" property="paymentTool" />
        <result column="payment_flow" jdbcType="VARCHAR" property="paymentFlow" />
        <result column="expire_time" jdbcType="INTEGER" property="expireTime" />
        <result column="is_support_refund" jdbcType="TINYINT" property="isSupportRefund" />
        <result column="is_partial_refund" jdbcType="TINYINT" property="isPartialRefund" />
        <result column="is_support_cancel" jdbcType="TINYINT" property="isSupportCancel" />
        <result column="additional_tax_code" jdbcType="VARCHAR" property="additionalTaxCode" />
        <result column="funding_cost" jdbcType="VARCHAR" property="fundingCost" />
        <result column="config_json" jdbcType="VARCHAR" property="configJson" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_need_report_merchant" jdbcType="TINYINT" property="isNeedReportMerchant" />
    </resultMap>

    <sql id="columns">
        id, channel_code, channel_method_code, product_code, country, currency, target_org, payment_tool,
    payment_flow, expire_time, is_support_refund, is_partial_refund, is_support_cancel, additional_tax_code, funding_cost,
    config_json, status, is_need_report_merchant
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from tb_funding_channel_method
        where status = 1
        <if test="country != null and country != ''">
            and country = #{country,jdbcType=VARCHAR}
        </if>
        <if test="channelMethodCode != null and channelMethodCode != ''">
            and channel_method_code = #{channelMethodCode,jdbcType=VARCHAR}
        </if>
        <if test="fundingCost != null and fundingCost != ''">
            and funding_cost = #{fundingCost,jdbcType=VARCHAR}
        </if>
        <if test="channelCode != null and channelCode != ''">
            and channel_code = #{channelCode,jdbcType=VARCHAR}
        </if>
        <if test="currency != null and currency != ''">
            and currency = #{currency,jdbcType=VARCHAR}
        </if>
        <if test="targetOrg != null and targetOrg != ''">
            and target_org = #{targetOrg,jdbcType=VARCHAR}
        </if>
        <if test="paymentTool != null and paymentTool != ''">
            and payment_tool = #{paymentTool,jdbcType=VARCHAR}
        </if>
        <if test="paymentFlow != null and paymentFlow != ''">
            and payment_flow = #{paymentFlow,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
