<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstContractProductSettlementDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstContractProductSettlementEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
        <result column="inst_product_code" jdbcType="VARCHAR" property="instProductCode" />
        <result column="inst_product_capability_code" jdbcType="VARCHAR" property="instProductCapabilityCode" />
        <result column="settlement_info" jdbcType="LONGVARCHAR" property="settlementInfo" />
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    </resultMap>
    <sql id="Base_Column_List">
        id,contract_no, inst_product_code, inst_product_capability_code, settlement_info,
    utc_create, utc_modified
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from tb_inst_contract_product_settlement
        where 1=1
        <if test="contractNo != null and contractNo != ''">
            and contract_no = #{contractNo,jdbcType=VARCHAR}
        </if>
        <if test="instProductCode != null and instProductCode != ''">
            and inst_product_code = #{instProductCode,jdbcType=VARCHAR}
        </if>
        <if test="instProductCapabilityCode != null and instProductCapabilityCode != ''">
            and inst_product_capability_code = #{instProductCapabilityCode,jdbcType=VARCHAR}
        </if>
        <if test="settlementInfo != null and settlementInfo != ''">
            and settlement_info = #{settlementInfo,jdbcType=LONGVARCHAR}
        </if>
    </select>
</mapper>
