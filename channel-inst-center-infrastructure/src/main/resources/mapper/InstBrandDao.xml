<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstBrandDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity">
    <id column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_code" jdbcType="VARCHAR" property="brandCode" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity">
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="brandId">
        SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_inst_brand (brand_code, brand_name,
      bd_id, bd_name, status)
    values (#{brandCode,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR},
      #{bdId,jdbcType=VARCHAR}, #{bdName,jdbcType=VARCHAR}, 'Y')
  </insert>
  <insert id="insertDirect" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity">
    insert into tb_inst_brand (brand_id, brand_code, brand_name,
    bd_id, bd_name, status)
    values (#{brandId,jdbcType=VARCHAR},#{brandCode,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR},
    #{bdId,jdbcType=VARCHAR}, #{bdName,jdbcType=VARCHAR}, 'Y')
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity">
    update tb_inst_brand
    <set>
      <if test="brandCode != null and brandCode != ''">
        brand_code = #{brandCode,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null and brandName != ''">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="bdId != null and bdId != ''">
        bd_id = #{bdId,jdbcType=VARCHAR},
      </if>
      <if test="bdName != null and bdName != ''">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=CHAR},
      </if>
    </set>
    where brand_id = #{brandId,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select brand_id, brand_code, brand_name, bd_id, bd_name, status, utc_create, utc_modified
    from tb_inst_brand
    where status = 'Y'
    <if test="brandId != null">
      and brand_id = #{brandId,jdbcType=BIGINT}
    </if>
    <if test="brandCode != null and brandCode != ''">
      and brand_code = #{brandCode,jdbcType=VARCHAR}
    </if>
    <if test="brandName != null and brandName != ''">
      and brand_name = #{brandName,jdbcType=VARCHAR}
    </if>
    <if test="bdId != null and bdId != ''">
      and bd_id = #{bdId,jdbcType=VARCHAR}
    </if>
    <if test="bdName != null and bdName != ''">
      and bd_name = #{bdName,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectByBdIds" resultMap="BaseResultMap">
    select brand_id, brand_code, brand_name, bd_id, bd_name, status, utc_create, utc_modified
    from tb_inst_brand
    where status = 'Y'
    and bd_id in
    <foreach collection="bdIds" index="index" item="bdId" open="(" separator="," close=")">
      #{bdId}
    </foreach>
  </select>
</mapper>