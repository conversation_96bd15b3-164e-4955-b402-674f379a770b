<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.repository.mapper.FundingChannelProductInfoMapper">

    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.repository.po.FundingChannelProductInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="paymentMethodType" column="payment_method_type" jdbcType="VARCHAR"/>
            <result property="paymentType" column="payment_type" jdbcType="VARCHAR"/>
            <result property="customerType" column="customer_type" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_code,payment_method_type,
        payment_type,customer_type,status,
        create_time,modified_time
    </sql>
</mapper>
