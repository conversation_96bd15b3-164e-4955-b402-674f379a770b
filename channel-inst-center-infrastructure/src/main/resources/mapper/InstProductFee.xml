<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstProductFeeDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="inst_product_code" jdbcType="VARCHAR" property="instProductCode" />
    <result column="inst_product_capability_code" jdbcType="VARCHAR" property="instProductCapabilityCode" />
    <result column="base_rate" jdbcType="VARCHAR" property="baseRate" />
    <result column="rate_time_type" jdbcType="VARCHAR" property="rateTimeType" />
    <result column="rate_time" jdbcType="VARCHAR" property="rateTime" />
    <result column="base_rate_source" jdbcType="VARCHAR" property="baseRateSource" />
    <result column="is_use_ndf" jdbcType="CHAR" property="isUseNdf" />
    <result column="ndf_currency" jdbcType="VARCHAR" property="ndfCurrency" />
    <result column="has_access_fee" jdbcType="CHAR" property="hasAccessFee" />
    <result column="access_fee_detail" jdbcType="VARCHAR" property="accessFeeDetail" />
    <result column="is_charge_afterward" jdbcType="CHAR" property="isChargeAfterward" />
    <result column="charge_afterward_detail" jdbcType="VARCHAR" property="chargeAfterwardDetail" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity">
    insert into tb_inst_product_fee (contract_no,inst_product_code,base_rate, rate_time_type,
      rate_time, base_rate_source, is_use_ndf,
      ndf_currency, has_access_fee, access_fee_detail,
      is_charge_afterward,charge_afterward_detail
      )
    values (#{contractNo,jdbcType=VARCHAR},#{instProductCode,jdbcType=VARCHAR}, #{baseRate,jdbcType=VARCHAR}, #{rateTimeType,jdbcType=VARCHAR},
      #{rateTime,jdbcType=VARCHAR}, #{baseRateSource,jdbcType=VARCHAR}, #{isUseNdf,jdbcType=CHAR},
      #{ndfCurrency,jdbcType=VARCHAR}, #{hasAccessFee,jdbcType=CHAR}, #{accessFeeDetail,jdbcType=VARCHAR},
      #{isChargeAfterward,jdbcType=CHAR},#{chargeAfterwardDetail,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity">
    update tb_inst_product_fee
    <set>
      <if test="instProductCode != null and instProductCode != ''">
        inst_product_code = #{instProductCode,jdbcType=VARCHAR},
      </if>
      <if test="baseRate != null and baseRate != ''">
        base_rate = #{baseRate,jdbcType=VARCHAR},
      </if>
      <if test="rateTimeType != null and rateTimeType != ''">
        rate_time_type = #{rateTimeType,jdbcType=VARCHAR},
      </if>
      <if test="rateTime != null and rateTime != ''">
        rate_time = #{rateTime,jdbcType=VARCHAR},
      </if>
      <if test="baseRateSource != null and baseRateSource != ''">
        base_rate_source = #{baseRateSource,jdbcType=VARCHAR},
      </if>
      <if test="isUseNdf != null and isUseNdf != ''">
        is_use_ndf = #{isUseNdf,jdbcType=CHAR},
      </if>
      <if test="ndfCurrency != null and ndfCurrency != ''">
        ndf_currency = #{ndfCurrency,jdbcType=VARCHAR},
      </if>
      <if test="hasAccessFee != null and hasAccessFee != ''">
        has_access_fee = #{hasAccessFee,jdbcType=CHAR},
      </if>
      <if test="accessFeeDetail != null and accessFeeDetail != ''">
        access_fee_detail = #{accessFeeDetail,jdbcType=VARCHAR},
      </if>
      <if test="isChargeAfterward != null and isChargeAfterward != ''">
        is_charge_afterward = #{isChargeAfterward,jdbcType=CHAR},
      </if>
      <if test="chargeAfterwardDetail != null and chargeAfterwardDetail != ''">
        charge_afterward_detail = #{chargeAfterwardDetail,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByContractNoAndProductCodes" resultMap="BaseResultMap">
    select id, contract_no,inst_product_code,base_rate, rate_time_type, rate_time, base_rate_source, is_use_ndf, ndf_currency,
    has_access_fee, access_fee_detail, is_charge_afterward,charge_afterward_detail, utc_create, utc_modified
    from tb_inst_product_fee
    where contract_no = #{contractNo,jdbcType=VARCHAR}
    and inst_product_code  in
    <foreach collection="productCodes" index="index" item="productCode" open="(" separator="," close=")">
      #{productCode}
    </foreach>
  </select>

  <delete id="deleteById">
    delete from tb_inst_product_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByEntity">
    delete from tb_inst_product_fee
    where contract_no = #{contractNo,jdbcType=VARCHAR}
    and inst_product_code = #{instProductCode,jdbcType=VARCHAR}
  </delete>

  <select id="selectAll" resultMap="BaseResultMap">
    select id, contract_no,inst_product_code,inst_product_capability_code,base_rate, rate_time_type, rate_time, base_rate_source, is_use_ndf, ndf_currency,
    has_access_fee, access_fee_detail, is_charge_afterward,charge_afterward_detail, utc_create, utc_modified
    from tb_inst_product_fee
    where 1=1
    <if test="instProductCode != null and instProductCode != ''">
      and inst_product_code = #{instProductCode,jdbcType=VARCHAR},
    </if>
    <if test="instProductCapabilityCode != null and instProductCapabilityCode != ''">
      and inst_product_capability_code = #{instProductCapabilityCode,jdbcType=VARCHAR},
    </if>
    <if test="baseRate != null and baseRate != ''">
      and base_rate = #{baseRate,jdbcType=VARCHAR},
    </if>
    <if test="rateTimeType != null and rateTimeType != ''">
      and rate_time_type = #{rateTimeType,jdbcType=VARCHAR},
    </if>
    <if test="rateTime != null and rateTime != ''">
      and rate_time = #{rateTime,jdbcType=VARCHAR},
    </if>
    <if test="baseRateSource != null and baseRateSource != ''">
      and base_rate_source = #{baseRateSource,jdbcType=VARCHAR},
    </if>
    <if test="isUseNdf != null and isUseNdf != ''">
      and is_use_ndf = #{isUseNdf,jdbcType=CHAR},
    </if>
    <if test="ndfCurrency != null and ndfCurrency != ''">
      and ndf_currency = #{ndfCurrency,jdbcType=VARCHAR},
    </if>
    <if test="hasAccessFee != null and hasAccessFee != ''">
      and has_access_fee = #{hasAccessFee,jdbcType=CHAR},
    </if>
    <if test="accessFeeDetail != null and accessFeeDetail != ''">
      and access_fee_detail = #{accessFeeDetail,jdbcType=VARCHAR},
    </if>
    <if test="isChargeAfterward != null and isChargeAfterward != ''">
      and is_charge_afterward = #{isChargeAfterward,jdbcType=CHAR},
    </if>
    <if test="chargeAfterwardDetail != null and chargeAfterwardDetail != ''">
      and charge_afterward_detail = #{chargeAfterwardDetail,jdbcType=VARCHAR},
    </if>
    order by utc_modified desc
  </select>
</mapper>
