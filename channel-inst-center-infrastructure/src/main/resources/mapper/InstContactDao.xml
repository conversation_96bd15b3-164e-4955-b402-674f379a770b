<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstContactDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone_no" jdbcType="VARCHAR" property="phoneNo" />
    <result column="im_no" jdbcType="VARCHAR" property="imNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity">
    insert into tb_inst_contact (id, inst_id, name, 
      position, title, email, 
      phone_no, im_no, remark, status)
    values (#{id,jdbcType=BIGINT}, #{instId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
      #{position,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{phoneNo,jdbcType=VARCHAR}, #{imNo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},'Y')
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity">
    update tb_inst_contact
    <set>
      <if test="instId != null and instId != ''">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="name != null and name != ''">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="position != null and position != ''">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="title != null and title != ''">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="email != null and email != ''">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phoneNo != null and phoneNo != ''">
        phone_no = #{phoneNo,jdbcType=VARCHAR},
      </if>
      <if test="imNo != null and imNo != ''">
        im_no = #{imNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, inst_id, name, position, title, email, phone_no, im_no, remark, status,
    utc_create, utc_modified
    from tb_inst_contact
    where status = 'Y'
    and inst_id = #{instId,jdbcType=BIGINT}
  </select>
  <select id="selectByInstIds" resultMap="BaseResultMap">
    select id, inst_id, name, position, title, email, phone_no, im_no, remark, status,
    utc_create, utc_modified
    from tb_inst_contact
    where status = 'Y'
    and inst_id in
    <foreach collection="instIds" index="index" item="instId" open="(" separator="," close=")">
      #{instId}
    </foreach>
  </select>
</mapper>