<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsAgreementMapper">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO">
        <id column="funds_agreement_no" jdbcType="VARCHAR" property="fundsAgreementNo" />
        <result column="biz_agreement_no" jdbcType="VARCHAR" property="bizAgreementNo" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="clearing_ccy" jdbcType="VARCHAR" property="clearingCcy" />
        <result column="clearing_pattern" jdbcType="VARCHAR" property="clearingPattern" />
        <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
        <result column="target_org" jdbcType="VARCHAR" property="targetOrg" />
        <result column="card_org" jdbcType="VARCHAR" property="cardOrg" />
        <result column="mid" jdbcType="VARCHAR" property="mid" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="memo" jdbcType="VARCHAR" property="memo" />
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
        <association property="bizAgreement" javaType="com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO">
            <id column="biz_agreement_no" jdbcType="VARCHAR" property="bizAgreementNo" />
            <result column="biz_name" jdbcType="VARCHAR" property="name" />
            <result column="biz_type" jdbcType="VARCHAR" property="type" />
            <result column="initiator" jdbcType="VARCHAR" property="initiator" />
            <result column="counter" jdbcType="VARCHAR" property="counter" />
            <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
            <result column="biz_utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
            <result column="biz_utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
        </association>
        <collection property="settleRules" ofType="com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO" javaType="list">
            <id column="settle_rule_no" jdbcType="VARCHAR" property="settleRuleNo" />
            <result column="funds_agreement_no" jdbcType="VARCHAR" property="fundsAgreementNo" />
            <result column="timezone" jdbcType="VARCHAR" property="timezone" />
            <result column="cutoff_time" jdbcType="VARCHAR" property="cutoffTime" />
            <result column="clearing_range_start" jdbcType="VARCHAR" property="clearingRangeStart" />
            <result column="clearing_range_end" jdbcType="VARCHAR" property="clearingRangeEnd" />
            <result column="settle_clearing_pattern" jdbcType="VARCHAR" property="clearingPattern" />
            <result column="clear_off_pattern" jdbcType="VARCHAR" property="clearOffPattern" />
            <result column="clear_off_time" jdbcType="VARCHAR" property="clearOffTime" />
            <result column="clear_off_type" jdbcType="VARCHAR" property="clearOffType" />
            <result column="settle_min_amount" jdbcType="DECIMAL" property="settleMinAmount" />
            <result column="settle_ccy" jdbcType="VARCHAR" property="settleCcy" />
            <result column="settle_account" jdbcType="VARCHAR" property="settleAccount" />
            <result column="settle_arrived" jdbcType="VARCHAR" property="settleArrived" />
            <result column="settle_utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
            <result column="settle_utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
        funds.*
    </sql>
    <sql id="Biz_Column_List">
        biz.counter, biz.initiator, biz.contract_no,
        biz.name AS biz_name, biz.type AS biz_type, biz.utc_create AS biz_utc_create, biz.utc_modified AS biz_utc_modified
    </sql>
    <sql id="Settle_column_List">
        settle.settle_rule_no, settle.funds_agreement_no, settle.timezone, settle.cutoff_time, settle.clearing_range_start, settle.clearing_range_end,
        settle.clear_off_pattern, settle.clear_off_time, settle.clear_off_type, settle.settle_min_amount, settle.settle_ccy, settle.settle_account, settle.settle_arrived,
        settle.clearing_pattern AS settle_clearing_pattern, settle.utc_create AS settle_utc_create, settle.utc_modified AS settle_utc_modified
    </sql>

    <select id="selectByConditions" parameterType="map" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
            ,
            <include refid="Biz_Column_List"/>
            ,
            <include refid="Settle_column_List"/>
            from tb_inst_funds_agreement funds
            LEFT JOIN tb_inst_biz_agreement biz
            ON funds.biz_agreement_no = biz.biz_agreement_no
            LEFT JOIN tb_inst_funds_settle_rule settle
            ON funds.funds_agreement_no = settle.funds_agreement_no
        WHERE 1=1
        <if test="type != null and type != ''">
            and funds.type = #{type}
        </if>
        <if test="name != null and name != ''">
            and funds.name like CONCAT('%', #{name}, '%')
        </if>
        <if test="clearingCurrency != null and clearingCurrency != ''">
            and funds.clearing_ccy = #{clearingCurrency}
        </if>
        <if test="status != null and status != ''">
            and funds.status = #{status}
        </if>
        <if test="counter != null and counter != ''">
            and biz.counter = #{counter}
        </if>
        <if test="initiator != null and initiator != ''">
            and biz.initiator = #{initiator}
        </if>
        order by funds.utc_modified desc
    </select>

    <select id="selectById" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Biz_Column_List"/>
        ,
        <include refid="Settle_column_List"/>
        from tb_inst_funds_agreement funds
        LEFT JOIN tb_inst_biz_agreement biz
        ON funds.biz_agreement_no = biz.biz_agreement_no
        LEFT JOIN tb_inst_funds_settle_rule settle
        ON funds.funds_agreement_no = settle.funds_agreement_no
        WHERE funds.funds_agreement_no = #{id}
    </select>

    <select id="selectSettleRulesByFundsAgreement" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Settle_column_List" />
        from tb_inst_funds_settle_rule settle
        WHERE settle.funds_agreement_no = #{funds_agreement_no}
    </select>
</mapper>