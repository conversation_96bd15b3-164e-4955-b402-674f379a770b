<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstBankAccountDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="country" jdbcType="CHAR" property="country" />
    <result column="currency" jdbcType="CHAR" property="currency" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="branch" jdbcType="VARCHAR" property="branch" />
    <result column="branch_address" jdbcType="VARCHAR" property="branchAddress" />
    <result column="swift_code" jdbcType="VARCHAR" property="swiftCode" />
    <result column="iban" jdbcType="VARCHAR" property="iban" />
    <result column="account_use" jdbcType="VARCHAR" property="accountUse" />
    <result column="recharge_can_use_ccy" jdbcType="VARCHAR" property="rechargeCanUseCcy" />
    <result column="recharge_use_order" jdbcType="VARCHAR" property="rechargeUseOrder" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity">
    insert into tb_inst_bank_account (id, inst_id, country,
      currency, bank_code, bank_name,
      account_name, account_no, branch,
      branch_address, swift_code, iban, account_use, recharge_can_use_ccy, recharge_use_order, remark, status)
    values (#{id,jdbcType=BIGINT}, #{instId,jdbcType=BIGINT}, #{country,jdbcType=CHAR},
      #{currency,jdbcType=CHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR},
      #{accountName,jdbcType=VARCHAR}, #{accountNo,jdbcType=VARCHAR}, #{branch,jdbcType=VARCHAR},
      #{branchAddress,jdbcType=VARCHAR}, #{swiftCode,jdbcType=VARCHAR}, #{iban,jdbcType=VARCHAR}, #{accountUse,jdbcType=VARCHAR},
      #{rechargeCanUseCcy,jdbcType=VARCHAR}, #{rechargeUseOrder,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},'Y')
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity">
    update tb_inst_bank_account
    <set>
      <if test="instId != null and instId != ''">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="country != null and country != ''">
        country = #{country,jdbcType=CHAR},
      </if>
      <if test="currency != null and currency != ''">
        currency = #{currency,jdbcType=CHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        bank_code = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null and bankName != ''">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null and accountName != ''">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null and accountNo != ''">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="branch != null and branch != ''">
        branch = #{branch,jdbcType=VARCHAR},
      </if>
      <if test="branchAddress != null and branchAddress != ''">
        branch_address = #{branchAddress,jdbcType=VARCHAR},
      </if>
      <if test="swiftCode != null and swiftCode != ''">
        swift_code = #{swiftCode,jdbcType=VARCHAR},
      </if>
      <if test="iban != null and iban != ''">
        iban = #{iban,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="accountUse != null and accountUse != ''">
         account_use like concat('%', #{accountUse,jdbcType=VARCHAR}, '%'),
      </if>
      <if test="rechargeCanUseCcy != null and rechargeCanUseCcy != ''">
        recharge_can_use_ccy = #{rechargeCanUseCcy,jdbcType=VARCHAR},
      </if>
      <if test="rechargeUseOrder != null and rechargeUseOrder != ''">
        recharge_use_order = #{rechargeUseOrder,jdbcType=VARCHAR}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCondition" resultMap="BaseResultMap">
    select id, inst_id, country, currency, bank_code, bank_name, account_name, account_no,
    branch, branch_address, swift_code, iban, account_use, recharge_can_use_ccy, recharge_use_order, remark, status, utc_create, utc_modified
    from tb_inst_bank_account
    where status = 'Y'
    <if test="instId != null and instId != ''">
      and inst_id = #{instId,jdbcType=BIGINT}
    </if>
    <if test="accountUse != null and accountUse != ''">
      and account_use like concat('%', #{accountUse,jdbcType=VARCHAR}, '%')
    </if>
  </select>
</mapper>