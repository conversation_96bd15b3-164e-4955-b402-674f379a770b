<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementScheduleDetailDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity">
    <id column="requirement_schedule_id" jdbcType="BIGINT" property="requirementScheduleId" />
    <id column="inst_product_code" jdbcType="VARCHAR" property="instProductCode" />
    <id column="inst_product_capability_code" jdbcType="VARCHAR" property="instProductCapabilityCode" />
    <result column="is_configured" jdbcType="CHAR" property="isConfigured" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity">
    insert into tb_inst_requirement_schedule_detail (requirement_schedule_id, inst_product_code, 
      inst_product_capability_code, is_configured, version)
    values (#{requirementScheduleId,jdbcType=BIGINT}, #{instProductCode,jdbcType=VARCHAR}, 
      #{instProductCapabilityCode,jdbcType=VARCHAR}, #{isConfigured,jdbcType=CHAR}, #{version,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity">
    update tb_inst_requirement_schedule_detail
    <set>
      <if test="isConfigured != null and isConfigured != ''">
        is_configured = #{isConfigured,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        version = #{version,jdbcType=VARCHAR},
      </if>
    </set>
    where requirement_schedule_id = #{requirementScheduleId,jdbcType=BIGINT}
      and inst_product_code = #{instProductCode,jdbcType=VARCHAR}
      and inst_product_capability_code = #{instProductCapabilityCode,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select requirement_schedule_id, inst_product_code, inst_product_capability_code, 
    is_configured, version, utc_create, utc_modified
    from tb_inst_requirement_schedule_detail
    <where>
      <if test="requirementScheduleId != null">
        and requirement_schedule_id = #{requirementScheduleId,jdbcType=BIGINT}
      </if>
      <if test="requirementScheduleIds != null and requirementScheduleIds.size() > 0">
        and requirement_schedule_id in
        <foreach item="item" index="index" collection="requirementScheduleIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="version != null and version != ''">
        and version = #{version,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>