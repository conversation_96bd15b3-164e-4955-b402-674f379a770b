<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstBusinessDictDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
        <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
        <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
        <result column="status" jdbcType="CHAR" property="status" />
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    </resultMap>
    <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity">
        insert into tb_inst_business_dict (business_type,business_no,dict_code,dict_name,extra_info,status)
        values (#{businessType,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR}, #{dictCode,jdbcType=VARCHAR},
        #{dictName,jdbcType=VARCHAR}, #{extraInfo,jdbcType=LONGVARCHAR}, #{status,jdbcType=CHAR})
    </insert>
    <insert id="insertBatch">
        insert into tb_inst_business_dict (business_type,business_no,dict_code,dict_name,extra_info,status)
        values
        <foreach collection="records" index="index" item="item" separator=",">
            (#{item.businessType,jdbcType=VARCHAR}, #{item.businessNo,jdbcType=VARCHAR},
            #{item.dictCode,jdbcType=VARCHAR}, #{item.dictName,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=LONGVARCHAR},
            'Y')
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity">
        update tb_inst_business_dict
        <set>
            <if test="businessNo != null and businessNo != ''">
                business_no = #{businessNo,jdbcType=VARCHAR},
            </if>
            <if test="dictName != null and dictName != ''">
                dict_name = #{dictName,jdbcType=VARCHAR},
            </if>
            <if test="extraInfo != null and extraInfo != ''">
                extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where business_type = #{businessType,jdbcType=VARCHAR}
        and dict_code = #{dictCode,jdbcType=VARCHAR}
    </update>
    <select id="selectByBusinessType" resultMap="BaseResultMap">
        select id,business_type,business_no,dict_code,dict_name,extra_info,status,utc_create,utc_modified
        from tb_inst_business_dict
        where business_type = #{item.businessType,jdbcType=VARCHAR}
    </select>
    <delete id="delete">
        delete from tb_inst_business_dict
        where
        id = #{id,jdbcType=BIGINT}
    </delete>
</mapper>