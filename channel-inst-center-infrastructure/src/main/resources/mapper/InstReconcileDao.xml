<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstReconcileDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="invoice_provider" jdbcType="VARCHAR" property="invoiceProvider" />
    <result column="reconcile_method" jdbcType="VARCHAR" property="reconcileMethod" />
    <result column="reconcile_template" jdbcType="VARCHAR" property="reconcileTemplate" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity">
    insert into tb_inst_reconcile (requirement_order_id, inst_id,
      channel_type, invoice_provider, reconcile_method, reconcile_template)
    values (#{requirementOrderId,jdbcType=BIGINT}, #{instId,jdbcType=BIGINT},
      #{channelType,jdbcType=VARCHAR}, #{invoiceProvider,jdbcType=VARCHAR}, #{reconcileMethod,jdbcType=VARCHAR},
      #{reconcileTemplate,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity">
    update tb_inst_reconcile
    <set>
      <if test="requirementOrderId != null">
        requirement_order_id = #{requirementOrderId,jdbcType=BIGINT},
      </if>
      <if test="instId != null">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null and channelType != ''">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProvider != null and invoiceProvider != ''">
        invoice_provider = #{invoiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="reconcileMethod != null and reconcileMethod != ''">
        reconcile_method = #{reconcileMethod,jdbcType=VARCHAR},
      </if>
      <if test="reconcileTemplate != null and reconcileTemplate != ''">
        reconcile_template = #{reconcileTemplate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, requirement_order_id, inst_id, channel_type, invoice_provider, reconcile_method, reconcile_template,
    utc_create, utc_modified
    from tb_inst_reconcile
    <where>
      <if test="requirementOrderId != null">
        and requirement_order_id = #{requirementOrderId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>