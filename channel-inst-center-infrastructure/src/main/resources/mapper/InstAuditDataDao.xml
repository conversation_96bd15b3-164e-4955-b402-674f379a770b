<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstAuditDataDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="data_attach_id" jdbcType="VARCHAR" property="dataAttachId" />
    <result column="id_number" jdbcType="VARCHAR" property="idNumber" />
    <result column="id_validity_date" jdbcType="DATE" property="idValidityDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="has_watermark_word" jdbcType="VARCHAR" property="hasWatermarkWord" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity">
    insert into tb_inst_audit_data (business_type, business_no,
      data_type, data_attach_id, id_number, 
      id_validity_date, remark, has_watermark_word)
    values (#{businessType,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR},
      #{dataType,jdbcType=VARCHAR}, #{dataAttachId,jdbcType=VARCHAR}, #{idNumber,jdbcType=VARCHAR}, 
      #{idValidityDate,jdbcType=DATE}, #{remark,jdbcType=VARCHAR}, #{hasWatermarkWord,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity">
    update tb_inst_audit_data
    <set>
      <if test="businessType != null and businessType != ''">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null and businessNo != ''">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null and dataType != ''">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="dataAttachId != null and dataAttachId != ''">
        data_attach_id = #{dataAttachId,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null and idNumber != ''">
        id_number = #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="idValidityDate != null">
        id_validity_date = #{idValidityDate,jdbcType=DATE},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="hasWatermarkWord != null and hasWatermarkWord != ''">
        has_watermark_word = #{hasWatermarkWord,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, business_type, business_no, data_type, data_attach_id, id_number, id_validity_date, 
    remark, has_watermark_word, utc_create, utc_modified
    from tb_inst_audit_data
    where 1=1
    <if test="id != null">
      and id = #{id,jdbcType=BIGINT}
    </if>
    <if test="businessType != null and businessType != ''">
      and business_type = #{businessType,jdbcType=VARCHAR}
    </if>
    <if test="businessNo != null and businessNo != ''">
      and business_no = #{businessNo,jdbcType=VARCHAR}
    </if>
    <if test="dataType != null and dataType != ''">
      and data_type = #{dataType,jdbcType=VARCHAR}
    </if>
    <if test="dataAttachId != null and dataAttachId != ''">
      and data_attach_id = #{dataAttachId,jdbcType=VARCHAR}
    </if>
    <if test="idNumber != null and idNumber != ''">
      and id_number = #{idNumber,jdbcType=VARCHAR}
    </if>
    <if test="idValidityDate != null">
      and id_validity_date = #{idValidityDate,jdbcType=DATE}
    </if>
    <if test="remark != null and remark != ''">
      and remark = #{remark,jdbcType=VARCHAR}
    </if>
  </select>
</mapper>