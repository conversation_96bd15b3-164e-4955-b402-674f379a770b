<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.ChannelInfoDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.ChannelInfoEntity">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="entity" jdbcType="VARCHAR" property="entity"/>
        <result column="technical_org" jdbcType="VARCHAR" property="technicalOrg"/>
        <result column="funds_settle_org" jdbcType="VARCHAR" property="fundsSettleOrg"/>
        <result column="biz_handle_org" jdbcType="VARCHAR" property="bizHandleOrg"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="interactive_mode" jdbcType="TINYINT" property="interactiveMode"/>
        <result column="is_need_report_merchant" jdbcType="TINYINT" property="isNeedReportMerchant"/>
        <result column="trans_code_map_group_id" jdbcType="VARCHAR" property="transCodeMapGroupId"/>
        <result column="response_code_map_group_id" jdbcType="VARCHAR" property="responseCodeMapGroupId"/>
        <result column="config_json" jdbcType="VARCHAR" property="configJson"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="funds_settle_inst_code" jdbcType="VARCHAR" property="fundsSettleInstCode"/>
        <result column="biz_handle_inst_code" jdbcType="VARCHAR" property="bizHandleInstCode"/>
        <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime"/>
    </resultMap>

    <sql id="columns">
        id
        , channel_code, entity, technical_org, funds_settle_org, biz_handle_org, channel_type, interactive_mode,
    is_need_report_merchant, trans_code_map_group_id, response_code_map_group_id, config_json, status,modified_time,funds_settle_inst_code,biz_handle_inst_code
    </sql>

    <select id="selectAll" resultMap="BaseResultMap"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.ChannelInfoEntity">
        select
        <include refid="columns"/>
        from tb_funding_channel_info
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="channelCode != null and channelCode != ''">
                and channel_code like concat('%', #{channelCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="entity != null and entity != ''">
                and entity = #{entity,jdbcType=VARCHAR}
            </if>
            <if test="technicalOrg != null and technicalOrg != ''">
                and technical_org like concat('%', #{technicalOrg,jdbcType=VARCHAR}, '%')
            </if>
            <if test="fundsSettleOrg != null and fundsSettleOrg != ''">
                and funds_settle_org like concat('%', #{fundsSettleOrg,jdbcType=VARCHAR}, '%')
            </if>
            <if test="bizHandleOrg != null and bizHandleOrg != ''">
                and biz_handle_org like concat('%', #{bizHandleOrg,jdbcType=VARCHAR}, '%')
            </if>
            <if test="channelType != null and channelType != ''">
                and channel_type = #{channelType,jdbcType=VARCHAR}
            </if>
            <if test="interactiveMode != null">
                and interactive_mode = #{interactiveMode,jdbcType=TINYINT}
            </if>
            <if test="isNeedReportMerchant != null">
                and is_need_report_merchant = #{isNeedReportMerchant,jdbcType=TINYINT}
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <update id="updateChannel" parameterType="com.payermax.channel.inst.center.infrastructure.entity.ChannelInfoEntity">
        update tb_funding_channel_info
        <set>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="channelCode != null and channelCode != ''">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="entity != null and entity != ''">
                entity = #{entity,jdbcType=VARCHAR},
            </if>
            <if test="technicalOrg != null and technicalOrg != ''">
                technical_org = #{technicalOrg,jdbcType=VARCHAR},
            </if>
            <if test="fundsSettleOrg != null and fundsSettleOrg != ''">
                funds_settle_org = #{fundsSettleOrg,jdbcType=VARCHAR},
            </if>
            <if test="bizHandleOrg != null and bizHandleOrg != ''">
                biz_handle_org = #{bizHandleOrg,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != ''">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="interactiveMode != null">
                interactive_mode = #{interactiveMode,jdbcType=TINYINT},
            </if>
            <if test="isNeedReportMerchant != null">
                is_need_report_merchant = #{isNeedReportMerchant,jdbcType=TINYINT},
            </if>
            <if test="transCodeMapGroupId != null and transCodeMapGroupId != ''">
                trans_code_map_group_id = #{transCodeMapGroupId,jdbcType=VARCHAR},
            </if>
            <if test="responseCodeMapGroupId != null and responseCodeMapGroupId != ''">
                response_code_map_group_id = #{responseCodeMapGroupId,jdbcType=VARCHAR},
            </if>
            <if test="configJson != null and configJson != ''">
                config_json = #{configJson,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="listAllChannelCodeOnDistinct" resultType="String">
        select
            distinct(channel_code)
        from tb_funding_channel_info
    </select>

    <select id="listAllBizHandleOrgOnDistinct" resultType="String">
        select
            distinct (biz_handle_org)
        from tb_funding_channel_info
    </select>

    <select id="getByChannelCode" resultMap="BaseResultMap">
        select <include refid="columns"/>
        from tb_funding_channel_info
        where channel_code = #{channelCode,jdbcType=VARCHAR} limit 1
    </select>

</mapper>