<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementScheduleDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="pd_id" jdbcType="VARCHAR" property="pdId" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="is_start" jdbcType="CHAR" property="isStart" />
    <result column="priority" jdbcType="VARCHAR" property="priority" />
    <result column="tb_url" jdbcType="VARCHAR" property="tbUrl" />
    <result column="plan_release_date" jdbcType="DATE" property="planReleaseDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <resultMap id="PageListResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="pd_id" jdbcType="VARCHAR" property="pdId" />
    <result column="pd_name" jdbcType="VARCHAR" property="pdName" />
    <result column="is_start" jdbcType="CHAR" property="isStart" />
    <result column="priority" jdbcType="VARCHAR" property="priority" />
    <result column="tb_url" jdbcType="VARCHAR" property="tbUrl" />
    <result column="plan_release_date" jdbcType="DATE" property="planReleaseDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="apply_priority" jdbcType="VARCHAR" property="applyPriority" />
    <result column="shareit_entity" jdbcType="VARCHAR" property="shareitEntity" />
    <result column="expect_release_time" jdbcType="VARCHAR" property="expectReleaseTime" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
    <result column="inst_name" jdbcType="VARCHAR" property="instName" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity"
          useGeneratedKeys="true" keyProperty="id">
    insert into tb_inst_requirement_schedule (requirement_order_id,
      pd_id, pd_name, is_start, 
      priority, tb_url, plan_release_date, 
      remark, status)
    values (#{requirementOrderId,jdbcType=BIGINT},
      #{pdId,jdbcType=VARCHAR}, #{pdName,jdbcType=VARCHAR}, #{isStart,jdbcType=CHAR}, 
      #{priority,jdbcType=VARCHAR}, #{tbUrl,jdbcType=VARCHAR}, #{planReleaseDate,jdbcType=DATE},
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity">
    update tb_inst_requirement_schedule
    <set>
      utc_modified = now(),
      <if test="requirementOrderId != null">
        requirement_order_id = #{requirementOrderId,jdbcType=BIGINT},
      </if>
      <if test="pdId != null and pdId != ''">
        pd_id = #{pdId,jdbcType=VARCHAR},
      </if>
      <if test="pdName != null and pdName != ''">
        pd_name = #{pdName,jdbcType=VARCHAR},
      </if>
      <if test="isStart != null and isStart != ''">
        is_start = #{isStart,jdbcType=CHAR},
      </if>
      <if test="priority != null and priority != ''">
        priority = #{priority,jdbcType=VARCHAR},
      </if>
      <if test="tbUrl != null and tbUrl != ''">
        tb_url = #{tbUrl,jdbcType=VARCHAR},
      </if>
      <if test="planReleaseDate != null">
        plan_release_date = #{planReleaseDate,jdbcType=DATE},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, requirement_order_id, pd_id, pd_name, is_start, priority,
    tb_url, plan_release_date, remark, status, utc_create, utc_modified
    from tb_inst_requirement_schedule
    <where>
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>
  <select id="selectScheduleList" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity"
          resultMap="PageListResultMap">
    select schedule.id, schedule.is_start, schedule.priority, schedule.status, schedule.requirement_order_id,
    schedule.plan_release_date, schedule.pd_id, schedule.utc_modified,
    ao.apply_no, ao.inst_id, ao.bd_id, ao.bd_name, ao.am_id, ao.am_name, ao.priority apply_priority, ao.shareit_entity, ao.expect_release_time,
    base.inst_code, base.inst_name, base.inst_id
    from tb_inst_requirement_schedule schedule
    join tb_inst_requirement_order ro on ro.id = schedule.requirement_order_id
    join tb_inst_apply_order ao on ao.apply_no = ro.apply_no
    join tb_inst_base_info base on base.inst_id = ro.inst_id
    join (
    select distinct detail.requirement_schedule_id from tb_inst_requirement_schedule_detail detail
    join tb_inst_product product on detail.inst_product_code = product.product_code
    join tb_inst_product_capability cap on detail.inst_product_capability_code = cap.capability_code
      <where>
        <if test="schedule != null">
          <if test="schedule.channelType != null and schedule.channelType != ''">
            and product.channel_type = #{schedule.channelType,jdbcType=VARCHAR}
          </if>
          <if test="schedule.paymentMethodType != null and schedule.paymentMethodType != ''">
            and product.payment_method_type = #{schedule.paymentMethodType,jdbcType=VARCHAR}
          </if>
          <if test="schedule.country != null and schedule.country != ''">
            and cap.country = #{schedule.country,jdbcType=VARCHAR}
          </if>
        </if>
      </where>
    ) tmp on schedule.id=tmp.requirement_schedule_id
    <where>
      <if test="schedule != null">
        <if test="schedule.applyNo != null and schedule.applyNo != ''">
          and ao.apply_no = #{schedule.applyNo,jdbcType=VARCHAR}
        </if>
        <if test="schedule.shareitEntity != null and schedule.shareitEntity != ''">
          and ao.shareit_entity = #{schedule.shareitEntity,jdbcType=VARCHAR}
        </if>
        <if test="schedule.instName != null and schedule.instName != ''">
          and base.inst_name like concat('%', #{schedule.instName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="schedule.applyPriority != null and schedule.applyPriority != ''">
          and ao.priority = #{schedule.applyPriority,jdbcType=VARCHAR}
        </if>
        <if test="schedule.applyNo != null and schedule.applyNo != ''">
          and ao.apply_no = #{schedule.applyNo,jdbcType=VARCHAR}
        </if>
        <if test="schedule.bdId != null and schedule.bdId != ''">
          and ao.bd_id = #{schedule.bdId,jdbcType=VARCHAR}
        </if>
        <if test="schedule.amId != null and schedule.amId != ''">
          and ao.am_id = #{schedule.amId,jdbcType=VARCHAR}
        </if>
        <if test="schedule.priority != null and schedule.priority != ''">
          and schedule.priority = #{schedule.priority,jdbcType=VARCHAR}
        </if>
        <if test="schedule.status != null and schedule.status != ''">
          and schedule.status = #{schedule.status,jdbcType=VARCHAR}
        </if>
        <if test="schedule.pdId != null and schedule.pdId != ''">
          and schedule.pd_id = #{schedule.pdId,jdbcType=VARCHAR}
        </if>
        <if test="schedule.bdList != null and schedule.bdList.size > 0">
          and ao.bd_id in
          <foreach collection="schedule.bdList" index="index" item="bdId" open="(" separator="," close=")">
            #{bdId}
          </foreach>
        </if>
        <if test="schedule.amList != null and schedule.amList.size > 0">
          and ao.am_id in
          <foreach collection="schedule.amList" index="index" item="amId" open="(" separator="," close=")">
            #{amId}
          </foreach>
        </if>
        <if test="schedule.pdList != null and schedule.pdList.size > 0">
          and schedule.pd_id in
          <foreach collection="schedule.pdList" index="index" item="pdId" open="(" separator="," close=")">
            #{pdId}
          </foreach>
        </if>
        <if test="schedule.expectReleaseTimeFrom != null and schedule.expectReleaseTimeTo != null">
          and ao.expect_release_time between #{schedule.expectReleaseTimeFrom,jdbcType=TIMESTAMP}
          and #{schedule.expectReleaseTimeTo,jdbcType=TIMESTAMP}
        </if>
        <if test="schedule.planReleaseDateFrom != null and schedule.planReleaseDateTo != null">
          and schedule.plan_release_date between #{schedule.planReleaseDateFrom,jdbcType=TIMESTAMP}
          and #{schedule.planReleaseDateTo,jdbcType=TIMESTAMP}
        </if>
      </if>
    </where>
    order by schedule.utc_modified desc
  </select>

</mapper>