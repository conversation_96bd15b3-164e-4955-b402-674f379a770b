<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstSubFundsAccountBucketDao">

    <resultMap id="BaseResultMap"
               type="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity">
        <id property="subAccountId" column="sub_account_id" jdbcType="VARCHAR"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="bucketId" column="bucket_id" jdbcType="BIGINT"/>
        <result property="numberSegmentId" column="number_segment_id" jdbcType="VARCHAR"/>
        <result property="numberSegmentNo" column="number_segment_no" jdbcType="VARCHAR"/>
        <result property="subUseType" column="sub_use_type" jdbcType="VARCHAR"/>
        <result property="subAccountNo" column="sub_account_no" jdbcType="VARCHAR"/>
        <result property="subAccountName" column="sub_account_name" jdbcType="VARCHAR"/>
        <result property="bSubAccountNo" column="b_sub_account_no" jdbcType="VARCHAR"/>
        <result property="merchantNo" column="merchant_no" jdbcType="VARCHAR"/>
        <result property="subMerchantNo" column="sub_merchant_no" jdbcType="VARCHAR"/>
        <result property="subAccountStatus" column="sub_account_status" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
        <result property="accountJson" column="account_json" jdbcType="VARCHAR"/>
        <result property="requestBody" column="request_body" jdbcType="VARCHAR"/>
        <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        sub_account_id
        ,account_id,bucket_id,
        number_segment_id,number_segment_no,sub_use_type,sub_account_status,
        sub_account_no,sub_account_name,b_sub_account_no,merchant_no,sub_merchant_no,
        status,apply_type,account_json,request_body,utc_create,
        utc_modified
    </sql>

    <select id="selectByQueryEntityWithoutMerchantNo" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account_bucket
        <where>
            account_id = #{accountId,jdbcType=VARCHAR}
            and sub_use_type = #{subUseType,jdbcType=VARCHAR}
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=CHAR}
            </if>
            <if test="subAccountStatus != null">
                and sub_account_status = #{subAccountStatus,jdbcType=INTEGER}
            </if>
        </where>
        order by number_segment_no
        limit 20
    </select>
    
    <select id="selectBindMerchantAccountByQueryEntity" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account_bucket
        <where>
            account_id = #{accountId,jdbcType=VARCHAR}
            and sub_use_type = #{subUseType,jdbcType=VARCHAR}
            and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            and sub_merchant_no is null
            <if test="status != null">
                and status = #{status,jdbcType=CHAR}
            </if>
            <if test="subAccountStatus != null">
                and sub_account_status = #{subAccountStatus,jdbcType=INTEGER}
            </if>
        </where>
        order by number_segment_no
        limit 20
    </select>

    <select id="selectBindSubMerchantAccountByQueryEntity" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account_bucket
        <where>
            account_id = #{accountId,jdbcType=VARCHAR}
            and sub_use_type = #{subUseType,jdbcType=VARCHAR}
            and merchant_no = #{merchantNo,jdbcType=VARCHAR}
            and sub_merchant_no = #{subMerchantNo,jdbcType=VARCHAR}
            <if test="status != null">
                and status = #{status,jdbcType=CHAR}
            </if>
            <if test="subAccountStatus != null">
                and sub_account_status = #{subAccountStatus,jdbcType=INTEGER}
            </if>
        </where>
        order by number_segment_no
        limit 20
    </select>

    <select id="selectUnboundMerchantAccountByQueryEntity" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account_bucket
        <where>
            account_id = #{accountId,jdbcType=VARCHAR}
            and sub_use_type = #{subUseType,jdbcType=VARCHAR}
            and merchant_no is null
            and sub_merchant_no is null
            <if test="status != null">
                and status = #{status,jdbcType=CHAR}
            </if>
            <if test="subAccountStatus != null">
                and sub_account_status = #{subAccountStatus,jdbcType=INTEGER}
            </if>
        </where>
        order by number_segment_no
        limit 20
    </select>

    <update id="updateBySubAccountId"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity">
        update tb_inst_sub_funds_account_bucket
        <set>
            <if test="status != null">
                status = #{status,jdbcType=CHAR},
            </if>
        </set>
        where sub_account_id = #{subAccountId,jdbcType=VARCHAR}
    </update>

    <insert id="insert" keyColumn="sub_account_id" keyProperty="subAccountId"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity"
            useGeneratedKeys="true">
        insert into tb_inst_sub_funds_account_bucket
        ( sub_account_id, account_id, bucket_id
        , number_segment_id,number_segment_no, sub_use_type, sub_account_no
        , sub_account_name,b_sub_account_no, merchant_no, sub_merchant_no, status
        , sub_account_status, account_json, request_body, apply_type)
        values ( #{subAccountId,jdbcType=VARCHAR}, #{accountId,jdbcType=VARCHAR}
               , #{bucketId,jdbcType=BIGINT}
               , #{numberSegmentId,jdbcType=VARCHAR}, #{numberSegmentNo,jdbcType=VARCHAR}, #{subUseType,jdbcType=VARCHAR}, #{subAccountNo,jdbcType=VARCHAR}
               , #{subAccountName,jdbcType=VARCHAR}, #{bSubAccountNo,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR}, #{subMerchantNo,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
               , #{subAccountStatus,jdbcType=INTEGER}, #{accountJson,jdbcType=VARCHAR}, #{requestBody,jdbcType=VARCHAR}, #{applyType,jdbcType=VARCHAR})
    </insert>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account_bucket
        where sub_account_id = #{subAccountId,jdbcType=VARCHAR}
    </select>

    <select id="selectAssignableCountByAccountId" resultType="java.lang.Integer">
        select count(1)
        from tb_inst_sub_funds_account_bucket bucket
        where bucket.account_id = #{accountId,jdbcType=VARCHAR}
        and bucket.status = 'Y'
        and not exists (select account.sub_account_id from tb_inst_sub_funds_account account where account.sub_account_id = bucket.sub_account_id and account.status=4)
    </select>
    
</mapper>
