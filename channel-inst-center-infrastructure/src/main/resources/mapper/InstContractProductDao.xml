<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstContractProductDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity">
    <id column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <id column="inst_product_code" jdbcType="VARCHAR" property="instProductCode" />
    <id column="inst_product_capability_code" jdbcType="VARCHAR" property="instProductCapabilityCode" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="fee_group_id" jdbcType="VARCHAR" property="feeGroupId" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity">
    insert into tb_inst_contract_product (contract_no, inst_product_code, inst_product_capability_code, version,
      fee_group_id
      )
    values (#{contractNo,jdbcType=VARCHAR}, #{instProductCode,jdbcType=VARCHAR}, #{instProductCapabilityCode,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR},
      #{feeGroupId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertBatch">
    insert into tb_inst_contract_product (contract_no, inst_product_code, inst_product_capability_code,version,
      fee_group_id
      )
    values
    <foreach collection="records" index="index" item="item" separator=",">
      (#{item.contractNo,jdbcType=VARCHAR}, #{item.instProductCode,jdbcType=VARCHAR}, #{item.instProductCapabilityCode,jdbcType=VARCHAR},#{item.version,jdbcType=VARCHAR},
      #{item.feeGroupId,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="selectAll" resultMap="BaseResultMap">
    select contract_no, inst_product_code, inst_product_capability_code, fee_group_id, version,
    utc_create, utc_modified
    from tb_inst_contract_product
    where 1=1
    <if test="contractNo != null and contractNo != ''">
      and contract_no = #{contractNo,jdbcType=VARCHAR}
    </if>
    <if test="instProductCode != null and instProductCode != ''">
      and inst_product_code = #{instProductCode,jdbcType=VARCHAR}
    </if>
    <if test="instProductCapabilityCode != null and instProductCapabilityCode != ''">
      and inst_product_capability_code = #{instProductCapabilityCode,jdbcType=VARCHAR}
    </if>
    <if test="version != null and version != ''">
      and version = #{version,jdbcType=VARCHAR}
    </if>
    <if test="feeGroupId != null and feeGroupId != ''">
      and fee_group_id = #{feeGroupId,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectByProductCodeAndCapabilityCode" resultMap="BaseResultMap">
    select contract_no, inst_product_code, inst_product_capability_code, fee_group_id,version,
    utc_create, utc_modified
    from tb_inst_contract_product
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and inst_product_capability_code = #{capabilityCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByProductCodeAndCapabilityCodes" resultMap="BaseResultMap">
    select contract_no, inst_product_code, inst_product_capability_code, fee_group_id,version,
    utc_create, utc_modified
    from tb_inst_contract_product
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and inst_product_capability_code in
    <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
      #{capabilityCode}
    </foreach>
  </select>

    <delete id="deleteByProductCode">
        delete from tb_inst_contract_product
        where inst_product_code = #{productCode,jdbcType=VARCHAR}
    </delete>

  <delete id="deleteByProductCodeAndCapabilityCode">
        delete from tb_inst_contract_product
        where inst_product_code = #{productCode,jdbcType=VARCHAR}
        and inst_product_capability_code in
        <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
          #{capabilityCode}
        </foreach>
    </delete>

  <delete id="deleteByProductCodeAndVersion">
    delete from tb_inst_contract_product
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and version = #{version,jdbcType=VARCHAR}
  </delete>

  <update id="updateFeeGroupId">
    update tb_inst_contract_product
    set fee_group_id = #{feeGroupId,jdbcType=VARCHAR}
    where contract_no = #{contractNo,jdbcType=VARCHAR}
    and inst_product_code = #{productCode,jdbcType=VARCHAR}
    and inst_product_capability_code in
    <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
      #{capabilityCode}
    </foreach>
  </update>
</mapper>