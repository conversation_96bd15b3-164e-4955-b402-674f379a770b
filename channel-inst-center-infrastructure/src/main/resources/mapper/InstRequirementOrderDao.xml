<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementOrderDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="api_doc_id" jdbcType="VARCHAR" property="apiDocId" />
    <result column="api_doc_url" jdbcType="VARCHAR" property="apiDocUrl" />
    <result column="platform_url" jdbcType="VARCHAR" property="platformUrl" />
    <result column="prod_platform_url" jdbcType="VARCHAR" property="prodPlatformUrl" />
    <result column="pay_doc_id" jdbcType="VARCHAR" property="payDocId" />
    <result column="pay_doc_url" jdbcType="VARCHAR" property="payDocUrl" />
    <result column="refund_doc_id" jdbcType="VARCHAR" property="refundDocId" />
    <result column="refund_doc_url" jdbcType="VARCHAR" property="refundDocUrl" />
    <result column="dispute_doc_id" jdbcType="VARCHAR" property="disputeDocId" />
    <result column="dispute_doc_url" jdbcType="VARCHAR" property="disputeDocUrl" />
    <result column="bill_doc_id" jdbcType="VARCHAR" property="billDocId" />
    <result column="bill_doc_url" jdbcType="VARCHAR" property="billDocUrl" />
    <result column="problem_handle_doc_id" jdbcType="VARCHAR" property="problemHandleDocId" />
    <result column="problem_handle_doc_url" jdbcType="VARCHAR" property="problemHandleDocUrl" />
    <result column="release_require_doc_id" jdbcType="VARCHAR" property="releaseRequireDocId" />
    <result column="release_require_doc_url" jdbcType="VARCHAR" property="releaseRequireDocUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity"
          useGeneratedKeys="true" keyProperty="id">
    insert into tb_inst_requirement_order (apply_no, inst_id, platform_url,prod_platform_url,
      api_doc_id,api_doc_url, pay_doc_id,pay_doc_url, refund_doc_id, refund_doc_url,
      dispute_doc_id,dispute_doc_url, bill_doc_id, bill_doc_url,problem_handle_doc_id, problem_handle_doc_url,
      release_require_doc_id,release_require_doc_url, remark, status)
    values (#{applyNo,jdbcType=VARCHAR}, #{instId,jdbcType=BIGINT}, #{platformUrl,jdbcType=VARCHAR},#{prodPlatformUrl,jdbcType=VARCHAR},
      #{apiDocId,jdbcType=VARCHAR},#{apiDocUrl,jdbcType=VARCHAR}, #{payDocId,jdbcType=VARCHAR},#{payDocUrl,jdbcType=VARCHAR}, #{refundDocId,jdbcType=VARCHAR},#{refundDocUrl,jdbcType=VARCHAR},
      #{disputeDocId,jdbcType=VARCHAR},#{disputeDocUrl,jdbcType=VARCHAR}, #{billDocId,jdbcType=VARCHAR},#{billDocUrl,jdbcType=VARCHAR}, #{problemHandleDocId,jdbcType=VARCHAR}, #{problemHandleDocUrl,jdbcType=VARCHAR},
      #{releaseRequireDocId,jdbcType=VARCHAR},#{releaseRequireDocUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity">
    update tb_inst_requirement_order
    <set>
      <if test="instId != null">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="applyNo != null and applyNo != ''">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="platformUrl != null and platformUrl != ''">
        platform_url = #{platformUrl,jdbcType=VARCHAR},
      </if>
      <if test="prodPlatformUrl != null and prodPlatformUrl != ''">
        prod_platform_url = #{prodPlatformUrl,jdbcType=VARCHAR},
      </if>
      <if test="apiDocId != null and apiDocId != ''">
        api_doc_id = #{apiDocId,jdbcType=VARCHAR},
      </if>
      <if test="apiDocUrl != null and apiDocUrl != ''">
        api_doc_url = #{apiDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="payDocId != null and payDocId != ''">
        pay_doc_id = #{payDocId,jdbcType=VARCHAR},
      </if>
      <if test="payDocUrl != null and payDocUrl != ''">
        pay_doc_url = #{payDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="refundDocId != null and refundDocId != ''">
        refund_doc_id = #{refundDocId,jdbcType=VARCHAR},
      </if>
      <if test="refundDocUrl != null and refundDocUrl != ''">
        refund_doc_url = #{refundDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="disputeDocId != null and disputeDocId != ''">
        dispute_doc_id = #{disputeDocId,jdbcType=VARCHAR},
      </if>
      <if test="disputeDocUrl != null and disputeDocUrl != ''">
        dispute_doc_url = #{disputeDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="billDocId != null and billDocId != ''">
        bill_doc_id = #{billDocId,jdbcType=VARCHAR},
      </if>
      <if test="billDocUrl != null and billDocUrl != ''">
        bill_doc_url = #{billDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="problemHandleDocId != null and problemHandleDocId != ''">
        problem_handle_doc_id = #{problemHandleDocId,jdbcType=VARCHAR},
      </if>
      <if test="problemHandleDocUrl != null and problemHandleDocUrl != ''">
        problem_handle_doc_url = #{problemHandleDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="releaseRequireDocId != null and releaseRequireDocId != ''">
        release_require_doc_id = #{releaseRequireDocId,jdbcType=VARCHAR},
      </if>
      <if test="releaseRequireDocUrl != null and releaseRequireDocUrl != ''">
        release_require_doc_url = #{releaseRequireDocUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, apply_no, inst_id, platform_url,prod_platform_url, api_doc_id, api_doc_url,pay_doc_id, pay_doc_url,refund_doc_id, refund_doc_url,dispute_doc_id,dispute_doc_url,
    bill_doc_id, bill_doc_url,problem_handle_doc_id, problem_handle_doc_url,release_require_doc_id, release_require_doc_url,remark, status, utc_create,
    utc_modified
    from tb_inst_requirement_order
    where apply_no = #{applyNo,jdbcType=VARCHAR}
  </select>
</mapper>