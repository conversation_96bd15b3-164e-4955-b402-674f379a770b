<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstTransFeeDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fee_group_id" jdbcType="VARCHAR" property="feeGroupId" />
    <result column="fee_type" jdbcType="VARCHAR" property="feeType" />
    <result column="mcc" jdbcType="VARCHAR" property="mcc" />
    <result column="calculate_type" jdbcType="VARCHAR" property="calculateType" />
    <result column="min_fee" jdbcType="DECIMAL" property="minFee" />
    <result column="max_fee" jdbcType="DECIMAL" property="maxFee" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="fee_detail" jdbcType="LONGVARCHAR" property="feeDetail" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity">
    insert into tb_inst_trans_fee (fee_group_id, fee_type,
      mcc, calculate_type, min_fee, 
      max_fee, fee_detail)
    values (#{feeGroupId,jdbcType=VARCHAR}, #{feeType,jdbcType=VARCHAR},
      #{mcc,jdbcType=VARCHAR}, #{calculateType,jdbcType=VARCHAR}, #{minFee,jdbcType=DECIMAL}, 
      #{maxFee,jdbcType=DECIMAL}, #{feeDetail,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertBatch" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity">
    insert into tb_inst_trans_fee (fee_group_id, fee_type,
      mcc, calculate_type, min_fee,
      max_fee, fee_detail)
    values
    <foreach collection="records" index="index" item="item" separator=",">
      (#{item.feeGroupId,jdbcType=VARCHAR}, #{item.feeType,jdbcType=VARCHAR},
      #{item.mcc,jdbcType=VARCHAR}, #{item.calculateType,jdbcType=VARCHAR}, #{item.minFee,jdbcType=DECIMAL},
      #{item.maxFee,jdbcType=DECIMAL}, #{item.feeDetail,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity">
    update tb_inst_trans_fee
    <set>
      <if test="feeType != null and feeType != ''">
        fee_type = #{feeType,jdbcType=VARCHAR},
      </if>
      <if test="mcc != null and mcc != ''">
        mcc = #{mcc,jdbcType=VARCHAR},
      </if>
      <if test="calculateType != null and calculateType != ''">
        calculate_type = #{calculateType,jdbcType=VARCHAR},
      </if>
      <if test="minFee != null">
        min_fee = #{minFee,jdbcType=DECIMAL},
      </if>
      <if test="maxFee != null">
        max_fee = #{maxFee,jdbcType=DECIMAL},
      </if>
      <if test="feeDetail != null and feeDetail != ''">
        fee_detail = #{feeDetail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, fee_group_id, fee_type, mcc, calculate_type, min_fee, max_fee, utc_create,
    utc_modified, fee_detail
    from tb_inst_trans_fee
    where fee_group_id in
    <foreach collection="feeGroupIds" index="index" item="feeGroupId" open="(" separator="," close=")">
      #{feeGroupId}
    </foreach>
  </select>

  <delete id="deleteByFeeGroupIds">
    delete from tb_inst_trans_fee
    where fee_group_id in
    <foreach collection="feeGroupIds" index="index" item="feeGroupId" open="(" separator="," close=")">
      #{feeGroupId}
    </foreach>
  </delete>
</mapper>