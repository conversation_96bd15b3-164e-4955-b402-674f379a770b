<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstAuditResultDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity">
    insert into tb_inst_audit_result (business_type, business_no,
      auditor, status, remark, extra_info)
    values (#{businessType,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR},
      #{auditor,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{extraInfo,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity">
    update tb_inst_audit_result
    <set>
      <if test="businessType != null and businessType != ''">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null and businessNo != ''">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null and auditor != ''">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null and extraInfo != ''">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, business_type, business_no, auditor, status, remark, extra_info, utc_create, 
    utc_modified
    from tb_inst_audit_result
    where 1=1
    <if test="id != null">
      and id = #{id,jdbcType=BIGINT}
    </if>
    <if test="businessType != null and businessType != ''">
      and business_type = #{businessType,jdbcType=VARCHAR}
    </if>
    <if test="businessNo != null and businessNo != ''">
      and business_no = #{businessNo,jdbcType=VARCHAR}
    </if>
    <if test="auditor != null and auditor != ''">
      and auditor = #{auditor,jdbcType=VARCHAR}
    </if>
    <if test="status != null and status != ''">
      and status = #{status,jdbcType=VARCHAR}
    </if>
    <if test="extraInfo != null and extraInfo != ''">
      and extra_info = #{extraInfo,jdbcType=VARCHAR}
    </if>
    order by id desc
  </select>
</mapper>