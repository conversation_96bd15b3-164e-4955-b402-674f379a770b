<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstAccountDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="key_type" jdbcType="VARCHAR" property="keyType" />
    <result column="encrypt_type" jdbcType="VARCHAR" property="encryptType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="key_value" jdbcType="LONGVARCHAR" property="keyValue" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity">
    insert into tb_inst_account (inst_id, requirement_order_id,
      type, env, account, 
      key_type, encrypt_type, remark, key_value
      )
    values (#{instId,jdbcType=BIGINT}, #{requirementOrderId,jdbcType=BIGINT},
      #{type,jdbcType=VARCHAR}, #{env,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, 
      #{keyType,jdbcType=VARCHAR}, #{encryptType,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{keyValue,jdbcType=LONGVARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity">
    update tb_inst_account
    <set>
      <if test="instId != null">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="requirementOrderId != null">
        requirement_order_id = #{requirementOrderId,jdbcType=BIGINT},
      </if>
      <if test="type != null and type != ''">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="env != null and env != ''">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="account != null and account != ''">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="keyType != null and keyType != ''">
        key_type = #{keyType,jdbcType=VARCHAR},
      </if>
      <if test="encryptType != null and encryptType != ''">
        encrypt_type = #{encryptType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="keyValue != null and keyValue != ''">
        key_value = #{keyValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, inst_id, requirement_order_id, type, env, account, key_type, encrypt_type, 
    remark, utc_create, utc_modified, key_value, status
    from tb_inst_account
    where status = 'Y'
    <if test="id != null">
      and id = #{id,jdbcType=BIGINT}
    </if>
    <if test="instId != null">
      and inst_id = #{instId,jdbcType=BIGINT}
    </if>
    <if test="requirementOrderId != null">
      and requirement_order_id = #{requirementOrderId,jdbcType=BIGINT}
    </if>
    <if test="type != null">
      and type = #{type,jdbcType=VARCHAR}
    </if>
    <if test="env != null">
      and env = #{env,jdbcType=VARCHAR}
    </if>
  </select>

  <delete id="delete">
    delete from tb_inst_account
    where requirement_order_id = #{requirementOrderId,jdbcType=BIGINT}
    and inst_id = #{instId,jdbcType=BIGINT}
    and env = #{env,jdbcType=VARCHAR}
    and id not in
    <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>