<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstNdaDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="inst_id" jdbcType="BIGINT" property="instId"/>
        <result column="apply_no" jdbcType="VARCHAR" property="applyNo"/>
        <result column="shareit_entity" jdbcType="CHAR" property="shareitEntity"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="nda_attach_id" jdbcType="VARCHAR" property="ndaAttachId"/>
        <result column="inst_sign_flag" jdbcType="VARCHAR" property="instSignFlag"/>
        <result column="shareit_sign_flag" jdbcType="VARCHAR" property="shareitSignFlag"/>
        <result column="inst_sign_attach_id" jdbcType="VARCHAR" property="instSignAttachId"/>
        <result column="shareit_sign_attach_id" jdbcType="VARCHAR" property="shareitSignAttachId"/>
        <result column="pair_sign_attach_id" jdbcType="VARCHAR" property="pairSignAttachId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="columns">
        id,
        inst_id, apply_no, shareit_entity, version, nda_attach_id, inst_sign_flag, shareit_sign_flag,
        inst_sign_attach_id, shareit_sign_attach_id, pair_sign_attach_id, remark, status
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from tb_inst_nda
        where inst_id = #{instId,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity">
    insert into tb_inst_nda (inst_id, apply_no,
      shareit_entity, version, nda_attach_id,
      inst_sign_flag, shareit_sign_flag, inst_sign_attach_id, shareit_sign_attach_id,
      pair_sign_attach_id, remark, status)
    values ( #{instId,jdbcType=BIGINT}, #{applyNo,jdbcType=VARCHAR},
      #{shareitEntity,jdbcType=CHAR}, #{version,jdbcType=VARCHAR}, #{ndaAttachId,jdbcType=VARCHAR},
      #{instSignFlag,jdbcType=VARCHAR}, #{shareitSignFlag,jdbcType=VARCHAR}, #{instSignAttachId,jdbcType=VARCHAR},
      #{shareitSignAttachId,jdbcType=VARCHAR}, #{pairSignAttachId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>

    <update id="update" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity">
        update tb_inst_nda
        <set>
            <if test="instId != null and instId != ''">
                inst_id = #{instId,jdbcType=BIGINT},
            </if>
            <if test="applyNo != null and applyNo != ''">
                apply_no = #{applyNo,jdbcType=VARCHAR},
            </if>
            <if test="shareitEntity != null and shareitEntity != ''">
                shareit_entity = #{shareitEntity,jdbcType=CHAR},
            </if>
            <if test="version != null and version != ''">
                version = #{version,jdbcType=VARCHAR},
            </if>
            <if test="ndaAttachId != null and ndaAttachId != ''">
                nda_attach_id = #{ndaAttachId,jdbcType=VARCHAR},
            </if>
            <if test="instSignFlag != null and instSignFlag != ''">
                inst_sign_flag = #{instSignFlag,jdbcType=VARCHAR},
            </if>
            <if test="shareitSignFlag != null and shareitSignFlag != ''">
                shareit_sign_flag = #{shareitSignFlag,jdbcType=VARCHAR},
            </if>
            <if test="instSignAttachId != null and instSignAttachId != ''">
                inst_sign_attach_id = #{instSignAttachId,jdbcType=VARCHAR},
            </if>
            <if test="shareitSignAttachId != null and shareitSignAttachId != ''">
                shareit_sign_attach_id = #{shareitSignAttachId,jdbcType=VARCHAR},
            </if>
            <if test="pairSignAttachId != null and pairSignAttachId != ''">
                pair_sign_attach_id = #{pairSignAttachId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>