<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.ChannelMerchantDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.ChannelMerchantEntity">
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
        <result column="entity" jdbcType="VARCHAR" property="entity" />
        <result column="mid" jdbcType="VARCHAR" property="mid" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="account_json" jdbcType="LONGVARCHAR" property="accountJson" />
        <result column="funds_settle_inst_code" jdbcType="VARCHAR" property="fundsSettleInstCode" />
        <result column="biz_handle_inst_code" jdbcType="VARCHAR" property="bizHandleInstCode" />
    </resultMap>
    <select id="selectAll" resultMap="BaseResultMap">
        select code, inst_code, entity, mid, name, status, account_json, biz_handle_inst_code, funds_settle_inst_code
        from tb_funding_channel_merchant
        where status = 1
        <if test="code != null and code != ''">
            and code = #{code,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
