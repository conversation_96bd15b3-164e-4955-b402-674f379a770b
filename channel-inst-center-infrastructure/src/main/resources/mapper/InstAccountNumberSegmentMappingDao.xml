<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstAccountNumberSegmentMappingDao">

    <resultMap id="BaseResultMap"
               type="com.payermax.channel.inst.center.infrastructure.entity.InstAccountNumberSegmentMappingEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="numberSegmentId" column="number_segment_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,account_id,number_segment_id,
        status,utc_create,utc_modified
    </sql>

    <update id="updateByEntity"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountNumberSegmentMappingEntity">
        update tb_inst_account_number_segment_mapping
        <set>
            <if test="status != null">
                status = #{status,jdbcType=CHAR},
            </if>
        </set>
        <where>
            number_segment_id = #{numberSegmentId,jdbcType=VARCHAR}
            <if test="id != null and id!=''">
                and id = #{id,jdbcType=BIGINT}
            </if>
        </where>
    </update>
</mapper>
