<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstDdDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="register_name" jdbcType="VARCHAR" property="registerName" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="register_address" jdbcType="VARCHAR" property="registerAddress" />
    <result column="company_scale" jdbcType="VARCHAR" property="companyScale" />
    <result column="validity_date" jdbcType="DATE" property="validityDate" />
    <result column="corporate_name" jdbcType="VARCHAR" property="corporateName" />
    <result column="corporate_birth_date" jdbcType="DATE" property="corporateBirthDate" />
    <result column="corporate_address" jdbcType="VARCHAR" property="corporateAddress" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <resultMap id="InstDdResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="register_name" jdbcType="VARCHAR" property="registerName" />
    <result column="register_no" jdbcType="VARCHAR" property="registerNo" />
    <result column="register_date" jdbcType="DATE" property="registerDate" />
    <result column="register_address" jdbcType="VARCHAR" property="registerAddress" />
    <result column="company_scale" jdbcType="VARCHAR" property="companyScale" />
    <result column="validity_date" jdbcType="DATE" property="validityDate" />
    <result column="corporate_name" jdbcType="VARCHAR" property="corporateName" />
    <result column="corporate_birth_date" jdbcType="DATE" property="corporateBirthDate" />
    <result column="corporate_address" jdbcType="VARCHAR" property="corporateAddress" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="business_scope" jdbcType="VARCHAR" property="businessScope" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />

    <result column="instId" jdbcType="BIGINT" property="instBaseInfoEntity.instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instBaseInfoEntity.instCode" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBaseInfoEntity.instBrandId" />
    <result column="inst_name" jdbcType="VARCHAR" property="instBaseInfoEntity.instName" />
    <result column="inst_types" jdbcType="VARCHAR" property="instBaseInfoEntity.instTypes" />
    <result column="entity_country" jdbcType="CHAR" property="instBaseInfoEntity.entityCountry" />
    <result column="is_fatf_member" jdbcType="CHAR" property="instBaseInfoEntity.isFatfMember" />
    <result column="remark" jdbcType="VARCHAR" property="instBaseInfoEntity.remark" />
    <result column="inst_status" jdbcType="CHAR" property="instBaseInfoEntity.status" />
    <result column="inst_utc_create" jdbcType="TIMESTAMP" property="instBaseInfoEntity.utcCreate" />
    <result column="inst_utc_modified" jdbcType="TIMESTAMP" property="instBaseInfoEntity.utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity" useGeneratedKeys="true" keyProperty="id">
    insert into tb_inst_dd (inst_id, apply_no,
      register_name, register_no, register_date, 
      register_address, company_scale, validity_date, 
      corporate_name, corporate_birth_date, corporate_address, 
      website, business_scope, status)
    values (#{instId,jdbcType=BIGINT}, #{applyNo,jdbcType=VARCHAR},
      #{registerName,jdbcType=VARCHAR}, #{registerNo,jdbcType=VARCHAR}, #{registerDate,jdbcType=DATE}, 
      #{registerAddress,jdbcType=VARCHAR}, #{companyScale,jdbcType=VARCHAR}, #{validityDate,jdbcType=DATE}, 
      #{corporateName,jdbcType=VARCHAR}, #{corporateBirthDate,jdbcType=DATE}, #{corporateAddress,jdbcType=VARCHAR}, 
      #{website,jdbcType=VARCHAR}, #{businessScope,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity">
    update tb_inst_dd
    <set>
      <if test="registerName != null and registerName != ''">
        register_name = #{registerName,jdbcType=VARCHAR},
      </if>
      <if test="registerNo != null and registerNo != ''">
        register_no = #{registerNo,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=DATE},
      </if>
      <if test="registerAddress != null and registerAddress != ''">
        register_address = #{registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="companyScale != null and companyScale != ''">
        company_scale = #{companyScale,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=DATE},
      </if>
      <if test="corporateName != null and corporateName != ''">
        corporate_name = #{corporateName,jdbcType=VARCHAR},
      </if>
      <if test="corporateBirthDate != null">
        corporate_birth_date = #{corporateBirthDate,jdbcType=DATE},
      </if>
      <if test="corporateAddress != null and corporateAddress != ''">
        corporate_address = #{corporateAddress,jdbcType=VARCHAR},
      </if>
      <if test="website != null and website != ''">
        website = #{website,jdbcType=VARCHAR},
      </if>
      <if test="businessScope != null and businessScope != ''">
        business_scope = #{businessScope,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, inst_id, apply_no, register_name, register_no, register_date, register_address, 
    company_scale, validity_date, corporate_name, corporate_birth_date, corporate_address, 
    website, business_scope, status, utc_create, utc_modified
    from tb_inst_dd
    where inst_id = #{instId,jdbcType=BIGINT}
  </select>
  <select id="selectDdList" resultMap="InstDdResultMap">
    select dd.id, dd.inst_id, dd.apply_no, dd.register_name, dd.register_no, dd.register_date, dd.register_address,
    dd.company_scale, dd.validity_date, dd.corporate_name, dd.corporate_birth_date, dd.corporate_address,
    dd.website, dd.business_scope, dd.status, dd.utc_create, dd.utc_modified,
    inst.inst_id instId, inst.inst_code, inst.inst_brand_id, inst.inst_name, inst.inst_types, inst.entity_country,
    inst.is_fatf_member, inst.remark, inst.status inst_status, inst.utc_create inst_utc_create, inst.utc_modified inst_utc_modified
    from tb_inst_dd dd left join tb_inst_base_info inst
    on dd.inst_id = inst.inst_id
    where inst.status = 'Y'
    <if test="instIds != null and instIds.size > 0">
      and dd.inst_id in
      <foreach collection="instIds" index="index" item="instId" open="(" separator="," close=")">
        #{instId}
      </foreach>
    </if>
    order by dd.utc_modified desc

  </select>
</mapper>