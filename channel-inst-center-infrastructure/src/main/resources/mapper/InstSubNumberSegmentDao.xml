<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstSubNumberSegmentDao">

    <resultMap id="BaseResultMap"
               type="com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="numberStart" column="number_start" jdbcType="VARCHAR"/>
        <result property="numberEnd" column="number_end" jdbcType="VARCHAR"/>
        <result property="maxUsed" column="max_used" jdbcType="VARCHAR"/>
        <result property="useType" column="use_type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        segment
        .
        id
        ,segment.name,segment.number_start,
        segment.number_end,segment.max_used,segment.use_type,
        segment.status,segment.utc_create,segment.utc_modified
    </sql>

    <update id="updateByPrimaryKeySelective">
        update tb_inst_sub_number_segment
        <set>
            <if test="record.name != null and record.name != ''">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.numberStart != null and record.numberStart != ''">
                number_start = #{record.numberStart,jdbcType=VARCHAR},
            </if>
            <if test="record.numberEnd != null and record.numberEnd != ''">
                number_end = #{record.numberEnd,jdbcType=VARCHAR},
            </if>
            <if test="record.maxUsed != null and record.maxUsed != ''">
                max_used = #{record.maxUsed,jdbcType=VARCHAR},
            </if>
            <if test="record.useType != null and record.useType != ''">
                use_type = #{record.useType,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null and record.status != ''">
                status = #{record.status,jdbcType=CHAR},
            </if>
        </set>
        where
        id = #{record.id,jdbcType=VARCHAR}
        <if test="originalMaxUsed != null">
            and max_used = #{originalMaxUsed,jdbcType=VARCHAR}
        </if>
        <if test="originalMaxUsed == null">
            and max_used is null
        </if>
    </update>

    <select id="selectByRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_number_segment segment
        inner join tb_inst_account_number_segment_mapping mapping
        on segment.id = mapping.number_segment_id
        <where>
            <if test="accountId != null and accountId != ''">
                and mapping.account_id = #{accountId,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and segment.status = #{status,jdbcType=CHAR}
                and mapping.status = #{status,jdbcType=CHAR}
            </if>
            <if test="useType != null and useType != ''">
                and find_in_set(#{useType,jdbcType=VARCHAR},segment.use_type)
            </if>
        </where>
        limit 500
    </select>

    <select id="selectCountByRecord" resultType="java.lang.Integer">
        select count(1)
        from tb_inst_sub_number_segment segment
        inner join tb_inst_account_number_segment_mapping mapping
        on segment.id = mapping.number_segment_id
        <where>
            <if test="accountId != null and accountId != ''">
                and mapping.account_id = #{accountId,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and segment.status = #{status,jdbcType=CHAR}
                and mapping.status = #{status,jdbcType=CHAR}
            </if>
            <if test="useType != null and useType != ''">
                and find_in_set(#{useType,jdbcType=VARCHAR},segment.use_type)
            </if>
        </where>
    </select>
</mapper>
