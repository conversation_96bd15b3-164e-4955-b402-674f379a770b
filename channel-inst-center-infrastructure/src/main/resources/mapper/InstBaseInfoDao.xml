<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity">
    <id column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBrandId" />
    <result column="inst_name" jdbcType="VARCHAR" property="instName" />
    <result column="inst_types" jdbcType="VARCHAR" property="instTypes" />
    <result column="entity_country" jdbcType="CHAR" property="entityCountry" />
    <result column="is_fatf_member" jdbcType="CHAR" property="isFatfMember" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <resultMap id="InstInfoMap" type="com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity">
    <id column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBrandId" />
    <result column="inst_name" jdbcType="VARCHAR" property="instName" />
    <result column="inst_types" jdbcType="VARCHAR" property="instTypes" />
    <result column="entity_country" jdbcType="CHAR" property="entityCountry" />
    <result column="is_fatf_member" jdbcType="CHAR" property="isFatfMember" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="brand_code" jdbcType="VARCHAR" property="instBrandCode" />
    <result column="brand_name" jdbcType="VARCHAR" property="instBrandName" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity">
    <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="instId">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tb_inst_base_info (inst_id, inst_code, inst_brand_id,
      inst_name, inst_types, entity_country, 
      is_fatf_member,am_id, remark, status)
    values (#{instId,jdbcType=BIGINT}, #{instCode,jdbcType=VARCHAR}, #{instBrandId,jdbcType=BIGINT},
      #{instName,jdbcType=VARCHAR}, #{instTypes,jdbcType=VARCHAR}, #{entityCountry,jdbcType=CHAR}, 
      #{isFatfMember,jdbcType=CHAR},#{amId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 'Y')
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity">
    update tb_inst_base_info
    <set>
      <if test="instCode != null and instCode != ''">
        inst_code = #{instCode,jdbcType=VARCHAR},
      </if>
      <if test="instBrandId != null">
        inst_brand_id = #{instBrandId,jdbcType=BIGINT},
      </if>
      <if test="instName != null and instName != ''">
        inst_name = #{instName,jdbcType=VARCHAR},
      </if>
      <if test="instTypes != null and instTypes != ''">
        inst_types = #{instTypes,jdbcType=VARCHAR},
      </if>
      <if test="entityCountry != null and entityCountry != ''">
        entity_country = #{entityCountry,jdbcType=CHAR},
      </if>
      <if test="isFatfMember != null and isFatfMember != ''">
        is_fatf_member = #{isFatfMember,jdbcType=CHAR},
      </if>
      <if test="amId != null and amId != ''">
        am_id = #{amId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where inst_id = #{instId,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select inst_id, inst_code, inst_brand_id, inst_name, inst_types, entity_country, 
    is_fatf_member,am_id, remark, status, utc_create, utc_modified
    from tb_inst_base_info
    where status = 'Y'
    <if test="instId != null and instId != ''">
      and inst_id = #{instId,jdbcType=BIGINT}
    </if>
    <if test="instCode != null and instCode != ''">
      and inst_code = #{instCode,jdbcType=VARCHAR}
    </if>
    <if test="instBrandId != null">
      and inst_brand_id = #{instBrandId,jdbcType=BIGINT}
    </if>
    <if test="instName != null and instName != ''">
      and inst_name = #{instName,jdbcType=VARCHAR}
    </if>
    <if test="instTypes != null and instTypes != ''">
      and inst_types = #{instTypes,jdbcType=VARCHAR}
    </if>
    <if test="entityCountry != null and entityCountry != ''">
      and entity_country = #{entityCountry,jdbcType=CHAR}
    </if>
    <if test="isFatfMember != null and isFatfMember != ''">
      and is_fatf_member = #{isFatfMember,jdbcType=CHAR}
    </if>
    <if test="amId != null and amId != ''">
      and am_id = #{amId,jdbcType=VARCHAR}
    </if>
    <if test="remark != null and remark != ''">
      and remark = #{remark,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="selectAllInstInfo" resultMap="InstInfoMap">
    select inst.inst_id, inst.inst_code, inst.inst_brand_id, inst.inst_name, inst.inst_types, inst.entity_country,
    inst.is_fatf_member,inst.am_id, inst.remark, inst.status, inst.utc_create, inst.utc_modified,brand.bd_id,brand.brand_code,brand.brand_name
    from tb_inst_base_info inst
    join tb_inst_brand brand
    on inst.inst_brand_id = brand.brand_id
    <if test="(record.channelType != null and record.channelType != '') || (record.paymentMethodType != null and record.paymentMethodType != '')">
      join (select distinct product.inst_id from tb_inst_product product
      <where>
        <if test="record.channelType != null and record.channelType != ''">
          and product.channel_type = #{record.channelType,jdbcType=VARCHAR}
        </if>
        <if test="record.paymentMethodType != null and record.paymentMethodType != ''">
          and product.payment_method_type = #{record.paymentMethodType,jdbcType=VARCHAR}
        </if>
      </where>
      ) tmp
      on tmp.inst_id = inst.inst_id
    </if>
    where inst.status = 'Y'
    <if test="record.instId != null and record.instId != ''">
      and inst.inst_id = #{instId,jdbcType=BIGINT}
    </if>
    <if test="record.instCode != null and record.instCode != ''">
      and inst.inst_code like concat('%', #{record.instCode,jdbcType=VARCHAR}, '%')
    </if>
    <if test="record.instBrandId != null">
      and inst.inst_brand_id = #{record.instBrandId,jdbcType=BIGINT}
    </if>
    <if test="record.instName != null and record.instName != ''">
      and inst.inst_name like concat('%', #{record.instName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="record.instTypes != null and record.instTypes != ''">
      and inst.inst_types = #{record.instTypes,jdbcType=VARCHAR}
    </if>
    <if test="record.entityCountry != null and record.entityCountry != ''">
      and inst.entity_country = #{record.entityCountry,jdbcType=CHAR}
    </if>
    <if test="record.isFatfMember != null and record.isFatfMember != ''">
      and inst.is_fatf_member = #{record.isFatfMember,jdbcType=CHAR}
    </if>
    <if test="record.remark != null and record.remark != ''">
      and inst.remark = #{record.remark,jdbcType=VARCHAR}
    </if>
    <if test="record.bdId != null and record.bdId != ''">
      and brand.bd_id = #{record.bdId,jdbcType=VARCHAR}
    </if>
    <if test="record.amId != null and record.amId != ''">
      and inst.am_id = #{record.amId,jdbcType=VARCHAR}
    </if>
    <if test="record.instBrandCode != null and record.instBrandCode != ''">
      and brand.brand_code like concat('%', #{record.instBrandCode,jdbcType=VARCHAR}, '%')
    </if>
    <if test="record.instBrandName != null and record.instBrandName != ''">
      and brand.brand_name like concat('%', #{record.instBrandName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="record.bdAndAmId != null and record.bdAndAmId != ''">
      and (brand.bd_id = #{record.bdAndAmId,jdbcType=VARCHAR} or inst.am_id = #{record.bdAndAmId,jdbcType=VARCHAR})
    </if>
    <if test="record.bdList != null and record.bdList.size > 0">
      and brand.bd_id in
      <foreach collection="record.bdList" index="index" item="bdId" open="(" separator="," close=")">
        #{bdId}
      </foreach>
    </if>
    <if test="record.amList != null and record.amList.size > 0">
      and inst.am_id in
      <foreach collection="record.amList" index="index" item="amId" open="(" separator="," close=")">
        #{amId}
      </foreach>
    </if>
    <if test="record.utcCreate != null">
      and  DATE_FORMAT(inst.utc_create, '%Y-%m-%d') = DATE_FORMAT(#{record.utcCreate},'%Y-%m-%d')
    </if>
    order by inst.utc_modified desc
  </select>
  <select id="selectByBrandId" resultMap="BaseResultMap">
    select inst_id, inst_code, inst_brand_id, inst_name, inst_types, entity_country,
    is_fatf_member,am_id, remark, status, utc_create, utc_modified
    from tb_inst_base_info
    where status = 'Y'
    and inst_brand_id in
    <foreach collection="brandIds" index="index" item="brandId" open="(" separator="," close=")">
      #{brandId}
    </foreach>
  </select>
  <select id="selectByInstCode" resultMap="BaseResultMap">
    select inst_id, inst_code, inst_brand_id, inst_name, inst_types, entity_country,
           is_fatf_member,am_id, remark, status, utc_create, utc_modified
    from tb_inst_base_info
    where status = 'Y'
    and inst_code = #{instCode,jdbcType=VARCHAR}
  </select>
</mapper>