<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstProductDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity">
    <id column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="payment_method_type" jdbcType="VARCHAR" property="paymentMethodType" />
    <result column="payment_tool" jdbcType="VARCHAR" property="paymentTool" />
    <result column="customer_type" jdbcType="CHAR" property="customerType" />
    <result column="is_limit_mcc" jdbcType="CHAR" property="isLimitMcc" />
    <result column="is_divide_mid" jdbcType="VARCHAR" property="isDivideMid" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="mcc_detail" jdbcType="LONGVARCHAR" property="mccDetail" />
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity">
    insert into tb_inst_product (product_code, inst_id, product_name,
      channel_type, payment_method_type,payment_tool, customer_type,
      is_limit_mcc, is_divide_mid, mcc_detail, extra_info
      )
    values (#{productCode,jdbcType=VARCHAR}, #{instId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR},
      #{channelType,jdbcType=VARCHAR}, #{paymentMethodType,jdbcType=VARCHAR},#{paymentTool,jdbcType=VARCHAR}, #{customerType,jdbcType=CHAR},
      #{isLimitMcc,jdbcType=CHAR}, #{isDivideMid,jdbcType=VARCHAR}, #{mccDetail,jdbcType=LONGVARCHAR}, #{extraInfo,jdbcType=LONGVARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity">
    update tb_inst_product
    <set>
      <if test="productName != null and productName != ''">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null and channelType != ''">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethodType != null and paymentMethodType != ''">
        payment_method_type = #{paymentMethodType,jdbcType=VARCHAR},
      </if>
      <if test="paymentTool != null and paymentTool != ''">
        payment_tool = #{paymentTool,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null and customerType != ''">
        customer_type = #{customerType,jdbcType=CHAR},
      </if>
      <if test="isLimitMcc != null and isLimitMcc != ''">
        is_limit_mcc = #{isLimitMcc,jdbcType=CHAR},
      </if>
      <if test="isDivideMid != null and isDivideMid != ''">
        is_divide_mid = #{isDivideMid,jdbcType=VARCHAR},
      </if>
      <if test="mccDetail != null and mccDetail != ''">
        mcc_detail = #{mccDetail,jdbcType=LONGVARCHAR},
      </if>
      <if test="extraInfo != null and extraInfo != ''">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where product_code = #{productCode,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select product_code, inst_id, product_name, channel_type, payment_method_type,payment_tool, customer_type,
    is_limit_mcc, is_divide_mid, utc_create, utc_modified, mcc_detail, extra_info
    from tb_inst_product
    where 1=1
    <if test="productCode != null and productCode != ''">
      and product_code = #{productCode,jdbcType=VARCHAR}
    </if>
    <if test="instId != null">
      and inst_id = #{instId,jdbcType=BIGINT}
    </if>
    order by utc_modified desc
  </select>
  <select id="selectByProductCodes" resultMap="BaseResultMap">
    select product_code, inst_id, product_name, channel_type, payment_method_type,payment_tool, customer_type,
    is_limit_mcc, is_divide_mid, utc_create, utc_modified, mcc_detail, extra_info
    from tb_inst_product
    where product_code in
      <foreach collection="productCodes" index="index" item="productCode" open="(" separator="," close=")">
          #{productCode}
      </foreach>
    order by utc_modified desc
  </select>

  <select id="selectByInstIds" resultMap="BaseResultMap">
    select product_code, inst_id, product_name, channel_type, payment_method_type, payment_tool,customer_type,
    is_limit_mcc, is_divide_mid, utc_create, utc_modified, mcc_detail, extra_info
    from tb_inst_product
    where inst_id in
    <foreach collection="instIds" index="index" item="instId" open="(" separator="," close=")">
      #{instId}
    </foreach>
    order by utc_modified desc
  </select>

  <delete id="deleteByProductCode">
    delete from tb_inst_product
    where product_code = #{productCode,jdbcType=VARCHAR}
  </delete>
</mapper>
