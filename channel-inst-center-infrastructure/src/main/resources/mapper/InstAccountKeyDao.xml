<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstAccountKeyDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.query.InstAccountKeyQueryEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="keyId" jdbcType="VARCHAR" property="keyId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="key_type" jdbcType="VARCHAR" property="keyType" />
    <result column="encrypt_type" jdbcType="VARCHAR" property="encryptType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="key_value" jdbcType="LONGVARCHAR" property="keyValue" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="account" jdbcType="VARCHAR" property="account" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity">
    insert into tb_inst_account_key (
      key_type, encrypt_type, remark, key_value,status
      )
    values (#{keyType,jdbcType=VARCHAR},
    #{encryptType,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR},
    #{keyValue,jdbcType=LONGVARCHAR},
    #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertBatch" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity">
    insert into tb_inst_account_key (account_id,key_type, encrypt_type, remark, key_value,status)
    values
    <foreach collection="records" index="index" item="item" separator=",">
    (#{item.accountId,jdbcType=BIGINT},
    #{item.keyType,jdbcType=VARCHAR},
    #{item.encryptType,jdbcType=VARCHAR},
    #{item.remark,jdbcType=VARCHAR},
    #{item.keyValue,jdbcType=LONGVARCHAR},
    #{item.status,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity">
    update tb_inst_account_key
    <set>
      <if test="keyType != null and keyType != ''">
        key_type = #{keyType,jdbcType=VARCHAR},
      </if>
      <if test="encryptType != null and encryptType != ''">
        encrypt_type = #{encryptType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="keyValue != null and keyValue != ''">
        key_value = #{keyValue,jdbcType=LONGVARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{keyId,jdbcType=BIGINT}
  </update>
  <delete id="deleteById">
    delete from tb_inst_account_key
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <select id="selectAll" resultMap="BaseResultMap">
    select tia.id as id,tiak.id as keyId, tia.inst_id, tia.requirement_order_id, tia.type, tia.env, tia.account,
    tiak.account_id,tiak.key_type, tiak.encrypt_type,tiak.remark, tiak.utc_create, tiak.utc_modified, tiak.key_value, tiak.status
    from tb_inst_account tia left join tb_inst_account_key tiak
    on tia.id = tiak.account_id
    where tia.status = 'Y'
    <if test="instId != null">
      and tia.inst_id = #{instId,jdbcType=BIGINT}
    </if>
    <if test="requirementOrderId != null">
      and tia.requirement_order_id = #{requirementOrderId,jdbcType=BIGINT}
    </if>
  </select>
</mapper>