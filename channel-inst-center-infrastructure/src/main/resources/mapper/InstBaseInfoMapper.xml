<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoMapper">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo">
    <!--@mbg.generated-->
    <!--@Table tb_inst_base_info-->
    <id column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBrandId" />
    <result column="inst_name" jdbcType="VARCHAR" property="instName" />
    <result column="inst_types" jdbcType="VARCHAR" property="instTypes" />
    <result column="entity_country" jdbcType="CHAR" property="entityCountry" />
    <result column="is_fatf_member" jdbcType="CHAR" property="isFatfMember" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="pd_id" jdbcType="VARCHAR" property="pdId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    inst_id, inst_code, inst_brand_id, inst_name, inst_types, entity_country, is_fatf_member, 
    bd_id, am_id, pd_id, remark, `status`, utc_create, utc_modified
  </sql>
</mapper>