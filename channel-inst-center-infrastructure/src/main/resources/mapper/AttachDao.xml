<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.AttachDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.AttachEntity">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="attach_name" jdbcType="VARCHAR" property="attachName" />
    <result column="attach_url" jdbcType="VARCHAR" property="attachUrl" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.payermax.channel.inst.center.infrastructure.entity.AttachEntity">
    insert into tb_inst_attach (attach_name, attach_url)
    values (#{attachName,jdbcType=VARCHAR}, #{attachUrl,jdbcType=VARCHAR})
  </insert>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, attach_name, attach_url, utc_create, utc_modified
    from tb_inst_attach
    where id in
    <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>