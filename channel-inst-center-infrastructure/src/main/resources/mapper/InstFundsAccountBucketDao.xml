<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountBucketDao">

    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
            <result property="bucketsId" column="buckets_id" jdbcType="BIGINT"/>
            <result property="keyName" column="key_name" jdbcType="VARCHAR"/>
            <result property="keyValue" column="key_value" jdbcType="VARCHAR"/>
            <result property="total" column="total" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
            <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_id,buckets_id,key_name,
        key_value,total,status,
        utc_modified,utc_create
    </sql>

    <select id="selectBucketsIdIsNullByAccountId" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_inst_funds_account_bucket
        where account_id = #{accountId,jdbcType=VARCHAR}
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=CHAR}
        </if>
        and buckets_id is null
    </select>

    <select id="selectBucketsIdNotNullByBucketsId" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_inst_funds_account_bucket
        where account_id = #{accountId,jdbcType=VARCHAR}
          and buckets_id = #{bucketsId,jdbcType=BIGINT}
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=CHAR}
        </if>
    </select>
</mapper>
