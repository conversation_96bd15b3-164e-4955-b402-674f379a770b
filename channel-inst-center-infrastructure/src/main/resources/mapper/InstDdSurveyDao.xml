<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstDdSurveyDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dd_id" jdbcType="BIGINT" property="ddId" />
    <result column="has_payment_license" jdbcType="CHAR" property="hasPaymentLicense" />
    <result column="has_trans_monitor" jdbcType="CHAR" property="hasTransMonitor" />
    <result column="has_due_diligence" jdbcType="CHAR" property="hasDueDiligence" />
    <result column="has_sanctions_scan" jdbcType="CHAR" property="hasSanctionsScan" />
    <result column="has_pci_certification" jdbcType="CHAR" property="hasPciCertification" />
    <result column="pci_certification_date" jdbcType="DATE" property="pciCertificationDate" />
    <result column="survey_attach_id" jdbcType="BIGINT" property="surveyAttachId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity">
    insert into tb_inst_dd_survey (id, dd_id, has_payment_license,
      has_trans_monitor, has_due_diligence, has_sanctions_scan, 
      has_pci_certification, pci_certification_date, survey_attach_id, 
      remark
      )
    values (#{id,jdbcType=BIGINT}, #{ddId,jdbcType=BIGINT}, #{hasPaymentLicense,jdbcType=CHAR},
      #{hasTransMonitor,jdbcType=CHAR}, #{hasDueDiligence,jdbcType=CHAR}, #{hasSanctionsScan,jdbcType=CHAR}, 
      #{hasPciCertification,jdbcType=CHAR}, #{pciCertificationDate,jdbcType=DATE}, #{surveyAttachId,jdbcType=BIGINT}, 
      #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity">
    update tb_inst_dd_survey
    <set>
      <if test="ddId != null">
        dd_id = #{ddId,jdbcType=BIGINT},
      </if>
      <if test="hasPaymentLicense != null and hasPaymentLicense != ''">
        has_payment_license = #{hasPaymentLicense,jdbcType=CHAR},
      </if>
      <if test="hasTransMonitor != null and hasTransMonitor != ''">
        has_trans_monitor = #{hasTransMonitor,jdbcType=CHAR},
      </if>
      <if test="hasDueDiligence != null and hasDueDiligence != ''">
        has_due_diligence = #{hasDueDiligence,jdbcType=CHAR},
      </if>
      <if test="hasSanctionsScan != null and hasSanctionsScan != ''">
        has_sanctions_scan = #{hasSanctionsScan,jdbcType=CHAR},
      </if>
      <if test="hasPciCertification != null and hasPciCertification != ''">
        has_pci_certification = #{hasPciCertification,jdbcType=CHAR},
      </if>
      <if test="pciCertificationDate != null">
        pci_certification_date = #{pciCertificationDate,jdbcType=DATE},
      </if>
      <if test="surveyAttachId != null">
        survey_attach_id = #{surveyAttachId,jdbcType=BIGINT},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, dd_id, has_payment_license, has_trans_monitor, has_due_diligence, has_sanctions_scan,
    has_pci_certification, pci_certification_date, survey_attach_id, remark, utc_create, utc_modified
    from tb_inst_dd_survey
    where dd_id = #{ddId,jdbcType=BIGINT}
  </select>
</mapper>