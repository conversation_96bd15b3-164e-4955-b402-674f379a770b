<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstKycDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="watermark_word" jdbcType="VARCHAR" property="watermarkWord" />
    <result column="survey_attach_id" jdbcType="VARCHAR" property="surveyAttachId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity"
          useGeneratedKeys="true" keyProperty="id">
    insert into tb_inst_kyc (inst_id, apply_no,
      watermark_word, survey_attach_id, remark, 
      status
      )
    values (#{instId,jdbcType=BIGINT}, #{applyNo,jdbcType=VARCHAR},
      #{watermarkWord,jdbcType=VARCHAR}, #{surveyAttachId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity">
    update tb_inst_kyc
    <set>
      <if test="instId != null and instId != ''">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="applyNo != null and applyNo != ''">
        apply_no = #{applyNo,jdbcType=VARCHAR},
      </if>
      <if test="watermarkWord != null and watermarkWord != ''">
        watermark_word = #{watermarkWord,jdbcType=VARCHAR},
      </if>
      <if test="surveyAttachId != null and surveyAttachId != ''">
        survey_attach_id = #{surveyAttachId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, inst_id, apply_no, watermark_word, survey_attach_id, remark, status, utc_create, 
    utc_modified
    from tb_inst_kyc
    where inst_id = #{instId,jdbcType=BIGINT}
  </select>
</mapper>