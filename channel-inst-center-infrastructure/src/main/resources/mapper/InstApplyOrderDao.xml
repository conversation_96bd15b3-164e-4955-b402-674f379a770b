<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstApplyOrderDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity">
    <id column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBrandId" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="shareit_entity" jdbcType="CHAR" property="shareitEntity" />
    <result column="cooperation_mode" jdbcType="VARCHAR" property="cooperationMode" />
    <result column="countrys" jdbcType="VARCHAR" property="countrys" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="priority" jdbcType="CHAR" property="priority" />
    <result column="expect_release_time" jdbcType="DATE" property="expectReleaseTime" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="am_name" jdbcType="VARCHAR" property="amName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="products" jdbcType="LONGVARCHAR" property="products" />
    <result column="stage_status" jdbcType="LONGVARCHAR" property="stageStatus" />
  </resultMap>
  <resultMap id="AllResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity">
    <!--申请单-->
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="bd_id" jdbcType="VARCHAR" property="bdId" />
    <result column="bd_name" jdbcType="VARCHAR" property="bdName" />
    <result column="inst_brand_id" jdbcType="BIGINT" property="instBrandId" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="shareit_entity" jdbcType="CHAR" property="shareitEntity" />
    <result column="cooperation_mode" jdbcType="VARCHAR" property="cooperationMode" />
    <result column="countrys" jdbcType="VARCHAR" property="countrys" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="priority" jdbcType="CHAR" property="priority" />
    <result column="expect_release_time" jdbcType="DATE" property="expectReleaseTime" />
    <result column="am_id" jdbcType="VARCHAR" property="amId" />
    <result column="am_name" jdbcType="VARCHAR" property="amName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="products" jdbcType="LONGVARCHAR" property="products" />
    <result column="stage_status" jdbcType="LONGVARCHAR" property="stageStatus" />
    <!--机构-->
    <result column="instId" jdbcType="BIGINT" property="instBaseInfoEntity.instId" />
    <result column="inst_code" jdbcType="VARCHAR" property="instBaseInfoEntity.instCode" />
    <result column="instBrandId" jdbcType="BIGINT" property="instBaseInfoEntity.instBrandId" />
    <result column="inst_name" jdbcType="VARCHAR" property="instBaseInfoEntity.instName" />
    <result column="inst_types" jdbcType="VARCHAR" property="instBaseInfoEntity.instTypes" />
    <result column="entity_country" jdbcType="CHAR" property="instBaseInfoEntity.entityCountry" />
    <result column="is_fatf_member" jdbcType="CHAR" property="instBaseInfoEntity.isFatfMember" />
    <result column="remark" jdbcType="VARCHAR" property="instBaseInfoEntity.remark" />
    <result column="inst_status" jdbcType="CHAR" property="instBaseInfoEntity.status" />
    <result column="inst_utc_create" jdbcType="TIMESTAMP" property="instBaseInfoEntity.utcCreate" />
    <result column="inst_utc_modified" jdbcType="TIMESTAMP" property="instBaseInfoEntity.utcModified" />
    <!--品牌-->
    <result column="brandId" jdbcType="BIGINT" property="instBrandEntity.brandId" />
    <result column="brand_code" jdbcType="VARCHAR" property="instBrandEntity.brandCode" />
    <result column="brand_name" jdbcType="VARCHAR" property="instBrandEntity.brandName" />
    <result column="brand_status" jdbcType="CHAR" property="instBrandEntity.status" />
    <result column="brand_bd_id" jdbcType="VARCHAR" property="instBrandEntity.bdId" />
    <result column="brand_bd_name" jdbcType="VARCHAR" property="instBrandEntity.bdName" />
    <result column="brand_utc_create" jdbcType="TIMESTAMP" property="instBrandEntity.utcCreate" />
    <result column="brand_utc_modified" jdbcType="TIMESTAMP" property="instBrandEntity.utcModified" />
    <!--DD-->
    <result column="id" jdbcType="BIGINT" property="instDdEntity.id" />
    <result column="ddInstId" jdbcType="BIGINT" property="instDdEntity.instId" />
    <result column="ddApplyNo" jdbcType="VARCHAR" property="instDdEntity.applyNo" />
    <result column="register_name" jdbcType="VARCHAR" property="instDdEntity.registerName" />
    <result column="register_no" jdbcType="VARCHAR" property="instDdEntity.registerNo" />
    <result column="register_date" jdbcType="DATE" property="instDdEntity.registerDate" />
    <result column="register_address" jdbcType="VARCHAR" property="instDdEntity.registerAddress" />
    <result column="company_scale" jdbcType="VARCHAR" property="instDdEntity.companyScale" />
    <result column="validity_date" jdbcType="DATE" property="instDdEntity.validityDate" />
    <result column="corporate_name" jdbcType="VARCHAR" property="instDdEntity.corporateName" />
    <result column="corporate_birth_date" jdbcType="DATE" property="instDdEntity.corporateBirthDate" />
    <result column="corporate_address" jdbcType="VARCHAR" property="instDdEntity.corporateAddress" />
    <result column="website" jdbcType="VARCHAR" property="instDdEntity.website" />
    <result column="business_scope" jdbcType="VARCHAR" property="instDdEntity.businessScope" />
    <result column="dd_status" jdbcType="VARCHAR" property="instDdEntity.status" />
    <result column="dd_utc_create" jdbcType="TIMESTAMP" property="instDdEntity.utcCreate" />
    <result column="dd_utc_modified" jdbcType="TIMESTAMP" property="instDdEntity.utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity">
    insert into tb_inst_apply_order (apply_no, bd_id, bd_name,
      inst_brand_id, inst_id, apply_type,
      shareit_entity, cooperation_mode, countrys,
      reason, priority, expect_release_time,
      am_id, am_name, status,products,stage_status
      )
    values (#{applyNo,jdbcType=VARCHAR}, #{bdId,jdbcType=VARCHAR}, #{bdName,jdbcType=VARCHAR},
      #{instBrandId,jdbcType=BIGINT}, #{instId,jdbcType=BIGINT}, #{applyType,jdbcType=VARCHAR},
      #{shareitEntity,jdbcType=CHAR}, #{cooperationMode,jdbcType=VARCHAR}, #{countrys,jdbcType=VARCHAR},
      #{reason,jdbcType=VARCHAR}, #{priority,jdbcType=CHAR}, #{expectReleaseTime,jdbcType=DATE},
      #{amId,jdbcType=VARCHAR}, #{amName,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{products,jdbcType=LONGVARCHAR}, '{}'
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity">
    update tb_inst_apply_order
    <set>
      <if test="bdId != null and bdId != ''">
        bd_id = #{bdId,jdbcType=VARCHAR},
      </if>
      <if test="bdName != null and bdName != ''">
        bd_name = #{bdName,jdbcType=VARCHAR},
      </if>
      <if test="instBrandId != null">
        inst_brand_id = #{instBrandId,jdbcType=BIGINT},
      </if>
      <if test="instId != null">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null and applyType != ''">
        apply_type = #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="shareitEntity != null and shareitEntity != ''">
        shareit_entity = #{shareitEntity,jdbcType=CHAR},
      </if>
      <if test="cooperationMode != null and cooperationMode != ''">
        cooperation_mode = #{cooperationMode,jdbcType=VARCHAR},
      </if>
      <if test="countrys != null and countrys != ''">
        countrys = #{countrys,jdbcType=VARCHAR},
      </if>
      <if test="reason != null and reason != ''">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="priority != null and priority != ''">
        priority = #{priority,jdbcType=CHAR},
      </if>
      <if test="expectReleaseTime != null">
        expect_release_time = #{expectReleaseTime,jdbcType=DATE},
      </if>
      <if test="amId != null and amId != ''">
        am_id = #{amId,jdbcType=VARCHAR},
      </if>
      <if test="amName != null and amName != ''">
        am_name = #{amName,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="products != null and products != ''">
        products = #{products,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where apply_no = #{applyNo,jdbcType=VARCHAR}
  </update>
  <update id="updateStageStatus" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity">
    update tb_inst_apply_order
    set
    <if test="completeTime != null and completeTime != ''">
      stage_status = JSON_SET(stage_status, CONCAT('$.', #{stage,jdbcType=VARCHAR}), #{status,jdbcType=VARCHAR}, CONCAT('$.', #{stage,jdbcType=VARCHAR}, '_COMPLETE'), #{completeTime,jdbcType=VARCHAR})
    </if>
    <if test="completeTime == null or completeTime == ''">
      stage_status = JSON_SET(stage_status, CONCAT('$.', #{stage,jdbcType=VARCHAR}), #{status,jdbcType=VARCHAR})
    </if>
    where apply_no = #{applyNo,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select apply_no, bd_id, bd_name, inst_brand_id, inst_id, apply_type, shareit_entity,
    cooperation_mode, countrys, reason, priority, expect_release_time, am_id, am_name,
    status, utc_create, utc_modified, products, stage_status
    from tb_inst_apply_order
    where apply_no = #{applyNo,jdbcType=VARCHAR}
  </select>

  <select id="selectApplyOrderListByInstIds" resultMap="BaseResultMap">
    select apply_no, bd_id, bd_name, inst_brand_id, inst_id, apply_type, shareit_entity,
    cooperation_mode, countrys, reason, priority, expect_release_time, am_id, am_name,
    status, utc_create, utc_modified, products, stage_status
    from tb_inst_apply_order
    where inst_id in
    <foreach collection="instIds" index="index" item="instId" open="(" separator="," close=")">
      #{instId}
    </foreach>
  </select>

  <select id="selectApplyOrderList" parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity" resultMap="AllResultMap">
    select applyOrder.apply_no, applyOrder.bd_id, applyOrder.bd_name, applyOrder.inst_brand_id, applyOrder.inst_id, applyOrder.apply_type, applyOrder.shareit_entity,
    applyOrder.cooperation_mode, applyOrder.countrys, applyOrder.reason, applyOrder.priority, applyOrder.expect_release_time, applyOrder.am_id, applyOrder.am_name,
    applyOrder.status, applyOrder.utc_create, applyOrder.utc_modified, applyOrder.products, applyOrder.stage_status,
    instInfo.inst_id instId,instInfo.inst_code,instInfo.inst_brand_id instBrandId,instInfo.inst_name,instInfo.inst_types,instInfo.entity_country,instInfo.is_fatf_member,
    instInfo.remark,instInfo.status inst_status,instInfo.utc_create inst_utc_create,instInfo.utc_modified inst_utc_modified,
    brand.brand_id brandId,brand.brand_code,brand.brand_name,brand.bd_id brand_bd_id,brand.bd_name brand_bd_name,brand.status brand_status,
    brand.utc_create brand_utc_create,brand.utc_modified brand_utc_modified,
    dd.id,dd.apply_no ddApplyNo,dd.inst_id ddInstId,dd.register_name,dd.register_no,dd.register_date,dd.register_address,
    dd.company_scale,dd.validity_date,dd.corporate_name,dd.corporate_birth_date,dd.corporate_address,
    dd.website,dd.business_scope,dd.status dd_status,dd.utc_create dd_utc_create,dd.utc_modified dd_utc_modified
    from tb_inst_apply_order applyOrder
    left join tb_inst_base_info instInfo on applyOrder.inst_id = instInfo.inst_id
    left join tb_inst_brand brand on instInfo.inst_brand_id = brand.brand_id
    left join tb_inst_dd dd on dd.apply_no = applyOrder.apply_no and instInfo.inst_id = dd.inst_id
    <where>
      <if test="applyOrder != null">
        <if test="applyOrder.applyNos != null and applyOrder.applyNos.size > 0">
          and applyOrder.apply_no in
          <foreach collection="applyOrder.applyNos" index="index" item="applyNo" open="(" separator="," close=")">
            #{applyNo}
          </foreach>
        </if>
        <if test="applyOrder.applyNo != null and applyOrder.applyNo != ''">
          and applyOrder.apply_no = #{applyOrder.applyNo,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.instBrandId != null">
          and applyOrder.inst_brand_id = #{applyOrder.instBrandId,jdbcType=BIGINT}
        </if>
        <if test="applyOrder.instId != null">
          and applyOrder.inst_id = #{applyOrder.instId,jdbcType=BIGINT}
        </if>
        <if test="applyOrder.instBrandEntity != null">
          <if test="applyOrder.instBrandEntity.brandName != null and applyOrder.instBrandEntity.brandName != ''">
            and brand.brand_name like CONCAT('%',#{applyOrder.instBrandEntity.brandName,jdbcType=VARCHAR},'%')
          </if>
        </if>
        <if test="applyOrder.instBaseInfoEntity != null">
          <if test="applyOrder.instBaseInfoEntity.instName != null and applyOrder.instBaseInfoEntity.instName != ''">
            and instInfo.inst_name like CONCAT('%',#{applyOrder.instBaseInfoEntity.instName,jdbcType=VARCHAR},'%')
          </if>
          <if test="applyOrder.instBaseInfoEntity.instCode != null and applyOrder.instBaseInfoEntity.instCode != ''">
            and instInfo.inst_code like CONCAT('%',#{applyOrder.instBaseInfoEntity.instCode,jdbcType=VARCHAR},'%')
          </if>
          <if test="applyOrder.instBaseInfoEntity.instTypes != null and applyOrder.instBaseInfoEntity.instTypes != ''">
            and instInfo.inst_types like CONCAT('%',#{applyOrder.instBaseInfoEntity.instTypes,jdbcType=VARCHAR},'%')
          </if>
        </if>
        <if test="applyOrder.instDdEntity != null">
          <if test="applyOrder.instDdEntity.registerName != null and applyOrder.instDdEntity.registerName != ''">
            and dd.register_name like CONCAT('%',#{applyOrder.instDdEntity.registerName,jdbcType=VARCHAR},'%')
          </if>
        </if>
        <if test="applyOrder.shareitEntity != null and applyOrder.shareitEntity != ''">
          and applyOrder.shareit_entity = #{applyOrder.shareitEntity,jdbcType=CHAR}
        </if>
        <if test="applyOrder.channelType != null and applyOrder.channelType != ''">
          and LOCATE(#{applyOrder.channelType,jdbcType=VARCHAR},JSON_EXTRACT(applyOrder.products, '$[*].channelType')) > 0
        </if>
        <if test="applyOrder.paymentMethodTypes != null and applyOrder.paymentMethodTypes != ''">
          and LOCATE(#{applyOrder.paymentMethodTypes,jdbcType=VARCHAR},JSON_EXTRACT(applyOrder.products, '$[*].paymentMethodTypes')) > 0
        </if>
        <if test="applyOrder.countrys != null and applyOrder.countrys != ''">
          and LOCATE(#{applyOrder.countrys,jdbcType=VARCHAR},applyOrder.countrys) > 0
        </if>
        <if test="applyOrder.bdId != null and applyOrder.bdId != ''">
          and applyOrder.bd_id = #{applyOrder.bdId,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.amId != null and applyOrder.amId != ''">
          and applyOrder.am_id = #{applyOrder.amId,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.dbAndAmId != null and applyOrder.dbAndAmId != ''">
          and (applyOrder.bd_id = #{applyOrder.dbAndAmId,jdbcType=VARCHAR} or applyOrder.am_id = #{applyOrder.dbAndAmId,jdbcType=VARCHAR})
        </if>
        <if test="applyOrder.bdList != null and applyOrder.bdList.size > 0">
          and applyOrder.bd_id in
          <foreach collection="applyOrder.bdList" index="index" item="bdId" open="(" separator="," close=")">
            #{bdId}
          </foreach>
        </if>
        <if test="applyOrder.amList != null and applyOrder.amList.size > 0">
          and applyOrder.am_id in
          <foreach collection="applyOrder.amList" index="index" item="amId" open="(" separator="," close=")">
            #{amId}
          </foreach>
        </if>
        <if test="applyOrder.cooperationMode != null and applyOrder.cooperationMode != ''">
          and applyOrder.cooperation_mode = #{applyOrder.cooperationMode,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.status != null and applyOrder.status != ''">
          and applyOrder.status = #{applyOrder.status,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.priority != null and applyOrder.priority != ''">
          and applyOrder.priority = #{applyOrder.priority,jdbcType=VARCHAR}
        </if>
        <if test="applyOrder.utcCreate != null">
          and applyOrder.utc_create = #{applyOrder.utcCreate,jdbcType=TIMESTAMP}
        </if>
        <if test="applyOrder.expectReleaseTime != null">
          and applyOrder.expect_release_time = #{applyOrder.expectReleaseTime,jdbcType=TIMESTAMP}
        </if>
        <if test="applyOrder.utcCreateFrom != null and applyOrder.utcCreateTo != null">
          and applyOrder.utc_create between #{applyOrder.utcCreateFrom,jdbcType=TIMESTAMP}
          and #{applyOrder.utcCreateTo,jdbcType=TIMESTAMP}
        </if>
        <if test="applyOrder.expectReleaseTimeFrom != null and applyOrder.expectReleaseTimeTo != null">
          and applyOrder.expect_release_time between #{applyOrder.expectReleaseTimeFrom,jdbcType=TIMESTAMP}
          and #{applyOrder.expectReleaseTimeTo,jdbcType=TIMESTAMP}
        </if>
        <if test="applyOrder.includeStageStatusMap != null and applyOrder.includeStageStatusMap.size > 0">
          and
          <foreach collection="applyOrder.includeStageStatusMap" index="key" item="item" open="(" separator=" and " close=")">
            JSON_EXTRACT(applyOrder.stage_status, CONCAT('$.', #{key})) in
            <foreach collection="item" index="index" item="subItem" open="(" separator="," close=")">
              #{subItem}
            </foreach>
          </foreach>
        </if>
        <if test="applyOrder.excludeStageStatusMap != null and applyOrder.excludeStageStatusMap.size > 0">
          and
          <foreach collection="applyOrder.excludeStageStatusMap" index="key" item="item" open="(" separator=" or " close=")">
            JSON_EXTRACT(applyOrder.stage_status, CONCAT('$.', #{key})) is null or
            JSON_EXTRACT(applyOrder.stage_status, CONCAT('$.', #{key})) not in
            <foreach collection="item" index="index" item="subItem" open="(" separator="," close=")">
              #{subItem}
            </foreach>
          </foreach>
        </if>
      </if>
    </where>
    order by applyOrder.utc_modified desc
  </select>
</mapper>