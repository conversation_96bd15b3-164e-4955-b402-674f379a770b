<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstProductAccountDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstProductAccountEntity">
    <id column="product_capability_code" jdbcType="VARCHAR" property="productCapabilityCode" />
    <id column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductAccountEntity">
    insert into tb_inst_product_account (product_capability_code, account_id, 
      requirement_order_id
      )
    values (#{productCapabilityCode,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, 
      #{requirementOrderId,jdbcType=BIGINT}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductAccountEntity">
    update tb_inst_product_account
    set requirement_order_id = #{requirementOrderId,jdbcType=BIGINT},
      utc_create = #{utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{utcModified,jdbcType=TIMESTAMP}
    where product_capability_code = #{productCapabilityCode,jdbcType=VARCHAR}
      and account_id = #{accountId,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select product_capability_code, account_id, requirement_order_id, utc_create, utc_modified
    from tb_inst_product_account
  </select>
</mapper>