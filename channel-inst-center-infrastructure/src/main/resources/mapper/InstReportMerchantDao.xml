<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstReportMerchantDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="requirement_order_id" jdbcType="BIGINT" property="requirementOrderId" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="channel_type" jdbcType="VARCHAR" property="channelType" />
    <result column="is_need_report_merchant" jdbcType="CHAR" property="isNeedReportMerchant" />
    <result column="report_require" jdbcType="VARCHAR" property="reportRequire" />
    <result column="report_type" jdbcType="VARCHAR" property="reportType" />
    <result column="report_process_time" jdbcType="VARCHAR" property="reportProcessTime" />
    <result column="report_template" jdbcType="VARCHAR" property="reportTemplate" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity">
    insert into tb_inst_report_merchant (requirement_order_id, inst_id,
      channel_type, is_need_report_merchant, report_require, 
      report_type, report_process_time, report_template)
    values (#{requirementOrderId,jdbcType=BIGINT}, #{instId,jdbcType=BIGINT},
      #{channelType,jdbcType=VARCHAR}, #{isNeedReportMerchant,jdbcType=CHAR}, #{reportRequire,jdbcType=VARCHAR}, 
      #{reportType,jdbcType=VARCHAR}, #{reportProcessTime,jdbcType=VARCHAR}, #{reportTemplate,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity">
    update tb_inst_report_merchant
    <set>
      <if test="requirementOrderId != null">
        requirement_order_id = #{requirementOrderId,jdbcType=BIGINT},
      </if>
      <if test="instId != null">
        inst_id = #{instId,jdbcType=BIGINT},
      </if>
      <if test="channelType != null and channelType != ''">
        channel_type = #{channelType,jdbcType=VARCHAR},
      </if>
      <if test="isNeedReportMerchant != null and isNeedReportMerchant != ''">
        is_need_report_merchant = #{isNeedReportMerchant,jdbcType=CHAR},
      </if>
      <if test="reportRequire != null and reportRequire != ''">
        report_require = #{reportRequire,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null and reportType != ''">
        report_type = #{reportType,jdbcType=VARCHAR},
      </if>
      <if test="reportProcessTime != null and reportProcessTime != ''">
        report_process_time = #{reportProcessTime,jdbcType=VARCHAR},
      </if>
      <if test="reportTemplate != null and reportTemplate != ''">
        report_template = #{reportTemplate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select id, requirement_order_id, inst_id, channel_type, is_need_report_merchant, 
    report_require, report_type, report_process_time, report_template, utc_create, utc_modified
    from tb_inst_report_merchant
    <where>
      <if test="requirementOrderId != null">
        and requirement_order_id = #{requirementOrderId,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>