<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstSubFundsAccountDao">

    <resultMap id="BaseResultMap"
               type="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity">
        <id property="subAccountId" column="sub_account_id" jdbcType="VARCHAR"/>
        <result property="businessKey" column="business_key" jdbcType="VARCHAR"/>
        <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
        <result property="bucketId" column="bucket_id" jdbcType="BIGINT"/>
        <result property="numberSegmentId" column="number_segment_id" jdbcType="VARCHAR"/>
        <result property="numberSegmentNo" column="number_segment_no" jdbcType="VARCHAR"/>
        <result property="subUseType" column="sub_use_type" jdbcType="VARCHAR"/>
        <result property="subAccountNo" column="sub_account_no" jdbcType="VARCHAR"/>
        <result property="subAccountName" column="sub_account_name" jdbcType="VARCHAR"/>
        <result property="bSubAccountNo" column="b_sub_account_no" jdbcType="VARCHAR"/>
        <result property="merchantNo" column="merchant_no" jdbcType="VARCHAR"/>
        <result property="subMerchantNo" column="sub_merchant_no" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="scenes" column="scenes" jdbcType="VARCHAR"/>
        <result property="accountJson" column="account_json" jdbcType="VARCHAR"/>
        <result property="requestBody" column="request_body" jdbcType="VARCHAR"/>
        <result property="utcCreate" column="utc_create" jdbcType="TIMESTAMP"/>
        <result property="utcModified" column="utc_modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        sub_account_id
        ,business_key,account_id,
        number_segment_id,number_segment_no,sub_use_type,sub_account_no,
        sub_account_name,b_sub_account_no,merchant_no,sub_merchant_no,scenes,status,bucket_id,
        account_json,request_body,utc_create,utc_modified
    </sql>
    
    <sql id="columnList">
        sub_account.sub_account_id, sub_account.business_key, sub_account.account_id, sub_account.bucket_id
    , sub_account.number_segment_id,sub_account.number_segment_no, sub_account.sub_use_type, sub_account.sub_account_no
    , sub_account.sub_account_name,sub_account.b_sub_account_no, sub_account.merchant_no, sub_account.sub_merchant_no, sub_account.status, sub_account.scenes
    , sub_account.account_json, sub_account.request_body
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account
        where sub_account_id = #{subAccountId,jdbcType=VARCHAR}
    </select>

    <insert id="insert" keyColumn="sub_account_id" keyProperty="subAccountId"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity"
            useGeneratedKeys="true">
        insert into tb_inst_sub_funds_account
        ( sub_account_id, business_key, account_id, bucket_id
        , number_segment_id,number_segment_no, sub_use_type, sub_account_no
        , sub_account_name,b_sub_account_no, merchant_no, sub_merchant_no, status, scenes
        , account_json, request_body)
        values ( #{subAccountId,jdbcType=VARCHAR}, #{businessKey,jdbcType=VARCHAR}, #{accountId,jdbcType=VARCHAR}
               , #{bucketId,jdbcType=BIGINT}
               , #{numberSegmentId,jdbcType=VARCHAR}, #{numberSegmentNo,jdbcType=VARCHAR}, #{subUseType,jdbcType=VARCHAR}, #{subAccountNo,jdbcType=VARCHAR}
               , #{subAccountName,jdbcType=VARCHAR}, #{bSubAccountNo,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR}, #{subMerchantNo,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}
               , #{scenes,jdbcType=VARCHAR}, #{accountJson,jdbcType=VARCHAR}, #{requestBody,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective">
        update tb_inst_sub_funds_account
        <set>
            <if test="record.status != null">
                status = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.accountJson != null and record.accountJson != ''">
                account_json = #{record.accountJson,jdbcType=VARCHAR},
            </if>
            <if test="record.subAccountNo != null and record.subAccountNo != ''">
                sub_account_no = #{record.subAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="record.subAccountName != null and record.subAccountName != ''">
                sub_account_name = #{record.subAccountName,jdbcType=VARCHAR},
            </if>
            <if test="record.bSubAccountNo != null and record.bSubAccountNo != ''">
                b_sub_account_no = #{record.bSubAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="record.numberSegmentId != null and record.numberSegmentId != ''">
                number_segment_id = #{record.numberSegmentId,jdbcType=VARCHAR},
            </if>
            <if test="record.numberSegmentNo != null and record.numberSegmentNo != ''">
                number_segment_no = #{record.numberSegmentNo,jdbcType=VARCHAR},
            </if>
            <if test="record.bucketId != null and record.bucketId != ''">
                bucket_id = #{record.bucketId,jdbcType=VARCHAR},
            </if>
        </set>
        where
        sub_account_id = #{record.subAccountId,jdbcType=VARCHAR}
        <if test="origStatus != null">
            and status = #{origStatus,jdbcType=INTEGER}
        </if>
    </update>

    <select id="selectByQueryEntity"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity"
            resultMap="BaseResultMap">
        select
        <include refid="columnList"/>
        from tb_inst_sub_funds_account sub_account
        left join tb_inst_funds_account account on account.account_id = sub_account.account_id
        where 1=1
        <if test="accountId != null and accountId != ''">
            and sub_account.account_id = #{accountId,jdbcType=VARCHAR}
        </if>
        <if test="businessKey != null and businessKey != ''">
            and sub_account.business_key = #{businessKey,jdbcType=VARCHAR}
        </if>
        <if test="subAccountNo != null and subAccountNo != ''">
            and sub_account.sub_account_no = #{subAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="bSubAccountNo != null and bSubAccountNo != ''">
            and sub_account.b_sub_account_no = #{bSubAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and sub_account.status = #{status,jdbcType=INTEGER}
        </if>
        <if test="accountNo != null and accountNo != ''">
            and account.account_no = #{accountNo,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectCountByAccountId" resultType="java.lang.Integer">
        select count(1)
        from tb_inst_sub_funds_account
        where account_id = #{accountId,jdbcType=VARCHAR}
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            and merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="utcCreate != null">
            <![CDATA[ and utc_create < #{utcCreate,jdbcType=TIMESTAMP} ]]>
        </if>
    </select>

    <select id="selectBucketIdCountByAccountId"
            resultType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketCountEntity">
        select bucket_id as bucketId, count(1) as count
        from tb_inst_sub_funds_account
        where account_id = #{accountId,jdbcType=VARCHAR}
          and bucket_id is not null
        group by bucket_id
    </select>

    <select id="selectCountBucketIdIsNullByAccountId"
            resultType="java.lang.Integer">
        select count(1)
        from tb_inst_sub_funds_account
        where account_id = #{accountId,jdbcType=VARCHAR}
          and bucket_id is null
    </select>

    <select id="selectSubAccountForTask"
            parameterType="com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_inst_sub_funds_account
        <where>
            <if test="entity.accountIds != null and entity.accountIds.size() > 1">
                and account_id in
                <foreach collection="entity.accountIds" index="index" item="accountId" open="(" separator="," close=")">
                    #{accountId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.accountIds != null and entity.accountIds.size() == 1">
                and account_id =
                <foreach collection="entity.accountIds" index="index" item="accountId" open="" separator="," close="">
                    #{accountId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.subAccountIds != null and entity.subAccountIds.size() > 1">
                and sub_account_id in
                <foreach collection="entity.subAccountIds" index="index" item="subAccountId" open="(" separator="," close=")">
                    #{subAccountId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.subAccountIds != null and entity.subAccountIds.size() == 1">
                and sub_account_id =
                <foreach collection="entity.subAccountIds" index="index" item="subAccountId" open="" separator="," close="">
                    #{subAccountId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.merchantNos != null and entity.merchantNos.size() > 1">
                and merchant_no in
                <foreach collection="entity.merchantNos" index="index" item="merchantNo" open="(" separator="," close=")">
                    #{merchantNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.merchantNos != null and entity.merchantNos.size() == 1">
                and merchant_no =
                <foreach collection="entity.merchantNos" index="index" item="merchantNo" open="" separator="," close="">
                    #{merchantNo,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="entity.status != null">
                and status = #{entity.status,jdbcType=INTEGER}
            </if>
            <if test="entity.utcCreate != null">
                and utc_create > #{entity.utcCreate,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by utc_create
    </select>
</mapper>
