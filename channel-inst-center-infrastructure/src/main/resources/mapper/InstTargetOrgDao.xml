<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstTargetOrgDao">
    <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="target_org_code" jdbcType="VARCHAR" property="targetOrgCode" />
        <result column="target_org_name" jdbcType="VARCHAR" property="targetOrgName" />
        <result column="status" jdbcType="CHAR" property="status" />
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    </resultMap>
    <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity">
    insert into tb_inst_target_org (target_org_code,target_org_name,status)
    values (#{targetOrgCode,jdbcType=VARCHAR}, #{targetOrgName,jdbcType=VARCHAR},
      #{status,jdbcType=CHAR})
  </insert>
    <insert id="insertBatch">
        insert into tb_inst_target_org (target_org_code,target_org_name,status)
        values
        <foreach collection="records" index="index" item="item" separator=",">
            (#{item.targetOrgCode,jdbcType=VARCHAR}, #{item.targetOrgName,jdbcType=VARCHAR},
            #{item.status,jdbcType=CHAR})
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity">
        update tb_inst_target_org
        <set>
            <if test="targetOrgCode != null and targetOrgCode != ''">
                target_org_code = #{targetOrgCode,jdbcType=VARCHAR},
            </if>
            <if test="targetOrgName != null and targetOrgName != ''">
                target_org_name = #{targetOrgName,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                status = #{status,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectAll" resultMap="BaseResultMap">
        select id,target_org_code,target_org_name,status,utc_create,utc_modified
        from tb_inst_target_org
        where 1=1
        <if test="targetOrgCode != null and targetOrgCode != ''">
            and target_org_code = #{targetOrgCode,jdbcType=VARCHAR}
        </if>
        <if test="targetOrgName != null and targetOrgName != ''">
            and target_org_name = #{targetOrgName,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=CHAR}
        </if>
        order by utc_modified desc
    </select>
</mapper>