package com.payermax.channel.inst.center.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstApplyOrderDao extends BaseMapper<InstApplyOrderEntity> {

    int insert(InstApplyOrderEntity record);

    List<InstApplyOrderEntity> selectAll();

    IPage<ApplyOrderQueryEntity> selectApplyOrderList(IPage<ApplyOrderQueryEntity> page, @Param("applyOrder") ApplyOrderQueryEntity applyOrderQueryEntity);

    int updateByPrimaryKey(InstApplyOrderEntity record);

    List<ApplyOrderQueryEntity> selectApplyOrderList(@Param("applyOrder") ApplyOrderQueryEntity applyOrderQueryEntity);

    List<InstApplyOrderEntity> selectApplyOrderListByInstIds(@Param("instIds") List<Long> instIds);

    int updateStageStatus(@Param("applyNo") String applyNo, @Param("stage") String stage, @Param("status") String status, @Param("completeTime") String completeTime);

}