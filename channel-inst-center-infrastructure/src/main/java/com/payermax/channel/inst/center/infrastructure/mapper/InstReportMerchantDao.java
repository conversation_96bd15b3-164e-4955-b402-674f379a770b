package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstReportMerchantDao {

    int insert(InstReportMerchantEntity record);

    List<InstReportMerchantEntity> selectAll(InstReportMerchantEntity queryEntity);

    int updateByPrimaryKey(InstReportMerchantEntity record);
}