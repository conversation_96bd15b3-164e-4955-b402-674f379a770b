package com.payermax.channel.inst.center.infrastructure.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 机构资金账户生成子级资金账户拓展请求参数表
 * <AUTHOR>
 * @TableName tb_inst_funds_account_bucket
 */
@Data
public class InstFundsAccountBucketEntity implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构帐号标识
     */
    private String accountId;

    /**
     * 机构帐号标识
     */
    private Long bucketsId;

    /**
     * 键
     */
    private String keyName;

    /**
     * 值
     */
    private String keyValue;

    /**
     * 可使用号码总数
     */
    private Integer total;

    /**
     * 状态：Y:可用，N:不可用
     */
    private String status;

    /**
     * 更新时间
     */
    private Date utcModified;

    /**
     * 创建时间
     */
    private Date utcCreate;

    private static final long serialVersionUID = 1L;
}