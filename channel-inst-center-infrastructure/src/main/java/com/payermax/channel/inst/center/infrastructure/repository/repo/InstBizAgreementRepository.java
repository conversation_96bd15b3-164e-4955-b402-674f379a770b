package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstBizAgreementMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Component
public class InstBizAgreementRepository extends ServiceImpl<InstBizAgreementMapper, InstBizAgreementPO> {


    /**
     * 根据业务协议唯一键查询
     */
    public InstBizAgreementPO getByUniqueKey(InstBizAgreementPO bizAgreement) {
        LambdaQueryWrapper<InstBizAgreementPO> queryWrapper = Wrappers.<InstBizAgreementPO>lambdaQuery()
                .eq(InstBizAgreementPO::getInitiator, bizAgreement.getInitiator())
                .eq(InstBizAgreementPO::getCounter, bizAgreement.getCounter())
                .eq(InstBizAgreementPO::getType, bizAgreement.getType());
        return getOne(queryWrapper);
    }

    /**
     * 业务协议修改唯一性检查，需排除当前业务协议
     */
    public Boolean modifyUniqueCheck(InstBizAgreementPO bizAgreement) {
        LambdaQueryWrapper<InstBizAgreementPO> queryWrapper = Wrappers.<InstBizAgreementPO>lambdaQuery()
                .eq(InstBizAgreementPO::getInitiator, bizAgreement.getInitiator())
                .eq(InstBizAgreementPO::getCounter, bizAgreement.getCounter())
                .eq(InstBizAgreementPO::getType, bizAgreement.getType())
                .ne(InstBizAgreementPO::getBizAgreementNo, bizAgreement.getBizAgreementNo());
        return CollectionUtils.isEmpty(list(queryWrapper));
    }
}
