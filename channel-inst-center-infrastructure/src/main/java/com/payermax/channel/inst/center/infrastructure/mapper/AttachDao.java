package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.AttachEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.AttachQueryEntity;

import java.util.List;

/**
 * 文件附件DAO
 *
 * <AUTHOR>
 * @date 2021/5/15 14:20
 */
public interface AttachDao {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(AttachEntity record);

    /**
     * 查询附件
     *
     * @param queryEntity
     * @return
     */
    List<AttachEntity> selectAll(AttachQueryEntity queryEntity);
}