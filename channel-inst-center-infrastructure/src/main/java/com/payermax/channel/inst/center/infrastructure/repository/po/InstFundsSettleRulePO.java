package com.payermax.channel.inst.center.infrastructure.repository.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 清算规则
 */
@Data
@TableName(value ="tb_inst_funds_settle_rule")
@Accessors(chain = true)
public class InstFundsSettleRulePO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 4-2-1-清算规则编号
     */
    @TableId(value = "settle_rule_no", type = IdType.INPUT)
    private String settleRuleNo;

    /**
     * 4-2-1-资金协议单号
     */
    private String fundsAgreementNo;

    /**
     * 4-2-1-时区-例:UTC8
     */
    private String timezone;

    /**
     * 4-2-1-切点-例:16:00:00
     */
    private String cutoffTime;

    /**
     * 4-2-1-清分范围开始
     */
    private String clearingRangeStart;

    /**
     * 4-2-1-清分范围结束
     */
    private String clearingRangeEnd;

    /**
     * 4-2-1-清分模式-主动/被动，是指双方主体中，哪方主体负责清分，产出清算文件
     */
    private String clearingPattern;

    /**
     * 4-2-1-清偿模式-主动/被动，是指双方主体，由哪方发起资金交割动作
     */
    private String clearOffPattern;

    /**
     * 4-2-1-清偿时间-出账单的规则-D1
     */
    private String clearOffTime;

    /**
     * 4-2-1-清偿类型-外部清偿/内部清偿
     */
    private String clearOffType;

    /**
     * 4-2-1-起结金额
     */
    private BigDecimal settleMinAmount;

    /**
     * 4-2-1-结算币种
     */
    private String settleCcy;

    /**
     * 4-2-1-结算账户
     */
    private String settleAccount;

    /**
     * 4-2-1-到账时间-D+3WD
     */
    private String settleArrived;

    /**
     * 4-2-1-换汇时间-D+3WD
     */
    private String settleExchange;

    /**
     * 4-2-1-到账时间-D+3WD
     */
    private String settlePayment;

    /**
     * 0-0-创建时间
     */
    private Date utcCreate;

    /**
     * 0-0-更新时间
     */
    private Date utcModified;

    /**
     * 是否新创建的，辅助数据库操作
     */
    @TableField(exist = false)
    private boolean newlyCreated;
}

