package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_inst_apply_order")
public class InstApplyOrderEntity {
    @TableId(value = "apply_no",type = IdType.INPUT)
    private String applyNo;

    private String bdId;

    private String bdName;

    private Long instBrandId;

    private Long instId;

    private String applyType;

    private String shareitEntity;

    private String cooperationMode;

    private String countrys;

    private String reason;

    private String priority;

    private Date expectReleaseTime;

    private String amId;

    private String amName;

    private String status;

    private Date utcCreate;

    private Date utcModified;

    private String products;

    private String stageStatus;
}