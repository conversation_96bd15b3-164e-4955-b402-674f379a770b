package com.payermax.channel.inst.center.infrastructure.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class InstBankAccountEntity {

    private Long id;

    private Long instId;

    private String country;

    private String currency;

    private String bankCode;

    private String bankName;

    private String accountName;

    private String accountNo;

    private String branch;

    private String branchAddress;

    private String swiftCode;

    private String iban;

    private String accountUse;

    private String rechargeCanUseCcy;

    private String rechargeUseOrder;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}