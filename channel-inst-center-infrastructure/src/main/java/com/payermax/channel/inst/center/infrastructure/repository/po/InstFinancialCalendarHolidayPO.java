package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;

/**
 * <AUTHOR>
 * @description 机构中心-金融日历-节假日
 * @date 2024-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(InstFinancialCalendarHolidayPO.TABLE_NAME)
public class InstFinancialCalendarHolidayPO implements Serializable {

    public static final String TABLE_NAME = "tb_inst_financial_calendar_holiday";


    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long holidayId;

    /**
     * 日历ID
     */
    private String calendarId;

    /**
     * 节假日日期
     */
    private LocalDate holidayDate;

    /**
     * 节假日月份
     */
    private Month holidayMonth;

    /**
     * 节假日操作类型
     */
    private String holidayOperate;

    /**
     * 是否工作日， 1 表示工作日， 0 表示非工作日
     */
    private Boolean isWorkday;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 修改时间
     */
    private LocalDateTime utcModified;
}
