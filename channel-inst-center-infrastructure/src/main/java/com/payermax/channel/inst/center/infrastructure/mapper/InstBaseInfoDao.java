package com.payermax.channel.inst.center.infrastructure.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstBaseInfoDao {
    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstBaseInfoEntity record);

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    List<InstBaseInfoEntity> selectAll(InstBaseInfoEntity record);

    /**
     * 根据机构code查询记录
     *
     * @return
     */
    List<InstBaseInfoEntity> selectByInstCode(String instCode);

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    IPage<InstBaseInfoQueryEntity> selectAllInstInfo(IPage<InstBaseInfoQueryEntity> page,@Param("record") InstBaseInfoQueryEntity record);

    /**
     * 查询记录
     *
     * @param brandIds
     * @return
     */
    List<InstBaseInfoEntity> selectByBrandId(List<Long> brandIds);

    /**
     * 更新记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstBaseInfoEntity record);
}