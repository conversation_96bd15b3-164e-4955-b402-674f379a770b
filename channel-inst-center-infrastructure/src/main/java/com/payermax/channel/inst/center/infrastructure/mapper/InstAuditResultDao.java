package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstAuditResultDao {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstAuditResultEntity record);

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    List<InstAuditResultEntity> selectAll(InstAuditResultEntity record);

    /**
     * 更新记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstAuditResultEntity record);
}