package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstAccountEntity {
    private Long id;

    private Long instId;

    private Long requirementOrderId;

    private String type;

    private String env;

    private String account;

    private String keyType;

    private String encryptType;

    private String remark;

    private Date utcCreate;

    private Date utcModified;

    private String keyValue;

    private String status;
}