package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractMidMappingMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.common.lang.util.AssertUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-13 3:22 PM
 */
@Service
public class InstNewContractMidMappingCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractMidMappingMapper instContractMidMappingMapper;

    private static Map<String, Map<String, InstContractMidMappingPO>> MID_2_CONTRACT_MAP = new HashMap<>();

    public InstContractMidMappingPO queryMappingByMid(String mid, String bizType) {
        AssertUtil.isTrue(MID_2_CONTRACT_MAP.containsKey(mid), "", "MID 未找到!");
        return MID_2_CONTRACT_MAP.get(mid).get(bizType);
    }

    /**
     * 校验MID有效性
     */
    public boolean isMidValid(String mid, String bizType) {
        return MID_2_CONTRACT_MAP.containsKey(mid);
    }

    @Override
    public void doInit() {
        QueryWrapper<InstContractMidMappingPO> queryWrapper = new QueryWrapper<>();
        List<InstContractMidMappingPO> instContractMidMappingPOS = instContractMidMappingMapper.selectList(queryWrapper);
        instContractMidMappingPOS.forEach(item -> {
            Map<String, InstContractMidMappingPO> bizType2Contract = MID_2_CONTRACT_MAP.computeIfAbsent(item.getChannelMerchantCode(), a -> new HashMap<>());
            bizType2Contract.put(item.getInstType(), item);
        });
    }

    @Override
    public void doRefresh() {
        Map<String, Map<String, InstContractMidMappingPO>> temp = new HashMap<>();
        List<InstContractMidMappingPO> instContractMidMappingPOS = instContractMidMappingMapper.selectList(new QueryWrapper<>());
        instContractMidMappingPOS.forEach(item -> {
                    Map<String, InstContractMidMappingPO> bizType2Contract = temp.computeIfAbsent(item.getChannelMerchantCode(), a -> new HashMap<>());
                    bizType2Contract.put(item.getInstType(), item);
                }
        );
        MID_2_CONTRACT_MAP = temp;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_MID_MAPPING;
    }
}
