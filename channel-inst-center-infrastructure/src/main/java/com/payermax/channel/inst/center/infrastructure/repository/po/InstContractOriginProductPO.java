package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> at 2023/6/9 5:24 PM
 **/
@TableName(InstContractOriginProductPO.TABLE_NAME)
@Data
public class InstContractOriginProductPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_origin_product";

    /**
     * 机构原始产品编码
     */
    @TableId(type = IdType.INPUT)
    private String instOriginProductNo;

    /**
     * 机构原始产品名称
     */
    private String instProductName;


    /**
     * 所属合同号 + 合同版本
     */
    private String contractNo;

    private String contractVersion;


    /**
     * 费用信息
     */
    @TableField(exist = false)
    private List<InstContractFeeItemPO> contractFeeItems;

    /**
     * 结算信息
     */
    @TableField(exist = false)
    private List<InstContractSettlementItemPO> settlementItems;

    /**
     * 标准化机构产品
     */
    @TableField(exist = false)
    private List<InstContractStandardProductPO> standardProducts;

    /**
     * 合同信息
     */
    @TableField(exist = false)
    private InstContractBaseInfoPO contractBaseInfo;

    /**
     * 合同版本信息
     */
    @TableField(exist = false)
    private InstContractVersionInfoPO contractVersionInfo;


    @TableField(exist = false)
    private boolean newlyCreated;
}
