package com.payermax.channel.inst.center.infrastructure.repository.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 清算规则
 */
@Data
@TableName(value = "tb_inst_funds_account_mid_mapping")
@Accessors(chain = true)
public class InstFundsAccountMidMappingPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 4-2-1-账户id
     */
    private String accountId;

    /**
     * 4-2-2-MID
     */
    private String mid;

    /**
     * 0-0-创建时间
     */
    private Date utcCreate;

    /**
     * 0-0-更新时间
     */
    private Date utcModified;

}

