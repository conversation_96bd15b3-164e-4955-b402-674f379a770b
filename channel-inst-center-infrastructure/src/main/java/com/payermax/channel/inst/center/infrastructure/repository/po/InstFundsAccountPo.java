package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构资金账号表
 * <AUTHOR>
 * @TableName tb_inst_funds_account
 */
@TableName(value ="tb_inst_funds_account")
@Data
public class InstFundsAccountPo implements Serializable {
    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    @TableId(value = "account_id", type = IdType.INPUT)
    private String accountId;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 机构开户名称
     */
    private String accountName;

    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;

    /**
     * 场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
     */
    private String scenes;

    /**
     * 是否支持子级账号 N:不支持，Y:支持
     */
    private String isSupportSubAccount;

    /**
     * 是否支持预申请 N:不支持，Y：支持
     */
    private String isSupportPreApply;

    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;

    /**
     * 子级账号生成规则，示例：citi${accountNo}${subAccouuntNo}
     */
    private String subAccountRule;

    /**
     * 最大生成子级账号数,空则无限制
     */
    private Long subAccountLimit;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 状态 N：不可用，Y：可用
     */
    private String status;

    /**
     * 优先级，值越大优先级越高
     */
    private Integer priority;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否支持名称自定义：N：不支持，Y：支持
     */
    private String isSupportCustomName;

    /**
     * 是否需要激活：N:不需要，Y:需要
     */
    private String isNeedActivation;

    /**
     * 激活模式 API模式：API，线下模式：OFFLINE
     */
    private String activationMode;

    /**
     * 关联机构MID
     */
    private String instMid;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 扩展配置
     */
    private String accountJson;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;

    /**
     * 账户别名{我司主体简称}_{机构简称}_{币种}_{账号后4位}
     */
    private String accountAlias;

    /**
     * 账户类别current account /saving account /call account
     */
    private String accountCategory;

    /**
     * 账户性质(在岸户/离岸户)
     */
    private String accountNature;

    /**
     * 国际银行账号
     */
    private String iban;

    /**
     * 开户时间
     */
    private Date accountOpeningTime;

    /**
     * 授权签字人
     */
    private String authorizedOfficer;

    /**
     * 账户相关文件
     */
    private String fileList;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 网银操作人
     */
    private String bankOperator;

    /**
     * 适用业务
     */
    private String bizType;

    /**
     * Y: 已删除, N: 未删除
     */
    private String isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        InstFundsAccountPo other = (InstFundsAccountPo) that;
        return (this.getAccountId() == null ? other.getAccountId() == null : this.getAccountId().equals(other.getAccountId()))
            && (this.getInstCode() == null ? other.getInstCode() == null : this.getInstCode().equals(other.getInstCode()))
            && (this.getEntity() == null ? other.getEntity() == null : this.getEntity().equals(other.getEntity()))
            && (this.getAccountNo() == null ? other.getAccountNo() == null : this.getAccountNo().equals(other.getAccountNo()))
            && (this.getAccountType() == null ? other.getAccountType() == null : this.getAccountType().equals(other.getAccountType()))
            && (this.getAccountName() == null ? other.getAccountName() == null : this.getAccountName().equals(other.getAccountName()))
            && (this.getUseType() == null ? other.getUseType() == null : this.getUseType().equals(other.getUseType()))
            && (this.getScenes() == null ? other.getScenes() == null : this.getScenes().equals(other.getScenes()))
            && (this.getIsSupportSubAccount() == null ? other.getIsSupportSubAccount() == null : this.getIsSupportSubAccount().equals(other.getIsSupportSubAccount()))
            && (this.getIsSupportPreApply() == null ? other.getIsSupportPreApply() == null : this.getIsSupportPreApply().equals(other.getIsSupportPreApply()))
            && (this.getSubAccountMode() == null ? other.getSubAccountMode() == null : this.getSubAccountMode().equals(other.getSubAccountMode()))
            && (this.getSubAccountRule() == null ? other.getSubAccountRule() == null : this.getSubAccountRule().equals(other.getSubAccountRule()))
            && (this.getSubAccountLimit() == null ? other.getSubAccountLimit() == null : this.getSubAccountLimit().equals(other.getSubAccountLimit()))
            && (this.getCountry() == null ? other.getCountry() == null : this.getCountry().equals(other.getCountry()))
            && (this.getCurrency() == null ? other.getCurrency() == null : this.getCurrency().equals(other.getCurrency()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getPriority() == null ? other.getPriority() == null : this.getPriority().equals(other.getPriority()))
            && (this.getWeight() == null ? other.getWeight() == null : this.getWeight().equals(other.getWeight()))
            && (this.getIsSupportCustomName() == null ? other.getIsSupportCustomName() == null : this.getIsSupportCustomName().equals(other.getIsSupportCustomName()))
            && (this.getIsNeedActivation() == null ? other.getIsNeedActivation() == null : this.getIsNeedActivation().equals(other.getIsNeedActivation()))
            && (this.getActivationMode() == null ? other.getActivationMode() == null : this.getActivationMode().equals(other.getActivationMode()))
            && (this.getInstMid() == null ? other.getInstMid() == null : this.getInstMid().equals(other.getInstMid()))
            && (this.getBankName() == null ? other.getBankName() == null : this.getBankName().equals(other.getBankName()))
            && (this.getBankAddress() == null ? other.getBankAddress() == null : this.getBankAddress().equals(other.getBankAddress()))
            && (this.getAccountJson() == null ? other.getAccountJson() == null : this.getAccountJson().equals(other.getAccountJson()))
            && (this.getUtcCreate() == null ? other.getUtcCreate() == null : this.getUtcCreate().equals(other.getUtcCreate()))
            && (this.getUtcModified() == null ? other.getUtcModified() == null : this.getUtcModified().equals(other.getUtcModified()))
            && (this.getAccountAlias() == null ? other.getAccountAlias() == null : this.getAccountAlias().equals(other.getAccountAlias()))
            && (this.getAccountCategory() == null ? other.getAccountCategory() == null : this.getAccountCategory().equals(other.getAccountCategory()))
            && (this.getAccountNature() == null ? other.getAccountNature() == null : this.getAccountNature().equals(other.getAccountNature()))
            && (this.getIban() == null ? other.getIban() == null : this.getIban().equals(other.getIban()))
            && (this.getAccountOpeningTime() == null ? other.getAccountOpeningTime() == null : this.getAccountOpeningTime().equals(other.getAccountOpeningTime()))
            && (this.getAuthorizedOfficer() == null ? other.getAuthorizedOfficer() == null : this.getAuthorizedOfficer().equals(other.getAuthorizedOfficer()))
            && (this.getFileList() == null ? other.getFileList() == null : this.getFileList().equals(other.getFileList()))
            && (this.getSwiftCode() == null ? other.getSwiftCode() == null : this.getSwiftCode().equals(other.getSwiftCode()))
            && (this.getMemo() == null ? other.getMemo() == null : this.getMemo().equals(other.getMemo()))
            && (this.getBankOperator() == null ? other.getBankOperator() == null : this.getBankOperator().equals(other.getBankOperator()))
            && (this.getBizType() == null ? other.getBizType() == null : this.getBizType().equals(other.getBizType()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAccountId() == null) ? 0 : getAccountId().hashCode());
        result = prime * result + ((getInstCode() == null) ? 0 : getInstCode().hashCode());
        result = prime * result + ((getEntity() == null) ? 0 : getEntity().hashCode());
        result = prime * result + ((getAccountNo() == null) ? 0 : getAccountNo().hashCode());
        result = prime * result + ((getAccountType() == null) ? 0 : getAccountType().hashCode());
        result = prime * result + ((getAccountName() == null) ? 0 : getAccountName().hashCode());
        result = prime * result + ((getUseType() == null) ? 0 : getUseType().hashCode());
        result = prime * result + ((getScenes() == null) ? 0 : getScenes().hashCode());
        result = prime * result + ((getIsSupportSubAccount() == null) ? 0 : getIsSupportSubAccount().hashCode());
        result = prime * result + ((getIsSupportPreApply() == null) ? 0 : getIsSupportPreApply().hashCode());
        result = prime * result + ((getSubAccountMode() == null) ? 0 : getSubAccountMode().hashCode());
        result = prime * result + ((getSubAccountRule() == null) ? 0 : getSubAccountRule().hashCode());
        result = prime * result + ((getSubAccountLimit() == null) ? 0 : getSubAccountLimit().hashCode());
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getCurrency() == null) ? 0 : getCurrency().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getPriority() == null) ? 0 : getPriority().hashCode());
        result = prime * result + ((getWeight() == null) ? 0 : getWeight().hashCode());
        result = prime * result + ((getIsSupportCustomName() == null) ? 0 : getIsSupportCustomName().hashCode());
        result = prime * result + ((getIsNeedActivation() == null) ? 0 : getIsNeedActivation().hashCode());
        result = prime * result + ((getActivationMode() == null) ? 0 : getActivationMode().hashCode());
        result = prime * result + ((getInstMid() == null) ? 0 : getInstMid().hashCode());
        result = prime * result + ((getBankName() == null) ? 0 : getBankName().hashCode());
        result = prime * result + ((getBankAddress() == null) ? 0 : getBankAddress().hashCode());
        result = prime * result + ((getAccountJson() == null) ? 0 : getAccountJson().hashCode());
        result = prime * result + ((getUtcCreate() == null) ? 0 : getUtcCreate().hashCode());
        result = prime * result + ((getUtcModified() == null) ? 0 : getUtcModified().hashCode());
        result = prime * result + ((getAccountAlias() == null) ? 0 : getAccountAlias().hashCode());
        result = prime * result + ((getAccountCategory() == null) ? 0 : getAccountCategory().hashCode());
        result = prime * result + ((getAccountNature() == null) ? 0 : getAccountNature().hashCode());
        result = prime * result + ((getIban() == null) ? 0 : getIban().hashCode());
        result = prime * result + ((getAccountOpeningTime() == null) ? 0 : getAccountOpeningTime().hashCode());
        result = prime * result + ((getAuthorizedOfficer() == null) ? 0 : getAuthorizedOfficer().hashCode());
        result = prime * result + ((getFileList() == null) ? 0 : getFileList().hashCode());
        result = prime * result + ((getSwiftCode() == null) ? 0 : getSwiftCode().hashCode());
        result = prime * result + ((getMemo() == null) ? 0 : getMemo().hashCode());
        result = prime * result + ((getBankOperator() == null) ? 0 : getBankOperator().hashCode());
        result = prime * result + ((getBizType() == null) ? 0 : getBizType().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", accountId=").append(accountId);
        sb.append(", instCode=").append(instCode);
        sb.append(", entity=").append(entity);
        sb.append(", accountNo=").append(accountNo);
        sb.append(", accountType=").append(accountType);
        sb.append(", accountName=").append(accountName);
        sb.append(", useType=").append(useType);
        sb.append(", scenes=").append(scenes);
        sb.append(", isSupportSubAccount=").append(isSupportSubAccount);
        sb.append(", isSupportPreApply=").append(isSupportPreApply);
        sb.append(", subAccountMode=").append(subAccountMode);
        sb.append(", subAccountRule=").append(subAccountRule);
        sb.append(", subAccountLimit=").append(subAccountLimit);
        sb.append(", country=").append(country);
        sb.append(", currency=").append(currency);
        sb.append(", status=").append(status);
        sb.append(", priority=").append(priority);
        sb.append(", weight=").append(weight);
        sb.append(", isSupportCustomName=").append(isSupportCustomName);
        sb.append(", isNeedActivation=").append(isNeedActivation);
        sb.append(", activationMode=").append(activationMode);
        sb.append(", instMid=").append(instMid);
        sb.append(", bankName=").append(bankName);
        sb.append(", bankAddress=").append(bankAddress);
        sb.append(", accountJson=").append(accountJson);
        sb.append(", utcCreate=").append(utcCreate);
        sb.append(", utcModified=").append(utcModified);
        sb.append(", accountAlias=").append(accountAlias);
        sb.append(", accountCategory=").append(accountCategory);
        sb.append(", accountNature=").append(accountNature);
        sb.append(", iban=").append(iban);
        sb.append(", accountOpeningTime=").append(accountOpeningTime);
        sb.append(", authorizedOfficer=").append(authorizedOfficer);
        sb.append(", fileList=").append(fileList);
        sb.append(", swiftCode=").append(swiftCode);
        sb.append(", memo=").append(memo);
        sb.append(", bankOperator=").append(bankOperator);
        sb.append(", bizType=").append(bizType);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}