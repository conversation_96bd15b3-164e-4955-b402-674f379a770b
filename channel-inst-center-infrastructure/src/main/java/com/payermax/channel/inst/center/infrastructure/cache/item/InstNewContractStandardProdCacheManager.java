package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractStandardProductMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractStandardProductPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-13 3:22 PM
 */
@Service
public class InstNewContractStandardProdCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractStandardProductMapper instContractStandardProductMapper;

    private static Map<String, List<InstContractStandardProductPO>> CACHE_MAP = new HashMap<>();


    public List<InstContractStandardProductPO> queryStandardProductByContract(String contractNo, String contractVersion) {

        return CACHE_MAP.get(contractNo + CACHE_CONNECT + contractVersion);
    }


    @Override
    public void doInit() {
        QueryWrapper<InstContractStandardProductPO> queryWrapper = new QueryWrapper<>();
        List<InstContractStandardProductPO> instContractStandardProductPOS = instContractStandardProductMapper.selectList(queryWrapper);
        instContractStandardProductPOS.forEach(item -> {
            List<InstContractStandardProductPO> standardProductPOList = CACHE_MAP
                    .computeIfAbsent(item.getContractNo() + CACHE_CONNECT + item.getContractVersion(), a -> new ArrayList<>());
            standardProductPOList.add(item);
        });
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstContractStandardProductPO>> temp = new HashMap<>();

        QueryWrapper<InstContractStandardProductPO> queryWrapper = new QueryWrapper<>();
        List<InstContractStandardProductPO> instContractStandardProductPOS = instContractStandardProductMapper.selectList(queryWrapper);
        instContractStandardProductPOS.forEach(item -> {
            List<InstContractStandardProductPO> standardProductPOList = temp
                    .computeIfAbsent(item.getContractNo() + CACHE_CONNECT + item.getContractVersion(), a -> new ArrayList<>());
            standardProductPOList.add(item);
        });
        CACHE_MAP = temp;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_STANDARD_PROD;
    }
}
