package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_inst_product")
public class InstProductEntity {
    @TableId(value = "product_code",type = IdType.INPUT)
    private String productCode;

    private Long instId;

    private String productName;

    private String channelType;

    private String paymentMethodType;

    private String paymentTool;

    private String customerType;

    private String isLimitMcc;

    private String isDivideMid;

    private Date utcCreate;

    private Date utcModified;

    private String mccDetail;

    private String extraInfo;
}
