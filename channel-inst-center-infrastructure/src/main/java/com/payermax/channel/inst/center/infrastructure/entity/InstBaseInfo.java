package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**

* <AUTHOR>
* @date 2023/11/23
* @DESC 
*/
@TableName(value = "tb_inst_base_info")
public class InstBaseInfo {
    /**
     * 机构标识
     */
    @TableId(value = "inst_id", type = IdType.INPUT)
    private Long instId;

    /**
     * 机构编码
     */
    @TableField(value = "inst_code")
    private String instCode;

    /**
     * 机构品牌标识
     */
    @TableField(value = "inst_brand_id")
    private Long instBrandId;

    /**
     * 机构名称
     */
    @TableField(value = "inst_name")
    private String instName;

    /**
     * 机构类型，可多个
     */
    @TableField(value = "inst_types")
    private String instTypes;

    /**
     * 主体所在地
     */
    @TableField(value = "entity_country")
    private String entityCountry;

    /**
     * 是否FATF成员国 Y:是 N:否
     */
    @TableField(value = "is_fatf_member")
    private String isFatfMember;

    /**
     * 渠道BD
     */
    @TableField(value = "bd_id")
    private String bdId;

    /**
     * 渠道AM
     */
    @TableField(value = "am_id")
    private String amId;

    /**
     * 渠道PD
     */
    @TableField(value = "pd_id")
    private String pdId;

    /**
     * 简介
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 状态 Y:启用 N:停用
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "utc_create")
    private Date utcCreate;

    /**
     * 更新时间
     */
    @TableField(value = "utc_modified")
    private Date utcModified;

    /**
     * 获取机构标识
     *
     * @return inst_id - 机构标识
     */
    public Long getInstId() {
        return instId;
    }

    /**
     * 设置机构标识
     *
     * @param instId 机构标识
     */
    public void setInstId(Long instId) {
        this.instId = instId;
    }

    /**
     * 获取机构编码
     *
     * @return inst_code - 机构编码
     */
    public String getInstCode() {
        return instCode;
    }

    /**
     * 设置机构编码
     *
     * @param instCode 机构编码
     */
    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    /**
     * 获取机构品牌标识
     *
     * @return inst_brand_id - 机构品牌标识
     */
    public Long getInstBrandId() {
        return instBrandId;
    }

    /**
     * 设置机构品牌标识
     *
     * @param instBrandId 机构品牌标识
     */
    public void setInstBrandId(Long instBrandId) {
        this.instBrandId = instBrandId;
    }

    /**
     * 获取机构名称
     *
     * @return inst_name - 机构名称
     */
    public String getInstName() {
        return instName;
    }

    /**
     * 设置机构名称
     *
     * @param instName 机构名称
     */
    public void setInstName(String instName) {
        this.instName = instName;
    }

    /**
     * 获取机构类型，可多个
     *
     * @return inst_types - 机构类型，可多个
     */
    public String getInstTypes() {
        return instTypes;
    }

    /**
     * 设置机构类型，可多个
     *
     * @param instTypes 机构类型，可多个
     */
    public void setInstTypes(String instTypes) {
        this.instTypes = instTypes;
    }

    /**
     * 获取主体所在地
     *
     * @return entity_country - 主体所在地
     */
    public String getEntityCountry() {
        return entityCountry;
    }

    /**
     * 设置主体所在地
     *
     * @param entityCountry 主体所在地
     */
    public void setEntityCountry(String entityCountry) {
        this.entityCountry = entityCountry;
    }

    /**
     * 获取是否FATF成员国 Y:是 N:否
     *
     * @return is_fatf_member - 是否FATF成员国 Y:是 N:否
     */
    public String getIsFatfMember() {
        return isFatfMember;
    }

    /**
     * 设置是否FATF成员国 Y:是 N:否
     *
     * @param isFatfMember 是否FATF成员国 Y:是 N:否
     */
    public void setIsFatfMember(String isFatfMember) {
        this.isFatfMember = isFatfMember;
    }

    /**
     * 获取渠道BD
     *
     * @return bd_id - 渠道BD
     */
    public String getBdId() {
        return bdId;
    }

    /**
     * 设置渠道BD
     *
     * @param bdId 渠道BD
     */
    public void setBdId(String bdId) {
        this.bdId = bdId;
    }

    /**
     * 获取渠道AM
     *
     * @return am_id - 渠道AM
     */
    public String getAmId() {
        return amId;
    }

    /**
     * 设置渠道AM
     *
     * @param amId 渠道AM
     */
    public void setAmId(String amId) {
        this.amId = amId;
    }

    /**
     * 获取渠道PD
     *
     * @return pd_id - 渠道PD
     */
    public String getPdId() {
        return pdId;
    }

    /**
     * 设置渠道PD
     *
     * @param pdId 渠道PD
     */
    public void setPdId(String pdId) {
        this.pdId = pdId;
    }

    /**
     * 获取简介
     *
     * @return remark - 简介
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置简介
     *
     * @param remark 简介
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取状态 Y:启用 N:停用
     *
     * @return status - 状态 Y:启用 N:停用
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态 Y:启用 N:停用
     *
     * @param status 状态 Y:启用 N:停用
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取创建时间
     *
     * @return utc_create - 创建时间
     */
    public Date getUtcCreate() {
        return utcCreate;
    }

    /**
     * 设置创建时间
     *
     * @param utcCreate 创建时间
     */
    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    /**
     * 获取更新时间
     *
     * @return utc_modified - 更新时间
     */
    public Date getUtcModified() {
        return utcModified;
    }

    /**
     * 设置更新时间
     *
     * @param utcModified 更新时间
     */
    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }
}