package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstRequirementScheduleEntity {
    private Long id;

    private Long requirementOrderId;

    private String pdId;

    private String pdName;

    private String isStart;

    private String priority;

    private String tbUrl;

    private Date planReleaseDate;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}