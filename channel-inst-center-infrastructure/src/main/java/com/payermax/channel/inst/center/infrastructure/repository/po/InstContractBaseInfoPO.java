package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> at 2023/6/9 5:23 PM
 **/
@TableName(InstContractBaseInfoPO.TABLE_NAME)
@Data
public class InstContractBaseInfoPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_base_info";

    @TableId
    private String contractNo;

    private String instCode;

    private String contractEntity;

    private String instProductType;

    private String status;

    /**
     * 是否新创建的，辅助数据库操作
     */
    @TableField(exist = false)
    private boolean newlyCreated;
}
