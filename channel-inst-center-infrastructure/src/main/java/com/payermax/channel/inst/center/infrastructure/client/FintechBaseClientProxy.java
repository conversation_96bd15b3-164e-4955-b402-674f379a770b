package com.payermax.channel.inst.center.infrastructure.client;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.common.enums.OmcEnum;
import com.payermax.channel.inst.center.common.exception.CustomException;
import com.payermax.channel.inst.center.common.utils.AwsS3Util;
import com.ushareit.fintech.base.constants.CommonConstants;
import com.ushareit.fintech.base.dto.DictDTO;
import com.ushareit.fintech.base.dto.DictItemDTO;
import com.ushareit.fintech.base.request.DictQueryRequest;
import com.ushareit.fintech.base.request.MailSendRequest;
import com.ushareit.fintech.base.response.BaseResponse;
import com.ushareit.fintech.base.service.DictFacade;
import com.ushareit.fintech.base.service.FileFacade;
import com.ushareit.fintech.base.service.MailFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 基础服务client代理
 *
 * <AUTHOR>
 * @date 2022/5/15 15:27
 */
@Component
@Slf4j
public class FintechBaseClientProxy {

    @NacosValue(value = "${aws.common.bucket.name:sg-pay-static-apse1/src/channel/omc/dev}", autoRefreshed = true)
    private String awsBucketName;

    @DubboReference(version = "1.0")
    private DictFacade dictFacade;

    @DubboReference(version = "1.0")
    private FileFacade fileFacade;

    @DubboReference(version = "1.0")
    private MailFacade mailFacade;

    @Resource
    private AwsS3Util awsS3Util;

    /**
     * 查询字典项
     *
     * @param dictCode
     * @return
     */
    public List<DictItemDTO> queryDict(String dictCode) {
        if (StringUtils.isBlank(dictCode)) {
            return new ArrayList<>();
        }
        try {
            DictQueryRequest queryRequest = new DictQueryRequest();
            queryRequest.setDictCode(dictCode);
            BaseResponse<DictDTO> dictResp = dictFacade.queryDict(queryRequest);
            if (dictResp == null) {
                return new ArrayList<>();
            }
            // 调用失败
            if (!CommonConstants.SUCCESS.equals(dictResp.getStatus())) {
                throw new CustomException(dictResp.getCode(), dictResp.getMsg());
            }
            if (dictResp.getData() == null) {
                return new ArrayList<>();
            }
            return dictResp.getData().getItemList();
        } catch (CustomException e) {
            throw e;
        } catch (Exception e) {
            log.error("queryDict error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "queryDict error");
        }
    }

    /**
     * 文件上传到S3
     *
     * @param fileName
     * @param fileContent
     * @return
     */
    public String uploadToAws(String bucketName, String fileName, byte[] fileContent) {
        if (StringUtils.isBlank(fileName) || fileContent == null) {
            return null;
        }
        try {
            // 如果入参bucketName不存在，则使用默认加上当前日期
            if (StringUtils.isBlank(bucketName)) {
                bucketName = String.format("%s/%s", awsBucketName, DateUtil.format(new Date(), DatePattern.PURE_DATE_FORMAT));
            }
            // 执行上传
            File file = new File(fileName);
            FileUtils.writeByteArrayToFile(file, fileContent);
            FileUtil.writeBytes(fileContent, file);

            String url = awsS3Util.uploadToS3(fileName, file, bucketName);
            FileUtil.del(file);
            return url;
        } catch (Exception e) {
            log.error("uploadToAws error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "uploadToAws error");
        }
    }

    /**
     * 发送邮件
     *
     * @param request
     * @return
     */
    public boolean sendMail(MailSendRequest request) {
        if (request == null) {
            return false;
        }
        try {
            List<MailSendRequest> mailList = new ArrayList<>();
            mailList.add(request);
            BaseResponse<Boolean> fileResp = mailFacade.sendMails(mailList);
            if (fileResp == null) {
                return false;
            }
            // 调用失败
            if (!CommonConstants.SUCCESS.equals(fileResp.getStatus())) {
                throw new CustomException(fileResp.getCode(), fileResp.getMsg());
            }
            if (fileResp.getData() == null) {
                return false;
            }
            return fileResp.getData().booleanValue();
        } catch (Exception e) {
            log.error("sendMails error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "sendMails error");
        }
    }

}
