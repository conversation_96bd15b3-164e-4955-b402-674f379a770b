package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class InstTransFeeEntity {
    private Long id;

    private String feeGroupId;

    private String feeType;

    private String mcc;

    private String calculateType;

    private BigDecimal minFee;

    private BigDecimal maxFee;

    private Date utcCreate;

    private Date utcModified;

    private String feeDetail;
}