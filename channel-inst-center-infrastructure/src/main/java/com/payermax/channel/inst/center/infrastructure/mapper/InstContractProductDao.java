package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstContractProductDao {
    int insert(InstContractProductEntity record);

    int insertBatch(List<InstContractProductEntity> records);

    List<InstContractProductEntity> selectAll(InstContractProductEntity entity);

    InstContractProductEntity selectByProductCodeAndCapabilityCode(@Param("productCode") String productCode, @Param("capabilityCode") String capabilityCode);

    List<InstContractProductEntity> selectByProductCodeAndCapabilityCodes(@Param("productCode") String productCode, @Param("capabilityCodes") List<String> capabilityCodes);

    int deleteByProductCode(String productCode);

    int deleteByProductCodeAndCapabilityCode(@Param("productCode") String productCode, @Param("capabilityCodes") List<String> capabilityCodes);

    int deleteByProductCodeAndVersion(@Param("productCode") String productCode, @Param("version") String version);

    int updateFeeGroupId(@Param("contractNo") String contractNo, @Param("productCode") String productCode, @Param("capabilityCodes") List<String> capabilityCodes, @Param("feeGroupId") String feeGroupId);

}