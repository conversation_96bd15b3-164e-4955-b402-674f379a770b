package com.payermax.channel.inst.center.infrastructure.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.AlertEnum;
import com.payermax.channel.inst.center.common.utils.TemplateUtil;
import com.payermax.channel.inst.center.infrastructure.alert.AlarmPlatformUtil;
import com.ushareit.fintech.dingtalk.DingTalkRobotClient;
import com.ushareit.fintech.dingtalk.config.FintechDingTalkProperties;
import com.ushareit.fintech.dingtalk.entity.MarkdownMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/13 12:15
 */
@Slf4j
@Component
public class DingAlertClient {

    @NacosValue(value = "${fintech.dingtalk.switch:true}", autoRefreshed = true)
    private boolean alertSwitch;

    private final DingTalkRobotClient dingTalkRobotClient;

    private final FintechDingTalkProperties fintechDingTalkProperties;


    public DingAlertClient(DingTalkRobotClient dingTalkRobotClient, FintechDingTalkProperties fintechDingTalkProperties) {
        this.dingTalkRobotClient = dingTalkRobotClient;
        this.fintechDingTalkProperties = fintechDingTalkProperties;
    }

    /**
     * 热加载告警控制需要通知的群组
     */
    @NacosValue(value = "#{${fintech.dingtalk.group.mapGroup}}", autoRefreshed = true)
    public void setDingGroupMap(Map<String, String> dingGroupMap) {
        fintechDingTalkProperties.reloadMapGroup(dingGroupMap);
    }

    /**
     * 环境
     */
    @Autowired
    private Environment env;

    /**
     * 发送钉钉告警消息
     *
     * @param groupName
     * @param markdownMessage
     */
    @Retryable(value = Exception.class, backoff = @Backoff(delay = 5 * 1000, multiplier = 2))
    public boolean sendMsg(String groupName, MarkdownMessage markdownMessage) {
        log.info("DingAlertUtil-sendMsg GroupName:{}, MarkdownMessage:{},  ", groupName, JSON.toJSONString(markdownMessage));
        dingTalkRobotClient.sendMarkdownMessage(groupName, markdownMessage);
        return true;
    }

    @Recover
    public boolean recover(Exception e, String groupName, MarkdownMessage markdownMessage) {
        //记录日志
        log.error("DingAlertUtil-recover GroupName:{}, MarkdownMessage:{},  Exception:{}", groupName, JSON.toJSONString(markdownMessage), e);
        return false;
    }

    public boolean sendMsg(String groupName, String title, String message) {
        if (!alertSwitch) {
            return true;
        }
        message = "## **".concat(title).concat("**【").concat(String.join("", Arrays.asList(env.getActiveProfiles()))).concat("】").concat("\n***  ").concat(message);
        log.info("DingAlertUtil-sendMsg groupName:{}, title:{}, message:{}", groupName, title, message);
        dingTalkRobotClient.sendMarkdownMessage(groupName, title, message);
        return true;
    }
    
    public void sendMsgForGroupSubAccount(String title, String template, Object params) {
        try {
            if (StringUtils.isNotEmpty(template)) {
                template = template.replace("#!", "$!");
                String message = TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, template, (Map<String, Object>) JSON.toJSON(params));
                sendMsg(CommonConstants.PUSH_SUB_ACCOUNT_HANDLE_GROUP, title, message);
                AlarmPlatformUtil.sendMessageByOperationType(AlertEnum.PUSH_SUB_ACCOUNT_HANDLE_GROUP, message, title);
            }
        } catch (Exception e) {
            log.error("sendMsgForGroupSubAccount alertMessage Exception!", e);
        }
    }

    public void sendMsgForExceptionGroupSubAccount(String title, String template, Object params) {
        try {
            if (StringUtils.isNotEmpty(template)) {
                template = template.replace("#!", "$!");
                String message = TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, template,(Map<String, Object>)JSON.toJSON(params));
                sendMsg(CommonConstants.PUSH_SUB_ACCOUNT_EXCEPTION_GROUP, title, message);
                AlarmPlatformUtil.sendMessageByOperationType(AlertEnum.PUSH_SUB_ACCOUNT_EXCEPTION_GROUP, message, title);
            }
        } catch (Exception e) {
            log.error("sendMsgForGroupSubAccount alertMessage Exception!", e);
        }
    }
}
