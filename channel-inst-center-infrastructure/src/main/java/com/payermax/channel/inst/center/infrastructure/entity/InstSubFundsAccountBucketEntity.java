package com.payermax.channel.inst.center.infrastructure.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 机构子级资金账号预申请账号表
 * <AUTHOR>
 * @TableName tb_inst_sub_funds_account_bucket
 */
@Data
public class InstSubFundsAccountBucketEntity implements Serializable {
    /**
     * 子级账号标识(机构标识_机构账号_国家_币种_17位数字)
     */
    private String subAccountId;

    /**
     * 机构帐号标识
     */
    private String accountId;

    /**
     * 子级机构账号创建总数标识
     */
    private Long bucketId;

    /**
     * 生成机构子级帐号号段标识
     */
    private String numberSegmentId;

    /**
     * 机构子级帐号号段生成号码
     */
    private String numberSegmentNo;

    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;

    /**
     * 子级账号号码
     */
    private String subAccountNo;

    /**
     * 子级账号名称
     */
    private String subAccountName;

    /**
     * 子级账号号码BBAN
     */
    private String bSubAccountNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 状态 状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer subAccountStatus;

    /**
     * 状态 Y:可分配，N：已分配
     */
    private String status;

    /**
     * 申请类型  PRE_APPLY：预申请，MERCHANT_APPLY：商户申请
     */
    private String applyType;

    /**
     * 账号相关拓展信息，json格式
     */
    private String accountJson;

    /**
     * 子账号申请请求参数
     */
    private String requestBody;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;

    private static final long serialVersionUID = 1L;
}