package com.payermax.channel.inst.center.infrastructure.client;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.common.enums.OmcEnum;
import com.payermax.channel.inst.center.common.exception.CustomException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.file.exchange.service.bo.TaskExecuteBO;
import com.payermax.file.exchange.service.facade.FileTaskFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/3 10:56
 **/
@Slf4j
@Component
public class FinFileExchangeClient {
    
    public static final String TASK_EXECUTE_MODE = "manual";
    
    public static final String SYSCODE = "inst-va";

    @DubboReference(version = "1.0")
    private FileTaskFacade fileTaskFacade;

    public String notifyFileExchange(String taskCode,Object requestBody,Object extrainfo, String requestId) {
        try {
            TaskExecuteBO taskExecuteBO = new TaskExecuteBO();
            taskExecuteBO.setSysCode(SYSCODE);
            taskExecuteBO.setTaskCode(taskCode);
            taskExecuteBO.setRequestId(requestId);
            taskExecuteBO.setTaskExecuteMode(TASK_EXECUTE_MODE);
            taskExecuteBO.setExtraInfo(JSON.toJSONString(extrainfo));
            taskExecuteBO.setHttpRequestBody(JSON.toJSONString(requestBody));
            Result<String> result = fileTaskFacade.executeTask(taskExecuteBO);
            if (Objects.nonNull(result)) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("notifyFileExchange error", e);
            throw new CustomException(OmcEnum.SysEnum.FAILED.getCode(), "notifyFileExchange error");
        }
        return null;
    }
    
}
