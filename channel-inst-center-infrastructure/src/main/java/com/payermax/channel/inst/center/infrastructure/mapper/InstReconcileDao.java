package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstReconcileDao {

    int insert(InstReconcileEntity record);

    List<InstReconcileEntity> selectAll(InstReconcileEntity queryEntity);

    int updateByPrimaryKey(InstReconcileEntity record);
}