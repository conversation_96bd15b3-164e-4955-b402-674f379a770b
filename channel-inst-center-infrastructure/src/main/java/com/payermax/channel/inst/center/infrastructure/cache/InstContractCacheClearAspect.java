package com.payermax.channel.inst.center.infrastructure.cache;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2024/5/14
 * @DESC
 */
@Aspect
@Component
@Slf4j
public class InstContractCacheClearAspect {

    // 匹配 InstContractFeeWorkflowServiceImpl 类中的所有方法
    @Pointcut("execution(* com.payermax.channel.inst.center.app.service.impl.InstContractFeeWorkflowServiceImpl.*(..)) && @annotation(com.payermax.channel.inst.center.infrastructure.cache.InstContractCacheClear)")
    public void instContractFeeWorkflowServicePointcut() {}

    // 匹配 com.payermax.channel.inst.center.controller 包下所有带InstContractCacheClear注解的方法
    @Pointcut("execution(* com.payermax.channel.inst.center.controller..*(..)) && @annotation(com.payermax.channel.inst.center.infrastructure.cache.InstContractCacheClear)")
    public void controllerMethodsPointcut() {}


    @AfterReturning(pointcut = "instContractFeeWorkflowServicePointcut() || controllerMethodsPointcut()", returning = "result")
    public void clearCache(JoinPoint joinPoint) {
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        Method method = sign.getMethod();
        InstContractCacheClear cacheClear = method.getAnnotation(InstContractCacheClear.class);
        // 清除缓存的逻辑
        log.info("开始更新缓存：{}", cacheClear.cacheName());
        CacheRegistry.getCacheManager(cacheClear.cacheName()).refresh();
        CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT).refresh();
        log.info("缓存更新完成");
    }
}
