package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @DESC
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="tb_inst_brand")
public class InstBrandPO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 品牌标识
     */
    @TableId(type = IdType.AUTO)
    private Long brandId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * bd负责人
     */
    private String bdId;

    /**
     * bd负责人名称
     */
    private String bdName;

    /**
     * 状态 y：启用，n：停用
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 更新时间
     */
    private LocalDateTime utcModified;

}

