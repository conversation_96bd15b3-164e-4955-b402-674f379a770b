package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstBrandMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBrandPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @DESC
 */
@Component
public class InstBrandRepository extends ServiceImpl<InstBrandMapper, InstBrandPO> {

    private final InstBrandMapper mapper;

    @Autowired
    public InstBrandRepository(InstBrandMapper mapper){
        this.mapper = mapper;
    }

    /**
     * 根据机构品牌 ID 查询
     * @param idList 机构品牌 ID 列表
     * @param onlyActive 是否只查询有效数据
     */
    public List<InstBrandPO> getByIdList(List<Long> idList, Boolean onlyActive){
        LambdaQueryWrapper<InstBrandPO> queryWrapper = Wrappers.<InstBrandPO>lambdaQuery()
                .in(InstBrandPO::getBrandId, idList)
                .eq(onlyActive, InstBrandPO::getStatus, "Y");
        return mapper.selectList(queryWrapper);
    }
}
