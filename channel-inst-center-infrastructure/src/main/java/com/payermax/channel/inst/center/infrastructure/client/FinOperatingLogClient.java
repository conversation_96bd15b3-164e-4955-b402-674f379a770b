package com.payermax.channel.inst.center.infrastructure.client;

import com.payermax.channel.inst.center.common.result.BaseResult;
import com.payermax.fin.operating.log.dto.WriteLogDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 15:20
 */
@FeignClient(name = "${fin-operating-log.name}", url = "${fin-operating-log.url}")
public interface FinOperatingLogClient {
    /**
     * upms记录操作日志
     *
     * @param writeLogDto
     * @return
     */
    @PostMapping(value = "/v1/log/write")
    BaseResult<Object> recordOperatingLog(WriteLogDto writeLogDto);
}
