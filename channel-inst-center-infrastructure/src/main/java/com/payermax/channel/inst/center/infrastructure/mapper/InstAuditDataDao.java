package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstAuditDataDao {

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    int insert(InstAuditDataEntity record);

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    List<InstAuditDataEntity> selectAll(InstAuditDataEntity record);

    /**
     * 更新记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstAuditDataEntity record);
}