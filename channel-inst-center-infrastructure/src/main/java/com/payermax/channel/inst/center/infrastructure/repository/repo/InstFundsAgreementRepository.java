package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsAgreementMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Component
public class InstFundsAgreementRepository extends ServiceImpl<InstFundsAgreementMapper, InstFundsAgreementPO> {

    /**
     * 根据唯一键获取资金协议
     */
    public Optional<InstFundsAgreementPO> getByUniqueKey(InstFundsAgreementPO fundsAgreement) {
        LambdaQueryWrapper<InstFundsAgreementPO> queryWrapper = Wrappers.<InstFundsAgreementPO>lambdaQuery()
                .eq(InstFundsAgreementPO::getBizAgreementNo, fundsAgreement.getBizAgreementNo())
                .eq(InstFundsAgreementPO::getType, fundsAgreement.getType())
                .eq(InstFundsAgreementPO::getClearingCcy, fundsAgreement.getClearingCcy())
                .eq(InstFundsAgreementPO::getClearingPattern, fundsAgreement.getClearingPattern())
                .eq(InstFundsAgreementPO::getStatus, fundsAgreement.getStatus())
                .eq(StringUtil.isNotBlank(fundsAgreement.getPaymentMethod()), InstFundsAgreementPO::getPaymentMethod, fundsAgreement.getPaymentMethod())
                .eq(StringUtil.isNotBlank(fundsAgreement.getTargetOrg()), InstFundsAgreementPO::getTargetOrg, fundsAgreement.getTargetOrg())
                .eq(StringUtil.isNotBlank(fundsAgreement.getCardOrg()), InstFundsAgreementPO::getCardOrg, fundsAgreement.getCardOrg())
                .eq(StringUtil.isNotBlank(fundsAgreement.getMid()), InstFundsAgreementPO::getMid, fundsAgreement.getMid())
                .isNull(StringUtil.isBlank(fundsAgreement.getPaymentMethod()), InstFundsAgreementPO::getPaymentMethod)
                .isNull(StringUtil.isBlank(fundsAgreement.getTargetOrg()), InstFundsAgreementPO::getTargetOrg)
                .isNull(StringUtil.isBlank(fundsAgreement.getCardOrg()), InstFundsAgreementPO::getCardOrg)
                .isNull(StringUtil.isBlank(fundsAgreement.getMid()), InstFundsAgreementPO::getMid)
                .last("limit 1");
        return Optional.ofNullable(getOne(queryWrapper));
    }

    /**
     * 唯一性校验，忽略自身
     */
    public Boolean uniqueCheckIgnoreSelf(InstFundsAgreementPO fundsAgreement){
        LambdaQueryWrapper<InstFundsAgreementPO> queryWrapper = Wrappers.<InstFundsAgreementPO>lambdaQuery()
                .eq(InstFundsAgreementPO::getBizAgreementNo, fundsAgreement.getBizAgreementNo())
                .eq(InstFundsAgreementPO::getType, fundsAgreement.getType())
                .eq(InstFundsAgreementPO::getClearingCcy, fundsAgreement.getClearingCcy())
                .eq(InstFundsAgreementPO::getClearingPattern, fundsAgreement.getClearingPattern())
                .eq(StringUtil.isNotBlank(fundsAgreement.getPaymentMethod()), InstFundsAgreementPO::getPaymentMethod, fundsAgreement.getPaymentMethod())
                .eq(StringUtil.isNotBlank(fundsAgreement.getTargetOrg()), InstFundsAgreementPO::getTargetOrg, fundsAgreement.getTargetOrg())
                .eq(StringUtil.isNotBlank(fundsAgreement.getCardOrg()), InstFundsAgreementPO::getCardOrg, fundsAgreement.getCardOrg())
                .eq(StringUtil.isNotBlank(fundsAgreement.getMid()), InstFundsAgreementPO::getMid, fundsAgreement.getMid())
                .isNull(StringUtil.isBlank(fundsAgreement.getPaymentMethod()), InstFundsAgreementPO::getPaymentMethod)
                .isNull(StringUtil.isBlank(fundsAgreement.getTargetOrg()), InstFundsAgreementPO::getTargetOrg)
                .isNull(StringUtil.isBlank(fundsAgreement.getCardOrg()), InstFundsAgreementPO::getCardOrg)
                .isNull(StringUtil.isBlank(fundsAgreement.getMid()), InstFundsAgreementPO::getMid)
                .eq(InstFundsAgreementPO::getStatus, fundsAgreement.getStatus())
                .ne(InstFundsAgreementPO::getFundsAgreementNo, fundsAgreement.getFundsAgreementNo());
        return count(queryWrapper) == 0;

    }

    /**
     * 根据传入实体查询资金协议及业务协议、清算规则
     * @param po 查询参数
     * @param initiator 协议发起方
     * @param counter 协议对手方
     * @return 资金协议列表
     */
    public List<InstFundsAgreementPO> queryByConditions(InstFundsAgreementPO po, String initiator, String counter, int pageNum, int pageSize) {
        String type = po.getType();
        String name = po.getName();
        String clearingCcy = po.getClearingCcy();
        String status = po.getStatus();
        Page<InstFundsAgreementPO> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectByConditions(page, type, name, clearingCcy, status, counter, initiator);
    }

    /**
     * 根据资金协议编号查询
     */
    public InstFundsAgreementPO selectById(String id) {
        return baseMapper.selectById(id);
    }


    /**
     * 保存时检查-空字符串->null
     */
    public Boolean saveWithCheck(InstFundsAgreementPO po) {
        UpdateWrapper<InstFundsAgreementPO> wrapper = new UpdateWrapper<>();
        wrapper.eq("funds_agreement_no", po.getFundsAgreementNo());
        if(StringUtil.isBlank(po.getPaymentMethod())){
            po.setPaymentMethod(null);
            wrapper.set("payment_method", null);
        }
        if(StringUtil.isBlank(po.getTargetOrg())){
            po.setTargetOrg(null);
            wrapper.set("target_org", null);
        }
        if(StringUtil.isBlank(po.getCardOrg())){
            po.setCardOrg(null);
            wrapper.set("card_org", null);
        }
        if(StringUtil.isBlank(po.getMid())){
            po.setMid(null);
            wrapper.set("mid", null);
        }
        return saveOrUpdate(po, wrapper);
    }
}
