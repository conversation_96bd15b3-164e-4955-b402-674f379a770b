package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 14:50
 */
@Mapper
public interface InstBusinessDictDao {

    int insert(InstBusinessDictEntity record);

    int insertBatch(List<InstBusinessDictEntity> records);

    int updateByPrimaryKey(InstBusinessDictEntity record);

    List<InstBusinessDictEntity> selectByBusinessType(String businessType);

    int delete(Long id);
}
