package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> at 2023/6/9 5:25 PM
 **/
@TableName(InstContractStandardProductPO.TABLE_NAME)
@Data
public class InstContractStandardProductPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_standard_product";

    @TableId(value = "inst_standard_product_no",type = IdType.INPUT)
    private String instStandardProductNo;

    private String instOriginProductNo;

    private String contractNo;

    private String contractVersion;

    private String paymentMethodType;

    private String targetOrg;

    private String cardOrg;

    @TableField(exist = false)
    private boolean newlyCreated;
}
