package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFinancialCalendarHolidayMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class InstFinancialCalendarHolidayRepository extends ServiceImpl<InstFinancialCalendarHolidayMapper, InstFinancialCalendarHolidayPO> {

    /**
     * 查询节假日列表-根据时间区间
     *
     * @param po        实体对象
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 节假日列表
     */
    public List<InstFinancialCalendarHolidayPO> queryByConditions(InstFinancialCalendarHolidayPO po, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<InstFinancialCalendarHolidayPO> queryWrapper = Wrappers.<InstFinancialCalendarHolidayPO>lambdaQuery()
                .eq(Objects.nonNull(po.getHolidayId()), InstFinancialCalendarHolidayPO::getHolidayId, po.getHolidayId())
                .eq(StringUtils.isNotBlank(po.getCalendarId()), InstFinancialCalendarHolidayPO::getCalendarId, po.getCalendarId())
                .eq(Objects.nonNull(po.getHolidayDate()), InstFinancialCalendarHolidayPO::getHolidayDate, po.getHolidayDate())
                .between(Objects.nonNull(startDate) && Objects.nonNull(endDate), InstFinancialCalendarHolidayPO::getHolidayDate, startDate, endDate)
                .eq(Objects.nonNull(po.getIsWorkday()), InstFinancialCalendarHolidayPO::getIsWorkday, po.getIsWorkday());
        return list(queryWrapper);
    }

    /**
     * 根据日历列表查询节假日
     *
     * @param calendarIds 日历 ID 列表
     * @return 节假日列表
     */
    public List<InstFinancialCalendarHolidayPO> queryByCalendarIds(List<String> calendarIds) {
        LambdaQueryWrapper<InstFinancialCalendarHolidayPO> queryWrapper = Wrappers.<InstFinancialCalendarHolidayPO>lambdaQuery()
                .in(InstFinancialCalendarHolidayPO::getCalendarId, calendarIds);
        return list(queryWrapper);
    }

    /**
     * 根据唯一ID查询日历
     */
    public List<InstFinancialCalendarHolidayPO> queryByHolidayIds(List<Long> holidayIds) {
        LambdaQueryWrapper<InstFinancialCalendarHolidayPO> queryWrapper = Wrappers.<InstFinancialCalendarHolidayPO>lambdaQuery()
                .in(InstFinancialCalendarHolidayPO::getHolidayId, holidayIds);
        return list(queryWrapper);
    }
}
