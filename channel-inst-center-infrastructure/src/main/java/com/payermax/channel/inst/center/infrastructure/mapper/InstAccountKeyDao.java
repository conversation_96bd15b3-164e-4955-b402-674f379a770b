package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstAccountKeyQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstAccountKeyDao {

    int insert(InstAccountKeyEntity record);

    List<InstAccountKeyQueryEntity> selectAll(@Param("requirementOrderId") Long requirementOrderId, @Param("instId") Long instId);

    int insertBatch(List<InstAccountKeyEntity> records);

    int updateByPrimaryKey(InstAccountKeyEntity record);

    int deleteById(Long id);
}