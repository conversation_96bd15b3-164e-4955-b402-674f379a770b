package com.payermax.channel.inst.center.infrastructure.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.payermax.channel.gateway.facade.api.ChannelGatewayApi;
import com.payermax.channel.gateway.facade.api.ChannelGatewayApiRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/03/06 21:48
 **/
@Component
@Slf4j
public class ChannelGatewayClientProxy {

    @DubboReference(version = "1.0", check = false, retries = 0, timeout = 10000)
    private ChannelGatewayApi channelGatewayApi;

    /**
     * 执行新渠道网关 dubbo方法
     */
    public Result<String> invoke(String request, String api, String version, String instBrand, String instMerchantCode) {
        // 参数校验
        if (StringUtils.isEmpty(request) || StringUtils.isEmpty(api) || StringUtils.isEmpty(version) 
                || StringUtils.isEmpty(instBrand) || StringUtils.isEmpty(instMerchantCode)) {
            log.error("ChannelGatewayClientProxy.invoke request parameter is empty.");
            return null;
        }
        ChannelGatewayApiRequest gatewayRequest = new ChannelGatewayApiRequest();
        gatewayRequest.setApi(api);
        gatewayRequest.setData(request);
        gatewayRequest.setVersion(version);
        gatewayRequest.setInstCode(instBrand);
        gatewayRequest.setInstMerchantCode(instMerchantCode);
        Result<String> result = channelGatewayApi.invoke(gatewayRequest);
        if (Objects.isNull(result)) {
            throw new BusinessException(ErrorCodeEnum.CHANNEL_NO_RESPONSE.getCode(), "下游响应报文为空");
        }
        return result;
    }
}
