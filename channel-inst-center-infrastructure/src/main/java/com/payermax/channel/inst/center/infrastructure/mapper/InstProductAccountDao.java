package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductAccountEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstProductAccountDao {

    int insert(InstProductAccountEntity record);

    List<InstProductAccountEntity> selectAll();

    int updateByPrimaryKey(InstProductAccountEntity record);
}