package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> at 2023/6/9 5:28 PM
 **/
@TableName(InstContractSettlementItemPO.TABLE_NAME)
@Data
@Accessors(chain = true)
public class InstContractSettlementItemPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_settlement_item";

    /**
     * 合同结算条款项id
     */
    @TableId(value = "inst_contract_settlement_item_no",type = IdType.INPUT)
    private String instContractSettlementItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;


    /**
     * 机构原始mid，及标准化后的内部渠道商户号
     */
    private String originMid;
    private String channelMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    private String mccLogic;
    private String originMcc;
    private String standardMcc;


    private String settleCurrency;

    private String settleFeeConfig;

    private String settleDateConfig;

    private String settlePaymentConfig;
}
