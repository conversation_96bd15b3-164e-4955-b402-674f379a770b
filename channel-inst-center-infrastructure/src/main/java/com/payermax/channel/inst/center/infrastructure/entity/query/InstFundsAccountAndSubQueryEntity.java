package com.payermax.channel.inst.center.infrastructure.entity.query;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 机构子级资金账号表
 *
 * <AUTHOR>
 */
@Data
public class InstFundsAccountAndSubQueryEntity extends InstFundsAccountEntity {
    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private List<String> accountIds;
    
    /**
     * 子级账号信息
     */
    private InstSubFundsAccountDO subAccountQuery;

    @Data
    public static class InstSubFundsAccountDO extends InstSubFundsAccountEntity {

        /**
         * 创建时间范围-开始时间
         */
        private Date beginCreateTime;

        /**
         * 创建时间范围-结束时间
         */
        private Date endCreateTime;


    }

}