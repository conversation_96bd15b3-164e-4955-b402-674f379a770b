package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

/**
 * @ClassName ChannelMethodEntity
 * @Description
 * <AUTHOR>
 * @Date 2023/4/7 15:27
 */
@Data
public class ChannelMethodEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道配置标识
     */
    private String channelMethodCode;

    /**
     * 标准产品code
     */
    private String productCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 支付工具
     */
    private String paymentTool;

    /**
     * 支付流程
     */
    private String paymentFlow;

    /**
     * 关单时间
     */
    private Integer expireTime;

    /**
     * 是否支持退款 1:支持，0:不支持
     */
    private Byte isSupportRefund;

    /**
     * 是否支持部分退款 1:支持，0:不支持
     */
    private Byte isPartialRefund;

    /**
     * 是否支持撤销 1:支持，0:不支持
     */
    private Byte isSupportCancel;

    /**
     * 扩展配置
     */
    private String configJson;

    /**
     * 状态 1：可用 0：不可用
     */
    private Byte status;

    /**
     * 是否需要报备商户 1：是 0：否 2：可报可不报
     */
    private Byte isNeedReportMerchant;

    /**
     * 渠道附加税唯一标识
     */
    private String additionalTaxCode;

    /**
     * 渠道成本，相对值
     */
    private Integer fundingCost;

    /**
     * 对客类型
     */
    private String customerType;

}
