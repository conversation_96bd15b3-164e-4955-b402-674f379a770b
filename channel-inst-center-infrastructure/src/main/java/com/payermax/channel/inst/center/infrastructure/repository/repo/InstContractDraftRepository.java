package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContractDraftMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class InstContractDraftRepository extends ServiceImpl<InstContractDraftMapper, InstContractDraft> {

    @Resource
    private InstContractDraftMapper instContractDraftMapper;

    /**
     * 批量插入草稿表
     */
    public boolean saveDraftBatch(List<InstContractDraft> dataList){
        return saveBatch(dataList);
    }

    /**
     * 根据草稿 ID 查询
     * @param id
     */
    public InstContractDraft queryById(String id){
        LambdaQueryWrapper<InstContractDraft> queryWrapper = Wrappers.<InstContractDraft>lambdaQuery()
                .eq(InstContractDraft::getDraftId,id)
                .last("limit 1");
        return getOne(queryWrapper);
    }


    /**
     * 根据机构列表查询所有草稿
     * @param instCodeList
     */
    public List<InstContractDraft> queryByInstCode(List<String> instCodeList){
        if(instCodeList.isEmpty()){
            return new ArrayList<>();
        }
        QueryWrapper<InstContractDraft> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("inst_code",instCodeList);
        queryWrapper.orderByDesc("utc_create");
        return instContractDraftMapper.selectList(queryWrapper);
    }

    /**
     * 根据草稿条件及录入时间查询草稿
     * @param draft 草稿条件
     * @param start 开始时间
     * @param end 结束时间
     */
    public List<InstContractDraft> queryByConditionsAndCreateTime(InstContractDraft draft, Date start, Date end){
        LambdaQueryWrapper<InstContractDraft> queryWrapper = Wrappers.lambdaQuery(draft)
                .between(InstContractDraft::getUtcCreate,start,end);
        return instContractDraftMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有草稿列表
     */
    public List<InstContractDraft> queryAll(){
        QueryWrapper<InstContractDraft> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("utc_create");
        return instContractDraftMapper.selectList(queryWrapper);
    }
}
