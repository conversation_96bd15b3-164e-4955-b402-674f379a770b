package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 资金协议
 */
@Data
@TableName(value ="tb_inst_funds_agreement")
@Accessors(chain = true)
public class InstFundsAgreementPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 4-2-1-资金协议单号
     */
    @TableId(value = "funds_agreement_no", type = IdType.INPUT)
    private String fundsAgreementNo;

    /**
     * 4-2-1-业务协议单号
     */
    private String bizAgreementNo;

    /**
     * 4-2-1-协议名称
     */
    private String name;

    /**
     * 4-2-1-协议类型
     */
    private String type;

    /**
     * 4-2-1-清算币种
     */
    private String clearingCcy;

    /**
     * 4-2-1-清算模式
     */
    private String clearingPattern;

    /**
     * 4-2-1-支付方式
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String paymentMethod;

    /**
     * 4-2-1-目标机构
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String targetOrg;

    /**
     * 4-2-1-卡组
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cardOrg;

    /**
     * 4-2-1-渠道MID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mid;

    /**
     * 4-2-1-状态-VALID/INVALID
     */
    private String status;

    /**
     * 4-2-1-备注
     */
    private String memo;

    /**
     * 0-0-创建时间
     */
    private Date utcCreate;

    /**
     * 0-0-更新时间
     */
    private Date utcModified;

    /**
     * 是否新创建的，辅助数据库操作
     */
    @TableField(exist = false)
    private boolean newlyCreated;

    @TableField(exist = false)
    private InstBizAgreementPO bizAgreement;

    @TableField(exist = false)
    private List<InstFundsSettleRulePO> settleRules;
}
