package com.payermax.channel.inst.center.infrastructure.repository.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> tracy
 * @version 2024/1/16 16:41
 */
@Data
public class AccumulationResponsePO implements Serializable {

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 机构产品类型
     */
    private String instProductType;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 交易国家
     */
    private String transactionCountry;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 扣款币种
     */
    private String feeDeductCurrency;

    /**
     * 费用配置
     */
    private String feeConfig;

    /**
     * MONTH/YEAR
     */
    private String accumulationCycle;

    /**
     * NUMBER/AMOUNT
     */
    private String accumulationType;

    /**
     * CURRENT/NEXT
     */
    private String accumulationMethod;

    /**
     * TOTAL/PERIODIC
     */
    private String accumulationRange;

    /**
     * PERIODIC/REALTIME
     */
    private String accumulationDeductTime;

    /**
     * Y
     */
    private String accumulationJoin;

    private String accumulationKey;

    /**
     * 标注Key的生成规则
     */
    private String accumulationMode;
}
