package com.payermax.channel.inst.center.infrastructure.entity.query;

import com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ApplyOrderQueryEntity
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 8:54
 */
@Data
public class ApplyOrderQueryEntity extends InstApplyOrderEntity {
    private InstBaseInfoEntity instBaseInfoEntity;
    private InstBrandEntity instBrandEntity;
    private InstDdEntity instDdEntity;
    private String channelType;
    private String paymentMethodTypes;
    private Date utcCreateFrom;
    private Date utcCreateTo;
    private Date expectReleaseTimeFrom;
    private Date expectReleaseTimeTo;
    private List<String> bdList;
    private List<String> amList;
    private String dbAndAmId;
    private List<String> applyNos;
    /**
     * 包含的阶段以及状态 {"NDA":["COMPLETE"]}
     */
    private Map<String, List<String>> includeStageStatusMap;
    /**
     * 不包含的阶段以及状态 {"NDA":["COMPLETE"]}
     */
    private Map<String, List<String>> excludeStageStatusMap;

}
