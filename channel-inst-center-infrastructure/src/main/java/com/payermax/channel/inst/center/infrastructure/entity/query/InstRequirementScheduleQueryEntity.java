package com.payermax.channel.inst.center.infrastructure.entity.query;

import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/4 11:43
 */
@Data
public class InstRequirementScheduleQueryEntity extends InstRequirementScheduleEntity {

    private String applyNo;

    private String shareitEntity;

    private Long instId;

    private String instCode;

    private String instName;

    private String bdId;

    private String amId;

    private String applyPriority;

    private String channelType;

    private String paymentMethodType;

    private String country;

    private Date expectReleaseTime;


    private List<String> bdList;
    private List<String> amList;
    private List<String> pdList;
    private Date expectReleaseTimeFrom;
    private Date expectReleaseTimeTo;
    private Date planReleaseDateFrom;
    private Date planReleaseDateTo;

}
