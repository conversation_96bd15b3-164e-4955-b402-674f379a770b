package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstBusinessDraftMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */
@Slf4j
@Component
public class InstBusinessDraftRepository extends ServiceImpl<InstBusinessDraftMapper, InstBusinessDraftPO> {

    /**
     * 根据条件查询
     */
    public List<InstBusinessDraftPO> queryByConditions(InstBusinessDraftPO po) {
        LambdaQueryWrapper<InstBusinessDraftPO> queryWrapper = Wrappers.<InstBusinessDraftPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(po.getDraftId()), InstBusinessDraftPO::getDraftId, po.getDraftId())
                .eq(StringUtils.isNotBlank(po.getBusinessType()), InstBusinessDraftPO::getBusinessType, po.getBusinessType())
                .eq(StringUtils.isNotBlank(po.getModuleName()), InstBusinessDraftPO::getModuleName, po.getModuleName())
                .eq(StringUtils.isNotBlank(po.getOperateType()), InstBusinessDraftPO::getOperateType, po.getOperateType())
                .eq(StringUtils.isNotBlank(po.getBusinessKey()), InstBusinessDraftPO::getBusinessKey, po.getBusinessKey())
                .eq(StringUtils.isNotBlank(po.getRelatedProcessId()), InstBusinessDraftPO::getRelatedProcessId, po.getRelatedProcessId())
                .eq(StringUtils.isNotBlank(po.getStatus()), InstBusinessDraftPO::getStatus, po.getStatus())
                .eq(StringUtils.isNotBlank(po.getOwner()), InstBusinessDraftPO::getOwner, po.getOwner());
        return list(queryWrapper);
    }



    /**
     * 根据状态更新草稿
     */
    public Boolean updateDraftStatus(String draftId, String status){
        LambdaUpdateWrapper<InstBusinessDraftPO> wrapper = Wrappers.<InstBusinessDraftPO>lambdaUpdate()
                .set(InstBusinessDraftPO::getStatus, status)
                .eq(InstBusinessDraftPO::getDraftId, draftId);
        return update(wrapper);
    }

    /**
     * 是否存在正在处理中的流程
     */
    public Boolean hasProcessing(InstBusinessDraftPO po){
        LambdaQueryWrapper<InstBusinessDraftPO> queryWrapper = Wrappers.<InstBusinessDraftPO>lambdaQuery()
                .eq(InstBusinessDraftPO::getBusinessKey, po.getBusinessKey())
                .eq(InstBusinessDraftPO::getBusinessType, po.getBusinessType())
                .eq(InstBusinessDraftPO::getModuleName, po.getModuleName())
                .eq(InstBusinessDraftPO::getOperateType, po.getOperateType())
                .eq(InstBusinessDraftPO::getStatus, InstProcessStatusEnum.PROCESSING.name());
        return CollectionUtils.isNotEmpty(list(queryWrapper));
    }

    /**
     * 根据业务场景获取未到终态的草稿
     */
    public List<InstBusinessDraftPO> getUnfinishedList(String businessKey, BusinessTypeEnum businessType, OperateModuleEnum moduleName, OperateTypeEnum operateType){
        AssertUtil.isTrue(StringUtils.isNotBlank(businessKey), ErrorCodeEnum.INST_CENTER_DRAFT_STATUS_ERROR.getCode(), "业务编号不能为空");

        List<String> finishedStatusList = Arrays.asList(InstProcessStatusEnum.PASS.name(), InstProcessStatusEnum.REJECT.name(), InstProcessStatusEnum.REVOKE.name(), InstProcessStatusEnum.STOP.name());

        LambdaQueryWrapper<InstBusinessDraftPO> queryWrapper = Wrappers.<InstBusinessDraftPO>lambdaQuery()
                .eq(InstBusinessDraftPO::getBusinessKey, businessKey)
                .eq(InstBusinessDraftPO::getBusinessType, businessType)
                .eq(InstBusinessDraftPO::getModuleName, moduleName)
                .eq(InstBusinessDraftPO::getOperateType, operateType)
                .notIn(InstBusinessDraftPO::getStatus, finishedStatusList);
        return list(queryWrapper);
    }

    /**
     * 获取正在处理中的流程
     */
    public List<InstBusinessDraftPO> getProcessingList(InstBusinessDraftPO po){
        LambdaQueryWrapper<InstBusinessDraftPO> queryWrapper = Wrappers.<InstBusinessDraftPO>lambdaQuery()
                .eq(InstBusinessDraftPO::getBusinessKey, po.getBusinessKey())
                .eq(InstBusinessDraftPO::getBusinessType, po.getBusinessType())
                .eq(InstBusinessDraftPO::getModuleName, po.getModuleName())
                .eq(InstBusinessDraftPO::getOperateType, po.getOperateType())
                .eq(InstBusinessDraftPO::getStatus, InstProcessStatusEnum.PROCESSING.name());
        return list(queryWrapper);
    }

    /**
     * 获取待重试的流程
     */
    public List<InstBusinessDraftPO> getWaitingRetryList(){
        LambdaQueryWrapper<InstBusinessDraftPO> queryWrapper = Wrappers.<InstBusinessDraftPO>lambdaQuery()
                .eq(InstBusinessDraftPO::getStatus, InstProcessStatusEnum.WAITING_RETRY.name());
        return list(queryWrapper);
    }

    /**
     * 处理重试或终止
     */
    public void handleRetryOrAbort(InstBusinessDraftPO draft, Boolean needRetry, int maxRetryCount){
        if (needRetry && maxRetryCount >= draft.getRetryCount()) {
            log.warn("执行失败，已进入重试状态: {}, {}, {}, {}, {}", draft.getDraftId(), draft.getBusinessKey(), draft.getBusinessType(), draft.getModuleName(),  draft.getOperateType());
            // 添加重试次数
            draft.setRetryCount(draft.getRetryCount() + 1);
            draft.setStatus(InstProcessStatusEnum.WAITING_RETRY.name());
            updateById(draft);
        } else if (!needRetry) {
            log.warn("无需重试, 已终止: {}, {}, {}, {}, {}", draft.getDraftId(), draft.getBusinessKey(), draft.getBusinessType(), draft.getModuleName(),  draft.getOperateType());
            updateDraftStatus(draft.getDraftId(), InstProcessStatusEnum.STOP.name());
        } else{
            log.error("超过最大重试次数, 已终止: {}, {}, {}, {}, {}", draft.getDraftId(), draft.getBusinessKey(), draft.getBusinessType(), draft.getModuleName(),  draft.getOperateType());
            updateDraftStatus(draft.getDraftId(), InstProcessStatusEnum.STOP.name());
        }
    }
}
