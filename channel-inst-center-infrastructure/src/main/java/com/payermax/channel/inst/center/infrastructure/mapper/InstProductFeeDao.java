package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstProductFeeDao {
    int insert(InstProductFeeEntity record);

    List<InstProductFeeEntity> selectByContractNoAndProductCodes(@Param("contractNo") String contratNo,@Param("productCodes") List<String> productCodes);

    int updateByPrimaryKey(InstProductFeeEntity record);

    int deleteById(Long id);

    int deleteByEntity(InstProductFeeEntity entity);

    List<InstProductFeeEntity> selectAll(InstProductFeeEntity entity);
}