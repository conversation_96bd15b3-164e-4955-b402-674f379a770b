package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstBankAccountDao {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstBankAccountEntity record);

    /**
     * 基于指定条件，查询机构银行账户
     * 备注：这里单写一个是因为其它类似selectAll传参不灵活，比如in条件场景下，希望传list，但是实体是String，因此新开一个
     *
     * @param queryCondition 查询条件
     * @return
     */
    List<InstBankAccountEntity> selectByCondition(InstBankAccountEntity queryCondition);

    /**
     * 更新记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstBankAccountEntity record);
}