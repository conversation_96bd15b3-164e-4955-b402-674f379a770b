package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstDdEntity {

    private Long id;

    private Long instId;

    private String applyNo;

    private String registerName;

    private String registerNo;

    private Date registerDate;

    private String registerAddress;

    private String companyScale;

    private Date validityDate;

    private String corporateName;

    private Date corporateBirthDate;

    private String corporateAddress;

    private String website;

    private String businessScope;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}