package com.payermax.channel.inst.center.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstDdDao extends BaseMapper<InstDdEntity> {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstDdEntity record);

    /**
     * 查询记录
     *
     * @param queryEntity
     * @return
     */
    List<InstDdEntity> selectAll(InstDdEntity queryEntity);

    /**
     * 更加记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstDdEntity record);

    /**
     * 根据机构id查询DD列表
     * @param
     * @return
     */
    List<InstDdQueryEntity> selectDdList(@Param("instIds") List<Long> instIds);
}
