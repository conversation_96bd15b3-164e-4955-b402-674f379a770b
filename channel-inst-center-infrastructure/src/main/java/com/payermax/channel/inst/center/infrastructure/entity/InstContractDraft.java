package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @DESC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tb_inst_contract_draft")
public class InstContractDraft implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 草稿id，唯一主键
     */
    @TableId
    @TableField(value = "draft_id")
    private String draftId;

    /**
     * 草稿数据
     */
    @TableField(value = "draft_data")
    private String draftData;

    /**
     * 业务类型
     */
    @TableField(value = "business_type")
    private String businessType;

    /**
     * 行为名称
     */
    @TableField(value = "action_type")
    private String actionType;

    /**
     * 所属编辑者
     */
    @TableField(value = "`owner`")
    private String owner;

    /**
     * 机构编码
     */
    @TableField(value = "inst_code")
    private String instCode;

    /**
     * 我方主体
     */
    @TableField(value = "contract_entity")
    private String contractEntity;

    /**
     * 机构产品名称
     */
    @TableField(value = "inst_product_name")
    private String instProductName;

    /**
     * 合同编号
     */
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 创建时间
     */
    @TableField(value = "effective_time")
    private Date effectiveTime;

    /**
     * 标准化人员
     */
    @TableField(value = "`operator`")
    private String operator;

    /**
     * 业务key
     */
    @TableField(value = "business_key")
    private String businessKey;

    /**
     * 扩展字段
     */
    @TableField(value = "extend_fields")
    private String extendFields;

    /**
     * 状态，新增init，新增待审核...
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 重试次数
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 创建时间
     */
    @TableField(value = "utc_create")
    private Date utcCreate;

    /**
     * 更新时间
     */
    @TableField(value = "utc_modified")
    private Date utcModified;
}