package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractOperateLogMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOperateLogPO;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/26
 * @DESC
 */
@Slf4j
@Setter
@Component
public class InstContractOperateLogRepository extends ServiceImpl<InstContractOperateLogMapper, InstContractOperateLogPO>{


    /**
     * 批量保存日志，失败不阻塞
     */
    public Boolean saveWithoutBlocking(List<InstContractOperateLogPO> po){
        try{
            return saveBatch(po);
        }catch (Exception e){
            log.error(ErrorCodeEnum.INST_CENTER_LOG_SAVE_ERROR.getCode(), ErrorCodeEnum.INST_CENTER_LOG_SAVE_ERROR.getMsg(), e);
            return Boolean.FALSE;
        }

    }
}
