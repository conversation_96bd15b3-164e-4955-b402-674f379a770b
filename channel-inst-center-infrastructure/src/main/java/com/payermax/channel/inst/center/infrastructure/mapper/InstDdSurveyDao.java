package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstDdSurveyDao {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstDdSurveyEntity record);

    /**
     * 查询记录
     *
     * @return
     */
    List<InstDdSurveyEntity> selectAll(InstDdSurveyEntity queryEntity);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstDdSurveyEntity record);
}