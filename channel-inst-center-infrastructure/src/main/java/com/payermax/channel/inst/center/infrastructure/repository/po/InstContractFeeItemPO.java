package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import com.payermax.channel.inst.center.common.enums.instcontract.CardTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.ClearNetworkEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CustomerTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.FeeBearerEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> at 2023/6/9 5:26 PM
 **/
@TableName(InstContractFeeItemPO.TABLE_NAME)
@Data
@Accessors(chain = true)
public class InstContractFeeItemPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_fee_item";

    /**
     * 合同费用条款项id
     */
    @TableId(value = "inst_contract_fee_item_no",type = IdType.INPUT)
    private String instContractFeeItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;


    /**
     * 机构原始mid，及标准化后的内部渠道商户号
     */
    private String originMid;
    private String channelMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    private String mccLogic;
    private String originMcc;
    private String standardMcc;
    /**
     * 清算网络
     * {@link ClearNetworkEnum}
     */
    private String clearNetwork;

    /**
     * 费用承担方
     * {@link FeeBearerEnum}
     */
    private String feeBearer;

    /**
     * 客户类型
     * {@link CustomerTypeEnum}
     */
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    private String cardType;

    /**
     * 资金源
     */
    private String fundingSource;
    private String subMerchantNo;

    /**
     * 税费配置
     */
    private String feeConfig;

    private String taxConfig;

    private String accumulatedFeeConfig;

    /**
     * 合约外汇加点
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal contractFxSpread;

    /**
     * 外汇加点
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal fxSpread;

    private String currencyExchangeTime;


    private Integer roundingScale;

    private String roundingMode;

    //-- 出款项目新增 --//
    /**
     * 发卡国家新增
     */
    private String cardIssueCountry;

    /**
     * 交易国家新增
     */
    private String transactionCountry;


    //----- 大阶梯相关
    private String accumulationCycle;

    private String accumulationType;

    private String accumulationMethod;

    private String accumulationRange;

    private String accumulationDeductTime;

    private String accumulationJoin;

    private String accumulationKey;

    private String accumulationMode;
    //----- 大阶梯相关

    // 业务唯一性约束
    private String feeBusinessKey;
}
