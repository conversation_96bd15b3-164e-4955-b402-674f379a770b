package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/1/25
 * @DESC
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName(InstContractOperateLogPO.TABLE_NAME)
public class InstContractOperateLogPO {

        public static final String TABLE_NAME = "tb_inst_new_contract_operate_log";

        /**
         * 记录单号
         */
        private String logNo;

        /**
         * 业务类型
         */
        private BusinessTypeEnum businessType;

        /**
         * 模块名称
         */
        private OperateModuleEnum moduleName;

        /**
         * 业务唯一键
         */
        private String businessUniqueKey;

        /**
         * 日志数据
         */
        private String logInfo;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 操作说明
         */
        private String operateContent;

        /**
         * 操作类型
         */
        private OperateTypeEnum operateType;

        /**
         * 操作结果
         */
        private LogOperateResTypeEnum operateRes;

        /**
         * 错误码
         */
        private String errorCode;

        /**
         * 错误详细信息
         */
        private String errorMsg;

        /**
         * 操作时间
         */
        private LocalDateTime operateTime;

}
