package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(InstBusinessDraftPO.TABLE_NAME)
public class InstBusinessDraftPO implements Serializable {

    public static final String TABLE_NAME = "tb_inst_business_draft";

    private static final long serialVersionUID = 1L;

    /**
     * 草稿id，唯一主键
     */
    @TableId(value = "draft_id", type = IdType.INPUT)
    private String draftId;

    /**
     * 草稿数据
     */
    private String draftData;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 行为名称
     */
    private String operateType;

    /**
     * 所属编辑者
     */
    private String owner;

    /**
     * 当前关联流程单号
     */
    private String relatedProcessId;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 扩展字段
     */
    private String extendFields;

    /**
     * 状态，新增init，新增待审核...
     */
    private String status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 更新时间
     */
    private LocalDateTime utcModified;
}
