package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.ChannelMethodEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/7 15:26
 */
@Mapper
public interface ChannelMethodDao {
    /**
     * 根据条件获取可用资金渠道能力配置
     *
     * @param entity
     * @return
     */
    List<ChannelMethodEntity> selectAll(ChannelMethodEntity entity);

}
