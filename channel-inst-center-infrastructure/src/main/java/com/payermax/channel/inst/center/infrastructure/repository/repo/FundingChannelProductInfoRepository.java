package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.FundingChannelProductInfoMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.FundingChannelProductInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class FundingChannelProductInfoRepository extends ServiceImpl<FundingChannelProductInfoMapper, FundingChannelProductInfo>{

    private final FundingChannelProductInfoMapper mapper;

    @Autowired
    public FundingChannelProductInfoRepository(FundingChannelProductInfoMapper mapper) {
        this.mapper = mapper;
    }

    /**
     * 根据支付类型查询支付方式
     */
    public List<FundingChannelProductInfo> queryByPaymentType(String paymentType){
        LambdaQueryWrapper<FundingChannelProductInfo> queryWrapper = Wrappers.<FundingChannelProductInfo>lambdaQuery()
                .eq(FundingChannelProductInfo::getPaymentType,paymentType)
                .eq(FundingChannelProductInfo::getStatus,1);
        return mapper.selectList(queryWrapper);

    }
}
