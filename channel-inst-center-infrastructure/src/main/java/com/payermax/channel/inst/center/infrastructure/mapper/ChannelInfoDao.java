package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.ChannelInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ChannelInfoDao {
    /**
     * 查询所有资金渠道配置
     *
     * @return List<ChannelInfoEntity>
     */
    List<ChannelInfoEntity> selectAll(ChannelInfoEntity entity);

    /**
     * 更新资金渠道配置状态
     *
     * @return Integer
     */
    Integer updateChannel(ChannelInfoEntity entity);

    /**
     * 列出所有的渠道编码（已去重）
     *
     * @return 渠道编码集合
     */
    List<String> listAllChannelCodeOnDistinct();

    /**
     * 列出所有的业务处理机构编码（已去重）
     *
     * @return 业务处理编码集合
     */
    List<String> listAllBizHandleOrgOnDistinct();

    /**
     * 根据渠道编码获取渠道信息
     *
     * @param channelCode 渠道编码
     * @return 渠道信息
     */
    ChannelInfoEntity getByChannelCode(@Param("channelCode") String channelCode);
}
