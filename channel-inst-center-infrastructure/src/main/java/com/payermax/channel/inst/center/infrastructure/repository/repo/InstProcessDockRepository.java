package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstProcessDockMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstProcessDockPO;
import com.payermax.common.lang.util.AssertUtil;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/28
 * @DESC
 */
@Component
public class InstProcessDockRepository extends ServiceImpl<InstProcessDockMapper, InstProcessDockPO> {


    /**
     * 根据 businessId 及状态判断流程是否已存在
     */
    public Boolean isProcessExistByBusinessIdAndStatus(String businessId, InstProcessStatusEnum status){
        LambdaQueryWrapper<InstProcessDockPO> wrapper = Wrappers.<InstProcessDockPO>lambdaQuery()
                .eq(InstProcessDockPO::getBusinessId, businessId)
                .eq(InstProcessDockPO::getProcessStatus, status.name());
        List<InstProcessDockPO> list = list(wrapper);
        return CollectionUtils.isEmpty(list);
    }

    /**
     * 根据 processId 及状态判断流程是否未执行完毕
     */
    public Boolean isInProcessingByProcessId(String processId, InstProcessStatusEnum status){
        LambdaQueryWrapper<InstProcessDockPO> wrapper = Wrappers.<InstProcessDockPO>lambdaQuery()
                .eq(InstProcessDockPO::getProcessId, processId)
                .eq(InstProcessDockPO::getProcessStatus, status.name())
                .last("LIMIT 1");
        InstProcessDockPO dockPo = getOne(wrapper);
        return Objects.nonNull(dockPo);
    }

    /**
     * 根据 流程 ID 查询修改信息
     * @param processId 流程 ID
     */
    public InstProcessDockPO queryByProcessId(String processId){
        LambdaQueryWrapper<InstProcessDockPO> wrapper = Wrappers.<InstProcessDockPO>lambdaQuery()
                .eq(InstProcessDockPO::getProcessId, processId)
                .eq(InstProcessDockPO::getProcessStatus, InstProcessStatusEnum.PROCESSING.name());
        List<InstProcessDockPO> dockList = list(wrapper);
        AssertUtil.isTrue(dockList.size() == 1, "流程信息不存在/存在多条");
        return dockList.get(0); //CHECKED
    }

    /**
     * 根据 ID 查询修改信息
     * @param businessId 业务主键
     */
    public InstProcessDockPO queryByBusinessId(String businessId){
        LambdaQueryWrapper<InstProcessDockPO> wrapper = Wrappers.<InstProcessDockPO>lambdaQuery()
                .eq(InstProcessDockPO::getBusinessId, businessId)
                .eq(InstProcessDockPO::getProcessStatus, InstProcessStatusEnum.PROCESSING.name());
        List<InstProcessDockPO> dockList = list(wrapper);
        AssertUtil.isTrue(dockList.size() == 1, "流程信息不存在/存在多条");
        return dockList.get(0); //CHECKED
    }


    /**
     * 状态修改
     * @param id id
     * @param statusEnum 状态
     */
    public Boolean setStatusById(Long id, InstProcessStatusEnum statusEnum){
        LambdaUpdateWrapper<InstProcessDockPO> wrapper = Wrappers.<InstProcessDockPO>lambdaUpdate()
                .eq(InstProcessDockPO::getId, id)
                .set(InstProcessDockPO::getProcessStatus, statusEnum);
        return update(wrapper);
    }
}
