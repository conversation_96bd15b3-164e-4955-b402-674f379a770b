package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstProductDao{
    int insert(InstProductEntity record);

    List<InstProductEntity> selectAll(InstProductEntity entity);

    List<InstProductEntity> selectByProductCodes(@Param("productCodes") List<String> productCodes);

    List<InstProductEntity> selectByInstIds(@Param("instIds") List<Long> instIds);

    int updateByPrimaryKey(InstProductEntity record);

    int deleteByProductCode(String productCode);
}