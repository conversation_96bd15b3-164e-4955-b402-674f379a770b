package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstProductCapabilityQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstProductCapabilityDao {
    int insert(InstProductCapabilityEntity record);

    int insertBatch(List<InstProductCapabilityEntity> records);

    List<InstProductCapabilityEntity> selectAll(InstProductCapabilityQueryEntity queryEntity);

    List<InstProductCapabilityEntity> selectByProductCodeAndVersion(@Param("productCode") String productCode,@Param("version") String version);

    List<InstProductCapabilityEntity> selectByProductCodeAndCapabilityCode(@Param("productCode") String productCode,@Param("capabilityCodes") List<String> capabilityCodes);

    List<InstProductCapabilityEntity> selectByCapabilityCodes(@Param("capabilityCodes") List<String> capabilityCodes);

    int updateByPrimaryKey(InstProductCapabilityEntity record);

    int deleteByProductCode(String productCode);

    int deleteByProductCodeAndVersion(@Param("productCode") String productCode,@Param("version") String version);

    int deleteByProductCodeAndCapabilityCode(@Param("productCode") String productCode,@Param("capabilityCodes") List<String> capabilityCodes);
}