package com.payermax.channel.inst.center.infrastructure.repository.mapper.ods;

import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OdsMapper {

    @Select("select * from ods_payment_channel_tb_inst_base_info")
    List<InstBaseInfoEntity> selectAllInstInfo();

    @Select("select * from ods_payment_channel_tb_inst_brand")
    List<InstBrandEntity> selectAllInstBrandInfo();
}
