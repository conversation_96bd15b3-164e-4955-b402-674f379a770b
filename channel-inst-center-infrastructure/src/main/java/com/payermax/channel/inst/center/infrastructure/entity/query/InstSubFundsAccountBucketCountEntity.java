package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.io.Serializable;

/**
 * 机构资金账户生成子级资金账户拓展请求参数表
 * <AUTHOR>
 * @TableName tb_inst_funds_account_bucket
 */
@Data
public class InstSubFundsAccountBucketCountEntity implements Serializable {

    /**
     * 主键
     */
    private Long bucketId;

    /**
     * 可使用号码总数
     */
    private Integer count;

    private static final long serialVersionUID = 1278348128734L;
}