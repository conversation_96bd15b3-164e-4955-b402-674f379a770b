package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstRequirementOrderEntity {
    private Long id;

    private String applyNo;

    private Long instId;

    private String apiDocId;

    private String apiDocUrl;

    private String platformUrl;

    private String prodPlatformUrl;

    private String payDocId;

    private String payDocUrl;

    private String refundDocId;

    private String refundDocUrl;

    private String disputeDocId;

    private String disputeDocUrl;

    private String billDocId;

    private String billDocUrl;

    private String problemHandleDocId;

    private String problemHandleDocUrl;

    private String releaseRequireDocId;

    private String releaseRequireDocUrl;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}