package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstReportMerchantEntity {
    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String isNeedReportMerchant;

    private String reportRequire;

    private String reportType;

    private String reportProcessTime;

    private String reportTemplate;

    private Date utcCreate;

    private Date utcModified;
}