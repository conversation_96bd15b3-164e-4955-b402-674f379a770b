package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/28
 * @DESC
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tb_inst_process_dock")
public class InstProcessDockPO {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    private String businessType;

    private String businessId;

    private String processId;

    private String processStatus;

    private String formContent;

    private String originalFormContent;

    private Date utcCreate;

    private String createUser;

    private Date utcModified;

    private String updateUser;
}
