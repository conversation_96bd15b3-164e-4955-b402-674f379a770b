package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContractProductDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 3:07 PM
 */
@Service
@Slf4j
public class InstContractProductCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractProductDao instContractProductDao;

    private static Map<String, List<InstContractProductEntity>> CACHE_MAP = new ConcurrentHashMap<>();
    private static Map<String, List<InstContractProductEntity>> CONTRACT_PRODUCT_CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<InstContractProductEntity> productEntities = instContractProductDao.selectAll(new InstContractProductEntity());
        productEntities.forEach(item -> {
            List<InstContractProductEntity> entities = CACHE_MAP.computeIfAbsent(item.getContractNo(), a -> new ArrayList<>());
            entities.add(item);
        });
        CONTRACT_PRODUCT_CACHE_MAP = productEntities.stream().collect(Collectors.groupingBy(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode())));
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstContractProductEntity>> temp = new ConcurrentHashMap<>();
        List<InstContractProductEntity> productEntities = instContractProductDao.selectAll(new InstContractProductEntity());
        productEntities.forEach(item -> {
            List<InstContractProductEntity> entities = temp.computeIfAbsent(item.getContractNo(), a -> new ArrayList<>());
            entities.add(item);
        });
        Map<String, List<InstContractProductEntity>> map = productEntities.stream().collect(Collectors.groupingBy(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode())));
        CACHE_MAP = temp;
        CONTRACT_PRODUCT_CACHE_MAP = map;
    }

    public List<InstContractProductEntity> getContractProductByContractNos(List<String> contractNos) {
        List<InstContractProductEntity> response = new ArrayList<>();
        contractNos.forEach(item -> {
            List<InstContractProductEntity> lists = CACHE_MAP.get(item);
            if (CollectionUtils.isNotEmpty(lists)) {
                response.addAll(lists);
            }
        });
        return response;
    }

    public List<InstContractProductEntity> getContractProductByContractNosAndProductCodes(List<String> contractNos,List<String> productCodes) {
        List<InstContractProductEntity> response = new ArrayList<>();
        contractNos.forEach(c -> productCodes.forEach(p -> {
            List<InstContractProductEntity> lists = CONTRACT_PRODUCT_CACHE_MAP.get(Joiner.on("_").join(c,p));
            if (CollectionUtils.isNotEmpty(lists)) {
                response.addAll(lists);
            }
        }));
        return response;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_CONTRACT_PRODUCT;
    }
}
