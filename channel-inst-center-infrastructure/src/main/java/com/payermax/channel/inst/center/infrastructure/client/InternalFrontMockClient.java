package com.payermax.channel.inst.center.infrastructure.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * front调用client
 *
 * <AUTHOR>
 * @date 2021/8/3 10:25
 */
@FeignClient(name = "InternalFrontMockClient", url = "${mock.domain:}")
public interface InternalFrontMockClient {

    @PostMapping(value = "/{urlPath}", consumes = {"application/json"})
    String internalExecute(@RequestHeader(value = "sign", required = false) String sign,
                           @RequestHeader(value = "payermaxVersion", required = false) String payermaxVersion,
                           @RequestParam("urlPath") String urlPath,
                           @RequestBody String body);
}
