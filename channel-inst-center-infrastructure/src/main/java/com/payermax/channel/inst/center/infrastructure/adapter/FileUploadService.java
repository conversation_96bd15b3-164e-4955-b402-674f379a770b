package com.payermax.channel.inst.center.infrastructure.adapter;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.infra.ionia.fs.FsProviderSelector;
import com.payermax.infra.ionia.fs.S3Util;
import com.payermax.infra.ionia.fs.dto.UploadRequest;
import com.payermax.infra.ionia.fs.dto.UploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR> 2022/11/1  10:18 PM
 */
@Component
@Slf4j
public class FileUploadService {

    @NacosValue(value = "${inst.bucketName}", autoRefreshed = true)
    private String bucketName;

    @Autowired
    private FsProviderSelector fsProviderSelector;

    @Deprecated
    public UploadResponse upLoadToS3(MultipartFile file) {
        try {
            //统一进行文件名称转换，防止重复，目前使用uuid逻辑
            return upLoadToS3(file, UUID.randomUUID() + SymbolConstants.SYMBOL_UNDERLINE + file.getOriginalFilename());
        } catch (Exception e) {
            throw new BizException(ErrorCodeEnum.FILE_UPLOAD_TO_S3_ERROR);
        }
    }

    public UploadResponse upLoadToS3(MultipartFile file, String fileName) {
        try {
            return upLoadToS3(file.getBytes(), fileName);
        } catch (Exception e) {
            throw new BizException(ErrorCodeEnum.FILE_UPLOAD_TO_S3_ERROR);
        }
    }

    public UploadResponse upLoadToS3(byte[] fileContent, String fileName) {
        try {
            UploadRequest request = new UploadRequest();
            // 注意，为规范桶的命名使用，新版S3已不支持桶中带目录路径，需要通过如下代码进行标准转换
            request.setBucketName(S3Util.extractBucketName(bucketName));
            request.setFileName(S3Util.extractFullFileName(bucketName, fileName));
            request.setFileContent(fileContent);
            // 上传文件
            UploadResponse uploadResponse = fsProviderSelector.get().upload(request);
            if (Objects.isNull(uploadResponse)) {
                log.error("upload file to s3 fail");
                throw new BizException(ErrorCodeEnum.FILE_UPLOAD_TO_S3_ERROR);
            }
            log.info("uploadResponse url:{}", uploadResponse.getUrl());
            return uploadResponse;
        } catch (Exception e) {
            log.error("upload file to s3 fail e:", e);
            throw new BizException(ErrorCodeEnum.FILE_UPLOAD_TO_S3_ERROR);
        }
    }
}
