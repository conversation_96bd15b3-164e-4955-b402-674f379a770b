package com.payermax.channel.inst.center.infrastructure.repository.repo;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.ChannelInfoCacheManager;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoMapper;
import com.payermax.common.lang.util.AssertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**

* <AUTHOR>
* @date 2023/11/23
* @DESC
*/
@Component
public class InstBaseInfoRepository extends ServiceImpl<InstBaseInfoMapper, InstBaseInfo> {

    private final InstBaseInfoMapper mapper;
    private final ChannelInfoCacheManager channelInfoCacheManager;
    private final InstBrandRepository instBrandRepository;

    @Autowired
    public InstBaseInfoRepository(InstBaseInfoMapper mapper, ChannelInfoCacheManager channelInfoCacheManager, InstBrandRepository instBrandRepository){
        this.mapper = mapper;
        this.channelInfoCacheManager = channelInfoCacheManager;
        this.instBrandRepository = instBrandRepository;
    }

    /**
     * 根据 BD 查询负责的机构名称
     * @return List<InstBaseInfo>
     */
    public List<String> queryInstByBd(String shareId){
        LambdaQueryWrapper<InstBaseInfo> queryWrapper = Wrappers.<InstBaseInfo>lambdaQuery()
                .eq(InstBaseInfo::getBdId,shareId).select(InstBaseInfo::getInstCode);
        return mapper.selectList(queryWrapper).stream().map(InstBaseInfo::getInstCode).collect(Collectors.toList());
    }

    /**
     * 根据机构编码列表查询机构
     * @param instCodeList 机构编码列表
     * @param onlyActive 是否只查询有效数据
     */
    public List<InstBaseInfo> queryByInstCodeList(List<String> instCodeList, Boolean  onlyActive){
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(instCodeList), "ERROR","机构编码列表不能为空");
        LambdaQueryWrapper<InstBaseInfo> queryWrapper = Wrappers.<InstBaseInfo>lambdaQuery()
                .in(InstBaseInfo::getInstCode, instCodeList)
                .eq(onlyActive, InstBaseInfo::getStatus, "Y");
        return mapper.selectList(queryWrapper);
    }
}
