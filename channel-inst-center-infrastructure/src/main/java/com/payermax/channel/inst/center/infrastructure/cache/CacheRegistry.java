package com.payermax.channel.inst.center.infrastructure.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-20 11:47 PM
 */
public class CacheRegistry {

    private static final Map<CacheEnum, AbstractLocalCacheManager> MAP = new ConcurrentHashMap<>();

    public static void registry(CacheEnum cacheEnum, AbstractLocalCacheManager cacheManager) {
        MAP.put(cacheEnum, cacheManager);
    }

    public static AbstractLocalCacheManager getCacheManager(CacheEnum cacheEnum) {
        return MAP.get(cacheEnum);
    }
}
