package com.payermax.channel.inst.center.infrastructure.entity;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 机构子级资金账号表
 *
 * <AUTHOR>
 * @TableName tb_inst_sub_funds_account
 */
@Data
public class InstSubFundsAccountEntity implements Serializable,Cloneable {

    private static final long serialVersionUID = 134136988823L;
    /**
     * 子级账号标识
     */
    private String subAccountId;

    /**
     * 业务申请唯一键
     */
    private String businessKey;

    /**
     * 机构帐号标识
     */
    private String accountId;

    /**
     * 子级机构账号创建总数标识
     */
    private Long bucketId;

    /**
     * 机构帐号号段标识
     */
    private String numberSegmentId;

    /**
     * 号段生成的账号号码
     */
    private String numberSegmentNo;

    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;

    /**
     * 子级账号号码
     */
    private String subAccountNo;

    /**
     * 子级账号名称
     */
    private String subAccountName;

    /**
     * 子级账号号码BBAN
     */
    private String bSubAccountNo;

    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;

    /**
     * 申请子级账号的子商户号
     */
    private String subMerchantNo;
    
    /**
     * 状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer status;

    /**
     * 申请子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     */
    private String scenes;

    /**
     * 账号相关拓展信息，json格式
     */
    private String accountJson;

    /**
     * 账号相关拓展信息
     */
    private AccountJsonBo accountJsonBo;

    /**
     * 子账号申请请求参数
     */
    private String requestBody;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;
    
    public AccountJsonBo getAccountJsonBo() {
        if (Objects.nonNull(accountJsonBo)) {
            return accountJsonBo;
        }
        if (StringUtils.isNotBlank(accountJson)) {
            accountJsonBo = JSON.parseObject(accountJson, AccountJsonBo.class);
        }
        if (Objects.isNull(accountJsonBo)) {
            accountJsonBo = new AccountJsonBo();
        }
        return accountJsonBo;
    }

    @Deprecated
    public void setAccountJson(String accountJson) {
        this.accountJson = accountJson;
    }
    
    public String getAccountJson() {
        if (accountJsonBo == null) {
            return accountJson;
        } else {
            return JSON.toJSONString(accountJsonBo);
        }
    }

    @Data
    public static class AccountJsonBo {

        /**
         * 渠道VA唯一编号
         */
        private String channelUniqueNumber;

        /**
         * 拒绝编码
         */
        private String rejCode;

        /**
         * 拒绝描述
         */
        private String rejMessage;

    }

}