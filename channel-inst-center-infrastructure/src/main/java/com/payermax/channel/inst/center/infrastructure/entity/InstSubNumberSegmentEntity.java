package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机构账号子级号段信息表
 *
 * @TableName tb_inst_sub_number_segment
 */
@Data
public class InstSubNumberSegmentEntity {

    /**
     * 号段标识
     */
    private String id;

    /**
     * 号段名称
     */
    private String name;

    /**
     * 号段号码开始（包含）
     */
    private String numberStart;

    /**
     * 号段号码结束（包含）
     */
    private String numberEnd;

    /**
     * 号段最近使用的号码
     */
    private String maxUsed;

    /**
     * 机构帐号号段用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;

    /**
     * 状态：N：不可用，Y：可用
     */
    private String status;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;


}