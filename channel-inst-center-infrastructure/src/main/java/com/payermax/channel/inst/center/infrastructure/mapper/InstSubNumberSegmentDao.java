package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubNumberSegmentQueryEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_inst_sub_number_segment(机构账号子级号段信息表)】的数据库操作Mapper
 * @createDate 2022-10-08 15:52:15
 * @Entity com.payermax.channel.inst.center.infrastructure.entity.TbInstSubNumberSegment
 */
public interface InstSubNumberSegmentDao {

    int updateByPrimaryKeySelective(@Param("record") InstSubNumberSegmentEntity record, @Param("originalMaxUsed") String originalMaxUsed);

    List<InstSubNumberSegmentEntity> selectByRecord(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity);

    int selectCountByRecord(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity);
}
