package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.util.Date;

/**
 * 机构资金账号扩展属性表
 *
 * <AUTHOR>
 */
@Data
public class InstFundsAccountExtQueryEntity {
    /**
     * 主键
     */
    private Long id;
    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;
    /**
     * 清算体系:ACH、CBFT,多个使用,分割
     */
    private String clearSystem;
    /**
     * 扩展属性key：SwiftCode,IBAN,IFSC,BankCode,BranchNo
     */
    private String extKey;
    /**
     * 扩展属性val
     */
    private String extValue;
    /**
     * 创建时间
     */
    private Date utcCreate;
    /**
     * 更新时间
     */
    private Date utcModified;

}