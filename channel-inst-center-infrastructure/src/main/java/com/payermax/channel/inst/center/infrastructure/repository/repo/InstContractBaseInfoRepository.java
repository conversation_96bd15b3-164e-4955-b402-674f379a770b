package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractBaseInfoMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.common.lang.model.dto.response.RowResponse;
import com.payermax.common.lang.util.AssertUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 3:37 PM
 **/
@Component
public class InstContractBaseInfoRepository extends ServiceImpl<InstContractBaseInfoMapper, InstContractBaseInfoPO> {

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private InstContractVersionInfoRepository instContractVersionInfoRepository;

    /**
     * 根据业务唯一键获取唯一生效的合同baseInfo
     * 业务唯一键包括：机构code  &&  签约主体 && 机构产品类型，且状态为生效
     */
    public InstContractBaseInfoPO queryActiveBaseInfoByBusinessKey(InstContractBaseInfoPO baseInfoPO) {
        AssertUtil.notNull(baseInfoPO, "", "queryActiveBaseInfoByBusinessKey, baseInfoPO must not null");
        AssertUtil.notEmpty(baseInfoPO.getInstCode(), "", "queryActiveBaseInfoByBusinessKey, baseInfoPO instCode must not empty");
        AssertUtil.notEmpty(baseInfoPO.getContractEntity(), "", "queryActiveBaseInfoByBusinessKey, baseInfoPO contractEntity must not empty");
        AssertUtil.notEmpty(baseInfoPO.getInstProductType(), "", "queryActiveBaseInfoByBusinessKey, baseInfoPO instProductType must not empty");
        AssertUtil.isTrue("ACTIVATED".equals(baseInfoPO.getStatus()), "", "queryActiveBaseInfoByBusinessKey, baseInfoPO status must ACTIVATED");

        return queryActiveBaseInfo(baseInfoPO.getInstCode(), baseInfoPO.getContractEntity(),
                baseInfoPO.getInstProductType(), baseInfoPO.getStatus());
    }

    public InstContractBaseInfoPO queryActiveBaseInfo(String instCode, String contractEntity, String businessType, String status) {
        LambdaQueryWrapper<InstContractBaseInfoPO> queryWrapper = Wrappers.<InstContractBaseInfoPO>lambdaQuery()
                .eq(InstContractBaseInfoPO::getInstCode, instCode)
                .eq(InstContractBaseInfoPO::getContractEntity, contractEntity)
                .eq(InstContractBaseInfoPO::getInstProductType, businessType)
                .eq(InstContractBaseInfoPO::getStatus, status);

        return getOne(queryWrapper);
    }

    public void signNewInstContract(InstContractBaseInfoPO basePO, InstContractVersionInfoPO versionPO) {
        transactionTemplate.execute(res -> {
            // 持久化合同基本信息
            if (basePO.isNewlyCreated()) {
                save(basePO);
            }

            // 持久化合同新版本
            instContractVersionInfoRepository.signNewContractVersion(versionPO);

            return null;
        });
    }

    /**
     *  查询机构合同，允许参数为空
     */
    public List<InstContractBaseInfoPO> queryActiveBaseInfoNullable(String instCode, String contractEntity, String businessType, String status) {
        LambdaQueryWrapper<InstContractBaseInfoPO> queryWrapper = Wrappers.<InstContractBaseInfoPO>lambdaQuery()
                .eq(StringUtils.isNotBlank(instCode),InstContractBaseInfoPO::getInstCode, instCode)
                .eq(StringUtils.isNotBlank(contractEntity),InstContractBaseInfoPO::getContractEntity, contractEntity)
                .eq(StringUtils.isNotBlank(businessType),InstContractBaseInfoPO::getInstProductType, businessType)
                .eq(StringUtils.isNotBlank(status),InstContractBaseInfoPO::getStatus, status);
        return list(queryWrapper);
    }

    /**
     *  查询机构合同,分页
     */
    public RowResponse<InstContractBaseInfoPO> queryActiveBaseInfoPageable(InstContractBaseInfoPO po, Integer pageNum, Integer pageSize) {
        if(ObjectUtils.isEmpty(po)){
            return new RowResponse<>(Collections.emptyList(),0);
        }
        LambdaQueryChainWrapper<InstContractBaseInfoPO> wrapper = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(StringUtils.isNotBlank(po.getInstCode()), InstContractBaseInfoPO::getInstCode, po.getInstCode())
                .eq(StringUtils.isNotBlank(po.getContractEntity()), InstContractBaseInfoPO::getContractEntity, po.getContractEntity())
                .eq(StringUtils.isNotBlank(po.getInstProductType()), InstContractBaseInfoPO::getInstProductType, po.getInstProductType())
                .eq(StringUtils.isNotBlank(po.getStatus()), InstContractBaseInfoPO::getStatus, po.getStatus());
        wrapper.orderByAsc(InstContractBaseInfoPO::getInstCode);
        Page<InstContractBaseInfoPO> poPage = wrapper.page(new Page<>(pageNum, pageSize));
        return new RowResponse<>(poPage.getRecords(), poPage.getTotal());
    }


    /**
     * 根据合同编号查询单个合同
     */
    public InstContractBaseInfoPO queryOneByNo(String contractNo){
        LambdaQueryWrapper<InstContractBaseInfoPO> queryWrapper = Wrappers.<InstContractBaseInfoPO>lambdaQuery().eq(InstContractBaseInfoPO::getContractNo,contractNo);
        return getOne(queryWrapper);
    }

    /**
     * 查询所有生效的签约合同
     */
    public List<InstContractBaseInfoPO> listAllEffectiveContract(){
        LambdaQueryWrapper<InstContractBaseInfoPO> queryWrapper = Wrappers.<InstContractBaseInfoPO>lambdaQuery().eq(InstContractBaseInfoPO::getStatus,"ACTIVATED");
        return list(queryWrapper);
    }
}
