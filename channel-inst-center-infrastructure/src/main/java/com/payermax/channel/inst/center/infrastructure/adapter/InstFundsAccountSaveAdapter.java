package com.payermax.channel.inst.center.infrastructure.adapter;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.infrastructure.util.RpcInvokeUtil;
import com.payermax.omc.channel.exchange.facade.InstFundsAccountFacade;
import com.payermax.omc.channel.exchange.facade.InstProcessCallbackFacade;
import com.payermax.omc.channel.exchange.facade.request.InstFundsAccountSaveRequest;
import com.payermax.workflow.server.dubbo.api.domain.WorkflowCallback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstFundsAccountSaveAdapter {

    @DubboReference(interfaceClass = InstFundsAccountFacade.class, version = "1.0", retries = 0, timeout = 100000, check = false)
    private InstFundsAccountFacade instFundsAccountFacade;

    @DubboReference(interfaceClass = InstProcessCallbackFacade.class, version = "1.0", retries = 0, timeout = 100000, check = false)
    private InstProcessCallbackFacade instProcessCallbackFacade;



    /**
     * 发起账户保存流程
     */
    public String saveWithProcess(String shareId, InstFundsAccountSaveRequest request) {
        return RpcInvokeUtil.commonRpcCaller(() -> instFundsAccountFacade.saveWithProcess(shareId,request),
                ErrorCodeEnum.INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALL_ERROR,
                ErrorCodeEnum.INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALL_ERROR.getMsg());
    }

    /**
     * 账户保存回调
     */
    public Boolean callbackHandler(WorkflowCallback workflowCallback) {
        return RpcInvokeUtil.commonRpcCaller(() -> ResultUtils.success(instProcessCallbackFacade.instAccountSuCallback(workflowCallback)),
                ErrorCodeEnum.INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALLBACK_ERROR,
                ErrorCodeEnum.INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALLBACK_ERROR.getMsg());
    }
}
