package com.payermax.channel.inst.center.infrastructure.entity.query;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity;
import lombok.Data;

/**
 * @ClassName InstAccountKeyQueryEntity
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 20:29
 */
@Data
public class InstAccountKeyQueryEntity extends InstAccountKeyEntity {

    private Long id;

    private Long instId;

    private Long requirementOrderId;

    private String type;

    private String env;

    private String account;
}
