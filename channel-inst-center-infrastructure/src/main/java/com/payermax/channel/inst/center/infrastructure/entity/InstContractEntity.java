package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstContractEntity {
    private String contractNo;

    private String realContractNo;

    private Long instId;

    private String applyNo;

    private String fundsSettleInst;

    private String servicePurchaser;

    private String serviceProvider;

    private Date startDate;

    private Date endDate;

    private String signFlag;

    private String contractAttachId;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}