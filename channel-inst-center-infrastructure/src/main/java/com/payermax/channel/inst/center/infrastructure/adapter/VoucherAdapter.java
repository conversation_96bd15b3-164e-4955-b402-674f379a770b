package com.payermax.channel.inst.center.infrastructure.adapter;

import com.payermax.common.distributed.DistributedIdManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> at 2023/6/18 11:58 AM
 **/
@Component
public class VoucherAdapter {

    @Resource
    private DistributedIdManager distributedIdManager;

    private static final String CONTRACT_BASE_NO    = "HT0000";

    private static final String CONTRACT_VERSION_NO = "BB0000";

    private static final String ORIGIN_PRODUCT_CODE = "OP0000";

    private static final String STANDARD_PRODUCT_CODE = "SP0000";

    private static final String FEE_ITEM_CODE = "FE0000";
    private static final String SETTLEMENT_ITEM_CODE = "SE0000";

    /**
     * 业务协议
     */
    private static final String BIZ_AGREEMENT_CODE = "BA0000";

    /**
     * 资金协议
     */
    private static final String FUNDS_AGREEMENT_CODE = "FA0000";

    /**
     * 资金清算规则
     */
    private static final String FUNDS_SETTLE_RULE_CODE = "FS0000";

    /**
     * 不让上层感知具体差异
     */
    public String getBaseInfoContractNo() {
        return getDistributedVoucher(CONTRACT_BASE_NO);
    }

    public String getContractVersionNo() {
        return getDistributedVoucher(CONTRACT_VERSION_NO);
    }

    public String getOriginProductCode() {
        return getDistributedVoucher(ORIGIN_PRODUCT_CODE);
    }

    public String getStandardProductCode() {
        return getDistributedVoucher(STANDARD_PRODUCT_CODE);
    }

    public String getInstProductFeeItemNo() {
        return getDistributedVoucher(FEE_ITEM_CODE);
    }
    public String getInstProductSettlementItemNo() {
        return getDistributedVoucher(SETTLEMENT_ITEM_CODE);
    }
    public String getBizAgreementCode() {
        return getDistributedVoucher(BIZ_AGREEMENT_CODE);
    }
    public String getFundsAgreementCode() {
        return getDistributedVoucher(FUNDS_AGREEMENT_CODE);
    }
    public String getFundsSettleRuleCode() {
        return getDistributedVoucher(FUNDS_SETTLE_RULE_CODE);
    }

    private String getDistributedVoucher(String sceneCode) {
        String randomShardingKey = String.format("%04d", (int) (Math.random() * 10000));

        return distributedIdManager.getVoucherSequenceNo(sceneCode, randomShardingKey);
    }
}
