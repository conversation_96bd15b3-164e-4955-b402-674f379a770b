package com.payermax.channel.inst.center.infrastructure.client;

import com.payermax.channel.inst.center.common.exception.CustomException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.crm.facade.CustomConfigInfoFacade;
import com.payermax.crm.facade.req.CompanyEntityListReq;
import com.payermax.crm.facade.resp.CompanyEntityListResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * OmcCrmFacadeClientProxy
 *
 * <AUTHOR>
 * @desc
 */
@Slf4j
@Component
public class OmcCrmFacadeClientProxy {

    @DubboReference(version = "1.0")
    private CustomConfigInfoFacade customConfigInfoFacade;

    public List<CompanyEntityListResp> companyEntityListQuery(CompanyEntityListReq companyEntityListReq) {
        Result<List<CompanyEntityListResp>> result = customConfigInfoFacade.companyEntityListQuery(companyEntityListReq);
        if(!result.isSuccess()) {
            log.warn("fail to query crm company entity, resp[{}]", result);
            throw new CustomException(result.getCode(), result.getMsg());
        }
        if(Objects.isNull(result.getData())) {
            return Collections.emptyList();
        }
        return result.getData();
    }
}
