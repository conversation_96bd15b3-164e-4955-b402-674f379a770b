package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountDao;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsAccountCustomMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAccountPo;
import com.payermax.common.lang.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC
 */
@Component
@RequiredArgsConstructor
public class InstFundsAccountRepository extends ServiceImpl<InstFundsAccountCustomMapper, InstFundsAccountPo> {

    private final InstFundsAccountDao fundsAccountDao;

    public InstFundsAccountEntity queryByAccountId(String accountId) {
        InstFundsAccountQueryEntity account = new InstFundsAccountQueryEntity();
        account.setAccountId(accountId);
        List<InstFundsAccountEntity> accountEntityList = fundsAccountDao.selectByQueryEntity(account);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(accountEntityList), "account not exist");
        AssertUtil.isTrue(accountEntityList.size() == 1, "account more than one");

        return accountEntityList.get(0); // CHECKED
    }
}
