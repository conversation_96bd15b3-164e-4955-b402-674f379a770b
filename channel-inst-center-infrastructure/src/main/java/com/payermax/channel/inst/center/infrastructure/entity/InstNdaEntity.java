package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

/**
 * NDA签订单实体类
 *
 * <AUTHOR>
 * @date 2022/5/13 11:20
 */
@Data
public class InstNdaEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 我司主体
     */
    private String shareitEntity;

    /**
     * NDA版本
     */
    private String version;

    /**
     * NDA原始文件
     */
    private String ndaAttachId;

    /**
     * 机构是否已签署
     */
    private String instSignFlag;

    /**
     * 我司是否已签署
     */
    private String shareitSignFlag;

    /**
     * 机构单签文件
     */
    private String instSignAttachId;

    /**
     * 我司单签文件
     */
    private String shareitSignAttachId;

    /**
     * 双签文件
     */
    private String pairSignAttachId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;
}