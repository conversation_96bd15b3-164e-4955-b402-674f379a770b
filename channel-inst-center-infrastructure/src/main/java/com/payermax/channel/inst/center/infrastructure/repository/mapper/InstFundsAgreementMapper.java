package com.payermax.channel.inst.center.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 针对表【tb_inst_funds_agreement】的数据库操作Mapper
 */
public interface InstFundsAgreementMapper extends BaseMapper<InstFundsAgreementPO> {

    /**
     * 根据 资金协议类型、资金协议名称、清算币种、协议对手方、协议发起方 查询资金协议、业务协议、结算规则
     * @param page 分页
     * @param type 资金协议类型
     * @param name 资金协议名称
     * @param clearingCurrency 清算币种
     * @param status 状态
     * @param counter 协议对手方
     * @param initiator 协议发起方
     * @return 资金协议
     */
    List<InstFundsAgreementPO> selectByConditions(IPage<InstFundsAgreementPO> page, @Param("type") String type, @Param("name") String name, @Param("clearingCurrency") String clearingCurrency,
                                                  @Param("status") String status, @Param("counter") String counter, @Param("initiator") String initiator);

    /**
     * 根据 ID 查询资金协议
     * @param id 资金协议 ID
     * @return 资金协议
     */
    InstFundsAgreementPO selectById(@Param("id") String id);
}
