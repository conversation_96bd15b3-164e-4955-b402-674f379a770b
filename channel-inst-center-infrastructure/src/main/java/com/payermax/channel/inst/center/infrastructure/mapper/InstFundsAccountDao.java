package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_inst_funds_account(机构资金账号表)】的数据库操作Mapper
 * @createDate 2022-10-08 15:52:15
 * @Entity com.payermax.channel.inst.center.infrastructure.entity.TbInstFundsAccount
 */
@Mapper
public interface InstFundsAccountDao {

    List<InstFundsAccountEntity> selectByQueryEntity(InstFundsAccountQueryEntity accountQueryEntity);

    List<InstFundsAccountEntity> selectAccountAndSubByRequest(InstFundsAccountAndSubQueryEntity queryEntity);
}
