package com.payermax.channel.inst.center.infrastructure.adapter;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.infrastructure.util.RpcInvokeUtil;
import com.payermax.solution.commcenter.client.notice.facade.dubbo.CommNoticeFacade;
import com.payermax.solution.commcenter.client.notice.facade.enums.OperationStatusEnum;
import com.payermax.solution.commcenter.client.notice.facade.model.NoticeElementDTO;
import com.payermax.solution.commcenter.client.notice.facade.model.request.CreateInnerNoticeRequest;
import com.payermax.solution.commcenter.client.notice.facade.model.response.CreateNoticeResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/13
 * @DESC
 */
@Slf4j
@Component
public class CommCenterNoticeAdapter {

    @DubboReference(interfaceClass = CommNoticeFacade.class, version = "1.0", retries = 0, timeout = 100000, check = false)
    private CommNoticeFacade commNoticeFacade;

    private static final String INNER_NOTICE_TARGET_TYPE = "PAYERMAX_EMPLOYEE";
    private static final String INNER_NOTICE_TARGET_ID = "FX_GROUP";
    private static final String INNER_NOTICE_NOTICE_TOOL_TYPE = "LARK_GROUP";
    private static final String SOURCE = "fx-service";


    /**
     * 发送 Webhook 通知
     */
    public CreateNoticeResponseDTO sendWebhookNotice(String businessKey, String webhookUrl, String sceneCode, Map<String, Object> content) {

        NoticeElementDTO element = new NoticeElementDTO();
        element.setTargetType(INNER_NOTICE_TARGET_TYPE);
        element.setTargetId(INNER_NOTICE_TARGET_ID);
        element.setNoticeToolType(INNER_NOTICE_NOTICE_TOOL_TYPE);
        element.setNoticeToolAddress(Collections.singletonList(webhookUrl));
        element.setElement(Collections.singletonList(content));

        CreateInnerNoticeRequest request = new CreateInnerNoticeRequest();
        request.setSource(SOURCE);
        request.setBusinessKey(businessKey);
        request.setSceneCode(sceneCode);
        request.setOperation(OperationStatusEnum.CREATE.name());
        request.setNoticeTargetList(Collections.singletonList(element));


        return RpcInvokeUtil.commonRpcCaller(() -> commNoticeFacade.createInternalNotice(request),
                ErrorCodeEnum.COMM_CENTER_NOTICE_ERROR,
                ErrorCodeEnum.COMM_CENTER_NOTICE_ERROR.getMsg());
    }
}
