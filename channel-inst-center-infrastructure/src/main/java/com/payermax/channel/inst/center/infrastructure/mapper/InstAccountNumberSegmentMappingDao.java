package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountNumberSegmentMappingEntity;

/**
 * <AUTHOR>
 * @description 针对表【tb_inst_account_number_segment_mapping(机构账号号段映射关系表)】的数据库操作Mapper
 * @createDate 2022-10-08 15:52:15
 * @Entity com.payermax.channel.inst.center.infrastructure.entity.TbInstAccountNumberSegmentMapping
 */
public interface InstAccountNumberSegmentMappingDao {

    int updateByEntity(InstAccountNumberSegmentMappingEntity record);

}
