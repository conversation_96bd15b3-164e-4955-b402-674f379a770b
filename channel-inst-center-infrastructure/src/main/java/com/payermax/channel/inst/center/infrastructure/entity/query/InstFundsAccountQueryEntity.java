package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 机构资金账号表
 *
 * <AUTHOR>
 */
@Data
public class InstFundsAccountQueryEntity {
    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;
    /**
     * 机构标识
     */
    private String instCode;
    /**
     * 签约主体
     */
    private String entity;
    /**
     * 机构账号
     */
    private String accountNo;
    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;
    /**
     * 机构开户名称
     */
    private String accountName;
    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;
    /**
     * 是否支持子级账号 N:不支持，1:支持
     */
    private String isSupportSubAccount;
    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;
    /**
     * 子级账号生成规则，示例：citi${instAccount}${subNumberSegment}
     */
    private String subAccountRule;
    /**
     * 国家
     */
    private List<String> country;
    /**
     * 币种
     */
    private String currency;
    /**
     * 状态 N：不可用，Y：可用
     */
    private String status;
    /**
     * 优先级，值越小优先级越高
     */
    private Integer priority;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 是否支持名称自定义：N：不支持，Y：支持
     */
    private String isSupportCustomName;
    /**
     * 是否需要激活：N:不需要，Y:需要
     */
    private String isNeedActivation;
    /**
     * 激活模式 API模式：API，线下模式：OFFLINE
     */
    private String activationMode;
    /**
     * 关联机构MID
     */
    private String instMid;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 账户别名
     */
    private String accountAlias;

    /**
     * 扩展配置
     */
    private String accountJson;
    /**
     * 创建时间
     */
    private Date utcCreate;
    /**
     * 更新时间
     */
    private Date utcModified;

    /**
     * 业务用途 I 代收/O 代付 / F 换汇
     */
    private String bizType;

    /**
     * 银行地址 JSON
     *{
     *   "city": "city",
     *   "address": "address",
     *   "country": "country",
     *   "postcode": "postcode"
     * }
     */
    private String bankAddressJson;

    /**
     * 商户号
     * */
    private String merchantNo;

}