package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstTransFeeDao {
    int insert(InstTransFeeEntity record);

    int insertBatch(List<InstTransFeeEntity> records);

    List<InstTransFeeEntity> selectAll(@Param("feeGroupIds") List<String> feeGroupIds);

    int updateByPrimaryKey(InstTransFeeEntity record);

    int deleteByFeeGroupIds(@Param("feeGroupIds") List<String> feeGroupIds);
}