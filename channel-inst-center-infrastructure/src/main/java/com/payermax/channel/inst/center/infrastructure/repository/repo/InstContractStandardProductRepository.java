package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractStandardProdCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractStandardProductMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractStandardProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.common.lang.util.AssertUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/25 8:13 PM
 **/
@Component
public class InstContractStandardProductRepository extends ServiceImpl<InstContractStandardProductMapper, InstContractStandardProductPO> {


    @Resource
    private InstNewContractStandardProdCacheManager instNewContractStandardProdCacheManager;

    /**
     * 使用缓存构建标准合同产品
     */
    protected void composeStandardProductForContract(List<InstContractVersionInfoPO> contractVersionInfoPOList) {
        if (CollectionUtils.isEmpty(contractVersionInfoPOList)) {
            return;
        }

        contractVersionInfoPOList.forEach((contractVersionInfoPO) -> {

            // 直接查缓存,不然DB操作太多了.必须确保标准产品缓存在合同之后!!
            List<InstContractStandardProductPO> standardProductPOList = instNewContractStandardProdCacheManager
                    .queryStandardProductByContract(contractVersionInfoPO.getContractNo(), contractVersionInfoPO.getContractVersion());

            contractVersionInfoPO.setStandardProducts(standardProductPOList);
        });
    }


    protected void batchSaveStandardProduct(List<InstContractStandardProductPO> standardProductPOList) {
        if (!CollectionUtils.isEmpty(standardProductPOList)) {
            standardProductPOList.forEach(item -> {
                if (item.isNewlyCreated()) {
                    save(item);
                }
            });
        }
    }

    public InstContractStandardProductPO queryExistOriginProduct(String originProductNo, String contractNo, String contractVersion,
                                                                 String paymentMethodType, String targetOrg, String cardOrg) {
        LambdaQueryWrapper<InstContractStandardProductPO> queryWrapper = Wrappers.<InstContractStandardProductPO>lambdaQuery()
                .eq(InstContractStandardProductPO::getContractNo, contractNo)
                .eq(InstContractStandardProductPO::getContractVersion, contractVersion)
                .eq(InstContractStandardProductPO::getInstOriginProductNo, originProductNo)
                .eq(InstContractStandardProductPO::getPaymentMethodType, paymentMethodType)
                .eq(InstContractStandardProductPO::getTargetOrg, targetOrg);
        if (cardOrg == null) {
            queryWrapper.isNull(InstContractStandardProductPO::getCardOrg);
        } else {
            queryWrapper.eq(InstContractStandardProductPO::getCardOrg, cardOrg);
        }

        List<InstContractStandardProductPO> standardProductPOList = list(queryWrapper);
        if (CollectionUtils.isEmpty(standardProductPOList)) {
            return null;
        }
        // 数据库唯一性约束，只能有一个
        AssertUtil.isTrue(standardProductPOList.size() == 1, "ERROR", "存在多个维度相同的标准产品");
        return standardProductPOList.get(0); //CHECKED
    }

    public InstContractStandardProductPO queryExistStandardProduct(String contractNo, String contractVersion, String paymentMethodType, String targetOrg, String cardOrg) {
        LambdaQueryWrapper<InstContractStandardProductPO> queryWrapper = Wrappers.<InstContractStandardProductPO>lambdaQuery()
                .eq(InstContractStandardProductPO::getContractNo, contractNo)
                .eq(InstContractStandardProductPO::getContractVersion, contractVersion)
                .eq(InstContractStandardProductPO::getPaymentMethodType, paymentMethodType)
                .eq(InstContractStandardProductPO::getTargetOrg, targetOrg)
                .eq(StringUtils.isNotBlank(cardOrg), InstContractStandardProductPO::getCardOrg, cardOrg);
        List<InstContractStandardProductPO> standardProductPOList = list(queryWrapper);
        if (CollectionUtils.isEmpty(standardProductPOList)) {
            return null;
        }
        // 数据库唯一性约束，只能有一个
        AssertUtil.isTrue(standardProductPOList.size() == 1, "ERROR", "存在多个维度相同的标准产品");
        return standardProductPOList.get(0); //NO_CHECK
    }

    public List<InstContractStandardProductPO> listByOriginProductNo(String originProductNo) {
        LambdaQueryWrapper<InstContractStandardProductPO> queryWrapper = Wrappers.<InstContractStandardProductPO>lambdaQuery()
                .eq(InstContractStandardProductPO::getInstOriginProductNo, originProductNo);
        return list(queryWrapper);
    }
}
