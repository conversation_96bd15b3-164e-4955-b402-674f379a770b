package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractSettlementItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-13 3:22 PM
 */
@Service
public class InstNewContractSettlementCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractSettlementItemMapper instContractSettlementItemMapper;

    private static Map<String, List<InstContractSettlementItemPO>> CACHE_MAP = new HashMap<>();


    public List<InstContractSettlementItemPO> querySettleItemByOriginProductNo(String originProductNo) {
        return CACHE_MAP.get(originProductNo);
    }


    @Override
    public void doInit() {
        QueryWrapper<InstContractSettlementItemPO> queryWrapper = new QueryWrapper<>();
        List<InstContractSettlementItemPO> contractSettlementItemPOS = instContractSettlementItemMapper.selectList(queryWrapper);
        contractSettlementItemPOS.forEach(item -> {
            List<InstContractSettlementItemPO> settlementItemPOS = CACHE_MAP
                    .computeIfAbsent(item.getInstOriginProductNo(), a -> new ArrayList<>());
            settlementItemPOS.add(item);
        });
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstContractSettlementItemPO>> temp = new HashMap<>();

        QueryWrapper<InstContractSettlementItemPO> queryWrapper = new QueryWrapper<>();
        List<InstContractSettlementItemPO> contractSettlementItemPOS = instContractSettlementItemMapper.selectList(queryWrapper);
        contractSettlementItemPOS.forEach(item -> {
            List<InstContractSettlementItemPO> settlementItemPOS = temp
                    .computeIfAbsent(item.getInstOriginProductNo(), a -> new ArrayList<>());
            settlementItemPOS.add(item);
        });
        CACHE_MAP = temp;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_SETTLE;
    }
}
