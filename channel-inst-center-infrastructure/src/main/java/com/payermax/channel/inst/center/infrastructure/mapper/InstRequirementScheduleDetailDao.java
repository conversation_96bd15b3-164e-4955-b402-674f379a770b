package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleDetailQueryEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstRequirementScheduleDetailDao {
    int insert(InstRequirementScheduleDetailEntity record);

    List<InstRequirementScheduleDetailEntity> selectAll(InstRequirementScheduleDetailQueryEntity queryEntity);

    int updateByPrimaryKey(InstRequirementScheduleDetailEntity record);
}