package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractMidMappingCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractMidMappingMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.common.lang.util.AssertUtil;
import lombok.Setter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 3:37 PM
 **/
@Component
@Setter
public class InstContractMidMappingRepository extends ServiceImpl<InstContractMidMappingMapper, InstContractMidMappingPO> {

    @Resource
    private InstNewContractMidMappingCacheManager instNewContractMidMappingCacheManager;

    @Resource
    private InstContractMidMappingMapper instContractMidMappingMapper;

    public InstContractMidMappingPO queryMappingByMid(String mid, String bizType) {
        return instNewContractMidMappingCacheManager.queryMappingByMid(mid, bizType);
    }

    public void initInstContractMidMapping(InstContractMidMappingPO instContractMidMappingPO) {
        instContractMidMappingMapper.insert(instContractMidMappingPO);
    }

    public List<InstContractMidMappingPO> queryAllEmptyContractItems() {
        QueryWrapper<InstContractMidMappingPO> wrapper = new QueryWrapper<>();
        wrapper.isNull("contract_no");
        return instContractMidMappingMapper.selectList(wrapper);
    }

    public void updateContractMidMapping(InstContractMidMappingPO contractMidMappingPO) {
        UpdateWrapper<InstContractMidMappingPO> wrapper = new UpdateWrapper<>();
        wrapper.eq("inst_code", contractMidMappingPO.getInstCode());
        wrapper.eq("inst_type", contractMidMappingPO.getInstType());
        wrapper.eq("entity", contractMidMappingPO.getEntity());
        wrapper.eq("channel_merchant_code", contractMidMappingPO.getChannelMerchantCode());
        AssertUtil.isTrue(instContractMidMappingMapper.update(contractMidMappingPO, wrapper) == 1, "ERROR", "updateContractMidMapping fail ");
    }

    public List<InstContractMidMappingPO> queryAllMapping() {
        LambdaQueryWrapper<InstContractMidMappingPO> queryWrapper = Wrappers.<InstContractMidMappingPO>lambdaQuery();
        return list(queryWrapper);
    }
}
