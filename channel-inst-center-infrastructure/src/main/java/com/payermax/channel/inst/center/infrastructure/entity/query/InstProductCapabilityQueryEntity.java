package com.payermax.channel.inst.center.infrastructure.entity.query;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import lombok.Data;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/19 22:16
 */
@Data
public class InstProductCapabilityQueryEntity extends InstProductCapabilityEntity {

    /**
     * 产品编码集合
     */
    List<String> productCodes;

    /**
     * 产品能力编码集合
     */
    List<String> capabilityCodes;

    /**
     * 目标机构集合
     */
    List<String> targetOrgs;

    /**
     * 卡组织集合
     */
    List<String> cardOrgs;

}
