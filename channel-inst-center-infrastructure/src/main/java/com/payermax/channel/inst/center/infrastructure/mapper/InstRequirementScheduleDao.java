package com.payermax.channel.inst.center.infrastructure.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstRequirementScheduleDao {

    int insert(InstRequirementScheduleEntity record);

    IPage<InstRequirementScheduleQueryEntity> selectScheduleList(IPage<InstRequirementScheduleQueryEntity> page, @Param("schedule")InstRequirementScheduleQueryEntity queryEntity);

    List<InstRequirementScheduleEntity> selectAll(InstRequirementScheduleQueryEntity queryEntity);

    int updateByPrimaryKey(InstRequirementScheduleEntity record);
}