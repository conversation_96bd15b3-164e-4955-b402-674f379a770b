package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstReconcileEntity {
    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String invoiceProvider;

    private String reconcileMethod;

    private String reconcileTemplate;

    private Date utcCreate;

    private Date utcModified;
}