package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> at 2022/10/16 00:11
 **/
@Data
public class InstSubAccountBathQueryEntity {

    /**
     * 机构账号
     */
    private List<String> accountIds;

    /**
     * 子级资金账号
     */
    private List<String> subAccountIds;

    /**
     * 商户号
     */
    private List<String> merchantNos;

    /**
     * 补偿子级账号状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    @NotNull(message = "status can not null")
    @Min(0)
    private Integer status;

    /**
     * 最近多少小时数
     */
    @NotNull(message = "recentHour can not null")
    @Min(1)
    private Integer recentHour;

    /**
     * 创建时间大于当前时间的数据
     */
    private Date utcCreate;

    /**
     * 分页每页大小
     */
    @NotNull(message = "pageSize can not null")
    @Min(1)
    private Integer pageSize;

    /**
     * 分页数
     */
    private Integer pageNum = 1;

    /**
     * 是否补偿重发
     */
    private Boolean compensateRetry;
}
