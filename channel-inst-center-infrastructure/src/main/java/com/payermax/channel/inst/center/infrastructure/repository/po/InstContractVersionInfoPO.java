package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/9 5:24 PM
 **/
@TableName(InstContractVersionInfoPO.TABLE_NAME)
@Data
public class InstContractVersionInfoPO {

    public static final String TABLE_NAME = "tb_inst_new_contract_version_info";

    private String contractNo;

    private String contractVersion;

    private String accountingType;

    private LocalDateTime effectStartTime;

    private LocalDateTime effectEndTime;

    private String status;

    /**
     * 原始机构产品
     */
    @TableField(exist = false)
    private List<InstContractOriginProductPO> originProducts;

    /**
     * 标准化机构产品
     */
    @TableField(exist = false)
    private List<InstContractStandardProductPO> standardProducts;

    /**
     * 是否新创建的，辅助数据库操作
     */
    @TableField(exist = false)
    private boolean newlyCreated;
}
