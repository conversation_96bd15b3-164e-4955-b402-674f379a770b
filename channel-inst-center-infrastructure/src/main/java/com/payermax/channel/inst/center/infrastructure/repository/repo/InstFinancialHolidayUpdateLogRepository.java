package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFinancialHolidayUpdateLogMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialHolidaysUpdateLogPO;
import com.payermax.common.lang.util.AssertUtil;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class InstFinancialHolidayUpdateLogRepository extends ServiceImpl<InstFinancialHolidayUpdateLogMapper, InstFinancialHolidaysUpdateLogPO> {

    /**
     * 查询节假日变更列表-根据时间区间
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 节假日变更列表
     */
    public List<InstFinancialHolidaysUpdateLogPO> queryByConditions(Date startDate, Date endDate) {
        AssertUtil.notNull(startDate, "ERROR", "startDate can not be empty");
        AssertUtil.notNull(endDate, "ERROR", "endDate can not be empty");
        LambdaQueryWrapper<InstFinancialHolidaysUpdateLogPO> queryWrapper =
                Wrappers.<InstFinancialHolidaysUpdateLogPO>lambdaQuery()
                        .between(true, InstFinancialHolidaysUpdateLogPO::getUtcCreate, startDate, endDate);
        return list(queryWrapper);
    }


}
