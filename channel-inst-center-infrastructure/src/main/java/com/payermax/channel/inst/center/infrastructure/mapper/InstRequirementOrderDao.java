package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface InstRequirementOrderDao {

    int insert(InstRequirementOrderEntity record);

    List<InstRequirementOrderEntity> selectAll(InstRequirementOrderEntity queryEntity);

    int updateByPrimaryKey(InstRequirementOrderEntity record);
}