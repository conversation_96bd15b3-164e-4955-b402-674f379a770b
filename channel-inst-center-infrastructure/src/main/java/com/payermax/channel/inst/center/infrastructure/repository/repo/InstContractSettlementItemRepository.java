package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractSettlementCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractSettlementItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 3:17 PM
 **/
@Component
@Slf4j
public class InstContractSettlementItemRepository extends ServiceImpl<InstContractSettlementItemMapper, InstContractSettlementItemPO> {

    @Resource
    private InstNewContractSettlementCacheManager instNewContractSettlementCacheManager;

    protected void composeSettleItemForProduct(InstContractOriginProductPO contractOriginProductPO) {

        List<InstContractSettlementItemPO> contractSettlementItemPOS = instNewContractSettlementCacheManager
                .querySettleItemByOriginProductNo(contractOriginProductPO.getInstOriginProductNo());

        contractOriginProductPO.setSettlementItems(contractSettlementItemPOS);
    }

    protected void batchSaveContractFeeItem(List<InstContractSettlementItemPO> contractSettlementItems) {
        saveBatch(contractSettlementItems);
    }


    /**
     * 根据原始产品查询单条结算信息
     */
    public InstContractSettlementItemPO queryOneByOriginProductNo(String originProductNo) {
        LambdaQueryWrapper<InstContractSettlementItemPO> queryWrapper = Wrappers.<InstContractSettlementItemPO>lambdaQuery()
                .eq(InstContractSettlementItemPO::getInstOriginProductNo, originProductNo)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据原始产品查询结算信息
     */
    public List<InstContractSettlementItemPO> queryByOriginProductNo(String originProductNo) {
        LambdaQueryWrapper<InstContractSettlementItemPO> queryWrapper = Wrappers.<InstContractSettlementItemPO>lambdaQuery()
                .eq(InstContractSettlementItemPO::getInstOriginProductNo, originProductNo);
        return list(queryWrapper);
    }


    /**
     * 根据 SettleItemNo 查询结算信息
     */
    public InstContractSettlementItemPO queryOneByNo(String settleItemNo) {
        LambdaQueryWrapper<InstContractSettlementItemPO> queryWrapper = Wrappers.<InstContractSettlementItemPO>lambdaQuery()
                .eq(InstContractSettlementItemPO::getInstContractSettlementItemNo, settleItemNo)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据 No 列表批量查询
     */
    public List<InstContractSettlementItemPO> queryListByNos(List<String> settleItemNos){
        LambdaQueryWrapper<InstContractSettlementItemPO> queryWrapper = Wrappers.<InstContractSettlementItemPO>lambdaQuery()
                .in(InstContractSettlementItemPO::getInstContractSettlementItemNo, settleItemNos);
        return list(queryWrapper);
    }


    /**
     * 保存，忽略唯一约束重复
     */
    public boolean saveIgnoreDuplicate(InstContractSettlementItemPO settlementItem){
        try{
           save(settlementItem);
        } catch (DuplicateKeyException e) {
            log.warn("Duplicate entry", e);
        }
        return true;
    }
}
