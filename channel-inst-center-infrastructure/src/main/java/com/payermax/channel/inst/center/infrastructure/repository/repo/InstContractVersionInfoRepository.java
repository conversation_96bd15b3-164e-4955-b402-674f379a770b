package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractVersionInfoMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 11:23 AM
 **/
@Component
public class InstContractVersionInfoRepository extends ServiceImpl<InstContractVersionInfoMapper, InstContractVersionInfoPO> {

    @Resource
    private InstContractOriginProductRepository instContractOriginProductRepository;

    @Resource
    private InstContractStandardProductRepository instContractStandardProductRepository;

    public List<InstContractVersionInfoPO> listAllEffectiveVersionContract() {
        // 1、先找到状态为EFFECTIVE的全部版本合同
        List<InstContractVersionInfoPO> effectiveVersionContract = list(Wrappers.<InstContractVersionInfoPO>lambdaQuery()
                .eq(InstContractVersionInfoPO::getStatus, "EFFECTIVE"));

        // 2、组装原始机构产品信息
        instContractOriginProductRepository.composeOriginProductForContract(effectiveVersionContract);

        // 3、组装标准化机构产品信息
        instContractStandardProductRepository.composeStandardProductForContract(effectiveVersionContract);

        return effectiveVersionContract;
    }

    public InstContractVersionInfoPO queryActiveVersion(String contractNo) {
        LambdaQueryWrapper<InstContractVersionInfoPO> queryWrapper = Wrappers.<InstContractVersionInfoPO>lambdaQuery()
                .eq(InstContractVersionInfoPO::getContractNo, contractNo)
                .le(InstContractVersionInfoPO::getEffectStartTime, LocalDateTime.now(ZoneOffset.UTC))
                .orderByDesc(InstContractVersionInfoPO::getEffectStartTime)
                .last("limit 1");

        return getOne(queryWrapper);
    }

    /**
     * 根据合约列表合约生效版本
     */
    public List<InstContractVersionInfoPO> queryActiveVersionByContracts(List<String> contractNoList) {
        if (CollectionUtils.isEmpty(contractNoList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InstContractVersionInfoPO> queryWrapper = Wrappers.<InstContractVersionInfoPO>lambdaQuery()
                .in(InstContractVersionInfoPO::getContractNo, contractNoList)
                .eq(InstContractVersionInfoPO::getStatus, "EFFECTIVE");

        return list(queryWrapper);
    }


    /**
     * 根据主键查询
     */
    public InstContractVersionInfoPO queryByPrimaryKey(InstContractVersionInfoPO po) {
        LambdaQueryWrapper<InstContractVersionInfoPO> queryWrapper = Wrappers.<InstContractVersionInfoPO>lambdaQuery()
                .eq(InstContractVersionInfoPO::getContractNo, po.getContractNo())
                .eq(InstContractVersionInfoPO::getContractVersion, po.getContractVersion());
        return getOne(queryWrapper);
    }

    /**
     * 根据主键更新
     */
    public Boolean updateByPrimaryKey(InstContractVersionInfoPO po) {
        LambdaUpdateWrapper<InstContractVersionInfoPO> wrapper = Wrappers.<InstContractVersionInfoPO>lambdaUpdate()
                    .eq(InstContractVersionInfoPO::getContractNo, po.getContractNo())
                    .eq(InstContractVersionInfoPO::getContractVersion, po.getContractVersion());

        return update(po, wrapper);
    }

    public void signNewContractVersion(InstContractVersionInfoPO versionInfoPO) {
        // 持久化合同新版本
        if (versionInfoPO.isNewlyCreated()) {
            save(versionInfoPO);
        }

        // 持久化机构产品 条款
        instContractOriginProductRepository.batchSaveOrigProduct(versionInfoPO.getOriginProducts());

        // 持久化机构标准化产品
        instContractStandardProductRepository.batchSaveStandardProduct(versionInfoPO.getStandardProducts());
    }
}
