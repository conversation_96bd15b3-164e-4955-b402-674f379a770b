package com.payermax.channel.inst.center.infrastructure.client;

import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * channel调用client
 *
 * <AUTHOR>
 * @date 2021/8/3 10:32
 */
@FeignClient(name = "InternalChannelClient", url = "${internal.channel.url}")
public interface InternalChannelClient {

    @PostMapping(value = "/{urlPath}", consumes = {"application/json"})
    @DigestLog(isRecord = true)
    String internalExecute(@RequestHeader(value = "payermaxChannelOrder", required = false) String payermaxChannelOrder,
                           @RequestHeader(value = "sign", required = false) String sign,
                           @RequestHeader(value = "channelCode", required = false) String channelCode,
                           @RequestParam("urlPath") String urlPath,
                           @RequestBody String body);

}
