package com.payermax.channel.inst.center.infrastructure.adapter;

import cn.hutool.core.net.URLDecoder;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.infra.ionia.fs.FsProviderSelector;
import com.payermax.infra.ionia.fs.S3Util;
import com.payermax.infra.ionia.fs.dto.DownloadRequest;
import com.payermax.infra.ionia.fs.dto.DownloadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR> 2022/11/1  10:18 PM
 */
@Component
@Slf4j
public class FileDownloadService {

    @NacosValue(value = "${inst.bucketName}", autoRefreshed = true)
    private String bucketName;

    @NacosValue(value = "${inst.s3.urlPrefix}", autoRefreshed = true)
    private String urlPrefix;

    @Autowired
    private FsProviderSelector fsProviderSelector;


    public DownloadResponse downLoadS3File(String fileName) {
        try {
            DownloadRequest request = downloadRequestCompose(fileName);
            DownloadResponse downloadResponse = fsProviderSelector.get().download(request);
            if (Objects.isNull(downloadResponse)) {
                log.error("downLoad S3 File fail");
            }
            log.info("s3 download success!,filename:{}",fileName);
            return downloadResponse;
        } catch (Exception e) {
            log.error("downLoad S3 File fail,e", e);
            throw new BizException(ErrorCodeEnum.FILE_DOWNLOAD_FROM_S3_ERROR);
        }
    }

    /**
     * 根据文件名获取 CDN 地址
     */
    public String getCdnUrlByFilename(String fileName) {
        try {
            DownloadRequest request = downloadRequestCompose(fileName);
            String url = fsProviderSelector.get().getCDNUrl(request);
            if (StringUtils.isBlank(url)) {
                log.error("downLoad S3 File fail");
            }
            log.info("s3 download success!,filename:{}",fileName);
            return url;
        } catch (Exception e) {
            log.error("downLoad S3 File fail,e", e);
            throw new BizException(ErrorCodeEnum.FILE_DOWNLOAD_FROM_S3_ERROR);
        }
    }

    public byte[] downloadContent(String fileName) {
        DownloadResponse download = downLoadS3File(fileName);
        return download.getFileContent();
    }

    public byte[] downloadContentByUrl(String url) {
        String fileName = convertUrlToFileName(url);
        log.info("url is :{},fileName is :{}", url, fileName);
        return downloadContent(fileName);
    }

    private String convertUrlToFileName(String url) {
        try {
            //需要替换域名为桶名
            return url.replace(urlPrefix, StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("转换url成fileName失败，url:{},e:{}", url, e);
            return url;
        }
    }

    /**
     * 构造下载请求
     */
    private DownloadRequest downloadRequestCompose(String fileName) {
        try{
            log.info("解码前的fileName:{}", fileName);
            fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8);
            log.info("解码后的fileName:{}", fileName);

            DownloadRequest request = new DownloadRequest();
            // 注意，为规范桶的命名使用，新版S3已不支持桶中带目录路径，需要通过如下代码进行标准转换
            request.setBucketName(S3Util.extractBucketName(bucketName));
            request.setFileName(S3Util.extractFullFileName(bucketName, fileName));
            return request;
        }catch (Exception e){
            log.error("compose download request err,e", e);
            throw new BizException(ErrorCodeEnum.FILE_DOWNLOAD_FROM_S3_ERROR);
        }
    }
}
