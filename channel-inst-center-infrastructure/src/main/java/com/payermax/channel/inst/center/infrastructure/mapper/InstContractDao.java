package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstContractDao {
    int insert(InstContractEntity record);

    List<InstContractEntity> selectAll(InstContractEntity record);

    List<InstContractEntity> selectByInstIds(@Param("instIds") List<Long> instIds);

    int updateByPrimaryKey(InstContractEntity record);
}