package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstBrandDao{

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstBrandEntity record);

    /**
     * 查询记录
     *
     * @param record
     * @return
     */
    List<InstBrandEntity> selectAll(InstBrandEntity record);

    /**
     * 查询记录
     *
     * @param bdIds
     * @return
     */
    List<InstBrandEntity> selectByBdIds(@Param("bdIds") List<String> bdIds);

    /**
     * 直接insert，线下同步使用
     *
     */
    int insertDirect(InstBrandEntity record);

    /**
     * 修改记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstBrandEntity record);
}