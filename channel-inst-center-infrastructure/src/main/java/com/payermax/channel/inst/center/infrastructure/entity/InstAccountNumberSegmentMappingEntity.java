package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

/**
 * 机构账号号段映射关系表
 *
 * <AUTHOR>
 * @TableName tb_inst_account_number_segment_mapping
 */
@Data
public class InstAccountNumberSegmentMappingEntity {

    /**
     * 主键
     */
    private Long id;
    /**
     * 机构帐号标识
     */
    private String accountId;
    /**
     * 机构帐号号段标识
     */
    private String numberSegmentId;
    /**
     * 状态：N：不可用，Y：可用
     */
    private String status;
    /**
     * 创建时间
     */
    private Date utcCreate;
    /**
     * 更新时间
     */
    private Date utcModified;

}