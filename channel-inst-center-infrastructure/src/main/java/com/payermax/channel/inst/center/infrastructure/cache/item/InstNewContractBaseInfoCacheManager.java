package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.common.lang.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/16
 * @DESC 合约基本信息缓存
 */
@Service
public class InstNewContractBaseInfoCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractBaseInfoRepository baseInfoRepository;

    /**
     * 当前置缓存最新生效的版本合同，因此只会有一个
     */
    private static Map<String, InstContractBaseInfoPO> CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<InstContractBaseInfoPO> baseInfoList = baseInfoRepository.listAllEffectiveContract();
        Map<String, InstContractBaseInfoPO> contractMap = baseInfoList.stream().collect(Collectors.toMap(InstContractBaseInfoPO::getContractNo,
                a -> a, (a, b) -> a));
        CACHE_MAP.putAll(contractMap);
    }

    @Override
    public void doRefresh() {
        List<InstContractBaseInfoPO> baseInfoList = baseInfoRepository.listAllEffectiveContract();
        Map<String, InstContractBaseInfoPO> temp = new ConcurrentHashMap<>(baseInfoList.size());
        Map<String, InstContractBaseInfoPO> contractMap = baseInfoList.stream().collect(Collectors.toMap(InstContractBaseInfoPO::getContractNo,
                a -> a, (a, b) -> a));
        temp.putAll(contractMap);
        CACHE_MAP = temp;
    }

    /**
     * 根据渠道、主体、业务类型查询生效的版本合同
     */
    public InstContractBaseInfoPO getContractInfo(String bizType, String instCode, String entity) {
        return CACHE_MAP.values().stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getInstCode().equals(instCode) && item.getContractEntity().equals(entity) && item.getInstProductType().equals(bizType))
                .findFirst()
                .orElseThrow( ()-> new BusinessException(ErrorCodeEnum.INST_CENTER_CONTRACT_QUERY_ERROR.getCode(),
                        String.format("查询不到机构合约，机构编码:【%s】, 主体:【%s】,业务类型:【%s】", instCode, entity, bizType)));
    }

    /**
     * 根据渠道、业务类型查询生效的合同版本
     */
    public List<InstContractBaseInfoPO> getContractInfoList(String bizType, String instCode) {
        return CACHE_MAP.values().stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getInstCode().equals(instCode) && item.getInstProductType().equals(bizType))
                .collect(Collectors.toList());

    }


    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_BASE_INFO;
    }
}
