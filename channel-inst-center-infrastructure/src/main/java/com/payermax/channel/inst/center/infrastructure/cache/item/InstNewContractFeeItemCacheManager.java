package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractFeeItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-13 3:22 PM
 */
@Service
public class InstNewContractFeeItemCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractFeeItemMapper instContractFeeItemMapper;

    private static Map<String, List<InstContractFeeItemPO>> CACHE_MAP = new HashMap<>();


    public List<InstContractFeeItemPO> queryFeeItemByOriginProductNo(String originProductNo) {
        return CACHE_MAP.get(originProductNo);
    }


    @Override
    public void doInit() {
        QueryWrapper<InstContractFeeItemPO> queryWrapper = new QueryWrapper<>();
        List<InstContractFeeItemPO> contractFeeItemPOS = instContractFeeItemMapper.selectList(queryWrapper);
        contractFeeItemPOS.forEach(item -> {
            List<InstContractFeeItemPO> feeItemPOS = CACHE_MAP
                    .computeIfAbsent(item.getInstOriginProductNo(), a -> new ArrayList<>());
            feeItemPOS.add(item);
        });
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstContractFeeItemPO>> temp = new HashMap<>();

        QueryWrapper<InstContractFeeItemPO> queryWrapper = new QueryWrapper<>();
        List<InstContractFeeItemPO> contractFeeItemPOS = instContractFeeItemMapper.selectList(queryWrapper);
        contractFeeItemPOS.forEach(item -> {
            List<InstContractFeeItemPO> feeItemPOS = temp
                    .computeIfAbsent(item.getInstOriginProductNo(), a -> new ArrayList<>());
            feeItemPOS.add(item);
        });
        CACHE_MAP = temp;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_FEE;
    }
}
