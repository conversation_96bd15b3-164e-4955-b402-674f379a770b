package com.payermax.channel.inst.center.infrastructure.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketCountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【tb_inst_sub_funds_account(机构子级资金账号表)】的数据库操作Mapper
 * @createDate 2022-10-08 15:52:15
 * @Entity com.payermax.channel.inst.center.infrastructure.entity.TbInstSubFundsAccount
 */
public interface InstSubFundsAccountDao {

    int insert(InstSubFundsAccountEntity record);

    InstSubFundsAccountEntity selectByPrimaryKey(String subAccountId);

    int updateByPrimaryKeySelective(@Param("origStatus") Integer origStatus, @Param("record") InstSubFundsAccountEntity record);

    List<InstSubFundsAccountEntity> selectByQueryEntity(InstSubFundsAccountQueryEntity record);

    int selectCountByAccountId(InstSubFundsAccountQueryEntity record);

    List<InstSubFundsAccountBucketCountEntity> selectBucketIdCountByAccountId(InstSubFundsAccountQueryEntity record);

    int selectCountBucketIdIsNullByAccountId(InstSubFundsAccountQueryEntity record);

    IPage<InstSubFundsAccountEntity> selectSubAccountForTask(IPage<InstSubAccountBathQueryEntity> page, @Param("entity") InstSubAccountBathQueryEntity queryEntity);

}
