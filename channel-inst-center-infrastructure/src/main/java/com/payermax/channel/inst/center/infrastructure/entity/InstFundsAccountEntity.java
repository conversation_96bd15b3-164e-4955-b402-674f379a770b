package com.payermax.channel.inst.center.infrastructure.entity;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 机构资金账号表
 *
 * <AUTHOR>
 * @TableName tb_inst_funds_account
 */
@Data
public class InstFundsAccountEntity implements Serializable {

    private static final long serialVersionUID = 134183136923L;

    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 机构开户名称
     */
    private String accountName;

    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;

    /**
     * 场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
     */
    private String scenes;

    /**
     * 是否支持子级账号 N:不支持，Y:支持
     */
    private String isSupportSubAccount;

    /**
     * 支持子级账号类型 SupportSubAccountTypeEnum，英文,分割
     */
    private String supportSubAccountType;

    /**
     * 是否支持子级账号预申请 N:不支持，1:支持
     */
    private String isSupportPreApply;

    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;

    /**
     * 子级账号生成规则，示例：citi${instAccount}${subNumberSegment}
     */
    private String subAccountRule;

    /**
     * 最大生成子级账号数,空则无限制
     */
    private Long subAccountLimit;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 状态 N：不可用，Y：可用
     */
    private String status;

    /**
     * 优先级，值越小优先级越高
     */
    private Integer priority;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否支持名称自定义：N：不支持，Y：支持
     */
    private String isSupportCustomName;

    /**
     * 是否需要激活：N:不需要，Y:需要
     */
    private String isNeedActivation;

    /**
     * 激活模式 API模式：API，线下模式：OFFLINE
     */
    private String activationMode;

    /**
     * 是否支持关闭机构子级账号：‘N’:不支持，‘Y’:支持
     */
    private String isSupportCloseAccount;

    /**
     * 关闭模式 API模式：API，线下模式：OFFLINE
     */
    private String closeMode;

    /**
     * 关联机构MID
     */
    private String instMid;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 财务属性
     */
    private String financialAttribute;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 银行地址 JSON 结构
     * {
     *     "address": "详细地址",
     *     "city": "城市",
     *     "country": "国家",
     *     "postcode": "邮编"
     * }
     */
    private String bankAddressJson;

    /**
     * 扩展配置
     */
    private String accountJson;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;

    /**
     * 拓展配置
     */
    private List<InstFundsAccountExtEntity> accountExtList;

    //----2023-04 新增的---//
    /**
     * 账户别名
     */
    private String accountAlias;

    /**
     * 账户类别(在岸OR离岸)
     */
    private String accountCategory;

    /**
     * 账户性质
     */
    private String accountNature;

    /**
     * 开户时间
     */
    private Date accountOpeningTime;

    /**
     * 授权签字人
     */
    private String authorizedOfficer;

    /**
     * 网银操作人
     */
    private String bankOperator;

    /**
     * 相关文件
     */
    private String fileList;

    /**
     * iban
     */
    private String iban;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 是否删除，Y: 已删除，N: 未删除
     */
    private String isDeleted;

    /**
     * 机构子级账号
     */
    private List<InstSubFundsAccountEntity> subAccount;


    /**
     * 业务用途 I 代收/O 代付 / F 换汇
     */
    private String bizType;

    /**
     * 账户关联的渠道MID.
     */
    private List<String> relatedMidList;

    /**
     * 主账号相关拓展信息
     */
    private AccountJsonBo accountJsonBo;

    public AccountJsonBo getAccountJsonBo() {
        if (Objects.nonNull(accountJsonBo)) {
            return accountJsonBo;
        }
        if (StringUtils.isNotBlank(accountJson)) {
            accountJsonBo = JSON.parseObject(accountJson, AccountJsonBo.class);
        }
        if (Objects.isNull(accountJsonBo)) {
            accountJsonBo = new AccountJsonBo();
        }
        return accountJsonBo;
    }

    public String getAccountJson() {
        if (accountJsonBo == null) {
            return accountJson;
        } else {
            return JSON.toJSONString(accountJsonBo);
        }
    }

    @Data
    public static class AccountJsonBo {

        /**
         * 渠道账号唯一编号
         */
        private String accountUniqueNumber;

        /**
         * 子级账号名称后端处理规则
         */
        private String subAccountNameRule;

        /**
         * 绑定商户号
         */
        private List<String> bindMerchantNo;

        /**
         * 机构品牌
         */
        private String instBrand;

        /**
         * 机构商户编码
         */
        private String instMerchantCode;

        /**
         * api接口 Map<api名称, api版本>
         */
        private HashMap<String, String> apiConfig;
    }
}