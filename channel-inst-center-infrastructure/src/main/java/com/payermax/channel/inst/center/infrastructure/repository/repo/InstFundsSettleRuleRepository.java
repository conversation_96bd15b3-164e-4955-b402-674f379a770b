package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsSettleRuleMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Component
@Slf4j
public class InstFundsSettleRuleRepository extends ServiceImpl<InstFundsSettleRuleMapper, InstFundsSettleRulePO> {

    /**
     * 唯一性校验
     */
    public Boolean uniqueCheck(InstFundsSettleRulePO po){
        LambdaQueryWrapper<InstFundsSettleRulePO> wrapper = Wrappers.<InstFundsSettleRulePO>lambdaQuery()
                .eq(InstFundsSettleRulePO::getFundsAgreementNo, po.getFundsAgreementNo())
                .eq(InstFundsSettleRulePO::getClearingPattern, po.getClearingPattern())
                .eq(InstFundsSettleRulePO::getClearOffPattern, po.getClearOffPattern())
                .eq(InstFundsSettleRulePO::getClearOffType, po.getClearOffType())
                .eq(InstFundsSettleRulePO::getSettleCcy, po.getSettleCcy());
        return CollectionUtils.isEmpty(list(wrapper));
    }

    /**
     * 保存，忽略唯一约束重复
     */
    public boolean saveIgnoreDuplicate(InstFundsSettleRulePO po){
        try{
            save(po);
        } catch (DuplicateKeyException e) {
            log.warn("Duplicate entry", e);
        }
        return true;
    }

}
