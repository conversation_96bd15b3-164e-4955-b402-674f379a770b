package com.payermax.channel.inst.center.infrastructure.cache;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-20 11:46 PM
 */
@Slf4j
public abstract class AbstractLocalCacheManager implements LocalCacheManager {

    protected static final String CACHE_CONNECT = "_";

    public AbstractLocalCacheManager() {
        CacheRegistry.registry(getCacheName(), this);
    }

    @Override
    public void init() {
        log.info("Init cache start:[{}]", getCacheName().name());
        doInit();
        log.info("Init cache end:[{}]", getCacheName().name());
    }

    @Override
    public void refresh() {
        log.info("Refresh cache thread:[{}] start:[{}]", Thread.currentThread().getName(), getCacheName().name());
        doRefresh();
        log.info("Refresh cache thread:[{}] end:[{}]", Thread.currentThread().getName(), getCacheName().name());

    }

    public abstract void doInit();

    public abstract void doRefresh();

}
