package com.payermax.channel.inst.center.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.AccumulationResponsePO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 1:59 PM
 **/
@Mapper
public interface InstContractFeeItemMapper extends BaseMapper<InstContractFeeItemPO> {

    @Select("SELECT  fee.inst_origin_product_no,base.inst_product_type,transaction_country,accumulation_cycle,accumulation_type,accumulation_method,accumulation_range,accumulation_deduct_time,accumulation_join,accumulation_key,accumulation_mode,product.payment_method_type,base.inst_code,pay_currency,fee_config\n" +
            "from tb_inst_new_contract_fee_item as fee inner JOIN tb_inst_new_contract_standard_product as product\n" +
            "on fee.inst_origin_product_no=product.inst_origin_product_no\n" +
            "inner JOIN tb_inst_new_contract_base_info as base\n" +
            "on base.contract_no=product.contract_no\n" +
            "where accumulation_key is not null;")
    List<AccumulationResponsePO> queryAllAccumulation();

}
