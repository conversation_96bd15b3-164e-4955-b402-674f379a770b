package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_inst_sub_funds_account_bucket(机构子级资金账号预申请账号表)】的数据库操作Mapper
* @createDate 2022-10-20 13:34:48
* @Entity com.payermax.channel.inst.center.infrastructure.InstSubFundsAccountBucket
*/
public interface InstSubFundsAccountBucketDao {

    List<InstSubFundsAccountBucketEntity> selectByQueryEntityWithoutMerchantNo(InstSubFundsAccountBucketQueryEntity queryEntity);

    List<InstSubFundsAccountBucketEntity> selectBindMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);
    
    List<InstSubFundsAccountBucketEntity> selectBindSubMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);

    List<InstSubFundsAccountBucketEntity> selectUnboundMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);

    int updateBySubAccountId(InstSubFundsAccountBucketEntity record);

    int insert(InstSubFundsAccountBucketEntity record);

    InstSubFundsAccountBucketEntity selectByPrimaryKey(String subAccountId);

    int selectAssignableCountByAccountId(String accountId);

}
