package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_inst_base_ino")
public class InstBaseInfoEntity {
    @TableId(value = "inst_id",type = IdType.AUTO)
    private Long instId;

    private String instCode;

    private Long instBrandId;

    private String instName;

    private String instTypes;

    private String entityCountry;

    private String isFatfMember;

    private String amId;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}