package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractOriginProdCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractOriginProductMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/19 2:24 PM
 **/
@Slf4j
@Component
public class InstContractOriginProductRepository extends ServiceImpl<InstContractOriginProductMapper, InstContractOriginProductPO> {

    @Resource
    private InstContractFeeItemRepository instContractFeeItemRepository;

    @Resource
    private InstContractSettlementItemRepository instContractSettlementItemRepository;

    @Resource
    private InstNewContractOriginProdCacheManager instNewContractOriginProdCacheManager;

    /**
     * 组装原始机构产品，到版本合同-内容使用缓存
     */
    protected void composeOriginProductForContract(List<InstContractVersionInfoPO> contractVersionInfoPOList) {
        if (CollectionUtils.isEmpty(contractVersionInfoPOList)) {
            return;
        }

        contractVersionInfoPOList.forEach((contractVersionInfoPO) -> {

            List<InstContractOriginProductPO> originProductPOList = instNewContractOriginProdCacheManager
                    .queryOriginProductByContract(contractVersionInfoPO.getContractNo(), contractVersionInfoPO.getContractVersion());

            if (CollectionUtils.isNotEmpty(originProductPOList)) {
                // 组装费用和结算信息

                originProductPOList.forEach((originProductPO) -> {
                    // 1、组装费用条款信息
                    instContractFeeItemRepository.composeFeeItemForProduct(originProductPO);

                    // 2、组装结算条款信息
                    instContractSettlementItemRepository.composeSettleItemForProduct(originProductPO);
                });
            }

            contractVersionInfoPO.setOriginProducts(originProductPOList);
        });
    }


    /**
     * 此方法会在 InstContractVersionInfoRepository 中的编程式事务中调用
     */
    protected void batchSaveOrigProduct(List<InstContractOriginProductPO> originProductList) {

        // 持久化原始机构产品
        if (!CollectionUtils.isEmpty(originProductList)) {
            originProductList.forEach(item -> {
                if (item.isNewlyCreated()) {
                    save(item);
                }
            });
        }

        // 持久化费用条款
        List<InstContractFeeItemPO> contractFeeItems = originProductList.stream()
                .map(InstContractOriginProductPO::getContractFeeItems)
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(contractFeeItems)) {
            instContractFeeItemRepository.batchSaveContractFeeItem(contractFeeItems);
        } else {
            log.warn("机构合约 成本条款 为空!");
        }

        // 持久化结算条款
        List<InstContractSettlementItemPO> contractSettlementItems = originProductList.stream()
                .map(InstContractOriginProductPO::getSettlementItems)
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(contractSettlementItems)) {
            instContractSettlementItemRepository.batchSaveContractFeeItem(contractSettlementItems);
        } else {
            log.warn("机构合约 结算条款 为空!");
        }
    }

    public InstContractOriginProductPO queryExistOriginProduct(String contractNo, String contractVersion, String productName) {
        LambdaQueryWrapper<InstContractOriginProductPO> queryWrapper = Wrappers.<InstContractOriginProductPO>lambdaQuery()
                .eq(InstContractOriginProductPO::getInstProductName, productName)
                .eq(InstContractOriginProductPO::getContractVersion, contractVersion)
                .eq(InstContractOriginProductPO::getContractNo, contractNo);

        List<InstContractOriginProductPO> contractFeeItemPOS = list(queryWrapper);
        if (CollectionUtils.isEmpty(contractFeeItemPOS)) {
            return null;
        }
        // 数据库唯一性约束，只能有一个
        AssertUtil.isTrue(contractFeeItemPOS.size()==1, "ERROR","存在多个维度相同的原始产品");
        return contractFeeItemPOS.get(0); //CHECKED
    }

    public InstContractOriginProductPO queryByOriginProductNo(String originProductNo) {
        LambdaQueryWrapper<InstContractOriginProductPO> queryWrapper = Wrappers.<InstContractOriginProductPO>lambdaQuery()
                .eq(InstContractOriginProductPO::getInstOriginProductNo, originProductNo)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }
}
