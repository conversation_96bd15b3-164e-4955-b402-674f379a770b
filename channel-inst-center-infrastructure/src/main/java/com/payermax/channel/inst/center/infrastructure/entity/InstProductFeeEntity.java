package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstProductFeeEntity {
    private Long id;

    private String contractNo;

    private String instProductCode;

    private String instProductCapabilityCode;

    private String baseRate;

    private String rateTimeType;

    private String rateTime;

    private String baseRateSource;

    private String isUseNdf;

    private String ndfCurrency;

    private String hasAccessFee;

    private String accessFeeDetail;

    private String isChargeAfterward;

    private String chargeAfterwardDetail;

    private Date utcCreate;

    private Date utcModified;
}
