package com.payermax.channel.inst.center.infrastructure.repository.repo.ods;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.ods.OdsMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@DS("ods")
public class OdsRepository {
    @Resource
    private OdsMapper odsMapper;

    public List<InstBaseInfoEntity> queryAllInstFormOds() {
        return odsMapper.selectAllInstInfo();
    }

    public List<InstBrandEntity> queryAllInstBrandFormOds() {
        return odsMapper.selectAllInstBrandInfo();
    }

}
