package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName InstBaseInfoQueryEntity
 * @Description
 * <AUTHOR>
 * @Date 2022/8/15 19:44
 */
@Data
public class InstBaseInfoQueryEntity{
    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 机构品牌标识
     */
    private Long instBrandId;

    /**
     * 机构名称
     */
    private String instName;

    /**
     * 机构类型
     */
    private String instTypes;

    /**
     * 机构主体所在地
     */
    private String entityCountry;

    /**
     * 是否fatf成员国
     */
    private String isFatfMember;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;
    /**
     * 机构品牌编码
     */
    private String instBrandCode;
    /**
     * 机构品牌名称
     */
    private String instBrandName;
    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * BD负责人
     */
    private String bdId;

    /**
     * AM负责人
     */
    private String amId;

    /**
     * BD负责人
     */
    private List<String> bdList;

    /**
     * AM负责人
     */
    private List<String> amList;

    /**
     * BdAndAm
     */
    private String bdAndAmId;
}
