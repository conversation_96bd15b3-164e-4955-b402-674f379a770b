package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description 机构中心-金融日历-日历
 * <AUTHOR>
 * @date 2024-08-26
 */
@TableName(InstFinancialCalendarPO.TABLE_NAME)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstFinancialCalendarPO implements Serializable {

    public static final String TABLE_NAME = "tb_inst_financial_calendar";

    private static final long serialVersionUID = 1L;

    /**
     * 日历ID
     */
    @TableId(type = IdType.INPUT)
    private String calendarId;

    /**
     * 日历年份
     */
    private String calendarYear;

    /**
     * 日历类型，国家/币种/银行（国家&币种）
     */
    private String calendarType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 周末列表
     */
    private String weekendList;

    /**
     * 源日历（被引用日历）
     */
    private String sourceCalendar;

    /**
     * 目标日历（引用此日历的日历）
     */
    private String targetCalendar;

    /**
     * 生效状态
     */
    private String status;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 修改时间
     */
    private LocalDateTime utcModified;


}