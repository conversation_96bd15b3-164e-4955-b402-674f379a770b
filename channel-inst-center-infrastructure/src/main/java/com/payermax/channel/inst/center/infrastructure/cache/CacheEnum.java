package com.payermax.channel.inst.center.infrastructure.cache;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 11:44 PM
 */
public enum CacheEnum {

    /**
     * 渠道信息
     */
    CHANNEL_INFO,

    /**
     * 渠道支付方式
     */
    CHANNEL_METHOD,

    /**
     * 渠道商户
     */
    CHANNEL_MERCHANT,

    /**
     * 机构基本信息
     */
    INST_BASE_INFO,

    /**
     * 机构合同
     */
    INST_CONTRACT,

    /**
     * 合同-产品映射
     */
    INST_CONTRACT_PRODUCT,

    /**
     * 合同-结算配置
     */
    INST_CONTRACT_PRODUCT_SETTLE,

    /**
     * 机构产品
     */
    INST_PRODUCT,

    /**
     * 机构产品能力
     */
    INST_PRODUCT_CAPABILITY,

    /**
     * 机构产品费
     */
    INST_PRODUCT_FEE,


    /**
     * 机构合同新版和MID的映射
     */
    INST_NEW_CONTRACT_MID_MAPPING,

    /**
     * 机构合同新版_标准产品
     */
    INST_NEW_CONTRACT_STANDARD_PROD,

    /**
     * 机构合同新版_原始产品
     */
    INST_NEW_CONTRACT_ORIGIN_PROD,

    /**
     * 机构合同新版_费用
     */
    INST_NEW_CONTRACT_FEE,

    /**
     * 机构合同新版_结算
     */
    INST_NEW_CONTRACT_SETTLE,

    /**
     * 机构合同基本信息
     */
    INST_NEW_CONTRACT_BASE_INFO,

    /**
     * 机构合同新版---这个一定要放在最后,他会组装所有的标准产品、原始产品、费用和结算条款
     */
    INST_NEW_CONTRACT,

    ;
}
