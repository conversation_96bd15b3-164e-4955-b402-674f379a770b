package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstProductCapabilityQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductCapabilityDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 3:21 PM
 */
@Service
@Slf4j
public class InstProductCapabilityCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstProductCapabilityDao instProductCapabilityDao;

    private Map<String, List<InstProductCapabilityEntity>> CACHE_MAP = new ConcurrentHashMap<>();

    private Map<String, InstProductCapabilityEntity> CAPABILITY_CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<InstProductCapabilityEntity> capabilityEntities = instProductCapabilityDao
                .selectAll(new InstProductCapabilityQueryEntity());

        capabilityEntities.forEach(item -> {
            List<InstProductCapabilityEntity> entities = CACHE_MAP.computeIfAbsent(item.getInstProductCode(), a -> new ArrayList<>());
            entities.add(item);
        });

        CAPABILITY_CACHE_MAP = capabilityEntities.stream().collect(Collectors.toMap(InstProductCapabilityEntity::getCapabilityCode, Function.identity()));
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstProductCapabilityEntity>> temp = new ConcurrentHashMap<>();

        List<InstProductCapabilityEntity> capabilityEntities = instProductCapabilityDao
                .selectAll(new InstProductCapabilityQueryEntity());
        capabilityEntities.forEach(item -> {
            List<InstProductCapabilityEntity> entities = temp.computeIfAbsent(item.getInstProductCode(), a -> new ArrayList<>());
            entities.add(item);
        });
        Map<String, InstProductCapabilityEntity> map = capabilityEntities.stream().collect(Collectors.toMap(InstProductCapabilityEntity::getCapabilityCode, Function.identity()));
        CACHE_MAP = temp;
        CAPABILITY_CACHE_MAP = map;
    }

    public List<InstProductCapabilityEntity> getProductCapabilityByProductCodes(List<String> productCodes) {
        List<InstProductCapabilityEntity> response = new ArrayList<>(productCodes.size());
        productCodes.forEach(item -> {
            List<InstProductCapabilityEntity> capabilityEntity = CACHE_MAP.get(item);
            if (CollectionUtils.isNotEmpty(capabilityEntity)) {
                response.addAll(capabilityEntity);
            }
        });
        return response;
    }

    public List<InstProductCapabilityEntity> getProductCapabilityByCapabilityCodes(List<String> capabilityCodes) {
        return capabilityCodes.stream().map(CAPABILITY_CACHE_MAP::get).collect(Collectors.toList());
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_PRODUCT_CAPABILITY;
    }
}
