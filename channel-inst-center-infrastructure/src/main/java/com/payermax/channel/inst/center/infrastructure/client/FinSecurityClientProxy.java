package com.payermax.channel.inst.center.infrastructure.client;

import com.payermax.channel.inst.center.common.enums.OmcEnum;
import com.payermax.channel.inst.center.common.exception.CustomException;
import com.payermax.common.lang.model.dto.Result;
import com.ushareit.fintech.security.api.SecurityService;
import com.ushareit.fintech.security.entity.req.EncryptRequest;
import com.ushareit.fintech.security.entity.req.QuerySecurityContentRequest;
import com.ushareit.fintech.security.entity.resp.EncryptResponse;
import com.ushareit.fintech.security.entity.resp.SecurityResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName FinSecurityClientProxy
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 16:45
 */
@Component
@Slf4j
public class FinSecurityClientProxy {
    @DubboReference
    private SecurityService securityService;

    /**
     * 加密
     *
     * @param encryptReq
     * @return
     */
    public EncryptResponse encrypt(EncryptRequest encryptReq) {
        if (encryptReq == null) {
            return new EncryptResponse();
        }
        try {
            Result<EncryptResponse> encryptResult = securityService.encrypt(encryptReq);
            if (encryptResult == null) {
                return new EncryptResponse();
            }
            if (!encryptResult.isSuccess()) {
                throw new CustomException(encryptResult.getCode(), encryptResult.getMsg());
            }
            EncryptResponse encryptResponse = encryptResult.getData();
            return encryptResponse;
        } catch (CustomException e) {
            throw new CustomException(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("call security service encrypt error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "encrypt error");
        }
    }

    /**
     * 批量加密
     *
     * @param encryptReqList
     * @return
     */
    public List<EncryptResponse> encryptBatch(List<EncryptRequest> encryptReqList) {
        if (CollectionUtils.isEmpty(encryptReqList)) {
            return Collections.emptyList();
        }
        try {
            Result<List<EncryptResponse>> batchEncryptResult = securityService.batchEncrypt(encryptReqList);
            if (batchEncryptResult == null) {
                return Collections.emptyList();
            }
            if (!batchEncryptResult.isSuccess()) {
                throw new CustomException(batchEncryptResult.getCode(), batchEncryptResult.getMsg());
            }
            List<EncryptResponse> encryptResponse = batchEncryptResult.getData();
            return encryptResponse;
        } catch (CustomException e) {
            throw new CustomException(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("call security service encrypt error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "encrypt error");
        }
    }

    /**
     * 解密
     *
     * @param token
     * @return
     */
    public SecurityResponse decrypt(String token) {
        if (StringUtils.isBlank(token)) {
            return new SecurityResponse();
        }
        try {
            QuerySecurityContentRequest request = new QuerySecurityContentRequest();
            request.setToken(token);
            request.setNeedDecrypt(Boolean.TRUE);
            Result<SecurityResponse> queryResult = securityService.query(request);
            if (queryResult == null) {
                return new SecurityResponse();
            }
            if (!queryResult.isSuccess()) {
                throw new CustomException(queryResult.getCode(), queryResult.getMsg());
            }
            SecurityResponse data = queryResult.getData();
            return data;
        } catch (CustomException e) {
            throw new CustomException(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("call security service decrypt error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "decrypt error");
        }
    }

    /**
     * 批量解密
     *
     * @param tokens
     * @return
     */
    public List<SecurityResponse> decryptBatch(List<String> tokens) {
        if (CollectionUtils.isEmpty(tokens)) {
            return Collections.emptyList();
        }
        try {
            List<QuerySecurityContentRequest> decryptRequest = tokens.stream().map(token -> {
                QuerySecurityContentRequest request = new QuerySecurityContentRequest();
                request.setToken(token);
                request.setNeedDecrypt(Boolean.TRUE);
                return request;
            }).collect(Collectors.toList());
            Result<List<SecurityResponse>> queryResult = securityService.batchQuery(decryptRequest);
            if (queryResult == null) {
                return Collections.emptyList();
            }
            if (!queryResult.isSuccess()) {
                throw new CustomException(queryResult.getCode(), queryResult.getMsg());
            }
            List<SecurityResponse> decryptResponse = queryResult.getData();
            return decryptResponse;
        } catch (CustomException e) {
            throw new CustomException(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("call security service decrypt error", e);
            throw new CustomException(OmcEnum.SysEnum.FEIGN_ERROR.getCode(), "decrypt error");
        }
    }
}
