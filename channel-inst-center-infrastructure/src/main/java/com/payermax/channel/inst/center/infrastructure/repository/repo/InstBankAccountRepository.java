package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstBankAccountMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/26
 * @DESC
 */
@Component
public class InstBankAccountRepository extends ServiceImpl<InstBankAccountMapper, InstBankAccountPO> {



    @Resource
    InstBankAccountMapper mapper;

    /**
     * 根据账户别名查询账户信息
     *
     * @param accountAlias 账户别名
     */
    public List<InstBankAccountPO> queryDetailByAccountAlias(String accountAlias) {
        LambdaQueryWrapper<InstBankAccountPO> queryWrapper =  Wrappers.<InstBankAccountPO>lambdaQuery()
                .like(InstBankAccountPO::getAccountName, accountAlias);
        return mapper.selectList(queryWrapper);
    }

    /**
     * 根据条件查询
     */
    public List<InstBankAccountPO> queryByConditions(InstBankAccountPO po){

        LambdaQueryWrapper<InstBankAccountPO> queryWrapper =  Wrappers.<InstBankAccountPO>lambdaQuery()
                .eq(Objects.nonNull(po.getInstId()), InstBankAccountPO::getInstId, po.getInstId());

        return list(queryWrapper);
    }


}
