package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 银行账户表
 *
 * @TableName tb_inst_bank_account
 */
@TableName(value ="tb_inst_bank_account")
@Data
public class InstBankAccountPO implements Serializable {

    /**
     * 账户标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 账户开户所在地
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开户银行
     */
    private String bankCode;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 开户名称
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String accountNo;

    /**
     * 分支行
     */
    private String branch;

    /**
     * 分支行地址
     */
    private String branchAddress;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * iban
     */
    private String iban;

    /**
     * 账户用途
     */
    private String accountUse;

    /**
     * 我方渠道充值场景下，可充值的币种列表
     */
    private String rechargeCanUseCcy;

    /**
     * 我方渠道充值场景下，当前账户的优先级
     */
    private String rechargeUseOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 Y:启用 N:停用
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 更新时间
     */
    private LocalDateTime utcModified;

}
