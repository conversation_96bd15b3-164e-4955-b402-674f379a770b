package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

@Data
public class InstProductCapabilityEntity {
    private String capabilityCode;

    private String instProductCode;

    private String targetOrg;

    private String cardOrg;

    private String country;

    private String currency;

    private String customerType;

    private String amountMultipleLimit;

    private String amountSingleLimit;

    private String amountDayLimit;

    private String amountMonthLimit;

    private String paymentTool;

    private String paymentFlow;

    private String availableTime;

    private Date utcCreate;

    private Date utcModified;

    private String settlementExtraInfo;

    private String extraInfo;

    private String version;
}
