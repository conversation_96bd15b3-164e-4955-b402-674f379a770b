package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFinancialCalendarMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Component
public class InstFinancialCalendarRepository extends ServiceImpl<InstFinancialCalendarMapper, InstFinancialCalendarPO> {

    /**
     * 根据条件查询日历列表并分页
     */
    public Page<InstFinancialCalendarPO> queryByConditionsPaging(InstFinancialCalendarPO po,  int pageNum, int pageSize) {
        LambdaQueryWrapper<InstFinancialCalendarPO> queryWrapper = Wrappers.<InstFinancialCalendarPO>lambdaQuery()
                .eq(Objects.nonNull(po.getCalendarId()), InstFinancialCalendarPO::getCalendarId, po.getCalendarId())
                .eq(StringUtils.isNotBlank(po.getCalendarYear()), InstFinancialCalendarPO::getCalendarYear, po.getCalendarYear())
                .eq(StringUtils.isNotBlank(po.getCountry()), InstFinancialCalendarPO::getCountry, po.getCountry())
                .eq(StringUtils.isNotBlank(po.getCurrency()), InstFinancialCalendarPO::getCurrency, po.getCurrency())
                .eq(StringUtils.isNotBlank(po.getOwner()), InstFinancialCalendarPO::getOwner, po.getOwner());
        queryWrapper.orderByDesc(InstFinancialCalendarPO::getCalendarYear);
        Page<InstFinancialCalendarPO> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据条件查询日历列表
     */
    public List<InstFinancialCalendarPO> queryByConditions(InstFinancialCalendarPO po) {
        LambdaQueryWrapper<InstFinancialCalendarPO> queryWrapper = Wrappers.<InstFinancialCalendarPO>lambdaQuery()
                .eq(Objects.nonNull(po.getCalendarId()), InstFinancialCalendarPO::getCalendarId, po.getCalendarId())
                .eq(StringUtils.isNotBlank(po.getCalendarYear()), InstFinancialCalendarPO::getCalendarYear, po.getCalendarYear())
                .eq(StringUtils.isNotBlank(po.getCountry()), InstFinancialCalendarPO::getCountry, po.getCountry())
                .eq(StringUtils.isNotBlank(po.getCurrency()), InstFinancialCalendarPO::getCurrency, po.getCurrency())
                .eq(StringUtils.isNotBlank(po.getOwner()), InstFinancialCalendarPO::getOwner, po.getOwner());
        return list(queryWrapper);
    }

    /**
     * 根据 id 列表查询日历列表
     */
    public List<InstFinancialCalendarPO> queryByCalendarIds(List<String> calendarIds) {
        if(CollectionUtils.isEmpty(calendarIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InstFinancialCalendarPO> queryWrapper = Wrappers.<InstFinancialCalendarPO>lambdaQuery()
                .in(InstFinancialCalendarPO::getCalendarId, calendarIds);
        return list(queryWrapper);
    }
}
