package com.payermax.channel.inst.center.infrastructure.repository.po;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 业务协议
 */
@Data
@TableName(value ="tb_inst_biz_agreement")
@Accessors(chain = true)
public class InstBizAgreementPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 4-2-1-业务协议单号
     */
    @TableId(value = "biz_agreement_no", type = IdType.INPUT)
    private String bizAgreementNo;

    /**
     * 4-2-1-协议名称
     */
    private String name;

    /**
     * 4-2-1-协议类型
     */
    private String type;

    /**
     * 4-2-1-协议发起方
     */
    private String initiator;

    /**
     * 4-2-1-协议对手方
     */
    private String counter;

    /**
     * 4-2-1-机构合约号-外部机构协议时不为空
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractNo;

    /**
     * 0-0-创建时间
     */
    private Date utcCreate;

    /**
     * 0-0-更新时间
     */
    private Date utcModified;

    /**
     * 是否新创建的，辅助数据库操作
     */
    @TableField(exist = false)
    private boolean newlyCreated;

}

