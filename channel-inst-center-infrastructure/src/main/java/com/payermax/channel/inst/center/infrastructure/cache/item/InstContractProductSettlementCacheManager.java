package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductSettlementEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContractProductSettlementDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2022-10-21 3:07 PM
 */
@Service
@Slf4j
public class InstContractProductSettlementCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractProductSettlementDao instContractProductSettlementDao;

    private static Map<String, List<InstContractProductSettlementEntity>> CONTRACT_PRODUCT_CACHE_MAP = new ConcurrentHashMap<>();
    private static Map<String, InstContractProductSettlementEntity> CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        this.initData();
    }

    @Override
    public void doRefresh() {
        this.initData();
    }

    private void initData() {
        List<InstContractProductSettlementEntity> productSettlementEntities = instContractProductSettlementDao.selectAll(new InstContractProductSettlementEntity());
        Map<String, List<InstContractProductSettlementEntity>> temp = productSettlementEntities.stream().collect(Collectors.groupingBy(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode())));
        Map<String, InstContractProductSettlementEntity> map = productSettlementEntities.stream().collect(Collectors.toMap(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode(), c.getInstProductCapabilityCode()), Function.identity()));
        CONTRACT_PRODUCT_CACHE_MAP = temp;
        CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP = map;
    }

    public List<InstContractProductSettlementEntity> getSettlementInfoByContractNoAndProductCode(String contractNo,String productCode) {
        return CONTRACT_PRODUCT_CACHE_MAP.get(Joiner.on("_").join(contractNo,productCode));
    }

    public InstContractProductSettlementEntity getSettlementInfoByContractNoAndProductCodeAndCapabilityCode(String contractNo,String productCode,String capabilityCode) {
        return CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP.get(Joiner.on("_").join(contractNo,productCode,capabilityCode));
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_CONTRACT_PRODUCT_SETTLE;
    }
}
