package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstAccountDao {

    int insert(InstAccountEntity record);

    int delete(@Param("requirementOrderId") Long requirementOrderId,@Param("instId") Long instId,@Param("env") String env,@Param("ids") List<Long> ids);

    List<InstAccountEntity> selectAll(InstAccountEntity queryEntity);

    int updateByPrimaryKey(InstAccountEntity record);
}