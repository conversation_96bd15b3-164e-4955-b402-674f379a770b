package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.ChannelMerchantEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/7 14:07
 */
@Mapper
public interface ChannelMerchantDao {

    /**
     * 根据条件查询渠道商户信息*
     * *
     * @param channelMerchantEntity
     * @return
     */
    List<ChannelMerchantEntity> selectAll(ChannelMerchantEntity channelMerchantEntity);
}
