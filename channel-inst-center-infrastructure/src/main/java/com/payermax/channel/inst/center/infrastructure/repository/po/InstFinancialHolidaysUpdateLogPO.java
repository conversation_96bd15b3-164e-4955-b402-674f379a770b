package com.payermax.channel.inst.center.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 机构中心-金融日历-节假日
 * @date 2024-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(InstFinancialHolidaysUpdateLogPO.TABLE_NAME)
public class InstFinancialHolidaysUpdateLogPO implements Serializable {

    public static final String TABLE_NAME = "tb_inst_financial_holiday_update_log";


    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * holidayDate
     */
    private String holidayDate;

    /**
     * 节假日名字
     */
    private String holidayName;

    /**
     * 原始信息
     */
    private String originInfo;

    /**
     * 修改后的信息
     */
    private String latestInfo;

    /**
     * 日历类型
     */
    private String calendarType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 创建时间
     */
    private Date utcCreate;

    /**
     * 更新时间
     */
    private Date utcModified;
}
