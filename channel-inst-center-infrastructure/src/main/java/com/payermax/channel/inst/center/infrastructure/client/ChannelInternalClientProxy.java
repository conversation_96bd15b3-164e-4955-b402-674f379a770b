package com.payermax.channel.inst.center.infrastructure.client;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.ushareit.fintech.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 渠道内部调用代理类
 *
 * <AUTHOR>
 * @date 2021/8/3 16:08
 */
@Slf4j
@Component
public class ChannelInternalClientProxy {

    @Autowired
    protected ChannelGatewayClientProxy channelGatewayClientProxy;

    /**
     * 请求下游并解析响应
     *
     * @param exchangeRequest
     * @param api
     * @param version
     * @param instBrand
     * @param instMerchantCode
     * @param responseClazz
     * @return
     */
    public Result sendRequest(String exchangeRequest, String api, String version, String instBrand, String instMerchantCode, Class responseClazz) {
        Result<String> response = this.invokeInterface(exchangeRequest, api, version, instBrand, instMerchantCode);
        return this.parseInternalResponse(response, responseClazz);
    }

    /**
     * 调用新渠道网关下游接口
     *
     * @param request
     * @param api
     * @param version
     * @param instBrand
     * @param instMerchantCode
     * @return
     */
    private Result<String> invokeInterface(String request, String api, String version, String instBrand, String instMerchantCode) {
        Result<String> internalResultDTO;
        try {
            // 发起调用
            internalResultDTO = channelGatewayClientProxy.invoke(request, api, version, instBrand, instMerchantCode);
        } catch (Throwable e) {
            log.error("【申请开户】通用网关查询请求出现异常, Exception: ", e);
            // 响应异常的处理
            internalResultDTO = new Result<>();
            if (e instanceof BusinessException) {
                BusinessException baseException = (BusinessException) e;
                internalResultDTO.setCode(baseException.getErrCode());
            } else {
                internalResultDTO.setCode(ErrorCodeEnum.INNER_ERROR.getCode());
            }
            internalResultDTO.setMsg(e.getMessage());
        }
        return internalResultDTO;
    }

    /**
     * 解析下游响应
     *
     * @param response
     * @param clazz
     * @return
     */
    public Result parseInternalResponse(Result<String> response, Class clazz) {
        try {
            if (StringUtils.isNotBlank(response.getData())) {
                return new Result<>(JsonUtils.toBean(clazz, response.getData()), response.getMsg(), response.getCode());
            }
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.JSON_FORMAT_ERROR.getCode(), e.getMessage(), e);
        }
        return response;
    }

}
