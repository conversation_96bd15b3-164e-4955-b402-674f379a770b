package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity;

import java.util.List;


/**
 * NDA签订单Mapper
 *
 * <AUTHOR>
 * @date 2022/5/13 13:36
 */
public interface InstNdaDao {
    /**
     * 查询记录
     *
     * @param instNdaEntity
     * @return
     */
    List<InstNdaEntity> selectAll(InstNdaEntity instNdaEntity);

    /**
     * 插入记录
     *
     * @param instNdaEntity
     * @return
     */
    int insert(InstNdaEntity instNdaEntity);

    /**
     * 更新记录
     *
     * @param instNdaEntity
     * @return
     */
    int update(InstNdaEntity instNdaEntity);
}
