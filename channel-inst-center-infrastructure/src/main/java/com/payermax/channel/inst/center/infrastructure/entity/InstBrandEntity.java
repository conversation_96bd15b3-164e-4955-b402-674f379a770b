package com.payermax.channel.inst.center.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_inst_brand")
public class InstBrandEntity {
    @TableId(value = "brand_id",type = IdType.AUTO)
    private Long brandId;

    private String brandCode;

    private String brandName;

    private String bdId;

    private String bdName;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}