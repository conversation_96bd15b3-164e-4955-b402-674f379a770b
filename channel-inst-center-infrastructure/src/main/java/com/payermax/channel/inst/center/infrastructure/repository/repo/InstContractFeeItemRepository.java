package com.payermax.channel.inst.center.infrastructure.repository.repo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractFeeItemCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractFeeItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 3:17 PM
 **/
@Component
public class InstContractFeeItemRepository extends ServiceImpl<InstContractFeeItemMapper, InstContractFeeItemPO> {

    @Resource
    private InstNewContractFeeItemCacheManager instNewContractFeeItemCacheManager;

    @Resource
    private InstContractFeeItemMapper feeItemMapper;

    protected void composeFeeItemForProduct(InstContractOriginProductPO contractOriginProductPO) {

        List<InstContractFeeItemPO> contractFeeItemPOS = instNewContractFeeItemCacheManager
                .queryFeeItemByOriginProductNo(contractOriginProductPO.getInstOriginProductNo());

        contractOriginProductPO.setContractFeeItems(contractFeeItemPOS);
    }


    protected void batchSaveContractFeeItem(List<InstContractFeeItemPO> contractFeeItems) {
        saveBatch(contractFeeItems);
    }

    /**
     * 根据费用编号列表查询
     */
    public List<InstContractFeeItemPO> queryByNoList(List<String> noList) {
        LambdaQueryWrapper<InstContractFeeItemPO> queryWrapper = Wrappers.<InstContractFeeItemPO>lambdaQuery()
                .in(InstContractFeeItemPO::getInstContractFeeItemNo, noList);
        return feeItemMapper.selectList(queryWrapper);
    }

    /**
     * 根据原始产品编号查询费用信息
     */
    public List<InstContractFeeItemPO> queryByOriginProductNo(String originProductNo) {
        LambdaQueryWrapper<InstContractFeeItemPO> queryWrapper = Wrappers.<InstContractFeeItemPO>lambdaQuery()
                .eq(InstContractFeeItemPO::getInstOriginProductNo, originProductNo);
        return feeItemMapper.selectList(queryWrapper);
    }

    /**
     * 根据 FeeItem 查询
     */
    public InstContractFeeItemPO queryByFeeItemNo(String feeItemNo) {
        LambdaQueryWrapper<InstContractFeeItemPO> queryWrapper = Wrappers.<InstContractFeeItemPO>lambdaQuery()
                .eq(InstContractFeeItemPO::getInstContractFeeItemNo, feeItemNo)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    /**
     * 修改时校验费用是否符合唯一性约束
     */
    public Boolean checkFeeModifiedUnique(String feeItemNo, String businessKey){
        LambdaQueryWrapper<InstContractFeeItemPO> queryWrapper = Wrappers.<InstContractFeeItemPO>lambdaQuery()
                .eq(InstContractFeeItemPO::getFeeBusinessKey, businessKey)
                .ne(InstContractFeeItemPO::getInstContractFeeItemNo, feeItemNo);
        return count(queryWrapper) == 0;
    }

    /**
     * 新增时校验费用是否符合唯一性约束
     */
    public Boolean checkFeeAddUnique(String businessKey){
        LambdaQueryWrapper<InstContractFeeItemPO> queryWrapper = Wrappers.<InstContractFeeItemPO>lambdaQuery()
                .eq(InstContractFeeItemPO::getFeeBusinessKey, businessKey);
        return count(queryWrapper) == 0;
    }

    /**
     * 更新时检查，枚举类型为空时使用 null
     */
    public Boolean updateWithCheck(InstContractFeeItemPO feeItem){
        LambdaUpdateWrapper<InstContractFeeItemPO> wrapper = Wrappers.<InstContractFeeItemPO>lambdaUpdate()
                .eq(InstContractFeeItemPO::getInstContractFeeItemNo, feeItem.getInstContractFeeItemNo());
        // 枚举类型数据当为空时，使用 null 替代
        if(StringUtil.isBlank(feeItem.getMccLogic())){
            wrapper.set(InstContractFeeItemPO::getMccLogic, null);
        }
        if(StringUtil.isBlank(feeItem.getRoundingMode())){
            wrapper.set(InstContractFeeItemPO::getRoundingMode, null);
        }
        if(StringUtil.isBlank(feeItem.getAccumulationCycle())){
            wrapper.set(InstContractFeeItemPO::getAccumulationCycle, null);
        }
        if(StringUtil.isBlank(feeItem.getAccumulationType())){
            wrapper.set(InstContractFeeItemPO::getAccumulationType, null);
        }
        if(StringUtil.isBlank(feeItem.getAccumulationMethod())){
            wrapper.set(InstContractFeeItemPO::getAccumulationMethod, null);
        }
        if(StringUtil.isBlank(feeItem.getAccumulationRange())){
            wrapper.set(InstContractFeeItemPO::getAccumulationRange, null);
        }
        if(StringUtil.isBlank(feeItem.getAccumulationDeductTime())){
            wrapper.set(InstContractFeeItemPO::getAccumulationDeductTime, null);
        }
        return update(feeItem, wrapper);
    }
}
