package com.payermax.channel.inst.center.app.dto.common;

import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.*;

/**
 * <AUTHOR> at 2023/6/11 8:53 AM
 **/
@Slf4j
public class ValidatorUtils {

    /**
     * 会校验完所有字段，failFast = false
     */
    private static final Validator validatorAll = Validation.byProvider(HibernateValidator.class).configure().failFast(false)
            .buildValidatorFactory().getValidator();

    /**
     * 发起对excel sheet内容校验
     * @return ExportErrorInfo，
     *         如果无异常，ExportErrorInfo.hasErrors == false
     *         如果存在异常，ExportErrorInfo.hasErrors == true，且exportErrorInfoItems中包含具体错误信息
     */
    public static <T extends ExcelUtil.BaseExcelRow> ExportErrorInfo validateAll(ExcelUtil.ExcelParseInfo<T> excelParseInfo) {
        ExportErrorInfo errorInfo = new ExportErrorInfo();
        if (excelParseInfo == null || CollectionUtils.isEmpty(excelParseInfo.getDataList())) {
            return errorInfo;
        }

        errorInfo.setExportErrorInfoItems(new ArrayList<>());
        excelParseInfo.getDataList().forEach(item -> {
            List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItem = validateItemAll(item, excelParseInfo.getHeaderMap());
            if(CollectionUtils.isNotEmpty(errorInfoItem)) {
                errorInfo.getExportErrorInfoItems().addAll(errorInfoItem);
            }
        });
        if(CollectionUtils.isNotEmpty(errorInfo.getExportErrorInfoItems())) {
            errorInfo.setHasErrors(Boolean.TRUE);
        }

        return errorInfo;
    }

    /**
     * 校验所有字段并返回不合法字段
     */
    public static <T extends ExcelUtil.BaseExcelRow> List<ExportErrorInfo.ExportErrorInfoItem> validateItemAll(T domain, Map<String, ExcelUtil.ExcelParseInfo.ExcelHeader> headerMap) {
        Set<ConstraintViolation<T>> validateResult = validatorAll.validate(domain);
        if(CollectionUtils.isEmpty(validateResult)) {
            return Collections.emptyList();
        }
        List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItemList = new ArrayList<>();
        ExportErrorInfo.ExportErrorInfoItem error;
        Iterator<ConstraintViolation<T>> it = validateResult.iterator();
        ExcelUtil.ExcelParseInfo.ExcelHeader excelHeader;
        while(it.hasNext()) {
            error = new ExportErrorInfo.ExportErrorInfoItem();
            ConstraintViolation<T> cv = it.next();
            excelHeader = headerMap.get(cv.getPropertyPath().toString());
            if(Objects.isNull(excelHeader)) {
                log.warn("fail to get header name, className[{}],fieldName[{}]", domain.getClass(), cv.getPropertyPath());
            }else {
                error.setColumnNo(excelHeader.getColumnNo());
                error.setColumnName(excelHeader.getName());
            }
            error.setRowNo(domain.getExcelRowNo());
            error.setCellValue(cv.getInvalidValue());
            error.setErrorMsg(cv.getMessage());
            errorInfoItemList.add(error);
        }

        return errorInfoItemList;
    }

    public static <T> Set<ConstraintViolation<T>> valid(T domain) {
        if(Objects.isNull(domain)) {
            return Collections.emptySet();
        }
        return validatorAll.validate(domain);
    }
}
