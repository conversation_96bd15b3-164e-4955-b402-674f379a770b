package com.payermax.channel.inst.center.app.model.contract.dataParser;

import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Getter
@Setter
public class InstProductParseContext{


    /**
     * 草稿信息
     */
    InstContractDraft draft;

    /**
     * 产品信息
     */
    AbstractInstContractFeeDTO draftDataDto;

    /**
     * 合同基础信息
     */
    InstContractBaseInfoPO instContractBaseInfo;

    /**
     * 合同版本信息
     */
    InstContractVersionInfoPO instContractVersionInfo;

    /**
     * 解析后携带feeItemNo、mcc 等信息的费用信息
     */
    List<InstContractFeeItemPO> feeItemList;


    /**
     * 解析的原始费用信息
     */
    InstContractFeeItemPO originFeeItem;

    /**
     * 原始产品信息
     */
    InstContractOriginProductPO originProduct;

    /**
     * 标准化产品信息
     */
    List<InstContractStandardProductPO> standardProductList;


    /**
     * 结算信息
     */
    InstContractSettlementItemPO  settlementItem;

    /**
     * 校验异常信息
     */
    Map<String, String> errorInfoMap;
}
