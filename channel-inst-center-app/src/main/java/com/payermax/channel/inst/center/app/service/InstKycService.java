package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity;

/**
 * KYC service
 *
 * <AUTHOR>
 * @date 2022/5/15 22:44
 */
public interface InstKycService {

    /**
     * 保存KYC
     *
     * @param record
     * @return
     */
    int save(InstKycEntity record);

    /**
     * 查询KYC
     *
     * @param instId
     * @return
     */
    InstKycEntity getByInstId(Long instId);

}
