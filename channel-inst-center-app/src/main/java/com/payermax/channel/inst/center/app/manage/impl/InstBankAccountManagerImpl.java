package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstBaseInfoService;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.fin.operating.log.dto.WriteLogDto;
import com.payermax.fin.operating.log.enums.OperatingTypeEnum;
import com.payermax.fin.operating.log.util.LogUserThreadLocal;
import com.payermax.channel.inst.center.common.constants.DictConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.StatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity;
import com.payermax.channel.inst.center.infrastructure.client.FinOperatingLogClient;
import com.payermax.channel.inst.center.app.manage.InstBankAccountManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.app.service.InstBankAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * @ClassName InstBankAccountManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 15:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstBankAccountManagerImpl implements InstBankAccountManager {

    @Value("${spring.application.name}")
    private String appId;

    @Autowired
    private FinOperatingLogClient finOperatingLogClient;

    @Autowired
    private InstBankAccountService instBankAccountService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Resource
    private InstBaseInfoService instBaseInfoService;

    @Override
    public List<InstBankAccountVO> queryByCondition(InstBankAccountReqDTO instBankAccountReqDTO) {
        InstBankAccountEntity queryCondition = new InstBankAccountEntity();
        queryCondition.setAccountUse(instBankAccountReqDTO.getAccountUse());

        // 1、先根据机构code找到配置id
        if (StringUtils.isNotBlank(instBankAccountReqDTO.getInstCode())) {
            String instCode = instBankAccountReqDTO.getInstCode();
            InstBaseInfoEntity instBaseInfoEntity = instBaseInfoService.queryByInstCode(instCode);
            queryCondition.setInstId(instBaseInfoEntity.getInstId());
        }

        // 2、查询
        List<InstBankAccountEntity> instBankAccountList = instBankAccountService.queryByCondition(queryCondition);

        return convertToQueryVoList(instBankAccountReqDTO, instBankAccountList);
    }


    @Override
    public int save(InstBankAccountReqDTO instBankAccountReqDTO) {
        this.recordOperatingLog(instBankAccountReqDTO);
        //请求转换
        InstBankAccountEntity instBankAccountEntity = reqDtoAssembler.toInstBankAccountEntity(instBankAccountReqDTO);
        return instBankAccountService.save(instBankAccountEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<InstBankAccountReqDTO> instBankAccountReqDTOs) {
        AssertUtil.isTrue(false, "ERROR", "方法已废弃，不可调用");
        return 0;
        //请求转换
        // List<InstBankAccountEntity> instBankAccountEntities = reqDtoAssembler.toInstBankAccountEntities(instBankAccountReqDTOs);
        // List<Long> ids = instBankAccountEntities.stream().filter(obj -> obj.getId() != null).map(InstBankAccountEntity::getId).collect(Collectors.toList());
        // if (CollectionUtils.isNotEmpty(ids)) {
        //     InstBankAccountEntity instBankAccountEntity = new InstBankAccountEntity();
        //     instBankAccountEntity.setInstId(instBankAccountReqDTOs.get(0).getInstId());  //NO_CHECK 方法未被调用
        //     List<InstBankAccountEntity> instBankAccountEntityList = instBankAccountService.queryByCondition(instBankAccountEntity);
        //     List<InstBankAccountEntity> deleteInstBankAccountList = instBankAccountEntityList.stream().filter(obj -> !ids.contains(obj.getId())).map(obj -> {
        //         obj.setStatus(StatusEnum.N.getValue());
        //         return obj;
        //     }).collect(Collectors.toList());
        //     if (CollectionUtils.isNotEmpty(deleteInstBankAccountList)) {
        //         instBankAccountEntities.addAll(deleteInstBankAccountList);
        //     }
        // }
        // AtomicInteger result = new AtomicInteger(0);
        // instBankAccountEntities.forEach(instBankAccountEntity -> {
        //     int count = instBankAccountService.save(instBankAccountEntity);
        //     result.addAndGet(count);
        // });
        // return result.get();
    }

    @Override
    public int delete(InstBankAccountReqDTO instBankAccountReqDTO) {
        Preconditions.checkArgument(instBankAccountReqDTO.getInstId() != null, "instId is mandatory");
        InstBankAccountEntity instBankAccountEntity = reqDtoAssembler.toInstBankAccountEntity(instBankAccountReqDTO);
        List<InstBankAccountEntity> instBankAccountEntities = instBankAccountService.queryByCondition(instBankAccountEntity);
        AtomicInteger result = new AtomicInteger(0);
        if (CollectionUtils.isNotEmpty(instBankAccountEntities)) {
            instBankAccountEntities = instBankAccountEntities.stream().map(obj -> {
                obj.setStatus(StatusEnum.N.getValue());
                return obj;
            }).collect(Collectors.toList());

            instBankAccountEntities.forEach(obj -> {
                int count = instBankAccountService.save(obj);
                result.addAndGet(count);
            });
        }
        return result.get();
    }

    @Async
    public void recordOperatingLog(InstBankAccountReqDTO instBankAccountReqDTO) {
        try {
            WriteLogDto writeLogDto = new WriteLogDto();
            writeLogDto.setUserId(LogUserThreadLocal.getUserId());
            writeLogDto.setAppId(appId);
            writeLogDto.setAppName(appId);
            writeLogDto.setLinkId(instBankAccountReqDTO.getInstId() != null ? String.valueOf(instBankAccountReqDTO.getInstId()) : String.valueOf(instBankAccountReqDTO.getId()));
            writeLogDto.setModuleId(DictConstants.DICT_INST_BANKACCOUNT_MODULE_ID);
            writeLogDto.setModuleName(DictConstants.DICT_INST_BANKACCOUNT_MODULE_NAME);
            writeLogDto.setActionType(getOperatingType(instBankAccountReqDTO));
            writeLogDto.setActionName(DictConstants.DICT_INST_BANKACCOUNT_MODULE_NAME);
            writeLogDto.setActionContent(JSONObject.toJSONString(instBankAccountReqDTO));
            writeLogDto.setApiName("com.payermax.omc.channel.exchange.client.controller.instcenter.InstBankAccountController#saveInstBankAccount");
            finOperatingLogClient.recordOperatingLog(writeLogDto);
        } catch (Exception e) {
            log.warn("记录操作日志异常：", e);
        }
    }

    /**
     * 获取操作类型
     *
     * @param instBankAccountReqDTO
     * @return
     */
    private Integer getOperatingType(InstBankAccountReqDTO instBankAccountReqDTO) {
        if (instBankAccountReqDTO.getId() == null) {
            return OperatingTypeEnum.ADD.getOperationType();
        }
        if (StatusEnum.Y.getValue().equalsIgnoreCase(instBankAccountReqDTO.getStatus())) {
            return OperatingTypeEnum.MODIFY.getOperationType();
        }
        if (StatusEnum.N.getValue().equalsIgnoreCase(instBankAccountReqDTO.getStatus())) {
            return OperatingTypeEnum.DEL.getOperationType();
        }
        return OperatingTypeEnum.DEFAULT.getOperationType();
    }

    /**
     * 查询响应转换
     *
     * @param instBankAccountReqDTO
     * @param instBankAccountList
     * @return
     */
    private List<InstBankAccountVO> convertToQueryVoList(InstBankAccountReqDTO instBankAccountReqDTO, List<InstBankAccountEntity> instBankAccountList) {
        List<InstBankAccountVO> instBankAccountVOList = new ArrayList<>();

        if (CollectionUtils.isEmpty(instBankAccountList)) {
            return instBankAccountVOList;
        }

        for (InstBankAccountEntity instBankAccountEntity : instBankAccountList) {
            // 1、基础信息填充
            InstBankAccountVO instBankAccountVO = respVoAssembler.toSingleInstBankAccountVo(instBankAccountEntity);
            instBankAccountVO.setInstCode(instBankAccountReqDTO.getInstCode());

            // 2、账户用途信息 && 充值可用币种 && 充值使用优先级 格式化
            if (StringUtils.isNotBlank(instBankAccountEntity.getAccountUse())) {
                instBankAccountVO.setAccountUseList(Arrays.asList(instBankAccountEntity.getAccountUse().split(SymbolConstants.SYMBOL_COMMA)));
            }

            if (StringUtils.isNotBlank(instBankAccountEntity.getRechargeCanUseCcy())) {
                instBankAccountVO.setRechargeCanUseCcyList(Arrays.asList(instBankAccountEntity.getRechargeCanUseCcy().split(SymbolConstants.SYMBOL_COMMA)));
            }

            if (StringUtils.isNotBlank(instBankAccountEntity.getRechargeUseOrder())) {
                instBankAccountVO.setRechargeUseOrder(Integer.valueOf(instBankAccountEntity.getRechargeUseOrder()));
            }

            instBankAccountVOList.add(instBankAccountVO);
        }
        return instBankAccountVOList;
    }
}
