package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstNdaDao;
import com.payermax.channel.inst.center.app.service.InstNdaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName NdaInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/13 10:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstNdaServiceImpl implements InstNdaService {

    @Autowired
    private InstNdaDao instNdaDao;

    @Override
    public InstNdaEntity query(InstNdaEntity instNdaEntity) {
        Preconditions.checkArgument(instNdaEntity.getInstId() != null, "param instId is mandatory");

        List<InstNdaEntity> instNdaEntityList = instNdaDao.selectAll(instNdaEntity);
        if (CollectionUtils.isNotEmpty(instNdaEntityList)) {
            return instNdaEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public int save(InstNdaEntity instNdaEntity) {
        Preconditions.checkArgument(instNdaEntity != null, "param instNdaEntity is mandatory");
        int result = 0;
        //主键不为空则根据主键更新信息
        if (null != instNdaEntity.getId()) {
            result = instNdaDao.update(instNdaEntity);
        } else {
            //主键为空则插入信息
            if (StringUtils.isBlank(instNdaEntity.getStatus())) {
                instNdaEntity.setStatus("INIT");
            }
            result = instNdaDao.insert(instNdaEntity);
        }
        return result;
    }
}
