package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName InstTransFeeReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 23:23
 */
@Data
public class InstTransFeeReqDTO implements Serializable {
    private static final long serialVersionUID = -1722392976617882840L;
    private String contractNo;
    private String productCode;
    private List<String> countrys;
    private List<String> targetOrgs;
    private List<String> cardOrgs;
    /**
     * 费用分组id，删除时使用
     */
    private String feeGroupId;
    /**
     * 交易费用列表
     */
    List<InstTransFeeDTO> transFeeDTOList;
}
