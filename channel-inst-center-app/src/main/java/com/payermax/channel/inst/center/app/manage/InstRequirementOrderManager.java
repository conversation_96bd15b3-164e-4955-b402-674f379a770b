package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstRequirementOrderReqDTO;
import com.payermax.channel.inst.center.app.response.InstRequirementOrderVO;

/**
 * 集成需求单Manager
 *
 * <AUTHOR>
 * @date 2022/6/4 14:35
 */
public interface InstRequirementOrderManager {

    /**
     * 保存信息
     *
     * @param reqDto
     * @return
     */
    int save(InstRequirementOrderReqDTO reqDto);

    /**
     * 根据申请单号查询信息
     *
     * @param applyNo
     * @return
     */
    InstRequirementOrderVO query(String applyNo);

}
