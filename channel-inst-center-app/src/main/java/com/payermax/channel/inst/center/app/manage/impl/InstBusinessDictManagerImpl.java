package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity;
import com.payermax.channel.inst.center.app.manage.InstBusinessDictManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstBusinessDictReqDTO;
import com.payermax.channel.inst.center.app.response.InstBusinessDictVO;
import com.payermax.channel.inst.center.app.service.InstBusinessDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstBusinessDictManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:43
 */
@Service
public class InstBusinessDictManagerImpl implements InstBusinessDictManager {

    @Autowired
    private InstBusinessDictService instBusinessDictService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public int save(InstBusinessDictReqDTO record) {
        InstBusinessDictEntity instBusinessDictEntity = reqDtoAssembler.toInstBusinessDictEntity(record);
        int result = instBusinessDictService.save(instBusinessDictEntity);
        return result;
    }

    @Override
    public int saveAll(List<InstBusinessDictReqDTO> instBusinessDictReqDTOS) {
        List<InstBusinessDictEntity> instBusinessDictEntities = reqDtoAssembler.toInstBusinessDictEntities(instBusinessDictReqDTOS);
        int result = instBusinessDictService.saveBatch(instBusinessDictEntities);
        return result;
    }

    @Override
    public List<InstBusinessDictVO> queryByBusinessType(String businessType) {
        List<InstBusinessDictEntity> businessDictEntityList = instBusinessDictService.getByBusinessType(businessType);
        List<InstBusinessDictVO> instBusinessDictVOs = respVoAssembler.toInstBusinessDictVos(businessDictEntityList);
        return instBusinessDictVOs;
    }
}
