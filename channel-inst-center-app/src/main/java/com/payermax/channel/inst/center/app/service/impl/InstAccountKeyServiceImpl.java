package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstAccountKeyQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAccountKeyDao;
import com.payermax.channel.inst.center.app.service.InstAccountKeyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstAccountKeyServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/29 10:45
 */
@Service
public class InstAccountKeyServiceImpl implements InstAccountKeyService {

    @Autowired
    private InstAccountKeyDao instAccountKeyDao;

    @Override
    public int saveBatch(List<InstAccountKeyEntity> records) {
        int result = instAccountKeyDao.insertBatch(records);
        return result;
    }

    @Override
    public int update(InstAccountKeyEntity record) {
        Preconditions.checkArgument(record.getKeyId() != null,"id is mandatory");
        int result = instAccountKeyDao.updateByPrimaryKey(record);
        return result;
    }

    @Override
    public List<InstAccountKeyQueryEntity> queryList(InstAccountKeyQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity.getRequirementOrderId() != null, "requirementOrderId is mandatory");
        Preconditions.checkArgument(queryEntity.getInstId() != null, "instId is mandatory");
        List<InstAccountKeyQueryEntity> instAccountKeyQueryEntities = instAccountKeyDao.selectAll(queryEntity.getRequirementOrderId(), queryEntity.getInstId());
        return instAccountKeyQueryEntities;
    }

    @Override
    public int delete(Long id) {
        Preconditions.checkArgument(id != null, "id is mandatory");
        int result = instAccountKeyDao.deleteById(id);
        return result;
    }
}
