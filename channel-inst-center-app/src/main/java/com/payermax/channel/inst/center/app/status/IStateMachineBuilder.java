package com.payermax.channel.inst.center.app.status;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.statemachine.StateMachine;

/**
 * <AUTHOR> at 2022/10/9 21:49
 **/
public interface IStateMachineBuilder<S, E> {

    /**
     * defined state machine builder
     *
     * @param initState
     * @param beanFactory
     * @return StateMachine<S, E>
     * @throws Exception
     */
    StateMachine<S, E> build(S initState, BeanFactory beanFactory) throws Exception;
}
