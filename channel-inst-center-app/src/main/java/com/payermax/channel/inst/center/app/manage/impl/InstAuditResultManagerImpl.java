package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity;
import com.payermax.channel.inst.center.app.manage.InstAuditResultManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstAuditResultReqDTO;
import com.payermax.channel.inst.center.app.response.InstAuditResultVO;
import com.payermax.channel.inst.center.app.service.InstAuditResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @ClassName InstAuditResultManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 9:28
 * @Version 1.0
 */
@Service
public class InstAuditResultManagerImpl implements InstAuditResultManager {

    @Autowired
    private InstAuditResultService instAuditResultService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public InstAuditResultVO query(InstAuditResultReqDTO instAuditResultReqDTO) {
        //请求转换
        InstAuditResultEntity instAuditResultReqEntity = reqDtoAssembler.toInstAuditResultEntity(instAuditResultReqDTO);
        //响应转换
        InstAuditResultEntity instAuditResultRespEntity = instAuditResultService.query(instAuditResultReqEntity);
        return respVoAssembler.toInstAuditResultVo(instAuditResultRespEntity);
    }

    @Override
    public int save(InstAuditResultReqDTO instAuditResultReqDTO) {
        //请求转换
        InstAuditResultEntity instAuditResultEntity = reqDtoAssembler.toInstAuditResultEntity(instAuditResultReqDTO);
        return instAuditResultService.save(instAuditResultEntity);

    }
}
