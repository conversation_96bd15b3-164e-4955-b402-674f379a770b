package com.payermax.channel.inst.center.app.manage.template.close;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/9 20:30
 **/
@Service
@Slf4j
public class OfflineCloseTemplate implements SubAccountCloseTemplate {

    public static final String ALERT_TITLE = "机构子级账号【线下关闭】";
    @Autowired
    DingAlertClient dingAlertClient;
    @NacosValue(value = "${inst.sub.account.alert.message.for.offline.close.message:}", autoRefreshed = true)
    private String subAccountOfflineCloseException;

    @Override
    public void close(RequestAccountDO requestAccountDO) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 已停用,或非已激活则不再进行后续操作
        if (Objects.equals(SubAccountStatusEnum.TERMINATED.getStatus(),instSubFundsAccountEntity.getStatus())
                || !Objects.equals(SubAccountStatusEnum.ACTIVATED.getStatus(),instSubFundsAccountEntity.getStatus())) {
            return;
        }
        
        try {
            // 发送钉钉通知
            dingAlertClient.sendMsgForGroupSubAccount(ALERT_TITLE, subAccountOfflineCloseException, requestAccountDO);
        } catch (Exception e) {
            log.info("OfflineCloseTemplate-close sendMsg Exception:{}", e);
        }
    }

}
