package com.payermax.channel.inst.center.app.status;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateMachine;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 状态执行器
 *
 * <AUTHOR>
 * @date 2021/8/16 15:02
 */
@Slf4j
@Component
public class StateMachineExecutor {

    @Autowired
    private BeanFactory beanFactory;

    @Autowired
    private IStateMachineBuilder<SubAccountStatusEnum, SubAccountModeEnum> subAccountStateApplyModeMachineBuilder;

    @Autowired
    private IStateMachineBuilder<SubAccountStatusEnum, ActivationModeEnum> subAccountStateActivationModeMachineBuilder;

    @Transactional
    public void transChangeSubAccountApplyState(StateRequest<SubAccountModeEnum, InstSubFundsAccountEntity> subAccountState) {
        this.changeSubAccountState(subAccountState);
    }

    @Transactional
    public void transChangeSubAccountActivationState(StateRequest<ActivationModeEnum, InstSubFundsAccountEntity> subAccountState) {
        this.changeActivationSubAccountState(subAccountState);
    }

    /**
     * 申请-改变子级资金账户状态
     *
     * @param subAccountState
     */
    public void changeSubAccountState(StateRequest<SubAccountModeEnum, InstSubFundsAccountEntity> subAccountState) {
        Preconditions.checkArgument(null != subAccountState, "param subAccountState is mandatory");

        InstSubFundsAccountEntity subFundsAccountEntity = subAccountState.getData();
        Integer originalStatus = subAccountState.getOriginalStatus();
        SubAccountModeEnum subAccountModeEnum = subAccountState.getEvent();
        Preconditions.checkArgument(null != subAccountModeEnum, "param subAccountState.event is mandatory");
        Preconditions.checkArgument(null != subFundsAccountEntity, "param subAccountState.data is mandatory");
        Preconditions.checkArgument(null != originalStatus, "param subAccountState.originalStatus is mandatory");

        String subAccountId = subFundsAccountEntity.getSubAccountId();
        Integer status = subFundsAccountEntity.getStatus();
        Preconditions.checkArgument(null != subAccountId, "param subAccountId is mandatory");
        Preconditions.checkArgument(null != status, "param status is mandatory");

        // 根据请求类型获取状态机构建器

        IStateMachineBuilder stateMachineBuilder;
        StateMachine stateMachine;

        SubAccountStatusEnum originalStatusEnum = null;
        SubAccountStatusEnum targetStatusEnum = null;

        try {
            stateMachineBuilder = subAccountStateApplyModeMachineBuilder;
            originalStatusEnum = SubAccountStatusEnum.getSubAccountStatusByStatus(originalStatus);
            targetStatusEnum = SubAccountStatusEnum.getSubAccountStatusByStatus(status);
            stateMachine = stateMachineBuilder.build(originalStatusEnum, beanFactory);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "create state machine error", e);

        }
        // 设置状态机上下文
        stateMachine.getExtendedState().getVariables().put(subFundsAccountEntity.getClass(), subFundsAccountEntity);
        stateMachine.getExtendedState().getVariables().put(StateConstants.VAR_KEY_ORIG_STATUS, originalStatus);
        // 发送事件
        boolean isSend = stateMachine.sendEvent(subAccountState.getEvent());
        if (!isSend) {
            log.warn("StateMachineExecutor.changeSubAccountState {} Account State From {} to {} Failed, Event:{}",
                    subAccountId, originalStatusEnum, targetStatusEnum, subAccountModeEnum);
            throw new BusinessException(ErrorCodeEnum.UNSUPPORTED_STATE_TRANS.getCode(), "StateMachine execute error");
        }
        // 执行结果，是否异常
        Exception error = stateMachine.getExtendedState().get(Exception.class, Exception.class);
        if (null != error) {
            if (error instanceof BusinessException) {
                throw (BusinessException) error;
            }
            log.error("StateMachineExecutor.changeSubAccountState accountId:{} Catch Exception", subAccountId, error);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "StateMachine execute error");
        }
    }

    /**
     * 激活-改变子级资金账户状态
     *
     * @param subAccountState
     */
    public void changeActivationSubAccountState(StateRequest<ActivationModeEnum, InstSubFundsAccountEntity> subAccountState) {
        Preconditions.checkArgument(null != subAccountState, "param subAccountState is mandatory");

        InstSubFundsAccountEntity subFundsAccountEntity = subAccountState.getData();
        Integer originalStatus = subAccountState.getOriginalStatus();
        ActivationModeEnum activationModeEnum = subAccountState.getEvent();
        Preconditions.checkArgument(null != activationModeEnum, "param activationModeEnum.event is mandatory");
        Preconditions.checkArgument(null != subFundsAccountEntity, "param subAccountState.data is mandatory");
        Preconditions.checkArgument(null != originalStatus, "param subAccountState.originalStatus is mandatory");

        String subAccountId = subFundsAccountEntity.getSubAccountId();
        Integer status = subFundsAccountEntity.getStatus();
        Preconditions.checkArgument(null != subAccountId, "param subAccountId is mandatory");
        Preconditions.checkArgument(null != status, "param status is mandatory");

        // 根据请求类型获取状态机构建器

        IStateMachineBuilder stateMachineBuilder;
        StateMachine stateMachine;

        SubAccountStatusEnum originalStatusEnum = null;
        SubAccountStatusEnum targetStatusEnum = null;

        try {
            stateMachineBuilder = subAccountStateActivationModeMachineBuilder;
            originalStatusEnum = SubAccountStatusEnum.getSubAccountStatusByStatus(originalStatus);
            targetStatusEnum = SubAccountStatusEnum.getSubAccountStatusByStatus(status);
            stateMachine = stateMachineBuilder.build(originalStatusEnum, beanFactory);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "create state machine error", e);

        }
        // 设置状态机上下文
        stateMachine.getExtendedState().getVariables().put(subFundsAccountEntity.getClass(), subFundsAccountEntity);
        stateMachine.getExtendedState().getVariables().put(StateConstants.VAR_KEY_ORIG_STATUS, originalStatus);
        // 发送事件
        boolean isSend = stateMachine.sendEvent(subAccountState.getEvent());
        if (!isSend) {
            log.warn("StateMachineExecutor.changeSubAccountState {} Account State From {} to {} Failed, Event:{}",
                    subAccountId, originalStatusEnum, targetStatusEnum, activationModeEnum);
            throw new BusinessException(ErrorCodeEnum.UNSUPPORTED_STATE_TRANS.getCode(), "StateMachine execute error");
        }
        // 执行结果，是否异常
        Exception error = stateMachine.getExtendedState().get(Exception.class, Exception.class);
        if (null != error) {
            if (error instanceof BusinessException) {
                throw (BusinessException) error;
            }
            log.error("StateMachineExecutor.changeSubAccountState accountId:{} Catch Exception", subAccountId, error);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "StateMachine execute error");
        }
    }

}
