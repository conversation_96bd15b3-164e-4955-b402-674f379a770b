package com.payermax.channel.inst.center.app.dto.contract;

import com.alibaba.fastjson.JSONObject;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractVersionStatusEnum;
import javafx.util.Pair;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstContractVersionInfoDto extends InstContractBaseInfo {

    private String contractVersion;

    private JSONObject accountingTypeJson;

    private LocalDateTime effectStartTime;

    private LocalDateTime effectEndTime;

    private ContractVersionStatusEnum versionStatus;

    private List<Pair<String,String>> accountingTypeList;

}
