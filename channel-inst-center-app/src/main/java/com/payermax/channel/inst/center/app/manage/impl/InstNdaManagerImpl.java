package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ContractStatusEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.NdaStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity;
import com.payermax.channel.inst.center.app.manage.InstNdaManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstNdaReqDTO;
import com.payermax.channel.inst.center.app.response.InstNdaVO;
import com.payermax.channel.inst.center.app.service.InstApplyOrderService;
import com.payermax.channel.inst.center.app.service.InstContractService;
import com.payermax.channel.inst.center.app.service.InstNdaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * @ClassName InstNdaManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/13 10:28
 * @Version 1.0
 */
@Service
public class InstNdaManagerImpl implements InstNdaManager {

    @Autowired
    private InstNdaService instNdaService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private InstContractService instContractService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public InstNdaVO query(InstNdaReqDTO instNdaReqDTO) {
        //请求转换
        InstNdaEntity instNdaReqEntity = reqDtoAssembler.toInstNdaEntity(instNdaReqDTO);
        //响应转换
        InstNdaEntity instNdaRespEntity = instNdaService.query(instNdaReqEntity);
        return respVoAssembler.toInstNdaVo(instNdaRespEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(InstNdaReqDTO instNdaReqDTO) {
        //请求转换
        InstNdaEntity instNdaEntity = reqDtoAssembler.toInstNdaEntity(instNdaReqDTO);
        int result = instNdaService.save(instNdaEntity);
        // 更新阶段状态
        instApplyOrderService.updateStageStatus(instNdaEntity.getApplyNo(), ApplyStageEnum.NDA, instNdaEntity.getStatus());
        if(NdaStatusEnum.COMPLETED.name().equalsIgnoreCase(instNdaEntity.getStatus())){
            InstContractEntity record = new InstContractEntity();
            record.setApplyNo(instNdaReqDTO.getApplyNo());
            record.setInstId(instNdaReqDTO.getInstId());
            InstContractEntity contract = instContractService.getByEntity(record);
            if(contract == null){
                record.setStatus(ContractStatusEnum.INIT.name());
                instContractService.save(record);
                // 更新阶段状态
                instApplyOrderService.updateStageStatus(instNdaEntity.getApplyNo(), ApplyStageEnum.CONTRACT, ContractStatusEnum.INIT.name());
            }
        }
        return result;

    }
}
