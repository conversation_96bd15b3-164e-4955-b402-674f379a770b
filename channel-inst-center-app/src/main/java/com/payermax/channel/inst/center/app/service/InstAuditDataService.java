package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity;

import java.util.List;


/**
 * 审核资料清单相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
public interface InstAuditDataService {

    /**
     * 查询审核信息
     *
     * @return
     */
    InstAuditDataEntity query(InstAuditDataEntity instAuditDataEntity);

    /**
     * 查询审核资料信息
     *
     * @param instAuditDataEntity
     * @return
     */
    List<InstAuditDataEntity> queryList(InstAuditDataEntity instAuditDataEntity);

    /**
     * 保存审核信息
     *
     * @return
     */
    int save(InstAuditDataEntity instAuditDataEntity);


}
