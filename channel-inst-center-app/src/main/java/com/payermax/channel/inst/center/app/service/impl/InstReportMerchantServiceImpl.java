package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstReportMerchantDao;
import com.payermax.channel.inst.center.app.service.InstReportMerchantService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机构商户报备Service实现
 *
 * <AUTHOR>
 * @date 2022/6/4 17:23
 */
@Service
public class InstReportMerchantServiceImpl implements InstReportMerchantService {

    @Autowired
    private InstReportMerchantDao instReportMerchantDao;

    @Override
    public int save(InstReportMerchantEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instReportMerchantDao.insert(record);
        } else {
            // 更新
            result = instReportMerchantDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstReportMerchantEntity query(InstReportMerchantEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");

        List<InstReportMerchantEntity> entityList = instReportMerchantDao.selectAll(queryEntity);
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

}
