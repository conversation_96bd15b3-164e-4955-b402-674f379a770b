package com.payermax.channel.inst.center.app.manage.template.mode;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> at 2022/10/9 16:08
 **/
@Service
@Slf4j
public class NumberSegmentModeTemplate extends AbstractSubAccountModeTemplate {

    @Override
    public void getSubAccountNo(RequestAccountDO requestAccountDO) {

        // 创建子级账号请求中部分参数
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();

        // 开通子级资金账户拓展参数
        instFundsAccountBucketManage.checkInstSubFundsAccountBucketId(requestAccountDO);

        // 用号段生成子级账号
        if (StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {

            // 用号段生成子级账号
            super.buildNumberSegmentAccountNoLock(requestAccountDO);

            // 检查子级账号是否生成
            if (StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {
                throw new BusinessException(ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getCode(), ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getMsg());
            }

        }

        // 号段模式需要将号段生成码赋值给子级资金账号字段
        instSubFundsAccountEntity.setSubAccountNo(instSubFundsAccountEntity.getNumberSegmentNo());

    }


    @Override
    public InstSubFundsAccountEntity buildCreateSubAccountRecord(RequestAccountDO requestAccountDO) {
        return super.buildCreateSubAccountRecord(requestAccountDO);

    }

    @Override
    public void handleActivationOrOthersAction(RequestAccountDO requestAccountDO) {
        super.checkIsNeedActivation(requestAccountDO);
    }
}
