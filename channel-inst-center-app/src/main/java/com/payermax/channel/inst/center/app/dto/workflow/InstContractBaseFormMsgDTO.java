package com.payermax.channel.inst.center.app.dto.workflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/17
 * @DESC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InstContractBaseFormMsgDTO {

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 我方主体
     */
    private String contractEntity;

    /**
     * 机构产品名称
     */
    private String instProductName;

    /**
     * 机构产品类型
     */
    private String instProductType;

    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 标准化信息
     */
    private String standardProductMsgList;

    /**
     * Diff 信息
     */
    private Map<String, Object> diffMsg;
}
