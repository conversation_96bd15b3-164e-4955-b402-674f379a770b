package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstDdReqDTO;
import com.payermax.channel.inst.center.app.response.InstDdQueryVO;
import com.payermax.channel.inst.center.app.response.InstDdVO;

import java.util.List;

/**
 * 机构DD manage
 *
 * <AUTHOR>
 * @date 2022/5/15 23:01
 */
public interface InstDdManage {

    /**
     * 保存DD信息
     *
     * @param ddReq
     * @return
     */
    int save(InstDdReqDTO ddReq);

    /**
     * 根据机构ID查询DD信息
     *
     * @param instId
     * @return
     */
    InstDdVO query(Long instId);

    /**
     * 根据机构id查询DD信息和机构信息
     * @param instIds
     * @return
     */
    InstDdQueryVO queryInstDdByInstIds(List<Long> instIds);
}
