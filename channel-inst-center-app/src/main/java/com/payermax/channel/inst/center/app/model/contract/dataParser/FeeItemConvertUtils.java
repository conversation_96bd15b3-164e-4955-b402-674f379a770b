package com.payermax.channel.inst.center.app.model.contract.dataParser;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayinDTO;
import com.payermax.channel.inst.center.app.dto.impl.StandardProductInfoDTO;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstProductTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.IDescOnExcelEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.TaxCalculateTypeEnumV2;
import com.payermax.channel.inst.center.common.enums.instcontract.WithdrawMethodEnumV2;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.enums.contract.content.TaxCalculateFormulaEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractFeeItemRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOriginProductRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractVersionInfoRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * Fee item 转换工具类
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class FeeItemConvertUtils {


    private final InstContractBaseInfoRepository instContractBaseInfoRepository;
    private final InstContractVersionInfoRepository instContractVersionInfoRepository;
    private final InstContractOriginProductRepository instContractOriginProductRepository;
    private final InstContractFeeItemRepository instContractFeeItemRepository;


    /**
     * 将传入的草稿Data转换成产品对应 DTO
     */
    public static AbstractInstContractFeeDTO data2FeeDto(InstContractDraft draft){
        // PayIn 覆盖 Payout 所有字段，直接复用
        return JSON.parseObject(draft.getDraftData(), InstContractFeePayinDTO.class);
    }


    /**
     * 获取草稿信息中的支付方式列表
     */
    public static String feeToPaymentType(InstContractDraft draft){
        AbstractInstContractFeeDTO fee = data2FeeDto(draft);
        if(Objects.nonNull(fee.getStandardProductInfo()) && CollectionUtils.isNotEmpty(fee.getStandardProductInfo().getPaymentList())){
            return fee.getStandardProductInfo().getPaymentList().stream().distinct()
                    .map(StandardProductInfoDTO.PaymentItem::getPaymentType)
                    .distinct()
                    .collect(Collectors.joining(","));
        }
        return "";
    }


    /**
     * 获取草稿信息中的目标机构列表
     */
    public static String feeToTarget(InstContractDraft draft){
        AbstractInstContractFeeDTO fee = data2FeeDto(draft);
        if(Objects.nonNull(fee.getStandardProductInfo()) && CollectionUtils.isNotEmpty(fee.getStandardProductInfo().getPaymentList())){
            return fee.getStandardProductInfo().getPaymentList().stream().distinct()
                    .map(StandardProductInfoDTO.PaymentItem::getTarget)
                    .distinct()
                    .collect(Collectors.joining(","));
        }
        return "";
    }


    /**
     * 退款配置拓展字段，退款是否退手续费、争议费用、争议币种
     */
    public static Map<String, String> composeRefundFeeExtendFields(InstContractFeePayinDTO feeDTO) {
        Map<String,String> extendFields = new HashMap<>(4);
        extendFields.put(InstFeeConfig.ExtendFieldsKey.SHOULD_TRADE_FEE_REFUNDED,feeDTO.getRefundTransactionFee());
        return extendFields;
    }



    /**
     * 根据在 Excel 中的数据返回枚举值
     */
    public static <E extends IDescOnExcelEnum> String getEnumByDescOnExcel(Class<E> classInstance, String value) {
        if(Objects.isNull(value) || StringUtils.isBlank(value)){
            return null;
        }
        E[] enums = classInstance.getEnumConstants();
        return Objects.requireNonNull(Arrays.stream(enums).filter(item -> StringUtils.equalsIgnoreCase(item.getDescOnExcel(), value))
                        .findFirst() //NO_CHECK 匹配枚举值，无风险
                        .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的数据:" + value)))
                .name();
    }


    /**
     * 字符串转 BigDecimal
     */
    @Named("convertRawBigDecimal")
    public static BigDecimal convertRawBigDecimal(String rawValue){
        if(Objects.isNull(rawValue) || StringUtils.isBlank(rawValue)){
            return null;
        }
        // TODO 解决Excel中数字带逗号问题，临时解决，后面通过修改 Excel 或者输入时校验解决
        rawValue = rawValue.replace(",", "");
        return new BigDecimal(rawValue);
    }


    /**
     * 根据产品类型获取业务类型
     */
    public static ContractBusinessTypeEnum getBusinessType(String businessType){
        String businessName = Stream.of(InstProductTypeEnum.values())
                .filter(e -> e.getValue().equals(businessType))
                .findFirst() //NO_CHECK 匹配枚举值，无风险
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的产品类型"))
                .getBusinessName();
        return ContractBusinessTypeEnum.valueOf(businessName);
    }

    /**
     * 根据税费计算公式获取枚举
     */
    public static TaxCalculateFormulaEnum getTaxCalculateFormula(String formula){
        String formulaDesc = Stream.of(TaxCalculateTypeEnumV2.values())
                .filter(e -> e.getDescOnExcel().equals(formula))
                .findFirst() //NO_CHECK 匹配枚举值，无风险
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的税费公式"))
                .getDesc();
        return Stream.of(TaxCalculateFormulaEnum.values()).filter(e -> e.getDesc().equals(formulaDesc))
                .findFirst() //NO_CHECK 匹配枚举值，无风险
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的税费公式"));
    }


    /**
     * 根据结算方式获取枚举
     */
    public static WithdrawMethodEnum getSettleMethod(String settleMethod){
        if(Objects.isNull(settleMethod) || StringUtils.isBlank(settleMethod)){
            return null;
        }
        String settleMethodDesc = Stream.of(WithdrawMethodEnumV2.values())
                .filter(e -> e.getDescOnExcel().equals(settleMethod))
                .findFirst() //NO_CHECK 匹配枚举值，无风险
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的结算方式"))
                .getDesc();
        return Stream.of(WithdrawMethodEnum.values()).filter(e -> e.getDesc().equals(settleMethodDesc))
                .findFirst() //NO_CHECK 匹配枚举值，无风险
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "暂不支持的结算方式"));
    }

    /**
     * 判断标准化进度
     */
    public static Boolean isStandardized(InstContractDraft draft){
        AbstractInstContractFeeDTO draftData = FeeItemConvertUtils.data2FeeDto(draft);
        boolean paymentStandardized = false;
        boolean tradeStandardized;
        // 判断产品标准化完成
        if(Objects.isNull(draftData.getStandardProductInfo())){
            return false;
        }
        if(CollectionUtils.isNotEmpty(draftData.getStandardProductInfo().getPaymentList())){
            paymentStandardized = draftData.getStandardProductInfo().getPaymentList().stream()
                    .allMatch(item -> StringUtil.isNotBlank(item.getPaymentType()) && StringUtil.isNotBlank(item.getTarget()));
        }
        // 行业分类列表为空时不需进行标准化
        if(CollectionUtils.isEmpty(draftData.getStandardProductInfo().getInstTradeList())){
            tradeStandardized = true;
        }else{
            tradeStandardized = draftData.getStandardProductInfo().getInstTradeList().stream()
                    .allMatch(item -> StringUtil.isNotBlank(item.getRelationType()) && StringUtil.isNotBlank(item.getTradeCode()));
        }
        return  paymentStandardized && tradeStandardized;
    }

    public static void feeListUniqueFillAndCheck(List<InstContractFeeItem> feeItems){
        if (CollectionUtils.isNotEmpty(feeItems)){
            // feeItem 业务唯一键填充
            feeItems.forEach(fee -> fee.setFeeBusinessKey(buildFeeItemBusinessKey(fee)));
            // 校验业务唯一性是否重复
            Map<String, List<InstContractFeeItem>> collect = feeItems.stream().collect(Collectors.groupingBy(InstContractFeeItem::getFeeBusinessKey));
            AssertUtil.isTrue(collect.entrySet().stream().allMatch(item -> item.getValue().size() == 1), ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(), "feeItem 业务唯一键重复");
        }
    }

    /**
     * 根据费用信息构造业务主键
     */
    public static String buildFeeItemBusinessKey(InstContractFeeItem feeItem) {
        return buildFeeItemBusinessKey(ContractVersionPOAssembler.INSTANCE.convertFeeItemPO(feeItem), feeItem.getInstOriginProductNo());
    }

    /**
     * 根据费用信息构造业务主键
     */
    public static String buildFeeItemBusinessKey(InstContractFeeItemPO feeItem,String originProductNo) {
        // 原始产品编码、支付币种、渠道商户号、渠道子商户号、原始 MCC、MCC 逻辑、资金来源、交易国家、清算网络、费用承担方、卡类型，构成确定 fee_item 业务唯一性的值
        // 此处包含 MCC 相关逻辑，因此需要在标准化完成之后才能进行
        List<String> keyList = Stream.of(originProductNo,feeItem.getPayCurrency(),feeItem.getChannelMerchantNo(),feeItem.getSubMerchantNo(),
                        feeItem.getOriginMcc(),feeItem.getMccLogic(),feeItem.getFundingSource(),feeItem.getTransactionCountry(),feeItem.getClearNetwork(),
                        feeItem.getFeeBearer(), feeItem.getCustomerType(), feeItem.getCardType())
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, SymbolConstants.SYMBOL_UNDERLINE);
    }

    /**
     * 根据费用信息构造业务主键(未标准化)
     */
    public static String buildFeeItemBusinessKeyUnStandardized(InstContractFeeItemPO feeItem,String originProductNo) {
        // 原始产品编码、支付币种、渠道商户号、渠道子商户号、原始 MCC、MCC 逻辑、资金来源、交易国家、清算网络、费用承担方，构成确定 fee_item 业务唯一性的值
        // 此处包含 MCC 相关逻辑，因此需要在标准化完成之后才能进行
        List<String> keyList = Stream.of(originProductNo,feeItem.getPayCurrency(),feeItem.getChannelMerchantNo(),feeItem.getSubMerchantNo(),
                        feeItem.getFundingSource(),feeItem.getTransactionCountry(),feeItem.getClearNetwork(), feeItem.getFeeBearer(),
                        feeItem.getCustomerType(),feeItem.getCardType())
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, SymbolConstants.SYMBOL_UNDERLINE);
    }



    /**
     * 根据传入数据判断是否存在费用信息并且返回费用信息业务唯一键 Set
     */
    public Set<String> getFeeItemKeySet(String contractEntity, String instCode, String businessType, String originProductNo, boolean isStandardized) {
        String status = ContractStatusEnum.ACTIVATED.name();
        Set<String> businessKeySet = new HashSet<>();

        // 1. 判断原始合同是否存在，存在则继续判断，否则可以跳出流程
        InstContractBaseInfoPO baseInfoPo = instContractBaseInfoRepository.queryActiveBaseInfo(instCode, contractEntity, businessType, status);
        if(Objects.isNull(baseInfoPo)) return businessKeySet;

        // 2. 判断 合同版本是否存在，存在则继续判断，否则可以跳出流程
        InstContractVersionInfoPO versionInfoPo = instContractVersionInfoRepository.queryActiveVersion(baseInfoPo.getContractNo());
        if(Objects.isNull(versionInfoPo)) return businessKeySet;

        // 3. 判断 根据合同版本判断原始产品是否存在，存在则继续判断，否则可以跳出流程
        InstContractOriginProductPO instContractOriginProduct = instContractOriginProductRepository.queryByOriginProductNo(originProductNo);
        if(Objects.isNull(instContractOriginProduct)) return businessKeySet;

        // 4. 根据 原始产品获取费用信息，费用信息存在则继续判断，否则可以跳出流程
        List<InstContractFeeItemPO> feeItemPoList = instContractFeeItemRepository.queryByOriginProductNo(instContractOriginProduct.getInstOriginProductNo());
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(feeItemPoList)) return businessKeySet;


        // fee_item 业务唯一键集合,根据是否标准化进行拼接
        if(isStandardized){
            businessKeySet = feeItemPoList.stream().map(InstContractFeeItemPO::getFeeBusinessKey).collect(Collectors.toSet());
        }else{
            businessKeySet = feeItemPoList.stream().map( item -> buildFeeItemBusinessKeyUnStandardized(item,instContractOriginProduct.getInstOriginProductNo())).collect(Collectors.toSet());
        }

        return businessKeySet;
    }
}
