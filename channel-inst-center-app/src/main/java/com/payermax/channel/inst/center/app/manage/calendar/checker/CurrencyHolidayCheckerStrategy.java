package com.payermax.channel.inst.center.app.manage.calendar.checker;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrencyHolidayCheckerStrategy extends AbstractHolidayCheckerStrategy {

    public CurrencyHolidayCheckerStrategy() {
        registry(CalendarTypeEnum.CURRENCY, this);
    }

    @Override
    List<InstFinancialCalendar> calendarFilter(List<InstFinancialCalendar> calendars, String country, String currency) {
        log.info("CurrencyHolidayChecker currency:{}", currency);
        AssertUtil.notEmpty(currency, "ERROR", "currency can not be empty");
        // 查询币种日历
        List<InstFinancialCalendar> filteredCalendars = calendarPreciseFilter(calendars, CommonConstants.STAR, currency);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(filteredCalendars), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "币种日历不存在");

        return filteredCalendars;
    }

    @Override
    public List<InstFinancialCalendar> calendarPreciseFilter(List<InstFinancialCalendar> calendars, String country, String currency) {
        List<InstFinancialCalendar> filteredCalendar = calendars.stream()
                .filter(calendar -> calendar.getCurrency().equals(currency) && calendar.getCalendarType() == CalendarTypeEnum.CURRENCY)
                .collect(Collectors.toList());
        AssertUtil.isTrue(filteredCalendar.size() <= 1, ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "错误匹配: 匹配到多个日历");
        // 填入节假日
        filteredCalendar.forEach(calendar -> calendar.setHolidays(getHolidays(calendar)));
        return filteredCalendar;
    }
}
