package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.AuditBusiTypeEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity;
import com.payermax.channel.inst.center.app.manage.InstKycManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstAuditDataReqDTO;
import com.payermax.channel.inst.center.app.request.InstKycReqDTO;
import com.payermax.channel.inst.center.app.response.InstKycVO;
import com.payermax.channel.inst.center.app.service.InstApplyOrderService;
import com.payermax.channel.inst.center.app.service.InstAuditDataService;
import com.payermax.channel.inst.center.app.service.InstKycService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * @ClassName InstKycManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 19:28
 * @Version 1.0
 */
@Service
public class InstKycManagerImpl implements InstKycManager {

    @Autowired
    private InstKycService instKycService;

    @Autowired
    private InstAuditDataService instAuditDataService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public InstKycVO query(Long instId) {
        //响应转换
        InstKycEntity instKycEntity = instKycService.getByInstId(instId);
        if (instKycEntity == null) {
            return null;
        }
        InstKycVO instKycVO = respVoAssembler.toInstKycVo(instKycEntity);

        // 查询审核资料数据
        InstAuditDataEntity queryAuditDataEntity = new InstAuditDataEntity();
        queryAuditDataEntity.setBusinessType(AuditBusiTypeEnum.KYC.name());
        queryAuditDataEntity.setBusinessNo(String.valueOf(instKycEntity.getId()));
        List<InstAuditDataEntity> instAuditDataEntities = instAuditDataService.queryList(queryAuditDataEntity);
        instKycVO.setAuditDataList(respVoAssembler.toInstAuditDataVo(instAuditDataEntities));

        return instKycVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(InstKycReqDTO instKycReqDTO) {
        //请求转换
        InstKycEntity instKycEntity = reqDtoAssembler.toInstKycEntity(instKycReqDTO);
        int result = instKycService.save(instKycEntity);

        // 更新阶段状态
        instApplyOrderService.updateStageStatus(instKycEntity.getApplyNo(), ApplyStageEnum.KYC, instKycEntity.getStatus());

        // 保存审核资料数据
        List<InstAuditDataReqDTO> auditDataList = instKycReqDTO.getAuditDataList();
        if (CollectionUtils.isNotEmpty(auditDataList)) {
            // 保存审核资料数据
            auditDataList.forEach(auditData -> {
                InstAuditDataEntity auditDataEntity = reqDtoAssembler.toInstAuditDataEntity(auditData);
                auditDataEntity.setBusinessType(AuditBusiTypeEnum.KYC.name());
                auditDataEntity.setBusinessNo(String.valueOf(instKycEntity.getId()));
                instAuditDataService.save(auditDataEntity);
            });
        }
        return result;

    }
}
