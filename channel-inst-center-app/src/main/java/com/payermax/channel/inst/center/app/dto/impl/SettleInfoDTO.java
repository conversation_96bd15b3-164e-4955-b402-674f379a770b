package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 机构合同手续费
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettleInfoDTO extends ExcelUtil.BaseExcelRow {



    /*--------------此处为 Excel 表格必填项---------------*/
    /**
     * 机构产品名称
     */
    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty("PaymentMethod Name\n机构产品名称")
    private String instProductName;

    /**
     * 机构产品名称
     */
    @NotEmpty(message = "支付币种不能为空")
    @ExcelProperty("Transaction Currency\n支付币种")
    private String transactionCurrency;

    /**
     * 机构产品名称
     */
    @NotEmpty(message = "结算币种不能为空")
    @ExcelProperty("Settlement Currency\n结算币种")
    private String settlementCurrency;

    /*--------------此处为 Excel 表格非必填项---------------*/

    /**
     * 起结金额
     */
    @ExcelProperty("Minimum settlement amount\n起结金额")
    private String minimumSettlementAmount;

    /**
     * 计费方式
     */
    @ExcelProperty("Calculation Method\n计费方式")
    private String calculationMethod;

    /**
     * 比例费用
     */
    @ExcelProperty("Percentage Ratio\n比例费用")
    private String percentageRatio;

    /**
     * 最低收费
     */
    @ExcelProperty("Min Fee\n最低收费")
    private String minFee;

    /**
     * 封顶收费
     */
    @ExcelProperty("Max Fee\n封顶收费")
    private String maxFee;

    /**
     * 固定收费
     */
    @ExcelProperty("Fixed Fee\n固定费用")
    private String fixedFee;

    /**
     * 固定费用币种
     */
    @ExcelProperty("Fixed Fee Currency\n固定费用币种")
    private String fixedFeeCurrency;


    /*--------------此处为标准化内容---------------*/



    /*--------------PayIn---------------*/
    /**
     * 结算周期
     */
    @ExcelIgnore
    private String settlementTerm;

    /**
     * 涵盖交易范围开始日期
     */
    @ExcelIgnore
    private String tradingRangeStart;

    /**
     *
     * 涵盖交易范围结束日期
     */
    @ExcelIgnore
    private String tradingRangeEnd;


    /**
     * 结算方式
     */
    @ExcelIgnore
    private String settlementMethod;

    /**
     * 结算账户
     */
    @ExcelIgnore
    private String settlementAccount;


    /**
     * 账单日
     */
    @ExcelIgnore
    private String statementDate;


    /**
     * 打款日
     */
    @ExcelIgnore
    private String paymentDate;

    /**
     * 到账日
     */
    @ExcelIgnore
    private String arriveDate;

    /**
     * 换汇日
     */
    @ExcelIgnore
    private String exchangeDate;


    /*--------------Payout---------------*/

    @ExcelIgnore
    private List<rechargeAccountInfo> rechargeAccountList;


    /**
     * 充值信息
     */
    @Data
    @Getter
    @Setter
    public static class rechargeAccountInfo {
        /**
         * 充值币种
         */
        private String rechargeCurrency;

        /**
         * 充值账户
         */
        private String rechargeAccount;

        /**
         * 机构收款银行帐户
         */
        private String instReceivingAccount;
    }

    /**
     * 机构产品名称+支付币种+结算币种
     * 构成用于和机构合同对应的唯一标识
     */
    public String getSettleKey() {
        List<String> keyList = Stream.of(getInstProductName(), getTransactionCurrency(), getSettlementCurrency())
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return org.apache.commons.lang3.StringUtils.join(keyList, SymbolConstants.SYMBOL_STRIKE_LINE);
    }
}
