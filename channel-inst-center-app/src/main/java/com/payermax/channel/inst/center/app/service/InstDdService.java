package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity;

import java.util.List;

/**
 * DD service
 *
 * <AUTHOR>
 * @date 2022/5/15 22:40
 */
public interface InstDdService {

    /**
     * 保存DD
     *
     * @param record
     * @return
     */
    int save(InstDdEntity record);

    /**
     * 根据机构ID查询
     *
     * @param instId
     * @return
     */
    InstDdEntity getByInstId(Long instId);

    /**
     * 根据机构id查询DD信息
     * @param instIds
     * @return
     */
    List<InstDdQueryEntity> getByInstIds(List<Long> instIds);
}
