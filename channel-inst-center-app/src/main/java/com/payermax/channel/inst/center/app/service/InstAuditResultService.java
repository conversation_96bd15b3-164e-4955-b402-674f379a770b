package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity;


/**
 * 审核结果相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/19 9:32
 */
public interface InstAuditResultService {

    /**
     * 查询审核结果信息
     *
     * @return
     */
    InstAuditResultEntity query(InstAuditResultEntity instAuditResultEntity);

    /**
     * 保存审核结果信息
     *
     * @return
     */
    int save(InstAuditResultEntity instAuditDataEntity);


}
