package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.event.InstSubNumberSegmentAlertEvent;
import com.payermax.channel.inst.center.app.manage.InstSubNumberSegmentManage;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName InstSubFundsAccountBucketManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/10/24 16:39
 * @Version 1.0
 */
@Service
@Slf4j
public class InstSubNumberSegmentManageImpl implements InstSubNumberSegmentManage {


    @Resource
    DingAlertClient dingAlertClient;

    @NacosValue(value = "${multilevel-cache.redis.prefix}", autoRefreshed = true)
    private String prefix;

    @NacosValue(value = "#{${inst.funds.account.number-segment.ding.map}}", autoRefreshed = true)
    private HashMap<String, Integer> numberSegmentDingMap;

    /**
     * 监听预申请账号表数据
     */
    @Override
    @Async(CommonConstants.ASYNC_TASK_EXECUTOR_NAME)
    @EventListener
    public void onApplicationEvent(InstSubNumberSegmentAlertEvent event) {
        InstFundsAccountEntity instFundsAccountEntity = event.getInstFundsAccountEntity();
        // 预申请是否有告警配置
        if (numberSegmentDingMap.containsKey(instFundsAccountEntity.getAccountId())) {
            List<InstSubNumberSegmentEntity> list = event.getSubNumberSegmentEntityList();

            long totalCount = 0;
            long usedCount = 1;
            for (InstSubNumberSegmentEntity numberSegment : list) {
                String numberStart = numberSegment.getNumberStart();
                String numberEnd = numberSegment.getNumberEnd();
                String originalMaxUsed = numberSegment.getMaxUsed();

                if (ObjectUtil.equal(numberStart, numberEnd)) {
                    // 单个子账号号段
                    totalCount++;
                } else {
                    // 范围子账号号段
                    BigDecimal numberStartBigDecimal = new BigDecimal(numberStart);
                    BigDecimal numberEndBigDecimal = new BigDecimal(numberEnd);
                    BigDecimal numberCount = numberEndBigDecimal.subtract(numberStartBigDecimal);
                    totalCount += numberCount.stripTrailingZeros().longValue() + 1L;
                    if (StringUtils.isNotBlank(originalMaxUsed)) {
                        BigDecimal originalMaxUsedBigDecimal = new BigDecimal(originalMaxUsed);
                        BigDecimal numberUsedCount = originalMaxUsedBigDecimal.subtract(numberStartBigDecimal);
                        usedCount += numberUsedCount.stripTrailingZeros().longValue() + 1L;
                    }
                }
            }

            // 剩余是否达到告警限制
            if ((totalCount - usedCount) < numberSegmentDingMap.get(instFundsAccountEntity.getAccountId())) {
                // 发送钉钉告警
                String title = "机构账号【号段】即将用尽";
                String message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 国家：%s\n- 币种：%s\n- 使用类型：%s\n- 号段剩余账号数：%s个\n"
                        , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instFundsAccountEntity.getCountry(), instFundsAccountEntity.getCurrency(), instFundsAccountEntity.getUseType(), totalCount - usedCount);
                dingAlertClient.sendMsgForExceptionGroupSubAccount(CommonConstants.PUSH_SUB_ACCOUNT_HANDLE_GROUP, title, message);
            }
        }
    }
}
