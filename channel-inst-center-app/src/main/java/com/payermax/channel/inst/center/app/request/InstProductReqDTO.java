package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstProductReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 15:20
 */
@Data
public class InstProductReqDTO implements Serializable {

    private static final long serialVersionUID = 7065061527201835884L;

    @ApiModelProperty(notes = "产品编码")
    private String productCode;

    @ApiModelProperty(notes = "机构标识")
    private String instId;

    @ApiModelProperty(notes = "产品名称")
    private String productName;

    @ApiModelProperty(notes = "渠道类型")
    private String channelType;

    @ApiModelProperty(notes = "支付方式类型")
    private String paymentMethodType;

    @ApiModelProperty(notes = "客户类型")
    private String customerType;

    @ApiModelProperty(notes = "有无行业限制 Y:是,N:否")
    private String isLimitMcc;

    @ApiModelProperty(notes = "行业限制详情 json")
    private String mccDetail;

    private String isDivideMid;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "扩展配置 json")
    private String extraInfo;

    @ApiModelProperty(notes = "机构产品能力集合")
    private List<InstProductCapabilityReqDTO> productCapabilityList;
    /**
     * 合同单号
     */
    @ApiModelProperty(hidden = true)
    private String contractNo;

    @ApiModelProperty(hidden = true)
    private String capabilityExtraInfo;

    @ApiModelProperty(hidden = true)
    private List<String> productCodes;

    /**
     * 对应一次操作的版本
     */
    @ApiModelProperty(hidden = true)
    private String version;

    /**
     * 一次操作的所有国家
     */
    @ApiModelProperty(hidden = true)
    private List<String> countrys;

    /**
     * 一次操作的所有目标机构
     */
    @ApiModelProperty(hidden = true)
    private List<String> targetOrgs;

    /**
     * 一次操作的所有卡组织
     */
    @ApiModelProperty(hidden = true)
    private List<String> cardOrgs;
}
