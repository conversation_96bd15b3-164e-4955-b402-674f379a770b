package com.payermax.channel.inst.center.app.manage.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.enums.CountryEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.app.manage.InstProductManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstProductCapabilityReqDTO;
import com.payermax.channel.inst.center.app.request.InstProductReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductCapabilityVO;
import com.payermax.channel.inst.center.app.response.InstProductVO;
import com.payermax.channel.inst.center.app.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InstProductManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 16:02
 */
@Service
public class InstProductManagerImpl implements InstProductManager {

    @Autowired
    private InstContractService instContractService;

    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductCapabilityService instProductCapabilityService;

    @Autowired
    private InstContractProductService instContractProductService;

    @Autowired
    private InstProductFeeService instProductFeeService;

    @Autowired
    private InstTransFeeService instTransFeeService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public int save(InstProductReqDTO productReqDTO) {
        InstProductEntity instProductEntity = reqDtoAssembler.toInstProductEntity(productReqDTO);
        int result = instProductService.save(instProductEntity);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAll(InstProductReqDTO productReqDTO) {
        InstProductEntity instProductEntity = reqDtoAssembler.toInstProductEntity(productReqDTO);
        //检查合同状态
        InstContractEntity entity = new InstContractEntity();
        entity.setContractNo(productReqDTO.getContractNo());
        InstContractEntity contractEntity = instContractService.getByEntity(entity);
        Preconditions.checkArgument(contractEntity != null, "contract is not exists");
        //不控制合同状态，后续迭代
        //Preconditions.checkArgument("INIT".equalsIgnoreCase(contractEntity.getStatus()) || "AUDIT_RETURN".equalsIgnoreCase(contractEntity.getStatus()), "the current contract status is not allowed");
        //保存产品
        instProductService.save(instProductEntity);
        int result = 0;
        if(StringUtils.isBlank(productReqDTO.getVersion())){
            //新增产品能力
            List<InstProductCapabilityReqDTO> productCapabilityReqDTOs = assembleProductCapability(instProductEntity.getProductCode(), productReqDTO);
            List<InstProductCapabilityEntity> instProductCapabilityEntities = reqDtoAssembler.toInstProductCapabilityEntities(productCapabilityReqDTOs);
            instProductCapabilityService.saveBatch(instProductCapabilityEntities);
            //新增合同签约产品能力信息
            String version = UUID.randomUUID().toString();
            List<InstContractProductEntity> instContractProductEntities = instProductCapabilityEntities.stream().map(obj -> {
                InstContractProductEntity instContractProductEntity = new InstContractProductEntity();
                instContractProductEntity.setContractNo(productReqDTO.getContractNo());
                instContractProductEntity.setInstProductCode(instProductEntity.getProductCode());
                instContractProductEntity.setInstProductCapabilityCode(obj.getCapabilityCode());
                instContractProductEntity.setVersion(version);
                return instContractProductEntity;
            }).collect(Collectors.toList());
            //后新增
            result = instContractProductService.saveBatch(instContractProductEntities);
            return result;
        }
        //查合同签约产品能力
        InstContractProductEntity contractProductEntity = new InstContractProductEntity();
        contractProductEntity.setContractNo(productReqDTO.getContractNo());
        contractProductEntity.setInstProductCode(instProductEntity.getProductCode());
        contractProductEntity.setVersion(productReqDTO.getVersion());
        List<InstContractProductEntity> contractProductEntities = instContractProductService.selectByEntity(contractProductEntity);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(contractProductEntities), "contract product capability is not exists");
        //删产品能力
        List<String> capabilityCodes = contractProductEntities.stream().map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList());
        instProductCapabilityService.deleteByProductCodeAndCapabilityCode(instProductEntity.getProductCode(),capabilityCodes);
        List<InstProductCapabilityReqDTO> productCapabilityReqDTOs = assembleProductCapability(instProductEntity.getProductCode(), productReqDTO);
        List<InstProductCapabilityEntity> instProductCapabilityEntities = reqDtoAssembler.toInstProductCapabilityEntities(productCapabilityReqDTOs);
        instProductCapabilityService.saveBatch(instProductCapabilityEntities);
        //保存合同签约产品信息
        instContractProductService.deleteByProductCodeAndVersion(instProductEntity.getProductCode(),productReqDTO.getVersion());
        List<InstContractProductEntity> instContractProductEntities = instProductCapabilityEntities.stream().map(obj -> {
            InstContractProductEntity instContractProductEntity = new InstContractProductEntity();
            instContractProductEntity.setContractNo(productReqDTO.getContractNo());
            instContractProductEntity.setInstProductCode(instProductEntity.getProductCode());
            instContractProductEntity.setInstProductCapabilityCode(obj.getCapabilityCode());
            instContractProductEntity.setVersion(productReqDTO.getVersion());
            return instContractProductEntity;
        }).collect(Collectors.toList());
        //后新增
        result = instContractProductService.saveBatch(instContractProductEntities);
        return result;
    }

    @Override
    public List<InstProductVO> query(InstProductReqDTO productReqDTO) {
        InstProductEntity instProductEntity = reqDtoAssembler.toInstProductEntity(productReqDTO);
        List<InstProductEntity> instProductEntities = instProductService.getByEntity(instProductEntity);
        if (CollectionUtils.isNotEmpty(instProductEntities)) {
            return respVoAssembler.toInstProductVos(instProductEntities);
        }
        return null;
    }

    @Override
    public List<InstProductVO> queryAll(String contractNo) {
        //合同下签约的所有产品
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.getByContractNo(contractNo);
        if(CollectionUtils.isEmpty(instContractProductEntities)){
            return null;
        }
        List<String> productCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCode).distinct().collect(Collectors.toList());
        List<InstProductVO> instProductVOs= queryInstProduct(productCodes);
        return instProductVOs;
    }

    private List<InstProductVO> queryInstProduct(List<String> productCodes) {
        //合同下所有产品
        List<InstProductEntity> instProductEntities = instProductService.getByProductCodes(productCodes);
        if(CollectionUtils.isNotEmpty(instProductEntities)){
            //合同下所有产品能力
            List<InstProductCapabilityEntity> productCapabilityEntities = instProductCapabilityService.getByProductCodes(productCodes);
            List<InstProductCapabilityVO> instProductCapabilityVOs = respVoAssembler.toInstProductCapabilityVos(productCapabilityEntities);
            List<InstProductVO> instProductVOs = respVoAssembler.toInstProductVos(instProductEntities);
            instProductVOs = instProductVOs.stream().sorted(Comparator.comparing(InstProductVO::getChannelType)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(instProductCapabilityVOs)){
                Map<String, List<InstProductCapabilityVO>> productCapabilityMap = instProductCapabilityVOs.stream().collect(Collectors.groupingBy(InstProductCapabilityVO::getInstProductCode));
                List<InstProductVO> finalInstProductVOList = instProductVOs.stream().map(productVO -> {
                    InstContractProductEntity contractProductEntity = new InstContractProductEntity();
                    contractProductEntity.setInstProductCode(productVO.getProductCode());
                    List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(contractProductEntity);
                    Map<String, InstContractProductEntity> versionMap = instContractProductEntities.stream().collect(Collectors.toMap(InstContractProductEntity::getInstProductCapabilityCode, Function.identity()));
                    List<InstProductCapabilityVO> productCapabilityVOList = productCapabilityMap.get(productVO.getProductCode());
                    productCapabilityVOList = productCapabilityVOList.stream().map(capability->{
                        capability.setVersion(versionMap.get(capability.getCapabilityCode()).getVersion());
                        capability.setFeeGroupId(versionMap.get(capability.getCapabilityCode()).getFeeGroupId());
                        return capability;
                    }).collect(Collectors.toList());
                    productVO.setProductCapabilityList(productCapabilityVOList);
                    return productVO;
                }).collect(Collectors.toList());
                return finalInstProductVOList;
            }
        }
        return null;
    }

    @Override
    public InstProductVO query(String productCode,String version) {
        Preconditions.checkArgument(StringUtils.isNotBlank(version), "version is mandatory");
        InstContractProductEntity contractProductEntity = new InstContractProductEntity();
        contractProductEntity.setInstProductCode(productCode);
        contractProductEntity.setVersion(version);
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(contractProductEntity);
        List<InstProductEntity> instProductEntityList = instProductService.getByProductCodes(Arrays.asList(productCode));
        if(CollectionUtils.isNotEmpty(instProductEntityList)){
            InstProductEntity instProductEntity = instProductEntityList.get(0); //NO_CHECK 方法未被调用
            InstProductVO instProductVO = respVoAssembler.toInstProductVo(instProductEntity);
            List<InstProductCapabilityEntity> productCapabilityEntityList = instProductCapabilityService.getByProductCodeAndCapabilityCodes(productCode, instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList()));
            instProductVO.setVersion(version);
            instProductVO.setCountrys(productCapabilityEntityList.stream().map(InstProductCapabilityEntity::getCountry).distinct().collect(Collectors.joining(",")));
            instProductVO.setTargetOrgs(productCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getTargetOrg())).map(InstProductCapabilityEntity::getTargetOrg).distinct().collect(Collectors.joining(",")));
            instProductVO.setCardOrgs(productCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getCardOrg())).map(InstProductCapabilityEntity::getCardOrg).distinct().collect(Collectors.joining(",")));
            instProductVO.setCapabilityExtraInfo(productCapabilityEntityList.get(0).getSettlementExtraInfo()); //NO_CHECK 方法未被调用
            return instProductVO;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(String productCode,String version) {
        int result = 0;
        InstContractProductEntity contractProductEntity = new InstContractProductEntity();
        contractProductEntity.setInstProductCode(productCode);
        contractProductEntity.setVersion(version);
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(contractProductEntity);
        Preconditions.checkArgument(instContractProductEntities != null, "contract product capability is not exists");
        //删除当前version相关的数据

        //1、删除产品能力配置的费用明细
        List<String> feeGroupIds = instContractProductEntities.stream().map(InstContractProductEntity::getFeeGroupId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(feeGroupIds)){
            InstContractProductEntity entity = new InstContractProductEntity();
            entity.setInstProductCode(productCode);
            List<InstContractProductEntity> instContractProductEntityList = instContractProductService.selectByEntity(entity);
            //检查当前产品下是否有其他version在使用的交易费用配置
            List<String> otherFeeGroupIds = instContractProductEntityList.stream().filter(obj -> !version.equals(obj.getVersion()) && StringUtils.isNotBlank(obj.getFeeGroupId()))
                    .map(InstContractProductEntity::getFeeGroupId).distinct().collect(Collectors.toList());
            //当前删除的费用配置没有别的能力在用
            if(CollectionUtils.isEmpty(otherFeeGroupIds)){
                instTransFeeService.deleteByFeeGroupIds(feeGroupIds);
            }else{
                feeGroupIds = feeGroupIds.stream().filter(feeGroupId->!otherFeeGroupIds.contains(feeGroupId)).distinct().collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(feeGroupIds)){
                    instTransFeeService.deleteByFeeGroupIds(feeGroupIds);
                }
            }
        }
        //2、删除产品能力
        instProductCapabilityService.deleteByProductCodeAndCapabilityCode(productCode,instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList()));
        //3、删除合同签约产品能力信息
        instContractProductService.deleteByProductCodeAndVersion(productCode,version);
        //4、删除产品信息
        List<InstProductCapabilityEntity> productCapabilityEntityList = instProductCapabilityService.getByProductCodes(Arrays.asList(productCode));
        if(CollectionUtils.isEmpty(productCapabilityEntityList)){
            //删除产品费用信息
            instProductFeeService.deleteByContractNoAndProductCode(instContractProductEntities.get(0).getContractNo(),productCode); //NO_CHECK 方法未被调用
            //删除产品
            result = instProductService.deleteByProductCode(productCode);
        }
        return result;
    }

    /**
     * 组装产品能力
     * 每一次操作的所有产品能力对应一个相同的操作版本
     * @param productReqDTO
     * @return
     */
    private List<InstProductCapabilityReqDTO> assembleProductCapability(String productCode, InstProductReqDTO productReqDTO) {
        List<String> countrys = productReqDTO.getCountrys();
        List<String> targetOrgs = productReqDTO.getTargetOrgs();
        List<String> cardOrgs = productReqDTO.getCardOrgs();
        boolean targetOrgNotEmpty = CollectionUtils.isNotEmpty(targetOrgs) && StringUtils.isNotBlank(targetOrgs.get(0)); //NO_CHECK 方法未被调用
        List<String> list = targetOrgNotEmpty ? targetOrgs : cardOrgs;
        List<InstProductCapabilityReqDTO> instProductCapabilityReqDTOs = list.stream().distinct().map(obj -> {
            List<InstProductCapabilityReqDTO> productCapabilityReqDTOS = countrys.stream().distinct().map(country -> {
                InstProductCapabilityReqDTO instProductCapabilityReqDTO = new InstProductCapabilityReqDTO();
                instProductCapabilityReqDTO.setCapabilityCode("PC" + UUID.randomUUID().toString().replaceAll("-","").toUpperCase());
                instProductCapabilityReqDTO.setInstProductCode(productCode);
                instProductCapabilityReqDTO.setCountry(country);
                instProductCapabilityReqDTO.setCurrency(CountryEnum.getCurrency(country));
                instProductCapabilityReqDTO.setTargetOrg(targetOrgNotEmpty ? obj : StringUtils.EMPTY);
                instProductCapabilityReqDTO.setCardOrg(!targetOrgNotEmpty ? obj : StringUtils.EMPTY);
                instProductCapabilityReqDTO.setSettlementExtraInfo(productReqDTO.getCapabilityExtraInfo());
                return instProductCapabilityReqDTO;
            }).collect(Collectors.toList());
            return productCapabilityReqDTOS;
        }).flatMap(obj -> obj.stream()).collect(Collectors.toList());
        return instProductCapabilityReqDTOs;
    }
}
