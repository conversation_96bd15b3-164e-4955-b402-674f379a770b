package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.nacos.common.utils.StringUtils;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.common.enums.instcontract.VaFeeTypeEnum;
import com.ushareit.fintech.common.model.constants.CommonConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/2/21
 * @DESC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstContractFeeVaDTO  extends AbstractInstContractFeeDTO {


    /**
     * 机构产品名称
     * {@link VaFeeTypeEnum}
     */
    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty("Fee Type\nVA费用类型")
    private String feeType;

    /**
     * 收款国家/区域
     */
    @ExcelProperty("Collect Country\n收款国家/区域")
    private String country;


    /**
     * VA 账号费信息
     */
    @ExcelIgnore
    private InstContractFeeVaDTO accountFeeInfo;

    /**
     * 机构 + 我方主体 + 合作模式 + 机构产品类型 + 机构产品名称 + 支付币种 + 费用类型(账号费、交易费)  + 国家+ 清算网络 + 费用承担方 + 生效时间
     * 构成唯一机构合约
     */
    @Override
    public String getInstContractKey(){
        List<String> keyList = Stream.of(getInstCode(), getContractEntity(), getCooperationMode(), getProductType(), getInstProductName(),
                        getTransactionCurrency(), getFeeType(), getCountry(), getClearNetWork(), getFeeBearer(),
                        String.valueOf(getEffectiveTime().getTime()))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, CommonConstants.SYMBOL_STRIKE_LINE);
    }

    /**
     * 生成不带 FeeType 的唯一键，用于将 账号费 和 交易费 组合
     * 机构 + 我方主体 + 合作模式 + 机构产品类型 + 机构产品名称 + 支付币种 + 国家+ 清算网络 + 费用承担方 + 生效时间
     */
    public String getInstContractKeyWithoutFeeType() {
        List<String> keyList = Stream.of(getInstCode(), getContractEntity(), getCooperationMode(), getProductType(), getInstProductName(),
                        getTransactionCurrency(), getCountry(), getClearNetWork(), getFeeBearer(),
                        String.valueOf(getEffectiveTime().getTime()))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList,CommonConstants.SYMBOL_STRIKE_LINE);
    }
}
