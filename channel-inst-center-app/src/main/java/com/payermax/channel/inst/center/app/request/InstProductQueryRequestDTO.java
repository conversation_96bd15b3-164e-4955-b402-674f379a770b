package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/24
 * @DESC
 */
@Data
public class InstProductQueryRequestDTO {

    /**
     * 机构
     */
    private String instCode;

    /**
     * 主体
     */
    private String entity;

    /**
     * 产品状态
     */
    private String status;

    /**
     * 录入时间区间
     */
    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private List<Date> entryTimeRange;

}
