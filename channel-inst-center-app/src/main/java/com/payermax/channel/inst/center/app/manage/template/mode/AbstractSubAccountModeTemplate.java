package com.payermax.channel.inst.center.app.manage.template.mode;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.event.InstSubNumberSegmentAlertEvent;
import com.payermax.channel.inst.center.app.manage.InstFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.manage.template.ApiTemplateUtil;
import com.payermax.channel.inst.center.app.manage.template.activation.SubAccountActivationTemplate;
import com.payermax.channel.inst.center.app.service.InstAccuntNumberSegmentMappingService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.app.service.InstSubNumberSegmentService;
import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.StatusEnumYN;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.IsSupportYNEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.common.utils.AccountIdUtils;
import com.payermax.channel.inst.center.common.utils.BigDecimalUtil;
import com.payermax.channel.inst.center.common.utils.StringFormatUtil;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubNumberSegmentQueryEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.infra.tool.lock.redisson.UredissonLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2022/10/10 11:17
 **/
@Service
@Slf4j
public abstract class AbstractSubAccountModeTemplate implements SubAccountModeTemplate {

    @Autowired
    public UredissonLock uredissonLock;
    @Autowired
    public ReqDoAssembler reqDoAssembler;
    @Autowired
    public DingAlertClient dingAlertClient;
    @Autowired
    public ApiTemplateUtil apiTemplateUtil;
    @Autowired
    public TransactionTemplate transactionTemplate;
    @Autowired
    public StateMachineExecutor stateMachineExecutor;
    @Autowired
    public SubAccountActivationTemplate apiActivationTemplate;
    @Autowired
    public ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    public InstSubFundsAccountService instSubFundsAccountService;
    @Autowired
    public SubAccountActivationTemplate offlineActivationTemplate;
    @Autowired
    public InstSubNumberSegmentService instSubNumberSegmentService;
    @Autowired
    public InstFundsAccountBucketManage instFundsAccountBucketManage;
    @Autowired
    InstAccuntNumberSegmentMappingService instAccuntNumberSegmentMappingService;

    @NacosValue(value = "${multilevel-cache.redis.prefix}", autoRefreshed = true)
    private String prefix;

    @NacosValue(value = "#{'${inst.funds.account.special.name.value:NETBANKPH01_41001000015_PH_PHP,DBSHK01_001220363_HK_USD,DBSHK01_1220363_HK_CNY,DBSHK01_1220363_HK_HKD}'.split(',')}", autoRefreshed = true)
    private List<String> specialNameValue;

    @Override
    public InstSubFundsAccountEntity buildCreateSubAccountRecord(RequestAccountDO requestAccountDO) {

        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountRequestDO instSubFundsAccountRequestDO = requestAccountDO.getInstSubFundsAccountRequestDO();

        InstSubFundsAccountEntity subFundsAccountEntity = reqDoAssembler.toInstSubFundsAccountEntity(instSubFundsAccountRequestDO);
        
        // 判断是否支持自定义名称
        if (Objects.equals(instFundsAccountEntity.getIsSupportCustomName(), IsSupportYNEnum.N.getType())) {
            // 【不支持】检查是否特殊机构主账号（特殊机构主账号是在不支持自定名称时，必须使用商户注册名）
            if (specialNameValue.contains(instFundsAccountEntity.getAccountId())) {
                // 置为商户注册名
                subFundsAccountEntity.setSubAccountName(instSubFundsAccountRequestDO.getSubAccountName());
            } else {
                // 置为机构主账号名称
                subFundsAccountEntity.setSubAccountName(instFundsAccountEntity.getAccountName());
            }
        } else {
            subFundsAccountEntity.setSubAccountName(instSubFundsAccountRequestDO.getSubAccountName());
        }

        // 执行子级账号名称的正则规则
        String subAccountName = subFundsAccountEntity.getSubAccountName();
        LinkedHashMap<String, Object> accountJsonMap = JSON.parseObject(instFundsAccountEntity.getAccountJson(),LinkedHashMap.class, Feature.OrderedField);
        if (Objects.nonNull(subAccountName) && Objects.nonNull(accountJsonMap) && accountJsonMap.containsKey(CommonConstants.INST_SUB_ACCOUNT_NAME_RULE)) {
            String subAccountNameRule = accountJsonMap.get(CommonConstants.INST_SUB_ACCOUNT_NAME_RULE).toString();
            LinkedHashMap<String, String> subAccountNameRuleMap = JSON.parseObject(subAccountNameRule,LinkedHashMap.class, Feature.OrderedField);
            for (Map.Entry<String, String> entry : subAccountNameRuleMap.entrySet()) {
                subAccountName = subAccountName.replaceAll(entry.getKey(),  entry.getValue());
            }
            subFundsAccountEntity.setSubAccountName(subAccountName);
        }
        
        try {
            // 新增子级账号记录-》初始化
            subFundsAccountEntity.setAccountId(instFundsAccountEntity.getAccountId());
            subFundsAccountEntity.setStatus(SubAccountStatusEnum.INITIATE.getStatus());
            subFundsAccountEntity.setSubAccountId(AccountIdUtils.buildSubAccountId(instFundsAccountEntity.getAccountId()));
            instSubFundsAccountService.insert(subFundsAccountEntity);
            InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
            queryEntity.setSubAccountId(subFundsAccountEntity.getSubAccountId());
            return instSubFundsAccountService.queryById(queryEntity);
        } catch (Exception e) {
            log.error("AbstractSubAccountModeTemplate-buildCreateSubAccountRcord record:{}, exception {}", JSON.toJSONString(instSubFundsAccountRequestDO), e);
            throw new BusinessException(ErrorCodeEnum.RECORD_SUB_ACCOUNT_FAIL.getCode(), ErrorCodeEnum.RECORD_SUB_ACCOUNT_FAIL.getMsg());
        }
    }

    public void checkIsNeedActivation(RequestAccountDO requestAccountDO) {
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 终态则不再进行后续操作
        if (SubAccountStatusEnum.isFinalStatus(instSubFundsAccountEntity.getStatus())) {
            return;
        }

        StateRequest stateRequest = new StateRequest(instSubFundsAccountEntity.getStatus(), SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
        // 需要激活
        if (ObjectUtil.equal(instFundsAccountEntity.getIsNeedActivation(), IsSupportYNEnum.Y.getType())) {
            // 更新状态 -》待激活
            instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.TO_BE_ACTIVATED.getStatus());
            stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
            // 获取激活处理模版
            SubAccountActivationTemplate accountActivationTemplate = this.routeActivationTemplate(instFundsAccountEntity.getActivationMode());
            accountActivationTemplate.activation(requestAccountDO);
        } else {
            // 号段模式不需要激活时,则更新状态 -》已激活
            if (StringUtils.equals(instFundsAccountEntity.getSubAccountMode(), SubAccountModeEnum.NUMBER_SEGMENT.getType())) {
                instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.ACTIVATED.getStatus());
                stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
            }
        }

    }

    /**
     * 使用号段计算子级账号LOCK
     **/
    public void buildNumberSegmentAccountNoLock(RequestAccountDO requestAccountDO) {
        // 生成子级账号模版为空，说明不需要使用号段生成子级账号
        if (StringUtils.isBlank(requestAccountDO.getInstFundsAccountEntity().getSubAccountRule())) {
            return;
        }
        
        long startTime = System.currentTimeMillis();
        // 分布式锁
        String key = prefix.concat(Thread.currentThread().getStackTrace()[1].getMethodName()).concat(":").concat(requestAccountDO.getInstFundsAccountEntity().getAccountId());
        boolean locked = uredissonLock.lock(key, 5, TimeUnit.SECONDS);
        if (locked) {
            try {
                this.buildNumberSegmentAccountNo(requestAccountDO);
                return;
            } catch (Exception e) {
                log.error("InstSubFundsAccountBucketManageImpl-buildNumberSegmentAccountNo Exception:{}，costTime:{}", e, (System.currentTimeMillis() - startTime));
                throw e;
            } finally {
                uredissonLock.release(key);
            }
        }
        throw new BusinessException(ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getCode(), ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getMsg());
    }
    
    /**
     * 使用号段计算子级账号
     **/
    public void buildNumberSegmentAccountNo(RequestAccountDO requestAccountDO) {
        // 创建子级账号请求中部分参数
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();

        // 可用号段
        // 查询结构账号下可用号段信息
        InstSubNumberSegmentQueryEntity instSubNumberSegmentQuery = new InstSubNumberSegmentQueryEntity();
        instSubNumberSegmentQuery.setAccountId(instFundsAccountEntity.getAccountId());
        instSubNumberSegmentQuery.setUseType(instSubFundsAccountEntity.getSubUseType());
        instSubNumberSegmentQuery.setStatus(StatusEnumYN.Y.getType());
        List<InstSubNumberSegmentEntity> instSubNumberSegmentEntityList = instSubNumberSegmentService.queryListByAccountId(instSubNumberSegmentQuery);
        // 机构使用号段模式生成子级资金账号，检查是否有号段可用，不存在则跳过
        if (CollectionUtil.isEmpty(instSubNumberSegmentEntityList)) {
            try {
                // 发送钉钉通知
                String title = "机构子级账号【无可用号段】告警";
                String message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 使用类型：%s\n- 申请商户：%s"
                        , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instSubFundsAccountEntity.getSubUseType(), instSubFundsAccountEntity.getMerchantNo());
                dingAlertClient.sendMsgForExceptionGroupSubAccount(CommonConstants.PUSH_SUB_ACCOUNT_EXCEPTION_GROUP, title, message);
            } catch (Exception e) {
                log.info("AbstractSubAccountModeTemplate-buildNumberSegmentSubAccountNo exception:{}", e);
            }
            return;
        }

        applicationEventPublisher.publishEvent(new InstSubNumberSegmentAlertEvent(this, instFundsAccountEntity, instSubNumberSegmentEntityList));

        // 生成可用子账号
        Iterator<InstSubNumberSegmentEntity> iterator = instSubNumberSegmentEntityList.iterator();
        long startTime = System.currentTimeMillis();
        while (iterator.hasNext()) {

            InstSubNumberSegmentEntity numberSegment = iterator.next();
            String numberStart = numberSegment.getNumberStart();
            String numberEnd = numberSegment.getNumberEnd();
            String originalMaxUsed = numberSegment.getMaxUsed();

            String maxUsed = null;
            if (ObjectUtil.equal(numberStart, numberEnd)) {
                // 单个子账号
                maxUsed = numberEnd;
            } else {
                // 子账号范围
                if (StringUtils.isBlank(originalMaxUsed)) {
                    maxUsed = numberStart;
                } else {
                    BigDecimal numberStartBigDecimal = new BigDecimal(originalMaxUsed);
                    BigDecimal subAccountNoBigDecimal = numberStartBigDecimal.add(BigDecimal.valueOf(1)).stripTrailingZeros();
                    // 补0
                    int fill0 = numberEnd.length() - subAccountNoBigDecimal.toPlainString().length();
                    maxUsed = BigDecimalUtil.bigDecimal2StringFill0(fill0, subAccountNoBigDecimal);
                }
            }

            try {
                numberSegment.setMaxUsed(maxUsed);
                if (ObjectUtil.equal(maxUsed, numberEnd)) {
                    numberSegment.setStatus(StatusEnumYN.N.getType());
                }

                // 模版计算子级账号
                this.buildNumberSegmentNo(numberSegment, instFundsAccountEntity, instSubFundsAccountEntity, requestAccountDO.getInstSubFundsAccountBucketsNotNullEntityList());
                // 更新子级账户信息 及 关联号段表中间表信息
                this.updateSubAccountInfo(instFundsAccountEntity, instSubFundsAccountEntity, numberSegment, originalMaxUsed);
                requestAccountDO.setInstSubNumberSegmentEntity(numberSegment);
                log.info("AbstractSubAccountModeTemplate-buildSubAccountNo costTime:{}", (System.currentTimeMillis() - startTime));
                break;
            } catch (Exception e) {
                log.info("AbstractSubAccountModeTemplate-buildSubAccountNo Exception:{}，costTime:{}", e, (System.currentTimeMillis() - startTime));
                continue;
            }
        }


    }

    /**
     * 事务更新子级账户信息（不涉及状态） 以及 关联号段表中间表信息
     **/
    public void updateSubAccountInfo(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity, InstSubNumberSegmentEntity instSubNumberSegmentEntity, String originalMaxUsed) {
        transactionTemplate.execute(status -> {
            try {
                StateRequest stateRequest = new StateRequest(instSubFundsAccountEntity.getStatus(), SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
                stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
                // 更新号段信息
                this.updateNumberSegmentMaxUsed(instSubNumberSegmentEntity, originalMaxUsed);
                return null;
            } catch (BusinessException e) {
                status.setRollbackOnly();
                log.info("AbstractSubAccountModeTemplate-updateSubAccountInfo instFundsAccountEntity:{},instSubFundsAccountEntity:{}, exception {}", JSON.toJSONString(instFundsAccountEntity), JSON.toJSONString(instSubFundsAccountEntity), e);
                throw e;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.info("AbstractSubAccountModeTemplate-updateSubAccountInfo instFundsAccountEntity:{},instSubFundsAccountEntity:{}, exception {}", JSON.toJSONString(instFundsAccountEntity), JSON.toJSONString(instSubFundsAccountEntity), e);
                throw new BusinessException(ErrorCodeEnum.UPDATE_SUB_ACCOUNT_FAIL.getCode(), ErrorCodeEnum.UPDATE_SUB_ACCOUNT_FAIL.getMsg());
            }
        });
    }

    /**
     * 更新号段表记录及关联表信息
     */
    public void updateNumberSegmentMaxUsed(InstSubNumberSegmentEntity instSubNumberSegmentEntity, String originalMaxUsed) {
        try {
            // 更新号段最新使用的子账号
            instSubNumberSegmentService.updateById(instSubNumberSegmentEntity, originalMaxUsed);
            // 更新账户号段中间表状态
            if (Objects.equals(instSubNumberSegmentEntity.getStatus(), StatusEnumYN.N.getType())) {
                InstAccountNumberSegmentMappingEntity segmentMappingEntity = new InstAccountNumberSegmentMappingEntity();
                segmentMappingEntity.setNumberSegmentId(instSubNumberSegmentEntity.getId());
                segmentMappingEntity.setStatus(instSubNumberSegmentEntity.getStatus());
                instAccuntNumberSegmentMappingService.updateByEntity(segmentMappingEntity);
            }
        } catch (Exception e) {
            log.info("AbstractSubAccountModeTemplate-updateNumberSegmentMaxUsed numberSegment:{},originalMaxUsed:{}, exception {}", JSON.toJSONString(instSubNumberSegmentEntity), originalMaxUsed, e);
            throw new BusinessException(ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getCode(), ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getMsg());
        }
    }

    /**
     * 模版计算子账号
     *
     * @param numberSegmentEntity
     * @param instFundsAccountEntity
     * @param instSubFundsAccountEntity
     * @param bucketEntityList
     */
    public void buildNumberSegmentNo(InstSubNumberSegmentEntity numberSegmentEntity, InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity, List<InstFundsAccountBucketEntity> bucketEntityList) {

        Map<String, Object> bucketMap = null;
        if (CollectionUtil.isNotEmpty(bucketEntityList)) {
            bucketMap = bucketEntityList.stream().collect(Collectors.toMap(InstFundsAccountBucketEntity::getKeyName, InstFundsAccountBucketEntity::getKeyValue));
        }
        // 示例：citi${instAccount}${subNumberSegment}
        String rule = instFundsAccountEntity.getSubAccountRule();
        String numberSegmentNo = StringFormatUtil.stringFormat(rule, numberSegmentEntity);
        numberSegmentNo = StringFormatUtil.stringFormat(numberSegmentNo, instFundsAccountEntity);
        numberSegmentNo = StringFormatUtil.stringFormat(numberSegmentNo, instSubFundsAccountEntity);
        numberSegmentNo = StringFormatUtil.stringFormat(numberSegmentNo, bucketMap);
        // 如果还存在${}参数未替换，报异常
        boolean chackFormat = StringFormatUtil.checkStringFormat(numberSegmentNo);
        if (!chackFormat) {
            throw new BusinessException(ErrorCodeEnum.TEMPLATE_CREATE_SUB_ACCOUNT.getCode(), ErrorCodeEnum.TEMPLATE_CREATE_SUB_ACCOUNT.getMsg());
        }
        instSubFundsAccountEntity.setNumberSegmentId(numberSegmentEntity.getId());
        instSubFundsAccountEntity.setNumberSegmentNo(numberSegmentNo);
    }

    /**
     * 根据子级资金账户激活模式获取一个处理模版
     **/
    @Override
    public SubAccountActivationTemplate routeActivationTemplate(String activationMode) {
        if (org.apache.commons.lang.StringUtils.isEmpty(activationMode)) {
            return null;
        }
        SubAccountActivationTemplate modeTemplate;
        switch (ActivationModeEnum.getByType(activationMode)) {
            case API:
                modeTemplate = apiActivationTemplate;
                break;
            case OFFLINE:
                modeTemplate = offlineActivationTemplate;
                break;
            default:
                throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_ACTIVATION_NOT_SUPPORT.getCode(), ErrorCodeEnum.SUB_ACCOUNT_ACTIVATION_NOT_SUPPORT.getMsg());
        }
        return modeTemplate;
    }
}
