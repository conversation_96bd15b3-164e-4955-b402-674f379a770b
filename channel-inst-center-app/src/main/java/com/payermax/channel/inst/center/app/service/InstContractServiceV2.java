package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.request.*;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportResponseDTO;
import com.payermax.channel.inst.center.app.response.InstContractInitResponseVo;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.FundingChannelProductInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * InstContractServiceV2
 *
 * <AUTHOR>
 * @desc
 */
public interface InstContractServiceV2 {

    /**
     * 初始化机构合同信息
     * @param instContractInitRequest
     * @return
     */
    @Deprecated
    InstContractImportResponseDTO initInstContract(InstContractImportRequestDTO instContractInitRequest);

    /**
     * 将机构合同保存到草稿表
     * @param instContractImportMsg
     */
    InstContractInitResponseVo instContractListSave(InstContractInitRequestDTO instContractImportMsg);

    /**
     * 解析机构合同 Excel 信息
     * @param request
     */
    InstContractImportDTO instContractParse(InstContractParseRequestDTO request);

    /**
     * 查询草稿列表(根据查询人过滤,财务BP/PD 可以看到所有草稿,GP只能看到负责机构的草稿)
     * @param shareId
     */
    List<InstProductItemVO> queryDraftList(String shareId);

    /**
     * 根据条件查询草稿列表
     * @param request
     */
    List<InstProductItemVO> queryDraftByConditions(InstProductQueryRequestDTO request);

    /**
     * 根据 ID 查询草稿
     * @param draftId
     */
    InstContractDraft queryDraftByDraftId(String draftId);

    /**
     * 根据 ID 删除草稿
     * @param draftId
     */
    Boolean delDraftById(String draftId);

    /**
     * 根据支付类型查询支付方式
     * @param paymentType 支付类型
     */
    List<FundingChannelProductInfo> queryPaymentMethodByType(String paymentType);

    /**
     * 保存产品结算信息到草稿
     * @param request 结算信息
     */
    Boolean saveSettlementInfoByDraftId(InstContractStandardRequestDTO.StandardSettleInfo request);

    /**
     * 保存产品标准化信息到草稿
     * @param request 标准化信息
     */
    Boolean saveStandardProductInfoByDraftId(InstContractStandardRequestDTO.StandardProductInfo request);

    /**
     * 保存换汇标准化信息到草稿
     * @param request 标准化信息
     */
    Boolean saveStandardFxInfo(@Validated @RequestBody InstContractStandardRequestDTO.StandardFxInfo request);


    /**
     * 机构产品提交复核
     * @param draftId
     * @param status
     */
    boolean productDraftStatusChange(String draftId,String status);


    /**
     * 机构产品复核确认
     * @param draftId
     */
    InstProductParseContext productAuditSubmit(String draftId);
}
