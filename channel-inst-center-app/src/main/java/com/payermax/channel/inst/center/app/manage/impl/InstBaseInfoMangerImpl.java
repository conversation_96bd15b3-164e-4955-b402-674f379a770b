package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstBaseInfoManger;
import com.payermax.channel.inst.center.app.service.InstBrandService;
import com.payermax.channel.inst.center.domain.subaccount.request.QueryInstBrandRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryInstBrandResponseDO;
import com.payermax.channel.inst.center.facade.request.QueryBrandByChannelCodeRequest;
import com.payermax.channel.inst.center.facade.response.InstBaseInfoWithBrandResponse;
import com.payermax.channel.inst.center.infrastructure.cache.item.ChannelInfoCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstBaseInfoCacheManager;
import com.payermax.channel.inst.center.infrastructure.entity.ChannelInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoDao;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBrandPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBrandRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/5/22  8:10 PM
 */
@Service
@Slf4j
@AllArgsConstructor
public class InstBaseInfoMangerImpl implements InstBaseInfoManger {

    @Resource
    private InstBaseInfoDao instBaseInfoDao;

    @Resource
    private InstBrandService instBrandService;

    @Resource
    private ReqDoAssembler reqDoAssembler;

    @Resource
    private ResDoAssembler resDoAssembler;

    private final ChannelInfoCacheManager channelInfoCacheManager;
    private final InstBaseInfoCacheManager baseInfoCacheManager;
    private final InstBrandRepository brandRepository;

    @Override
    public List<String> listInstCode() {
        // 1、查库
        List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoDao.selectAll(new InstBaseInfoEntity());

        // 2、校验
        if (CollectionUtils.isEmpty(instBaseInfoEntityList)) {
            return new ArrayList<>();
        }

        // 3、组装返回
        List<String> instCodeList = new ArrayList<>();
        instBaseInfoEntityList.forEach((instBaseInfo) -> instCodeList.add(instBaseInfo.getInstCode()));

        // 过滤机构code不为空的
        return instCodeList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public List<QueryInstBrandResponseDO> listInstBrand(QueryInstBrandRequestDO queryInstBrandRequestDO) {
        InstBrandEntity instBrandEntity = reqDoAssembler.toInstBrandEntity(queryInstBrandRequestDO);
        List<InstBrandEntity> instBrandEntityList = instBrandService.query(instBrandEntity);
        return resDoAssembler.toQueryInstBrandResponseDOList(instBrandEntityList);
    }

    /**
     * 根据 channelCode 查询机构及品牌，channelCode 不存在时直接返回原值
     */
    @Override
    public List<InstBaseInfoWithBrandResponse> listByChannelCode(QueryBrandByChannelCodeRequest request) {
        List<String> channelCodeList = request.getChannelCodeList();
        return channelCodeList.stream().map(channelCode -> {
            InstBaseInfoWithBrandResponse response = new InstBaseInfoWithBrandResponse().setChannelCode(channelCode);

            // 1. 查询渠道信息
            Optional<ChannelInfoEntity> channelInfo = Optional.ofNullable(channelInfoCacheManager.getChannelInfoByChannelCode(channelCode));
            // 渠道信息 或 BizHandleInstCode 不存在时直接返回
            if (!channelInfo.isPresent() || StringUtils.isBlank(channelInfo.get().getBizHandleInstCode())) {
                return response;
            }

            // 2. 查询机构信息
            Optional<InstBaseInfoEntity> instBaseInfoEntity = Optional.ofNullable(baseInfoCacheManager.getInstBaseInfoByCode(channelInfo.get().getBizHandleInstCode()));
            // 机构信息 不存在时直接返回
            if (!instBaseInfoEntity.isPresent()) {
                return response;
            }

            // 3. 查询品牌信息
            InstBrandPO brandPO = brandRepository.getById(instBaseInfoEntity.get().getInstBrandId());
            resDoAssembler.toInstBaseInfoWithBrandResponse(response,channelInfo.get(), instBaseInfoEntity.get(), brandPO);
            return response;
        }).collect(Collectors.toList());
    }
}
