package com.payermax.channel.inst.center.app.request.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstContractVersionUpgradeRequest extends InstContractBaseRequest {

    /**
     * 开始生效时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime effectiveStartTime;

    /**
     * 费用列表
     */
    private List<InstContractFeeItemRequestDTO> feeList;
}
