package com.payermax.channel.inst.center.app.model.contract.desensitizer;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/11
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum SensitiveType {

    /**
     * 费用信息
     */
    FEE_VALUE("OMC_FUNDS_FX_INST_CONTRACT_DATA_FEE_VALUE"),

    /**
     * 税费信息
     */
    TAX_VALUE("OMC_FUNDS_FX_INST_CONTRACT_DATA_TAX_VALUE"),


    /**
     * 换汇信息
     */
    FX_VALUE("OMC_FUNDS_FX_INST_CONTRACT_DATA_FX_VALUE"),


    /**
     * 换汇信息
     */
    SETTLE_VALUE("OMC_FUNDS_FX_INST_CONTRACT_DATA_SETTLE_VALUE"),

    /**
     * 忽略信息
     */
    IGNORE_VALUE(""),

    /**
     * 所有信息
     */
    ALL_VALUE("")
    ;

    private final String AuthCode;
}
