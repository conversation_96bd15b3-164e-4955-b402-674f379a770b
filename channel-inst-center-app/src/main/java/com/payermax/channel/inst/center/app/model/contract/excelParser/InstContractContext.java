package com.payermax.channel.inst.center.app.model.contract.excelParser;

import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.impl.AccumulatedInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.SettleInfoDTO;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstContractContext {


    /**
     * 机构编码
     * */
    private String instCode;

    /**
     * 合同主体
     * */
    private String contractEntity;

    /**
     * 合作模式
     * */
    private String cooperationMode;

    /**
     * 机构产品类型
     * */
    private String productType;

    /**
     * Payin 类型
     * */
    private String payinType;

    /**
     * 合同生效时间
     * */
    private Date effectiveTime;

    /**
     * 合同费用文件地址
     * */
    private String contractFeeFile;

    /**
     * 合同编号
     * */
    private String contractNo;

    /**
     * 合同影印件地址
     * */
    private List<String> photocopyUrl;

    /**
     * 备注
     * */
    private String remark;

    /**
     * 费用解析信息
     */
    private ExcelUtil.ExcelParseInfo<? extends AbstractInstContractFeeDTO> feeInfo;

    /**
     * 手续费解析信息
     */
    private ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> accumulatedInfo;

    /**
     * 结算费解析信息
     */
    private ExcelUtil.ExcelParseInfo<SettleInfoDTO> settlementInfo;

    /**
     * 是否出现校验异常
     */
    private Boolean isHasError;

    /**
     * 校验异常信息
     */
    private Map<String, ExportErrorInfo> errorInfoMap;


}
