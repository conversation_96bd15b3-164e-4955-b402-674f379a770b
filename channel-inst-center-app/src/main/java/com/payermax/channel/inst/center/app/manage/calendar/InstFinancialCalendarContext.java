package com.payermax.channel.inst.center.app.manage.calendar;

import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarHolidayDTO;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @DESC
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstFinancialCalendarContext {

    /**
     * 日历信息
     */
    private InstFinancialCalendar calendar;

    /**
     * 关联币种
     */
    private String relatedCurrency;

    /**
     * 是否生成关联币种日历
     */
    private Boolean generateRelatedCurrency;

    /**
     * 关联国家
     */
    private String relatedCountry;

    /**
     * 是否生成关联国家日历
     */
    private Boolean generateRelatedCountry;

    /**
     * 节假日 DTO 列表
     */
    private List<InstFinancialCalendarHolidayDTO> holidayDTOList;

    /**
     * 节假日列表
     */
    private List<InstFinancialCalendarHoliday> holidayList;

    /**
     * 新增节假日日期列表
     */
    private List<LocalDate> addHolidayDateList;

    /**
     * 取消节假日日期列表
     */
    private List<LocalDate> cancelHolidayDateList;


}
