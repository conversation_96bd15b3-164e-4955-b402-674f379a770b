package com.payermax.channel.inst.center.app.request.calendar;

import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarHolidayDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/27
 * @DESC
 */
@Data
public class InstFinancialCalendarSaveRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日历id
     */
    @NotBlank(message = "calendarId is required")
    private String calendarId;

    /**
     * 年度
     */
    private String calendarYear;

    /**
     * 日历类型
     */
    private String calendarType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 关联币种
     */
    private String relatedCurrency;

    /**
     * 是否生成关联币种日历
     */
    @NotNull(message = "generateRelatedCurrency is required")
    private Boolean generateRelatedCurrency;

    /**
     * 关联国家
     */
    private String relatedCountry;

    /**
     * 是否生成关联国家日历
     */
    @NotNull(message = "generateRelatedCountry is required")
    private Boolean generateRelatedCountry;

    /**
     * 描述
     */
    private String description;

    /**
     * 周末列表
     */
    private List<String> weekendList;

    /**
     * 节假日列表
     */
    private List<InstFinancialCalendarHolidayDTO> holidayList;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 流程发起人
     */
    private String shareId;


}
