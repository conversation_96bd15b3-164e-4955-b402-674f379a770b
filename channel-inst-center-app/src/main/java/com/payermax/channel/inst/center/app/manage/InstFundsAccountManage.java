package com.payermax.channel.inst.center.app.manage;

import cn.hutool.core.lang.Pair;
import com.payermax.channel.inst.center.domain.subaccount.request.*;
import com.payermax.channel.inst.center.domain.subaccount.response.*;

import java.util.List;

/**
 * 机构资金账号Manage
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstFundsAccountManage {

    /**
     * 查询可用机构资金账号
     *
     * @param accountDO
     * @return
     */
    List<QueryAccountsResponseDO> queryAccount(QueryAccountsRequestDO accountDO);

    /**
     * 查询可用机构资金账号
     *
     * @param createSubAccountRequestDO
     * @return
     */
    CreateSubAccountResponseDO createSubAccount(CreateSubAccountRequestDO createSubAccountRequestDO);

    /**
     * 查询可用机构资金账号
     *
     * @param queryAccountDetailRequestDO
     * @return
     */
    QueryAccountDetailResponseDO queryAccountDetail(QueryAccountDetailRequestDO queryAccountDetailRequestDO);

    /**
     * 查询可用机构资金账号
     *
     * @param queryAccountDetailByIdRequestDO
     * @return
     */
    QueryAccountDetailByIdResponseDO queryAccountDetailById(QueryAccountDetailByIdRequestDO queryAccountDetailByIdRequestDO);

}
