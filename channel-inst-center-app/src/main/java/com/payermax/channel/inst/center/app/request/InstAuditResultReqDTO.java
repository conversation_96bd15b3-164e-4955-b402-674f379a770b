package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstAuditResultReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 9:42
 * @Version 1.0
 */
@Data
public class InstAuditResultReqDTO implements Serializable {

    private static final long serialVersionUID = -1456875540917938408L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 业务类型
     */
    @ApiModelProperty(notes = "业务类型")
    private String businessType;

    /**
     * 业务主键
     */
    @ApiModelProperty(notes = "业务主键")
    private String businessNo;

    /**
     * 审核人
     */
    @ApiModelProperty(notes = "审核人")
    private String auditor;

    /**
     * 审核结果 AGREE：同意，REFUSE：拒绝，RETURN：驳回
     */
    @ApiModelProperty(notes = "审核结果")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 扩展字段
     */
    @ApiModelProperty(notes = "扩展字段")
    private String extraInfo;
}
