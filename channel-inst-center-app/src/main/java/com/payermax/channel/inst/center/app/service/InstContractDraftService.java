package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.request.InstContractInitRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractInitResponseVo;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-16 14:57:58
 */
public interface InstContractDraftService {

    /**
     * 将初始化的机构合同存入草稿
     * @param instContract 机构合同信息
     */
    InstContractInitResponseVo initInstContract(InstContractInitRequestDTO instContract);

    /**
     * 根据 shareId 查询草稿列表
     * 根据查询人过滤,财务BP/PD 可以看到所有草稿,GP只能看到负责机构的草稿)
     * @param shareId
     */
    List<InstProductItemVO> queryList(String shareId);

    /**
     * 根据条件查询草稿列表
     * @param draft 草稿条件
     * @param start 开始时间
     * @param end 结束时间
     */
    List<InstProductItemVO> queryByConditions(InstContractDraft draft, Date start, Date end);

    /**
     * 将机构产品解析结果入库
     * @param context 解析上下文
     */
    boolean draftParseResultSaving(InstProductParseContext context);
}
