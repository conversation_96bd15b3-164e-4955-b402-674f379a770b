package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;

import java.util.List;

/**
 * 机构品牌信息相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
public interface InstBrandService{

    /**
     * 查询机构品牌信息
     *
     * @return
     */
    List<InstBrandEntity> query(InstBrandEntity instBrandEntity);

    /**
     * 根据品牌id查询机构品牌信息
     * @param bdIds
     * @return
     */
    List<InstBrandEntity> queryByBdIds(List<String> bdIds);

    /**
     * 保存机构品牌信息
     *
     * @return
     */
    int save(InstBrandEntity instBrandEntity);

}
