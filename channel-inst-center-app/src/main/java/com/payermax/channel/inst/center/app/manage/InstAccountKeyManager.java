package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstAccountKeyReqDTO;
import com.payermax.channel.inst.center.app.request.InstAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstAccountKeyQueryVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 20:42
 */
public interface InstAccountKeyManager {
    /**
     * 查询商户密钥信息
     *
     * @return
     */
    List<InstAccountKeyQueryVO> queryMidKeys(InstAccountReqDTO instAccountReqDTO);

    /**
     * 保存商户密钥信息
     *
     * @return
     */
    int saveBatch(InstAccountReqDTO instAccountReqDTO);

    /**
     * 修改商户密钥信息
     *
     * @return
     */
    int modifyMidKey(InstAccountKeyReqDTO instAccountKeyReqDTO);

    /**
     * 删除商户密钥信息
     * @param id
     * @return
     */
    int deleteMidKey(Long id);
}
