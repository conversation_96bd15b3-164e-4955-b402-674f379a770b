package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName InstAccountKeyReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 21:44
 */
@Data
public class InstAccountKeyReqDTO {
    @ApiModelProperty(notes = "主键id")
    private Long keyId;

    @ApiModelProperty(notes = "商户id")
    private Long accountId;

    @ApiModelProperty(notes = "密钥类型")
    private String keyType;

    @ApiModelProperty(notes = "密钥值")
    private String keyValue;

    @ApiModelProperty(notes = "加密方法")
    private String encryptType;

    @ApiModelProperty(notes = "备注")
    private String remark;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "创建时间")
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    private Date utcModified;
}
