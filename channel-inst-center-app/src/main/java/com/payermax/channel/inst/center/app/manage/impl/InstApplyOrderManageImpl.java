package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Splitter;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.manage.InstApplyOrderManage;
import com.payermax.channel.inst.center.app.manage.InstBaseInfoManager;
import com.payermax.channel.inst.center.app.request.InstApplyOrderReqDTO;
import com.payermax.channel.inst.center.app.response.ApplyOrderQueryVO;
import com.payermax.channel.inst.center.app.service.*;
import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity;
import com.payermax.common.lang.util.AssertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InstApplyOrderManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 23:13
 */
@Service
public class InstApplyOrderManageImpl implements InstApplyOrderManage {

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private InstBaseInfoService instBaseInfoService;

    @Autowired
    private InstBrandService instBrandService;

    @Autowired
    private InstDdService instDdService;

    @Autowired
    private InstContractService instContractService;

    @Autowired
    private InstContractProductService instContractProductService;

    @Autowired
    private InstRequirementOrderService instRequirementOrderService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private InstBaseInfoManager instBaseInfoManager;

    @Override
    public PageResult<ApplyOrderQueryVO> queryApplyOrders(InstApplyOrderReqDTO instApplyOrderReqDTO) {
        ApplyOrderQueryEntity applyOrderQueryEntity = reqDtoAssembler.toApplyOrderQueryEntity(instApplyOrderReqDTO);
        IPage<ApplyOrderQueryEntity> applyOrderQueryEntityPage = instApplyOrderService.queryPageList(applyOrderQueryEntity, instApplyOrderReqDTO.getPageNum(), instApplyOrderReqDTO.getPageSize());
        List<ApplyOrderQueryEntity> records = applyOrderQueryEntityPage.getRecords();
        List<ApplyOrderQueryVO> applyOrderQueryVOs = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(records)){
            applyOrderQueryVOs = respVoAssembler.toApplyOrdersQueryVo(records);
        }
        PageResult<ApplyOrderQueryVO> voPage = new PageResult<>();
        voPage.setTotal(applyOrderQueryEntityPage.getTotal());
        voPage.setRows(applyOrderQueryVOs);
        return voPage;
    }

    @Override
    public String save(InstApplyOrderReqDTO instApplyOrderReqDTO) {
        AssertUtil.isTrue(false, "ERROR", "方法已废弃，不可调用");
        return null;
        // InstBaseInfoEntity instBaseInfoEntity = reqDtoAssembler.toInstBaseInfoEntity(instApplyOrderReqDTO.getInstBaseInfoReqDTO());
        // InstBrandEntity instBrandEntity = reqDtoAssembler.toInstBrandEntity(instApplyOrderReqDTO.getInstBrandReqDTO());
        // InstDdEntity instDdEntity = reqDtoAssembler.toInstDdEntity(instApplyOrderReqDTO.getInstDdReqDTO());
        // InstApplyOrderEntity instApplyOrderEntity = reqDtoAssembler.toInstApplyOrderEntity(instApplyOrderReqDTO);
        // transactionTemplate.execute((status)->{
        //     //机构品牌信息
        //     instBrandEntity.setStatus(StatusEnum.Y.getValue());
        //     if(StringUtils.isNotBlank(instBrandEntity.getBrandName())){
        //         instBrandEntity.setBrandName(StringUtils.replaceAll(instBrandEntity.getBrandName(),StringUtils.SPACE,StringUtils.EMPTY));
        //     }
        //     instBrandService.save(instBrandEntity);
        //
        //     //机构信息
        //     instBaseInfoEntity.setInstBrandId(instBrandEntity.getBrandId());
        //     instBaseInfoEntity.setStatus(StatusEnum.Y.getValue());
        //     if(CollectionUtils.isNotEmpty(instApplyOrderReqDTO.getInstTypes())){
        //         instBaseInfoEntity.setInstTypes(Joiner.on(",").join(instApplyOrderReqDTO.getInstTypes()));
        //     }
        //     instBaseInfoService.save(instBaseInfoEntity);
        //
        //     //申请单信息
        //     instApplyOrderEntity.setInstId(instBaseInfoEntity.getInstId());
        //     instApplyOrderEntity.setInstBrandId(instBrandEntity.getBrandId());
        //     instApplyOrderEntity.setStatus(instApplyOrderReqDTO.getStatus());
        //     if(CollectionUtils.isNotEmpty(instApplyOrderReqDTO.getAccessCountrys())){
        //         instApplyOrderEntity.setCountrys(Joiner.on(",").join(instApplyOrderReqDTO.getAccessCountrys()));
        //     }
        //     if(CollectionUtils.isNotEmpty(instApplyOrderReqDTO.getAccessProducts())){
        //         instApplyOrderEntity.setProducts(JSONObject.toJSONString(instApplyOrderReqDTO.getAccessProducts()));
        //     }
        //     instApplyOrderService.saveEntity(instApplyOrderEntity);
        //     // 更新阶段状态
        //     instApplyOrderService.updateStageStatus(instApplyOrderEntity.getApplyNo(), ApplyStageEnum.APPLY, instApplyOrderEntity.getStatus());
        //
        //     //机构DD
        //     if(ApplyTypeEnum.NEW.getValue().equals(instApplyOrderEntity.getApplyType())
        //         || ApplyTypeEnum.NEW_ENTITY.getValue().equals(instApplyOrderEntity.getApplyType()) ){
        //         instDdEntity.setApplyNo(instApplyOrderEntity.getApplyNo());
        //         instDdEntity.setInstId(instBaseInfoEntity.getInstId());
        //         instDdEntity.setStatus(DdStatusEnum.INIT.name());
        //         instDdService.save(instDdEntity);
        //     }
        //
        //     // 机构主体新产品接入
        //     if(ApplyTypeEnum.NEW_PRODUCT.getValue().equals(instApplyOrderEntity.getApplyType())
        //         && ApplyStatusEnum.AUDIT_AGREE.name().equals(instApplyOrderEntity.getStatus())){
        //         // 更新阶段状态
        //         instApplyOrderService.updateStageStatus(instApplyOrderEntity.getApplyNo(), ApplyStageEnum.NDA, NdaStatusEnum.COMPLETED.name());
        //         instApplyOrderService.updateStageStatus(instApplyOrderEntity.getApplyNo(), ApplyStageEnum.DD, DdStatusEnum.COMPLETED.name());
        //         instApplyOrderService.updateStageStatus(instApplyOrderEntity.getApplyNo(), ApplyStageEnum.KYC, KycStatusEnum.AUDIT_AGREE.name());
        //         // 初始化合同信息
        //         InstContractEntity record = new InstContractEntity();
        //         record.setApplyNo(instApplyOrderEntity.getApplyNo());
        //         record.setInstId(instApplyOrderEntity.getInstId());
        //         InstContractEntity contract = instContractService.getByEntity(record);
        //         if(contract == null){
        //             record.setStatus(ContractStatusEnum.INIT.name());
        //             instContractService.save(record);
        //             // 更新阶段状态
        //             instApplyOrderService.updateStageStatus(instApplyOrderEntity.getApplyNo(), ApplyStageEnum.CONTRACT, ContractStatusEnum.INIT.name());
        //         }
        //     }
        //
        //     // 审批通过=》分配品牌编码、机构主体编码 并推送机构信息到集团合同系统
        //     if(ApplyStatusEnum.AUDIT_AGREE.name().equalsIgnoreCase(instApplyOrderEntity.getStatus())
        //         && !ApplyTypeEnum.NEW_PRODUCT.getValue().equals(instApplyOrderEntity.getApplyType())){
        //         InstBrandEntity brand = new InstBrandEntity();
        //         brand.setBrandId(instBrandEntity.getBrandId());
        //         List<InstBrandEntity> brandEntityList = instBrandService.query(brand);
        //         Preconditions.checkArgument(CollectionUtils.isNotEmpty(brandEntityList),"brand is not exists");
        //         InstBaseInfoEntity instInfo = instBaseInfoService.queryById(instBaseInfoEntity.getInstId());
        //         Preconditions.checkArgument(instInfo != null,"instInfo is not exists");
        //         InstBaseInfoReqDTO instBaseInfoReqDTO = new InstBaseInfoReqDTO();
        //         instBaseInfoReqDTO.setInstBrandId(instBrandEntity.getBrandId());
        //         instBaseInfoReqDTO.setInstBrandCode(Optional.ofNullable(brandEntityList.get(0).getBrandName()).map(String::toUpperCase).orElse(brandEntityList.get(0).getBrandName())); //NO_CHECK 方法未被调用
        //         instBaseInfoReqDTO.setInstId(instBaseInfoEntity.getInstId());
        //         instBaseInfoReqDTO.setInstCode(getInstCode(instBrandEntity.getBrandId(),instBaseInfoReqDTO.getInstBrandCode(),instInfo.getEntityCountry()));
        //         instBaseInfoReqDTO.setInstName(instInfo.getInstName());
        //         instBaseInfoManager.updateInstCode(instBaseInfoReqDTO);
        //     }
        //     return Boolean.TRUE;
        // });
        // return instApplyOrderEntity.getApplyNo();
    }

    /**
     * 生成机构主体编码
     * 规则：品牌编码+机构主体所在地+序号
     *  序号：品牌在所在地的主体个数。第一个01，第二个02，以此类推
     * @param brandId
     * @param entityCountry
     * @return
     */
    private String getInstCode(Long brandId,String brandCode, String entityCountry) {
        String finalInstCode = StringUtils.EMPTY;
        if(brandId == null || StringUtils.isBlank(brandCode) || StringUtils.isBlank(entityCountry)){
            return finalInstCode;
        }
        List<InstBaseInfoEntity> instBaseInfoEntities = instBaseInfoService.queryByBrandId(Arrays.asList(brandId));
        if(CollectionUtils.isNotEmpty(instBaseInfoEntities)){
            long count = instBaseInfoEntities.stream().filter(obj -> StringUtils.isNotBlank(obj.getInstCode()) && entityCountry.equalsIgnoreCase(obj.getEntityCountry())).count();
            String seqNo = String.valueOf(count+1);
            finalInstCode = brandCode.concat(entityCountry).concat(count >= 9 ? seqNo : "0".concat(seqNo));
        }
        return finalInstCode;
    }

    @Override
    public ApplyOrderQueryVO query(String applyNo) {
        ApplyOrderQueryEntity applyOrderQueryEntity = instApplyOrderService.queryByApplyNo(applyNo);
        ApplyOrderQueryVO applyOrderQueryVO = respVoAssembler.toApplyOrderQueryVo(applyOrderQueryEntity);
        if(applyOrderQueryEntity != null){
            InstContractEntity record = new InstContractEntity();
            record.setApplyNo(applyNo);
            record.setInstId(applyOrderQueryEntity.getInstId());
            InstContractEntity contractEntity = instContractService.getByEntity(record);
            if(contractEntity != null){
                applyOrderQueryVO.setContractNo(contractEntity.getContractNo());
                List<InstContractProductEntity> instContractProductEntities = instContractProductService.getByContractNo(contractEntity.getContractNo());
                applyOrderQueryVO.setIsHasProductConfigFlag(CollectionUtils.isNotEmpty(instContractProductEntities));
            }
            InstRequirementOrderEntity orderEntity = instRequirementOrderService.get(applyNo);
            applyOrderQueryVO.setHasRequirementOrderId(orderEntity != null);
        }
        return applyOrderQueryVO;
    }

    @Override
    public List<ApplyOrderQueryVO> queryExistedApplyOrders(InstApplyOrderReqDTO instApplyOrderReqDTO) {
        List<String> accessCountrys = instApplyOrderReqDTO.getAccessCountrys();
        List<InstApplyOrderReqDTO.AccessProduct> accessProducts = instApplyOrderReqDTO.getAccessProducts();
        ApplyOrderQueryEntity applyOrderQueryEntity = reqDtoAssembler.toApplyOrderQueryEntity(instApplyOrderReqDTO);
        List<ApplyOrderQueryVO> applyOrderQueryVOs = new ArrayList<>();
        //按品牌+机构查已存在的申请单（没有状态）
        List<ApplyOrderQueryEntity> applyOrderQueryEntityList = instApplyOrderService.queryList(applyOrderQueryEntity);
        if(CollectionUtils.isNotEmpty(applyOrderQueryEntityList)){
            List<ApplyOrderQueryEntity> existedApplyOrders = applyOrderQueryEntityList.stream().filter(order -> {
                //检查已接入的国家/地区
                String countrys = order.getCountrys();
                if(StringUtils.isBlank(countrys)){
                    return Boolean.FALSE;
                }
                Set<String> countrySet = Splitter.on(",").splitToList(countrys).stream().collect(Collectors.toSet());
                Optional<String> anyCountry = accessCountrys.stream().filter(country -> countrySet.contains(country)).findAny();
                if(!anyCountry.isPresent()){
                    return Boolean.FALSE;
                }
                //检查已接入产品
                String products = order.getProducts();
                if(StringUtils.isBlank(products)){
                    return Boolean.FALSE;
                }
                List<InstApplyOrderReqDTO.AccessProduct> productList = JSONObject.parseArray(products, InstApplyOrderReqDTO.AccessProduct.class);
                if(CollectionUtils.isEmpty(productList)){
                    return Boolean.FALSE;
                }
                Set<String> channelTypeSet = productList.stream().map(InstApplyOrderReqDTO.AccessProduct::getChannelType).collect(Collectors.toSet());
                //检查已接入产品的渠道类型
                Optional<InstApplyOrderReqDTO.AccessProduct> anyChannelType = accessProducts.stream().filter(accessProduct -> channelTypeSet.contains(accessProduct.getChannelType())).findAny();
                if(!anyChannelType.isPresent()){
                    return Boolean.FALSE;
                }
                //检查已接入产品的支付方式类型
                Set<String> paymentMethodTypeSet = productList.stream().map(InstApplyOrderReqDTO.AccessProduct::getPaymentMethodTypes).flatMap(obj -> obj.stream()).collect(Collectors.toSet());
                Optional<String> anyPaymentMethodType = accessProducts.stream().map(InstApplyOrderReqDTO.AccessProduct::getPaymentMethodTypes).flatMap(obj -> obj.stream()).filter(paymentMethodType -> paymentMethodTypeSet.contains(paymentMethodType)).findAny();
                if(!anyPaymentMethodType.isPresent()){
                    return Boolean.FALSE;
                }
                return Boolean.TRUE;
            }).collect(Collectors.toList());
            applyOrderQueryVOs = respVoAssembler.toApplyOrdersQueryVo(existedApplyOrders);
        }
        return applyOrderQueryVOs;
    }
}
