package com.payermax.channel.inst.center.app.manage.template.close;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.manage.template.ApiTemplateUtil;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/9 20:30
 **/
@Service
@Slf4j
public class ApiCloseTemplate implements SubAccountCloseTemplate {
    public static final String CLOSE_SUB_ACCOUNT = "closeSubAccount";
    public static final String ALERT_TITLE = "机构子级账号【API关闭】异常";
    @Autowired
    DingAlertClient dingAlertClient;
    @Autowired
    ApiTemplateUtil apiTemplateUtil;
    @NacosValue(value = "${inst.sub.account.alert.message.for.api.close.exception:}", autoRefreshed = true)
    private String subAccountApiCloseException;

    @Override
    public void close(RequestAccountDO requestAccountDO) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 获取机构账号
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        // 已停用,或非已激活则不再进行后续操作
        if (Objects.equals(SubAccountStatusEnum.TERMINATED.getStatus(),instSubFundsAccountEntity.getStatus())
            || !Objects.equals(SubAccountStatusEnum.ACTIVATED.getStatus(),instSubFundsAccountEntity.getStatus())) {
            return;
        }
        
        Boolean checkResult = apiTemplateUtil.checkRequestParams(requestAccountDO, CLOSE_SUB_ACCOUNT);
        
        if (Boolean.FALSE.equals(checkResult)) {
            return;
        }
        // 构建调用渠道网关API查询接口请求参数
        JSONObject channelRequest = apiTemplateUtil.buildChannelRequest(instFundsAccountEntity, instSubFundsAccountEntity, requestAccountDO.getInstSubFundsAccountBucketsNotNullEntityList());

        try {
            // 发送渠道网关API请求
            apiTemplateUtil.sendRequest(requestAccountDO, CLOSE_SUB_ACCOUNT, channelRequest, ApiTemplateUtil.APPLY_STATE);
        } catch (Exception e) {
            log.error("ApiCloseTemplate-sendRequest Exception!", e);
            // 发送钉钉通知
            dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountApiCloseException, channelRequest);
        }
    }
}
