package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstBusinessDictReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:33
 */
@Data
public class InstBusinessDictReqDTO implements Serializable {
    private static final long serialVersionUID = 244476023332190428L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "业务类型")
    private String businessType;

    @ApiModelProperty(notes = "关联业务键")
    private String businessNo;

    @ApiModelProperty(notes = "字典项编码")
    private String dictCode;

    @ApiModelProperty(notes = "字典项名称")
    private String dictName;

    @ApiModelProperty(notes = "扩展信息")
    private String extraInfo;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;
}
