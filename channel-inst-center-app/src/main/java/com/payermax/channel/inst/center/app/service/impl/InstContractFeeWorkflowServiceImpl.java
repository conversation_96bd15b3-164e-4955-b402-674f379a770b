package com.payermax.channel.inst.center.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractFeeWorkflowAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractProductAssembler;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractBaseFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractFeeFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractSettleFormMsgDTO;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemModifiedNotifyDTO;
import com.payermax.channel.inst.center.app.rocketmq.producer.InstContractRocketProducer;
import com.payermax.channel.inst.center.app.service.InstContractFeeWorkflowService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.TemplateUtil;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.cache.InstContractCacheClear;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import com.payermax.channel.inst.center.infrastructure.repository.repo.*;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.response.ProcessResponse;
import com.payermax.operating.omc.portal.workflow.facade.common.em.ProcessResult;
import com.payermax.operating.omc.portal.workflow.facade.common.em.TaskResult;
import com.payermax.operating.omc.portal.workflow.facade.dubbo.ProcessDubboServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/29
 * @DESC
 */
@Service
@Slf4j
@DubboService(version = "1.0", timeout = 1000)
public class InstContractFeeWorkflowServiceImpl implements InstContractFeeWorkflowService {

    /**
     * 费用修改工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.feeModifiedProcess.key:process_inst-center-fee-modified-event-review}", autoRefreshed = true)
    private String feeModifiedProcessKey;
    @NacosValue(value = "${omc.workflow.process.feeModifiedProcess.desc:机构中心费用信息修改审批}", autoRefreshed = true)
    private String feeModifiedProcessDesc;

    /**
     * 结算修改工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.settleModifiedProcess.key:process_inst-center-settle-modified-event-review}", autoRefreshed = true)
    private String settleModifiedProcessKey;
    @NacosValue(value = "${omc.workflow.process.settleModifiedProcess.desc:机构中心结算信息修改审批}", autoRefreshed = true)
    private String settleModifiedProcessDesc;

    /**
     * 结算批量修改工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.settleBatchModifiedProcess.key:process_inst-center-settle-batch-modified-event-review}", autoRefreshed = true)
    private String settleBatchModifiedProcessKey;
    @NacosValue(value = "${omc.workflow.process.settleBatchModifiedProcess.desc:机构中心结算批量信息修改审批}", autoRefreshed = true)
    private String settleBatchModifiedProcessDesc;
    @NacosValue(value = "${omc.workflow.process.settleBatchModifiedProcess.formMsgTemplate:}", autoRefreshed = true)
    private String settleBatchModifiedProcessFormMsgTemplate;

    /**
     * FX 批量修改工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.fxBatchModifiedProcess.key:process_inst-center-fx-batch-modified-event-review}", autoRefreshed = true)
    private String fxBatchModifiedProcessKey;
    @NacosValue(value = "${omc.workflow.process.fxBatchModifiedProcess.desc:机构中心FX信息批量修改审批}", autoRefreshed = true)
    private String fxBatchModifiedProcessDesc;
    @NacosValue(value = "${omc.workflow.process.fxBatchModifiedProcess.formMsgTemplate:}", autoRefreshed = true)
    private String fxBatchModifiedProcessFormMsgTemplate;


    @DubboReference(version = "1.0", timeout = 10000)
    private ProcessDubboServiceI processDubboServiceI;

    private final OperateLogFactory operateLogFactory;
    private final InstProcessDockRepository processDockRepository;
    private final InstContractFeeItemRepository contractFeeItemRepository;
    private final InstContractSettlementItemRepository contractSettlementItemRepository;
    private final InstContractOriginProductRepository originProductRepository;
    private final InstContractBaseInfoRepository contractBaseInfoRepository;
    private final InstContractStandardProductRepository contractStandardProductRepository;
    private final InstContractOperateLogRepository contractOperateLogRepository;
    private final InstContractFeeWorkflowAssembler contractFeeWorkflowAssembler;
    private final InstContractProductAssembler contractProductAssembler;
    private final InstContractRocketProducer instContractRocketProducer;

    @Autowired
    public InstContractFeeWorkflowServiceImpl(OperateLogFactory operateLogFactory, InstProcessDockRepository processDockRepository, InstContractFeeItemRepository contractFeeItemRepository
            , InstContractSettlementItemRepository contractSettlementItemRepository, InstContractOriginProductRepository originProductRepository, InstContractBaseInfoRepository contractBaseInfoRepository
            , InstContractStandardProductRepository contractStandardProductRepository, InstContractOperateLogRepository contractOperateLogRepository, InstContractFeeWorkflowAssembler contractFeeWorkflowAssembler
            , InstContractProductAssembler contractProductAssembler, InstContractRocketProducer instContractRocketProducer) {
        this.operateLogFactory = operateLogFactory;
        this.processDockRepository = processDockRepository;
        this.contractFeeItemRepository = contractFeeItemRepository;
        this.contractSettlementItemRepository = contractSettlementItemRepository;
        this.originProductRepository = originProductRepository;
        this.contractBaseInfoRepository = contractBaseInfoRepository;
        this.contractStandardProductRepository = contractStandardProductRepository;
        this.contractOperateLogRepository = contractOperateLogRepository;
        this.contractFeeWorkflowAssembler = contractFeeWorkflowAssembler;
        this.contractProductAssembler = contractProductAssembler;
        this.instContractRocketProducer = instContractRocketProducer;
    }


    /**
     * 费用信息修改发起流程
     *
     * @param shareId            用户 ID
     * @param formMessage        审批表单信息
     * @param originFeeItem      原始信息
     * @param newFeeItem         修改信息
     */
    @Override
    public Boolean feeConfigModifiedProcessStart(String shareId, InstContractFeeFormMsgDTO formMessage, InstContractFeeItemPO originFeeItem, InstContractFeeItemPO newFeeItem) {
        String businessType = String.format("%s_%s", LogScenesTypeEnum.FEE_MANAGE.getName(), OperateTypeEnum.UPDATE);
        InstProcessDockPO processDockPo = contractFeeWorkflowAssembler.fee2ProcessDockPo(shareId, InstProcessStatusEnum.PROCESSING.name(), businessType, originFeeItem, newFeeItem);
        Map<String, Object> formMessageMap = JSON.parseObject(JSON.toJSONString(formMessage), new TypeReference<Map<String, Object>>() {});

        return commonProcessStart(shareId, originFeeItem.getInstContractFeeItemNo(), processDockPo, feeModifiedProcessKey, feeModifiedProcessDesc, formMessageMap);
    }


    /**
     * 费用信息修改流程回调
     *
     * @param eventInfo 流程信息
     */
    @Transactional(rollbackFor = Exception.class)
    @InstContractCacheClear(cacheName = CacheEnum.INST_NEW_CONTRACT_FEE)
    @Override
    public Result<Boolean> instContractFeeModifiedCallback(WfProcessEventInfo eventInfo) {
        log.info("----------------FEE_ITEM_MODIFIED_CALLBACK_START----------------");
        // 查询流程数据
        InstProcessDockPO processDock = getProcessWithCheck(eventInfo.getProcessInfo().getProcessId());
        InstContractOperateLogPO logPO = operateLogFactory.composeFeeEditOperateLog(processDock, null, eventInfo.getProcessInfo().getFinalComment());

        // 修改流程状态并构造日志信息
        switch (eventInfo.getProcessInfo().getResult()){
            case PASS:
                logPO.setOperateRes(LogOperateResTypeEnum.SUCCESS);
                contractFeeItemRepository.updateById(JSON.parseObject(processDock.getFormContent(), InstContractFeeItemPO.class));
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.PASS);
                sendFeeConfigModifiedNotify(processDock);
                break;
            case REJECT:
            default:
                logPO.setOperateRes(LogOperateResTypeEnum.FAIL);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.REJECT);
        }

        // 记录日志
        contractOperateLogRepository.save(logPO);
        log.info("----------------FEE_ITEM_MODIFIED_CALLBACK_END----------------");
        return ResultUtil.success(eventInfo.getProcessInfo().getResult().equals(ProcessResult.PASS));
    }

    /**
     * 结算信息修改发起流程
     *
     * @param shareId            用户 ID
     * @param formMessage        审批表单信息
     * @param originSettleItem   原始信息
     * @param newSettleItem      修改信息
     * @return 是否成功
     */
    @Override
    public Boolean settleConfigModifiedProcessStart(String shareId, InstContractSettleFormMsgDTO formMessage, InstContractSettlementItemPO originSettleItem, InstContractSettlementItemPO newSettleItem) {
        String businessType = String.format("%s_%s", LogScenesTypeEnum.SETTLE_MANAGE.getName(), OperateTypeEnum.UPDATE);
        InstProcessDockPO processDockPo = contractFeeWorkflowAssembler.settle2ProcessDockPo(shareId, InstProcessStatusEnum.PROCESSING.name(), businessType, originSettleItem, newSettleItem);
        Map<String, Object> formMessageMap = JSON.parseObject(JSON.toJSONString(formMessage), new TypeReference<Map<String, Object>>() {});

        return commonProcessStart(shareId, originSettleItem.getInstContractSettlementItemNo(), processDockPo, settleModifiedProcessKey, settleModifiedProcessDesc, formMessageMap);
    }

    /**
     * 结算信息修改流程回调
     *
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> instContractSettleModifiedCallback(WfProcessEventInfo eventInfo) {
        log.info("----------------SETTLE_ITEM_MODIFIED_CALLBACK_START----------------");
        // 查询流程数据
        InstProcessDockPO processDock = getProcessWithCheck(eventInfo.getProcessInfo().getProcessId());
        InstContractOperateLogPO logPO = operateLogFactory.composeSettleEditOperateLog(processDock, null, eventInfo.getProcessInfo().getFinalComment());

        // 修改流程状态并构造日志信息
        switch (eventInfo.getProcessInfo().getResult()){
            case PASS:
                logPO.setOperateRes(LogOperateResTypeEnum.SUCCESS);
                contractSettlementItemRepository.updateById(JSON.parseObject(processDock.getFormContent(), InstContractSettlementItemPO.class));
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.PASS);
                break;
            case REJECT:
            default:
                logPO.setOperateRes(LogOperateResTypeEnum.FAIL);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.REJECT);
        }

        // 记录日志
        contractOperateLogRepository.save(logPO);
        log.info("----------------SETTLE_ITEM_MODIFIED_CALLBACK_END----------------");
        return ResultUtil.success(eventInfo.getProcessInfo().getResult().equals(ProcessResult.PASS));
    }

    /**
     * 结算信息批量修改发起流程
     *
     * @param shareId          用户 ID
     * @param formMsgList      审批表单信息
     * @param originSettleItems 原始信息
     * @param modifiedSettleItems 修改信息
     * @param settleItemNos 结算No列表
     * @param businessKey 业务唯一键
     * @return 是否成功
     */
    @Override
    public Boolean settleConfigBatchModifiedProcessStart(String shareId, List<InstContractSettleFormMsgDTO> formMsgList, List<InstContractSettlementItemPO> originSettleItems, List<InstContractSettlementItemPO> modifiedSettleItems, List<String> settleItemNos, String businessKey) {
        String businessType = String.format("%s_%s", LogScenesTypeEnum.SETTLE_MANAGE.getName(), OperateTypeEnum.UPDATE);
        InstProcessDockPO processDockPo = contractFeeWorkflowAssembler.settleItems2ProcessDock(shareId, InstProcessStatusEnum.PROCESSING.name(), businessType, businessKey, originSettleItems, modifiedSettleItems);
        return commonProcessStart(shareId, businessKey, processDockPo, settleBatchModifiedProcessKey, settleBatchModifiedProcessDesc, Collections.singletonMap("diffMsg", composeBatchModifiedMsg(formMsgList, settleBatchModifiedProcessFormMsgTemplate)));
    }

    /**
     * 结算信息批量修改流程回调
     *
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Boolean> instContractSettleBatchModifiedCallback(WfProcessEventInfo eventInfo) {
        log.info("----------------SETTLE_ITEM_BATCH_MODIFIED_CALLBACK_START----------------");
        // 查询流程数据
        InstProcessDockPO processDock = getProcessWithCheck(eventInfo.getProcessInfo().getProcessId());
        InstContractOperateLogPO logPO = operateLogFactory.composeSettleBatchModifiedOperateLog(processDock, null, eventInfo.getProcessInfo().getFinalComment());

        // 修改流程状态并构造日志信息
        switch (eventInfo.getProcessInfo().getResult()){
            case PASS:
                logPO.setOperateRes(LogOperateResTypeEnum.SUCCESS);
                List<InstContractSettlementItemPO> settlementItemPoList = JSON.parseArray(processDock.getFormContent(), InstContractSettlementItemPO.class);
                contractSettlementItemRepository.updateBatchById(settlementItemPoList);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.PASS);
                break;
            case REJECT:
            default:
                logPO.setOperateRes(LogOperateResTypeEnum.FAIL);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.REJECT);
        }

        // 记录日志
        contractOperateLogRepository.save(logPO);
        log.info("----------------SETTLE_ITEM_BATCH_MODIFIED_CALLBACK_END----------------");
        return ResultUtil.success(eventInfo.getProcessInfo().getResult().equals(ProcessResult.PASS));
    }

    /**
     * FX 批量修改流程发起
     *
     * @param shareId       用户 ID
     * @param formMsgList       审批表单信息
     * @param originItems   原始信息
     * @param modifiedItems 修改信息
     * @param itemNos       编号列表
     * @param businessKey   业务唯一键
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fxBatchModifiedProcessStart(String shareId, List<InstContractFeeFormMsgDTO> formMsgList, List<InstContractFeeItemPO> originItems, List<InstContractFeeItemPO> modifiedItems, List<String> itemNos, String businessKey) {
        String businessType = String.format("%s_%s", LogScenesTypeEnum.FX_MANAGE.getName(), OperateTypeEnum.UPDATE);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(modifiedItems), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), "修改信息不能为空");
        InstProcessDockPO processDockPo = contractFeeWorkflowAssembler.feeItems2ProcessDock(shareId, InstProcessStatusEnum.PROCESSING.name(), businessType, businessKey, originItems, modifiedItems);
        return commonProcessStart(shareId, businessKey, processDockPo, fxBatchModifiedProcessKey, fxBatchModifiedProcessDesc,
                Collections.singletonMap("diffMsg", composeBatchModifiedMsg(formMsgList, fxBatchModifiedProcessFormMsgTemplate)));
    }

    /**
     * FX 批量修改流程回调
     *
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> instContractFxBatchModifiedCallback(WfProcessEventInfo eventInfo) {
        log.info("----------------FX_ITEM_BATCH_MODIFIED_CALLBACK_START----------------");
        // 查询流程数据
        InstProcessDockPO processDock = getProcessWithCheck(eventInfo.getProcessInfo().getProcessId());
        InstContractOperateLogPO logPO = operateLogFactory.composeFxBatchModifiedOperateLog(processDock, null, eventInfo.getProcessInfo().getFinalComment());

        // 修改流程状态并构造日志信息
        switch (eventInfo.getProcessInfo().getResult()){
            case PASS:
                logPO.setOperateRes(LogOperateResTypeEnum.SUCCESS);
                List<InstContractFeeItemPO> feeItemList = JSON.parseArray(processDock.getFormContent(), InstContractFeeItemPO.class);
                contractFeeItemRepository.updateBatchById(feeItemList);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.PASS);
                break;
            case REJECT:
            default:
                logPO.setOperateRes(LogOperateResTypeEnum.FAIL);
                processDockRepository.setStatusById(processDock.getId(),InstProcessStatusEnum.REJECT);
        }

        // 记录日志
        contractOperateLogRepository.save(logPO);
        log.info("----------------FX_ITEM_BATCH_MODIFIED_CALLBACK_END----------------");
        return ResultUtil.success(eventInfo.getProcessInfo().getResult().equals(ProcessResult.PASS));
    }

    /**
     * 通用流程发起
     * @param shareId 用户ID
     * @param businessUniqueKey 业务唯一标识
     * @param processDockPo 流程 PO
     * @param processKey 工作流唯一标识
     * @param processDesc 工作流描述
     * @param formMessageMap 表单信息
     */
    private Boolean commonProcessStart(String shareId,String businessUniqueKey, InstProcessDockPO processDockPo, String processKey, String processDesc, Map<String, Object> formMessageMap) {
        // 发起流程
        Result<ProcessResponse.ProcessStart> processStartResult = startWorkflowWithCheck(shareId, businessUniqueKey, processKey, processDesc, formMessageMap);
        log.info("发起流程成功, shareId: {},businessUniqueKey: {}", shareId, businessUniqueKey);

        // 发起流程成功，保存修改信息
        try{
            processDockPo.setProcessId(processStartResult.getData().getProcessId());
            processDockRepository.save(processDockPo);
        }catch (Exception e){
            processDrop(processStartResult.getData());
            log.error("保存修改信息失败",e);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(),"流程发起失败");
        }

        return processStartResult.isSuccess();
    }

    /**
     * 发起工作流
     * @param shareId 用户 shareId
     * @param processKey 流程 key
     * @param processDesc 流程描述
     * @param formMap 流程表单信息
     * @return 发起结果
     */
    private Result<ProcessResponse.ProcessStart> startWorkflowWithCheck(String shareId, String businessUniqueKey, String processKey, String processDesc, Map<String,Object> formMap) {
        // 校验流程唯一性
        AssertUtil.isTrue(processDockRepository.isProcessExistByBusinessIdAndStatus(businessUniqueKey,InstProcessStatusEnum.PROCESSING),"error","已存在审核中的批量修改流程");

        // 发起流程
        ProcessRequest.ProcessStart processStart = new ProcessRequest.ProcessStart();
        processStart.setProcessDefKey(processKey);
        processStart.setApplicantShareId(shareId);
        processStart.setProcessDesc(processDesc);
        processStart.setFormInfoMap(formMap);
        Result<ProcessResponse.ProcessStart> processStartResult = processDubboServiceI.start(processStart);
        AssertUtil.isTrue(processStartResult.isSuccess(),"error","流程发起失败");
        return processStartResult;
    }

    /**
     * 校验并返回流程信息
     */
    private InstProcessDockPO getProcessWithCheck(String processId) {
        // 流程幂等校验
        AssertUtil.isTrue(processDockRepository.isInProcessingByProcessId(processId, InstProcessStatusEnum.PROCESSING),"error","流程不存在或者已执行完成");
        // 查询流程数据
        return processDockRepository.queryByProcessId(processId);
    }

    /**
     * 剔除流程
     *
     * @param processStartResult process 信息
     * @return 是否成功
     */
    private Boolean processDrop(ProcessResponse.ProcessStart processStartResult) {
        // 1. 用于剔除流程的用户
        String shareId = "INST_CENTER_SU";
        ProcessRequest.TaskComplete taskComplete = new ProcessRequest.TaskComplete();
        taskComplete.setProcessId(processStartResult.getProcessId());
        taskComplete.setTaskId(processStartResult.getTaskId());
        taskComplete.setComment("系统自动剔除流程");
        taskComplete.setResult(TaskResult.STOP);
        return processDubboServiceI.taskComplete(shareId, taskComplete).isSuccess();
    }


    /**
     * 构造批量修改流程消息
     */
    private String composeBatchModifiedMsg(List<? extends InstContractBaseFormMsgDTO> formMsgList, String template){
        String formatedTemplate = template.replace("#!", "$!");
        return formMsgList.stream().map(item -> {
            Map<String, Object> params = JSON.parseObject(JSON.toJSONString(item), new TypeReference<Map<String, Object>>() {});
            if(CollectionUtils.isEmpty(item.getDiffMsg())){
                params.put("noChange", true);
            }
            return TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, formatedTemplate, params);
        }).collect(Collectors.joining("\n"));
    }

    /**
     * 发送 费用变更 MQ
     */
    private void sendFeeConfigModifiedNotify(InstProcessDockPO processDock){
        try {
            log.info("发送费率修改通知消息");
            InstContractFeeItemPO originFeeItem = JSON.parseObject(processDock.getOriginalFormContent(), InstContractFeeItemPO.class);
            InstContractFeeItemPO modifiedFeeItem = JSON.parseObject(processDock.getFormContent(), InstContractFeeItemPO.class);
            InstContractOriginProductPO originProductPO = originProductRepository.queryByOriginProductNo(originFeeItem.getInstOriginProductNo());
            InstContractBaseInfoPO baseInfoPO = contractBaseInfoRepository.queryOneByNo(originProductPO.getContractNo());
            InstContractFeeItemModifiedNotifyDTO notifyDTO = contractProductAssembler.feeItem2NotifyMsg(originFeeItem);
            contractProductAssembler.baseInfo2NorifyMsg(notifyDTO, baseInfoPO);
            notifyDTO.setOldFeeConfig(JSON.parseObject(JSON.toJSONString(JSON.parseObject(originFeeItem.getFeeConfig(), Map.class).get("TRADE")), FeeConfig.class));
            notifyDTO.setNewFeeConfig(JSON.parseObject(JSON.toJSONString(JSON.parseObject(modifiedFeeItem.getFeeConfig(), Map.class).get("TRADE")), FeeConfig.class));
            notifyDTO.setOldTaxConfig(JSON.parseArray(originFeeItem.getTaxConfig(), TaxConfig.class));
            notifyDTO.setNewTaxConfig(JSON.parseArray(modifiedFeeItem.getTaxConfig(), TaxConfig.class));
            List<InstContractStandardProductPO> standardProductList = contractStandardProductRepository.listByOriginProductNo(originProductPO.getInstOriginProductNo());
            if (CollectionUtils.isNotEmpty(standardProductList)) {
                notifyDTO.setPaymentMethodTypeGroup(standardProductList.stream().map(contractProductAssembler::standardProductPO2QueryMsg).collect(Collectors.toList()));
            }
            SendResult sendResult = instContractRocketProducer.sendFeeItemFeeConfigModifiedNotify(notifyDTO);
            log.info("sendResult:{},{}",sendResult.getSendStatus(),sendResult.getMsgId());
        } catch (Exception e) {
            log.error("发送费率修改通知消息异常", e);
        }
    }


}
