package com.payermax.channel.inst.center.app.manage.contract.settle;

import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundDefiniteTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundSignTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;

/**
 * <AUTHOR> 2023/6/21  7:03 PM
 */
public abstract class AbstractDslSimpleTypeResolver extends AbstractDslResolver {

    /**
     * 获取周期类型
     */
    public abstract RoundTypeEnum getRoundType();

    private Matcher fillTransactionCycleType(SettleRoundDSLEntity roundDSLEntity, String dsl) {
        Matcher matherByDSL = getMatherByDSL(dsl);
        RoundTypeEnum relativeInfoType = getRelativeInfoType(matherByDSL);
        AssertUtil.isTrue(getRoundType().equals(relativeInfoType), "", "");
        roundDSLEntity.setTransactionCycleType(relativeInfoType);

        return matherByDSL;
    }

    @Override
    public void fillDslSettleCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String billDateDSL = settleDate.getBillDate();

        DSLEntity billDateDslEntity = getDslEntity(billDateDSL);

        roundDSLEntity.setBillDateDsl(billDateDslEntity);
    }

    @Override
    public void fillDslPaymentCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String paymentDate = settleDate.getPaymentDate();

        DSLEntity paymentDateDslEntity = getDslEntity(paymentDate);

        roundDSLEntity.setPaymentDateDsl(paymentDateDslEntity);
    }

    @Override
    public void fillDslArriveCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String arrivedDateDSL = settleDate.getArriveDate();

        DSLEntity arrivedDateDslEntity = getDslEntity(arrivedDateDSL);

        roundDSLEntity.setArrivedDateDsl(arrivedDateDslEntity);
    }

    @Override
    public void fillDslExchangeCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String exchangeDate = settleDate.getExchangeDate();

        DSLEntity exchangedDateDslEntity = getDslEntity(exchangeDate);

        roundDSLEntity.setExchangeDateDsl(exchangedDateDslEntity);
    }


    @Override
    public void fillDslTransactionStartCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String startDateDSL = settleDate.getTransactionStartDate();

        // 1、填充周期类型
        Matcher matherByDSL = fillTransactionCycleType(roundDSLEntity, startDateDSL);

        // 2、获取符号
        RoundSignTypeEnum signTypeEnum = getDefiniteInfoSign(matherByDSL);

        // 3、填充周期偏移
        String definiteInfo = getDefiniteInfo(matherByDSL);
        if (RoundSignTypeEnum.ADD.equals(signTypeEnum)) {
            roundDSLEntity.setTransactionCycleStart(Integer.parseInt(definiteInfo));
        } else {
            roundDSLEntity.setTransactionCycleStart(-Integer.parseInt(definiteInfo));
        }
    }

    @Override
    public void fillDslTransactionEndCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String endDateDSL = settleDate.getTransactionEndDate();

        // 1、填充周期类型
        Matcher matherByDSL = fillTransactionCycleType(roundDSLEntity, endDateDSL);

        // 2、获取符号
        RoundSignTypeEnum signTypeEnum = getDefiniteInfoSign(matherByDSL);

        // 3、填充周期偏移
        String definiteInfo = getDefiniteInfo(matherByDSL);
        if (RoundSignTypeEnum.ADD.equals(signTypeEnum)) {
            roundDSLEntity.setTransactionCycleEnd(Integer.parseInt(definiteInfo));
        } else {
            roundDSLEntity.setTransactionCycleEnd(-Integer.parseInt(definiteInfo));
        }
    }

    private DSLEntity getDslEntity(String billDateDSL) {
        Matcher matherByDSL = getMatherByDSL(billDateDSL);

        // 1、eg: M / W
        RoundTypeEnum relativeInfoType = getRelativeInfoType(matherByDSL);
        AssertUtil.isTrue(Objects.nonNull(relativeInfoType) && getRoundType().equals(relativeInfoType), "", "");

        //--- 绝对信息---//
        // 2、eg: - / +
        RoundSignTypeEnum definiteInfoSign = getDefiniteInfoSign(matherByDSL);

        // 3、eg: 1
        String definiteInfo = getDefiniteInfo(matherByDSL);
        //--- 绝对信息---//

        //--- 相对信息---//
        // 2、eg: - / +
        RoundSignTypeEnum relativeInfoSign = getRelativeInfoSign(matherByDSL);

        // 3、eg: 1
        String relativeInfo = getRelativeInfo(matherByDSL);
        //--- 相对信息---//

        DSLEntity billDateDslEntity = new DSLEntity();
        billDateDslEntity.setRoundRelativeType(relativeInfoType);
        billDateDslEntity.setRoundDefiniteSignType(definiteInfoSign);
        billDateDslEntity.setRoundRelativeSignType(relativeInfoSign);
        if (StringUtil.isNotEmpty(relativeInfo)) {
            billDateDslEntity.setRoundRelativeOffset(Integer.parseInt(relativeInfo));
        }
        if (StringUtil.isNotEmpty(definiteInfo)) {
            billDateDslEntity.setRoundDefiniteOffset(Integer.parseInt(StringUtils.replaceChars(definiteInfo, RoundDefiniteTypeEnum.WD.name(), "")));
        }

        // 填充是否考虑节假日信息
        fillIsConsiderHoliday(definiteInfo, billDateDslEntity);
        return billDateDslEntity;
    }
}
