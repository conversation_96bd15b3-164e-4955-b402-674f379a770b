package com.payermax.channel.inst.center.app.model.contract;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2023/12/25
 * @DESC
 */
@Documented
@Constraint(validatedBy = EnumValue.EnumValueValidator.class)
@Target( { ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface EnumValue {

    String message() default "Invalid value. This is not permitted.";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    Class<? extends Enum<?>> enumClass();
    String enumMethod() default "getDescOnExcel";


    class EnumValueValidator implements ConstraintValidator<EnumValue, String> {
        private Class<? extends Enum<?>> enumClass;
        private String enumMethod;

        @Override
        public void initialize(EnumValue constraintAnnotation) {
            enumMethod = constraintAnnotation.enumMethod();
            enumClass = constraintAnnotation.enumClass();
        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if(value == null) {
                return true;
            }


            try {
                Method method = enumClass.getMethod(enumMethod);
                Enum<?>[] enumValArr = enumClass.getEnumConstants();

                for(Enum<?> enumVal : enumValArr) {
                    if(value.equals(method.invoke(enumVal))) {
                        return true;
                    }
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                throw new RuntimeException("Reflection exception in EnumValueValidator.", e);
            }

            return false;
        }
    }
}