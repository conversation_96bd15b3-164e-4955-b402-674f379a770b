package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InstContractInitRequestDTO {

    /**
     * 机构编码
     * */
    @NotBlank(message = "[instCode] is mandatory")
    private String instCode;

    /**
     * 合同主体
     * */
    @NotBlank(message = "[contractEntity] is mandatory")
    private String contractEntity;

    /**
     * 合作模式
     * */
    @NotBlank(message = "[cooperationMode] is mandatory")
    private String cooperationMode;

    /**
     * 机构产品类型
     * */
    @NotBlank(message = "[productType] is mandatory")
    private String productType;

    /**
     * 合同生效时间
     * */
    @NotNull(message = "[effectiveTime] is mandatory")
    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private Date effectiveTime;

    /**
     * 合同费用文件地址
     * */
    @NotBlank(message = "[contractFeeFile] is mandatory")
    private String contractFeeFile;

    @NotBlank(message = "[owner] is mandatory")
    private String owner;

    /**
     * 合同编号
     * */
    private String contractNo;

    /**
     * 合同影印件地址
     * */
    private List<String> photocopyUrl;

    /**
     * 备注
     * */
    private String remark;

    /**
     * 数据列表
     */
    @NotNull(message = "[dataList] is mandatory")
    private List<?> dataList;

}
