package com.payermax.channel.inst.center.app.manage.contract;

import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13
 * @DESC
 */
public interface InstContractQueryService {

    /**
     * 根据渠道商户号获取交易时间生效的合同版本,包括所有原始和标准合同
     */
    InstContractVersionInfo queryActiveContract(String mid, String bizType, long transactionTime);

    /**
     * 根据 bizType、instCode、entity 查询交易时间生效的合同版本
     */
    InstContractVersionInfo queryActiveContract(String bizType, String instCode, String entity, long transactionTime);

    /**
     * 根据 bizType、instCode、entity、version查询最新合同版本
     */
    InstContractVersionInfo queryLatestContract(String bizType, String instCode, String entity);

    /**
     * 根据 bizType、instCode 查询交易时间生效的所有合同版本
     */
    List<InstContractVersionInfo> queryInstActiveContract(String bizType, String instCode, long transactionTime);
}
