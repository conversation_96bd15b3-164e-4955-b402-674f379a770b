package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.app.manage.InstFxSettleManage;
import com.payermax.channel.inst.center.app.request.InstFxSettleReqDTO;
import com.payermax.channel.inst.center.app.request.context.InstFxSettleQueryErrorInfoContext;
import com.payermax.channel.inst.center.app.response.InstFxSettleInfoVO;
import com.payermax.channel.inst.center.app.response.InstSettleInfoVO;
import com.payermax.channel.inst.center.common.enums.instcenter.InstInfoErrorCodeEnum;
import com.payermax.channel.inst.center.infrastructure.cache.item.*;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.ushareit.fintech.common.util.AssertUtil;
import com.ushareit.fintech.common.util.MD5Utils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 11:13 AM
 */
@Service
@Slf4j
@Setter
public class InstFxSettleManageImpl implements InstFxSettleManage {

    public static final String INST_FX_SETTLE_REDIS_PREFIX = "instCenter:fxSettle";

    @Resource
    private ChannelInfoCacheManager channelInfoCacheManager;

    @Resource
    private ChannelMerchantCacheManager channelMerchantCacheManager;

    @Resource
    private ChannelMethodCacheManager channelMethodCacheManager;

    @Resource
    private InstBaseInfoCacheManager instBaseInfoCacheManager;

    @Resource
    private InstContractCacheManager instContractCacheManager;

    @Resource
    private InstContractProductCacheManager instContractProductCacheManager;

    @Resource
    private InstContractProductSettlementCacheManager instContractProductSettlementCacheManager;

    @Resource
    private InstProductCacheManager instProductCacheManager;

    @Resource
    private InstProductCapabilityCacheManager instProductCapabilityCacheManager;

    @Resource
    private InstProductFeeCacheManager instProductFeeCacheManager;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @NacosValue(value = "#{${inst.in.channel.payment.method.map}}", autoRefreshed = true)
    private Map<String, String> in;

    @NacosValue(value = "#{${inst.out.channel.payment.method.map}}", autoRefreshed = true)
    private Map<String, String> out;

    @NacosValue(value = "${channel.fxInfo.query.resultCacheExpireTime:5}", autoRefreshed = true)
    public long resultCacheExpireTime;

    @Override
    @DigestLog(isRecord = true)
    public InstFxSettleInfoVO queryFxSettleInfo(InstFxSettleReqDTO instFxSettleReqDTO) {

        //0. 根据channelCode+主体 获取instCode
        ChannelInfoEntity channelInfo = fetchInstCodeByChannelCode(instFxSettleReqDTO);
        String instCode = channelInfo.getBizHandleInstCode();

        //1. 根据instCode找到instID
        InstBaseInfoEntity instBaseInfoEntity = fetchInstInfoByInstCode(instFxSettleReqDTO, instCode);

        //2. 根据机构ID+主体查询机构合同
        List<InstContractEntity> instContractEntities = fetchAllContractByInstIdAndEntity(instFxSettleReqDTO, instBaseInfoEntity.getInstId(), instFxSettleReqDTO.getEntity());

        //3. 根据合同查询所有签约产品
        List<InstContractProductEntity> instContractProductEntities = fetchContractProductsByContracts(instFxSettleReqDTO, instContractEntities);
        Map<String, String> productCode2ContractMap = new HashMap<>();
        instContractProductEntities.forEach(key -> productCode2ContractMap.put(key.getInstProductCode(), key.getContractNo()));

        //5. 筛选出符合支付方式类型的产品
        List<String> finalProductCodes = fetchAllMatchedProductCode(instFxSettleReqDTO, channelInfo, instContractProductEntities);

        //6. 根据签约的产品+交易时的信息获取机构能力
        InnerProductCapability instProductCapabilityEntity = fetchProductCapability(instFxSettleReqDTO, finalProductCodes);

        //7. 根据最终的产品号获取换汇的时机
        InstProductFeeEntity instProductFee = fetchFeeInfoByProductCapability(instProductCapabilityEntity
                , instFxSettleReqDTO, productCode2ContractMap.get(instProductCapabilityEntity.getProductCode()));

        //8. 构建最终返回
        return buildResponse(instProductCapabilityEntity, instProductFee);
    }

    @Override
    @DigestLog(isRecord = true)
    public InstFxSettleInfoVO queryFxSettleInfoV2(InstFxSettleReqDTO instFxSettleReqDTO) {
        //先读取缓存
        InstFxSettleInfoVO instFxSettleInfoVO = queryFromRedis(instFxSettleReqDTO);
        if (Objects.nonNull(instFxSettleInfoVO)) {
            return instFxSettleInfoVO;
        }
        //0. 根据channelCode或channelMerchantCode 获取instCode
        ChannelInfoEntity channelInfo = fetchInstCodeByChannelCode(instFxSettleReqDTO);
        String instCode = fetchInstCode(instFxSettleReqDTO, channelInfo);

        //1. 根据instCode找到机构信息
        InstBaseInfoEntity instBaseInfoEntity = fetchInstInfoByInstCode(instFxSettleReqDTO, instCode);

        //2. 根据机构ID+主体查询机构合同
        List<InstContractEntity> instContractEntities = fetchAllContractByInstIdAndEntity(instFxSettleReqDTO, instBaseInfoEntity.getInstId(), instFxSettleReqDTO.getEntity());

        //3. 根据合同查询所有签约产品
        List<InstContractProductEntity> instContractProductEntities = fetchContractProductsByContracts(instFxSettleReqDTO, instContractEntities);
        Map<String, String> productCode2ContractMap = new HashMap<>();
        instContractProductEntities.forEach(key -> productCode2ContractMap.put(key.getInstProductCode(), key.getContractNo()));

        //4. 根据支付方式编码获取支付方式信息
        ChannelMethodEntity channelMethodInfo = fetchChannelMethodInfoByChannelMethodCode(instFxSettleReqDTO);

        //5. 筛选出符合的产品
        List<String> finalProductCodes = fetchAllMatchedProductCodeV2(instFxSettleReqDTO, channelInfo, channelMethodInfo, instContractProductEntities);

        //6. 根据签约的产品+交易时的信息获取机构能力
        InstProductCapabilityEntity instProductCapability = fetchProductCapabilityV2(instFxSettleReqDTO, finalProductCodes, channelMethodInfo, instContractProductEntities);

        //7. 获取结算信息
        InnerProductCapability productCapability = fetchProductCapabilitySettleInfo(instFxSettleReqDTO, instProductCapability, productCode2ContractMap.get(instProductCapability.getInstProductCode()));

        //8. 获取换汇时机
        InstProductFeeEntity instProductFee = fetchFeeInfoByProductCapabilityV2(productCapability
                , instFxSettleReqDTO, productCode2ContractMap.get(productCapability.getProductCode()), instProductCapability.getCapabilityCode());

        //9. 构建最终返回
        instFxSettleInfoVO = buildResponse(productCapability, instProductFee);

        //10. 缓存查询结果
        this.writeResultToRedis(instFxSettleReqDTO, instFxSettleInfoVO);
        return instFxSettleInfoVO;
    }

    private InstFxSettleInfoVO queryFromRedis(InstFxSettleReqDTO instFxSettleReqDTO) {
        try {
            String paramsKey = this.getRequestParamsCacheKey(instFxSettleReqDTO);
            String result = stringRedisTemplate.opsForValue().get(paramsKey);
            InstFxSettleInfoVO instFxSettleInfoVO = JSONObject.parseObject(result, InstFxSettleInfoVO.class);
            return instFxSettleInfoVO;
        } catch (Exception e) {
            log.warn("queryFromRedis exception:", e);
        }
        return null;
    }

    private void writeResultToRedis(InstFxSettleReqDTO instFxSettleReqDTO, InstFxSettleInfoVO instFxSettleInfoVO) {
        //key-> 查询入参MD5值，value->结算换汇结果
        try {
            String paramsKey = getRequestParamsCacheKey(instFxSettleReqDTO);
            stringRedisTemplate.opsForValue().set(paramsKey, JSONObject.toJSONString(instFxSettleInfoVO), resultCacheExpireTime, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.warn("writeResultToRedis exception:", e);
        }
    }

    private String getRequestParamsCacheKey(InstFxSettleReqDTO instFxSettleReqDTO) {
        String params = MD5Utils.encode(Joiner.on(StrPool.COLON).join(instFxSettleReqDTO.getChanelCode(),
                StringUtil.defaultIfBlank(instFxSettleReqDTO.getChannelMerchantCode(), StringUtil.EMPTY_STRING),
                StringUtil.defaultIfBlank(instFxSettleReqDTO.getChannelMethodCode(), StringUtil.EMPTY_STRING),
                instFxSettleReqDTO.getEntity(),
                instFxSettleReqDTO.getPaymentMethodType(),
                instFxSettleReqDTO.getPaymentCcy(),
                StringUtil.defaultIfBlank(instFxSettleReqDTO.getTargetOrg(), StringUtil.EMPTY_STRING),
                StringUtil.defaultIfBlank(instFxSettleReqDTO.getCardOrg(), StringUtil.EMPTY_STRING)));
        return Joiner.on(StrPool.COLON).join(INST_FX_SETTLE_REDIS_PREFIX, params);
    }

    /**
     * 获取结算信息
     *
     * @param instProductCapability
     * @return
     */
    private InnerProductCapability fetchProductCapabilitySettleInfo(InstFxSettleReqDTO instFxSettleReqDTO, InstProductCapabilityEntity instProductCapability, String contractNo) {
        String instProductCode = instProductCapability.getInstProductCode();
        String capabilityCode = instProductCapability.getCapabilityCode();
        //找结算信息
        List<InstContractProductSettlementEntity> productSettlementInfos = instContractProductSettlementCacheManager.getSettlementInfoByContractNoAndProductCode(contractNo, instProductCode);
        checkArgument(CollectionUtils.isNotEmpty(productSettlementInfos), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.SETTLE_INFO_NOT_FOUND.name())
                .errMsg("未找到结算信息!合同编号:[" + contractNo + "],产品编码:[" + instProductCode + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(contractNo)
                .productCode(instProductCode)
                .build());
        //产品维度结算配置
        InstContractProductSettlementEntity productSettleInfo = instContractProductSettlementCacheManager.getSettlementInfoByContractNoAndProductCodeAndCapabilityCode(contractNo, instProductCode, "DEFAULT");
        //能力维度结算配置
        InstContractProductSettlementEntity capabilitySettleInfo = instContractProductSettlementCacheManager.getSettlementInfoByContractNoAndProductCodeAndCapabilityCode(contractNo, instProductCode, capabilityCode);
        String settlementInfo = StringUtils.EMPTY;
        //结算配置最终取值：优先取能力维度配置的结算信息，能力维度没有则取产品维度的结算信息
        //产品维度
        if (Objects.nonNull(productSettleInfo)) {
            settlementInfo = productSettleInfo.getSettlementInfo();
        }
        //能力维度
        if (Objects.nonNull(capabilitySettleInfo)) {
            settlementInfo = capabilitySettleInfo.getSettlementInfo();
        }
        checkArgument(StringUtils.isNotBlank(settlementInfo), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.SETTLE_INFO_NOT_FOUND.name())
                .errMsg("未找到结算信息!合同编号:[" + contractNo + "],产品编码:[" + instProductCode + "],能力编码:[" + instProductCapability.getCapabilityCode() + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(contractNo)
                .productCode(instProductCode)
                .capabilityCode(instProductCapability.getCapabilityCode())
                .build());
        InnerProductCapability productCapability = new InnerProductCapability();
        productCapability.setProductCode(instProductCode);
        productCapability.setSettleInfoVO(JSON.parseObject(settlementInfo, InstSettleInfoVO.class));
        return productCapability;
    }

    /**
     * 按渠道类型+支付方式类型+支付工具刷选产品
     * 合同签约产品中是否有符合条件的产品
     *
     * @param instFxSettleReqDTO
     * @param channelInfo
     * @param instContractProductEntities
     * @return
     */
    private List<String> fetchAllMatchedProductCodeV2(InstFxSettleReqDTO instFxSettleReqDTO, ChannelInfoEntity channelInfo
            , ChannelMethodEntity channelMethodInfo, List<InstContractProductEntity> instContractProductEntities) {
        List<String> instProductCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCode).distinct().collect(Collectors.toList());
        //合同签约产品中所有产品信息
        List<InstProductEntity> instProductEntities = instProductCacheManager.getByChannelTypeAndPaymentMethodType(instProductCodes, channelInfo.getChannelType(), instFxSettleReqDTO.getPaymentMethodType());
        checkArgument(CollectionUtils.isNotEmpty(instProductEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_NOT_FOUND.name())
                .errMsg("未找到产品信息!渠道编码:[" + channelInfo.getChannelCode() + "],渠道类型:[" + channelInfo.getChannelType() + "],支付方式类型:[" + instFxSettleReqDTO.getPaymentMethodType() + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        //筛选出满足条件的产品
        List<String> finalInstProductCodes = instProductEntities.stream()
                .filter(item -> {
                    if (channelMethodInfo != null && StringUtils.isNotBlank(channelMethodInfo.getPaymentTool())) {
                        return channelMethodInfo.getPaymentTool().equals(item.getPaymentTool());
                    }
                    return true;
                })
                .map(InstProductEntity::getProductCode).distinct().collect(Collectors.toList());
        checkArgument(CollectionUtils.isNotEmpty(finalInstProductCodes), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_NOT_FOUND.name())
                .errMsg("未找到产品信息!渠道编码:[" + channelInfo.getChannelCode() + "],渠道类型:[" + channelInfo.getChannelType() + "],支付方式类型:[" + instFxSettleReqDTO.getPaymentMethodType() + "],支付工具:[" + channelMethodInfo.getPaymentTool() + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        return finalInstProductCodes;
    }

    private List<String> fetchAllMatchedProductCode(InstFxSettleReqDTO instFxSettleReqDTO, ChannelInfoEntity channelInfo
            , List<InstContractProductEntity> instContractProductEntities) {
        List<String> instProductCodes = new ArrayList<>(instContractProductEntities.size());
        instContractProductEntities.forEach(item -> instProductCodes.add(item.getInstProductCode()));

        List<InstProductEntity> instProductEntities = instProductCacheManager.getByProductCodes(instProductCodes);
        Set<String> finalProductCodes = new HashSet<>(instProductEntities.size());
        instProductEntities.forEach(item -> {
            if (instFxSettleReqDTO.getPaymentMethodType().equals(item.getPaymentMethodType())
                    && channelInfo.getChannelType().equals(item.getChannelType())) {
                finalProductCodes.add(item.getProductCode());
            }
        });
        return new ArrayList<>(finalProductCodes);
    }

    private ChannelInfoEntity fetchInstCodeByChannelCode(InstFxSettleReqDTO instFxSettleReqDTO) {
        String channelCode = instFxSettleReqDTO.getChanelCode();
        String paymentMethodType = instFxSettleReqDTO.getPaymentMethodType();
        ChannelInfoEntity channelInfo = channelInfoCacheManager.getChannelInfoByChannelCode(channelCode);
        checkArgument(Objects.nonNull(channelInfo), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.CHANNEL_INFO_NOT_FOUND.name())
                .errMsg("根据channelCode未获取到机构信息!:[" + channelCode + "]")
                .channelCode(channelCode)
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        String channelType = channelInfo.getChannelType();
        if ("I".equals(channelType)) {
            String mappingType = in.getOrDefault(paymentMethodType, paymentMethodType);
            instFxSettleReqDTO.setPaymentMethodType(mappingType);
        } else if ("O".equals(channelType)) {
            String mappingType = out.getOrDefault(paymentMethodType, paymentMethodType);
            instFxSettleReqDTO.setPaymentMethodType(mappingType);
        } else {
            throw new IllegalArgumentException("ChannelType illegal!:[" + channelType + "]");
        }
        return channelInfo;
    }

    /**
     * 错误信息处理
     * 1、记录错误信息到redis
     * 2、抛异常
     * errCodeKey
     * channelCodeKey
     *
     * @param context
     */
    private void checkArgument(boolean expression, InstFxSettleQueryErrorInfoContext context) {
        if (!expression) {
            try {
                //保存每种错误码对应的渠道集合
                //key-> instCenter:fxSettle:[errCode]  value->Set(channelCode...)
                String errCodeToChannelCodesKey = Joiner.on(StrPool.COLON).join(INST_FX_SETTLE_REDIS_PREFIX, context.getErrorCode());
                stringRedisTemplate.opsForSet().add(errCodeToChannelCodesKey, context.getChannelCode());
                stringRedisTemplate.expire(errCodeToChannelCodesKey, 1, TimeUnit.DAYS);
                //保存每个渠道对应的错误码集合
                //key-> instCenter:fxSettle:[channelCode] value->Set(errCode...)
                String channelCodeToErrCodesKey = Joiner.on(StrPool.COLON).join(INST_FX_SETTLE_REDIS_PREFIX, context.getChannelCode());
                stringRedisTemplate.opsForSet().add(channelCodeToErrCodesKey, context.getErrorCode());
                stringRedisTemplate.expire(channelCodeToErrCodesKey, 1, TimeUnit.DAYS);
                //保存错误详情
                //key-> instCenter:fxSettle:[errCode][channelCode]  value->errContext
                String channelCodeKey = Joiner.on(StrPool.COLON).join(errCodeToChannelCodesKey, context.getChannelCode());
                stringRedisTemplate.opsForValue().set(channelCodeKey, JSONObject.toJSONString(context), 1, TimeUnit.DAYS);
            } catch (Exception e) {
                log.warn("checkArgument error:", e);
            } finally {
                throw new IllegalArgumentException(context.getErrMsg());
            }
        }
    }

    /**
     * 先按渠道商户编码找instCode
     * 找不到再按渠道编码找
     *
     * @param instFxSettleReqDTO
     * @return
     */
    private String fetchInstCode(InstFxSettleReqDTO instFxSettleReqDTO, ChannelInfoEntity channelInfoEntity) {
        String instCode = ofNullable(this.fetchInstCodeByChannelMerchantCode(instFxSettleReqDTO)).map(ChannelMerchantEntity::getBizHandleInstCode).orElse(channelInfoEntity.getBizHandleInstCode());
        checkArgument(StringUtils.isNotBlank(instCode), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.BIZ_HANDLE_INST_CODE_NOT_FOUND.name())
                .errMsg("渠道信息中biz_handle_inst_code未配置:[" + instFxSettleReqDTO.getChanelCode() + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        return instCode;
    }

    private ChannelMerchantEntity fetchInstCodeByChannelMerchantCode(InstFxSettleReqDTO instFxSettleReqDTO) {
        return channelMerchantCacheManager.getChannelMerchantByChannelMerchantCode(instFxSettleReqDTO.getChannelMerchantCode());
    }

    private ChannelMethodEntity fetchChannelMethodInfoByChannelMethodCode(InstFxSettleReqDTO instFxSettleReqDTO) {
        String channelMethodCode = instFxSettleReqDTO.getChannelMethodCode();
        if (StringUtils.isBlank(channelMethodCode)) {
            return new ChannelMethodEntity();
        }
        ChannelMethodEntity channelMethodEntity = channelMethodCacheManager.getChannelMethodByChannelMethodCode(channelMethodCode);
        AssertUtil.notNull(channelMethodEntity, "根据channelMethodCode未获取到支付方式信息!:[" + channelMethodCode + "]");
        return channelMethodEntity;
    }

    private InstFxSettleInfoVO buildResponse(InnerProductCapability instProductCapabilityEntity, InstProductFeeEntity instProductFee) {
        InstFxSettleInfoVO instFxSettleInfoVO = new InstFxSettleInfoVO();
        instFxSettleInfoVO.setExchangeType(instProductFee == null ? "NoExchange" : instProductFee.getRateTimeType());
        instFxSettleInfoVO.setInstSettleInfoVO(instProductCapabilityEntity.getSettleInfoVO());
        return instFxSettleInfoVO;
    }

    /**
     * 获取换汇信息
     *
     * @param instProductCapabilityEntity
     * @param instFxSettleReqDTO
     * @param contractNo
     * @param capabilityCode
     * @return
     */
    private InstProductFeeEntity fetchFeeInfoByProductCapabilityV2(InnerProductCapability instProductCapabilityEntity
            , InstFxSettleReqDTO instFxSettleReqDTO, String contractNo, String capabilityCode) {
        String settleCcy = instProductCapabilityEntity.getSettleInfoVO().getSettleCurrency();
        if (instFxSettleReqDTO.getPaymentCcy().equals(settleCcy)) {
            log.info("productCode:[{}] no exchange!", instProductCapabilityEntity.getProductCode());
            return null;
        }
        String finalInstProductCode = instProductCapabilityEntity.getProductCode();
        List<InstProductFeeEntity> productFeeEntities = instProductFeeCacheManager.getFeeByContractNoAndProductCode(contractNo, finalInstProductCode);
        checkArgument(CollectionUtils.isNotEmpty(productFeeEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.EXCHANGE_INFO_NOT_FOUND.name())
                .errMsg("未找到机构换汇信息!合同编号:[" + contractNo + "],产品编码:[" + finalInstProductCode + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(contractNo)
                .productCode(instProductCapabilityEntity.getProductCode())
                .build());
        //产品维度配置
        InstProductFeeEntity productFee = instProductFeeCacheManager.getFeeByContractNoAndProductCodeAndCapabilityCode(contractNo, finalInstProductCode, "DEFAULT");
        //能力维度配置
        InstProductFeeEntity capabilityFee = instProductFeeCacheManager.getFeeByContractNoAndProductCodeAndCapabilityCode(contractNo, finalInstProductCode, capabilityCode);
        InstProductFeeEntity instProductFeeEntity = null;
        //产品维度
        if (Objects.nonNull(productFee)) {
            instProductFeeEntity = productFee;
        }
        //能力维度
        if (Objects.nonNull(capabilityFee)) {
            instProductFeeEntity = capabilityFee;
        }
        checkArgument(Objects.nonNull(instProductFeeEntity), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.EXCHANGE_INFO_NOT_FOUND.name())
                .errMsg("未找到机构换汇信息!合同编号:[" + contractNo + "],产品编码:[" + finalInstProductCode + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(contractNo)
                .productCode(instProductCapabilityEntity.getProductCode())
                .build());
        return instProductFeeEntity;
    }

    private InstProductFeeEntity fetchFeeInfoByProductCapability(InnerProductCapability instProductCapabilityEntity
            , InstFxSettleReqDTO instFxSettleReqDTO, String contractNo) {
        String payCcy = instFxSettleReqDTO.getPaymentCcy();
        String settleCcy = instProductCapabilityEntity.getSettleInfoVO().getSettleCurrency();
        if (payCcy.equals(settleCcy)) {
            log.info("productCode:[{}] no exchange!", instProductCapabilityEntity.getProductCode());
            return null;
        }
        String finalInstProductCode = instProductCapabilityEntity.getProductCode();
        List<InstProductFeeEntity> productFeeEntities = instProductFeeCacheManager.getFeeByProductCode(finalInstProductCode);
        checkArgument(CollectionUtils.isNotEmpty(productFeeEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.EXCHANGE_INFO_NOT_FOUND.name())
                .errMsg("未找到机构换汇信息!产品编码:[" + finalInstProductCode + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .productCode(finalInstProductCode)
                .build());
        //如果查出来的FEE有多个,还要再根据合同过滤一下
        if (productFeeEntities.size() > 1) {
            productFeeEntities = productFeeEntities.stream().filter(a -> a.getContractNo().equals(contractNo))
                    .collect(Collectors.toList());
        }
        checkArgument(CollectionUtils.isNotEmpty(productFeeEntities) && productFeeEntities.size() == 1,
                InstFxSettleQueryErrorInfoContext.builder()
                        .errorCode(InstInfoErrorCodeEnum.EXCHANGE_INFO_NOT_FOUND.name())
                        .errMsg("未找到机构换汇信息!合同编号:[" + contractNo + "],产品编码:[" + finalInstProductCode + "]")
                        .channelCode(instFxSettleReqDTO.getChanelCode())
                        .instFxSettleReqDTO(instFxSettleReqDTO)
                        .productCode(finalInstProductCode)
                        .build());
        return productFeeEntities.get(0); //NO_CHECK 方法未被调用
    }

    private InnerProductCapability fetchProductCapability(InstFxSettleReqDTO instFxSettleReqDTO, List<String> instProductCodes) {

        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityCacheManager
                .getProductCapabilityByProductCodes(instProductCodes);
        checkArgument(CollectionUtils.isNotEmpty(instProductCapabilityEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_CAPABILITY_NOT_FOUND.name())
                .errMsg("未找到机构产品能力!产品编码:[" + JSON.toJSONString(instProductCodes) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .productCode(JSONObject.toJSONString(instProductCodes))
                .build());
        List<InstProductCapabilityEntity> filteredCapability = instProductCapabilityEntities.stream()
                .filter(a -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getPaymentCcy())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getPaymentCcy().equals(a.getCurrency());
                    }
                })
                .filter(b -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getCardOrg()) || StringUtils.isBlank(b.getCardOrg())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getCardOrg().equals(b.getCardOrg());
                    }
                })
                .filter(c -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getTargetOrg()) || "*".equals(instFxSettleReqDTO.getTargetOrg())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getTargetOrg().equals(c.getTargetOrg());
                    }
                })
                .collect(Collectors.toList());
        checkArgument(CollectionUtils.isNotEmpty(filteredCapability), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_CONTRACT_PRODUCT_CAPABILITY_NOT_FOUND.name())
                .errMsg("未找到合同签约产品能力!,产品:[" + JSON.toJSONString(instProductCodes) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .productCode(JSON.toJSONString(instProductCodes))
                .build());
        // 因为一个产品会有多个产品能力,而多个产品能力可能结算信息相同,这里做了兼容。如果相同则直接认为是一个
        Set<InnerProductCapability> finalCapability = new HashSet<>();
        filteredCapability.forEach(item -> {
            InnerProductCapability innerProductCapability = new InnerProductCapability();
            innerProductCapability.setProductCode(item.getInstProductCode());
            innerProductCapability.setSettleInfoVO(JSON.parseObject(item.getSettlementExtraInfo(), InstSettleInfoVO.class));
            finalCapability.add(innerProductCapability);
        });
        checkArgument(finalCapability.size() == 1, InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_CAPABILITY_REPEAT.name())
                .errMsg("根据产品找到多个机构能力!产品:[" + JSON.toJSONString(finalCapability) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .productCode(JSON.toJSONString(instProductCodes))
                .build());
        return finalCapability.iterator().next();
    }

    /**
     * 找合同签约的产品能力
     * 1、找合同签约产品的产品能力
     * 2、筛选出满足交易要素的能力信息（币种、卡组/目标机构、对客类型）
     *
     * @param instFxSettleReqDTO
     * @param instProductCodes
     * @param channelMethodEntity
     * @return
     */
    private InstProductCapabilityEntity fetchProductCapabilityV2(InstFxSettleReqDTO instFxSettleReqDTO, List<String> instProductCodes, ChannelMethodEntity channelMethodEntity, List<InstContractProductEntity> instContractProductEntities) {
        //产品下所有能力
        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityCacheManager
                .getProductCapabilityByProductCodes(instProductCodes);
        checkArgument(CollectionUtils.isNotEmpty(instProductCapabilityEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_CAPABILITY_NOT_FOUND.name())
                .errMsg("未找到机构产品能力!产品编码:[" + JSON.toJSONString(instProductCodes) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .productCode(JSONObject.toJSONString(instProductCodes))
                .build());
        //合同签约的产品能力
        List<String> contractNos = instContractProductEntities.stream().map(InstContractProductEntity::getContractNo).distinct().collect(Collectors.toList());
        instContractProductEntities = instContractProductCacheManager.getContractProductByContractNosAndProductCodes(contractNos, instProductCodes);
        List<String> contractProductCapabilityCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCapabilityCode).distinct().collect(Collectors.toList());
        instProductCapabilityEntities = instProductCapabilityCacheManager.getProductCapabilityByCapabilityCodes(contractProductCapabilityCodes);
        //从合同签约的产品能力中筛选出满足条件的能力
        List<InstProductCapabilityEntity> filteredCapability = instProductCapabilityEntities.stream()
                .filter(b -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getPaymentCcy())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getPaymentCcy().equals(b.getCurrency());
                    }
                })
                .filter(c -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getCardOrg())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getCardOrg().equals(c.getCardOrg());
                    }
                })
                .filter(d -> {
                    if (StringUtils.isBlank(instFxSettleReqDTO.getTargetOrg()) || "*".equals(instFxSettleReqDTO.getTargetOrg())) {
                        return true;
                    } else {
                        return instFxSettleReqDTO.getTargetOrg().equals(d.getTargetOrg());
                    }
                })
                .filter(e -> {
                    if (channelMethodEntity == null || StringUtils.isBlank(channelMethodEntity.getCustomerType())) {
                        return true;
                    } else {
                        return channelMethodEntity != null && StringUtils.isNotBlank(channelMethodEntity.getCustomerType()) && channelMethodEntity.getCustomerType().equals(e.getCustomerType());
                    }
                })
                .collect(Collectors.toList());
        checkArgument(CollectionUtils.isNotEmpty(filteredCapability), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_CONTRACT_PRODUCT_CAPABILITY_NOT_FOUND.name())
                .errMsg("未找到合同签约产品能力!合同:[" + JSON.toJSONString(contractNos) + "],产品:[" + JSON.toJSONString(instProductCodes) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(JSON.toJSONString(contractNos))
                .productCode(JSON.toJSONString(instProductCodes))
                .build());
        List<String> finalCapabilityCodes = filteredCapability.stream().map(InstProductCapabilityEntity::getCapabilityCode).collect(Collectors.toList());
        checkArgument(filteredCapability.size() == 1, InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_PRODUCT_CAPABILITY_REPEAT.name())
                .errMsg("根据合同和产品找到多个能力!合同:[" + JSON.toJSONString(contractNos) + "],产品:[" + JSON.toJSONString(instProductCodes) + "]能力:[" + JSON.toJSONString(finalCapabilityCodes) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .contractNo(JSON.toJSONString(contractNos))
                .productCode(JSON.toJSONString(instProductCodes))
                .build());
        return filteredCapability.get(0); //NO_CHECK 方法未被调用
    }

    private List<InstContractProductEntity> fetchContractProductsByContracts(InstFxSettleReqDTO instFxSettleReqDTO, List<InstContractEntity> instContractVOS) {
        List<String> contractNos = new ArrayList<>(instContractVOS.size());
        instContractVOS.forEach(item -> contractNos.add(item.getContractNo()));
        List<InstContractProductEntity> instContractProductEntities = instContractProductCacheManager.getContractProductByContractNos(contractNos);
        checkArgument(CollectionUtils.isNotEmpty(instContractProductEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_CONTRACT_PRODUCT_NOT_FOUND.name())
                .errMsg("根据合同编号未找到签约产品 contractNo:[" + JSON.toJSONString(contractNos) + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        return instContractProductEntities;
    }

    private List<InstContractEntity> fetchAllContractByInstIdAndEntity(InstFxSettleReqDTO instFxSettleReqDTO, long instId, String entity) {
        List<InstContractEntity> instContractEntities = instContractCacheManager.getContractsByInstId(instId);
        checkArgument(CollectionUtils.isNotEmpty(instContractEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_CONTRACT_NOT_FOUND.name())
                .errMsg("根据机构未找到合同 instId:[" + instId + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .instId(instId)
                .build());
        List<InstContractEntity> finalInstContractEntities = instContractCacheManager.getContractsByInstIdAndEntity(instId, entity);
        checkArgument(CollectionUtils.isNotEmpty(finalInstContractEntities), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_CONTRACT_NOT_FOUND.name())
                .errMsg("根据机构和我司主体未找到合同 instId:[" + instId + "],entity:[" + entity + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .instId(instId)
                .build());
        return finalInstContractEntities;
    }

    private InstBaseInfoEntity fetchInstInfoByInstCode(InstFxSettleReqDTO instFxSettleReqDTO, String instCode) {
        InstBaseInfoEntity instBaseInfoEntity = instBaseInfoCacheManager.getInstBaseInfoByCode(instCode);
        checkArgument(Objects.nonNull(instBaseInfoEntity), InstFxSettleQueryErrorInfoContext.builder()
                .errorCode(InstInfoErrorCodeEnum.INST_BASE_INFO_NOT_FOUND.name())
                .errMsg("根据机构主体编码未找到机构信息 instCode:[" + instCode + "]")
                .channelCode(instFxSettleReqDTO.getChanelCode())
                .instFxSettleReqDTO(instFxSettleReqDTO)
                .build());
        return instBaseInfoEntity;
    }

    @Getter
    @Setter
    private static class InnerProductCapability {
        /**
         * 产品码
         */
        private String productCode;

        /**
         * 结算信息
         */
        private InstSettleInfoVO settleInfoVO;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof InnerProductCapability)) return false;
            InnerProductCapability that = (InnerProductCapability) o;
            return Objects.equals(productCode, that.productCode) && Objects.equals(settleInfoVO, that.settleInfoVO);
        }

        @Override
        public int hashCode() {
            return Objects.hash(productCode, settleInfoVO);
        }
    }
}
