package com.payermax.channel.inst.center.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketCountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstSubFundsAccountDao;
import com.payermax.channel.inst.center.infrastructure.util.MigrationAlgorithmUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/8 16:50
 **/
@Service
public class InstSubFundsAccountServiceImpl implements InstSubFundsAccountService {

    @Autowired
    InstSubFundsAccountDao instSubFundsAccountDao;


    @Override
    public InstSubFundsAccountEntity queryById(InstSubFundsAccountQueryEntity instSubFundsAccountQueryEntity) {
        Preconditions.checkArgument(instSubFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instSubFundsAccountQueryEntity.getSubAccountId()), "param subAccountId is mandatory");
        
        return MigrationAlgorithmUtils.selectHandle(() -> instSubFundsAccountDao.selectByPrimaryKey(instSubFundsAccountQueryEntity.getSubAccountId()), Objects::isNull);
    }

    @Override
    public InstSubFundsAccountEntity queryByBusinessKey(InstSubFundsAccountQueryEntity instSubFundsAccountQueryEntity) {
        Preconditions.checkArgument(instSubFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instSubFundsAccountQueryEntity.getBusinessKey()), "param businessKey is mandatory");
        List<InstSubFundsAccountEntity> list = MigrationAlgorithmUtils.selectHandle(() -> instSubFundsAccountDao.selectByQueryEntity(instSubFundsAccountQueryEntity), Objects::isNull);
        if (CollUtil.isNotEmpty(list)) {
            return CollUtil.getFirst(list);// NO_CHECK
        }
        return null;
    }

    @Override
    public List<InstSubFundsAccountEntity> queryListByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity) {
        Preconditions.checkArgument(subFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(subFundsAccountQueryEntity.getAccountId()), "param accountId is mandatory");
        
        return MigrationAlgorithmUtils.selectHandle(() -> instSubFundsAccountDao.selectByQueryEntity(subFundsAccountQueryEntity), CollectionUtils::isEmpty);
    }

    @Override
    public List<InstSubFundsAccountEntity> queryListByQueryEntity(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity) {
        Preconditions.checkArgument(subFundsAccountQueryEntity != null, "param record is mandatory");
        
        return MigrationAlgorithmUtils.selectHandle(() -> instSubFundsAccountDao.selectByQueryEntity(subFundsAccountQueryEntity), CollectionUtils::isEmpty);
    }

    @Override
    public int insert(InstSubFundsAccountEntity instSubFundsAccountEntity) {
        return instSubFundsAccountDao.insert(instSubFundsAccountEntity);
    }

    @Override
    public int update(Integer origStatus, InstSubFundsAccountEntity instSubFundsAccountEntity) {
        return instSubFundsAccountDao.updateByPrimaryKeySelective(origStatus, instSubFundsAccountEntity);
    }

    @Override
    public int queryCountByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity) {
        Preconditions.checkArgument(subFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(subFundsAccountQueryEntity.getAccountId()), "param accountId is mandatory");

        return instSubFundsAccountDao.selectCountByAccountId(subFundsAccountQueryEntity);
    }

    @Override
    public List<InstSubFundsAccountBucketCountEntity> queryBucketIdCountByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity) {
        Preconditions.checkArgument(subFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(subFundsAccountQueryEntity.getAccountId()), "param accountId is mandatory");

        return instSubFundsAccountDao.selectBucketIdCountByAccountId(subFundsAccountQueryEntity);
    }

    @Override
    public int queryCountBucketIdIsNullByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity) {
        Preconditions.checkArgument(subFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(subFundsAccountQueryEntity.getAccountId()), "param accountId is mandatory");
        
        return instSubFundsAccountDao.selectCountBucketIdIsNullByAccountId(subFundsAccountQueryEntity);
    }

    @Override
    public IPage<InstSubFundsAccountEntity> querySubAccountForTask(InstSubAccountBathQueryEntity subAccountBathQueryEntity) {
        Preconditions.checkArgument(subAccountBathQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(ObjectUtil.isNotNull(subAccountBathQueryEntity.getStatus()), "param status is mandatory");

        Page<InstSubAccountBathQueryEntity> page = new Page<>(subAccountBathQueryEntity.getPageNum(), subAccountBathQueryEntity.getPageSize());

        return instSubFundsAccountDao.selectSubAccountForTask(page, subAccountBathQueryEntity);
    }
}
