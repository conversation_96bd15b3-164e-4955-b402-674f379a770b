package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.event.InstFunsAccountBucketsAlertEvent;
import com.payermax.channel.inst.center.app.manage.InstFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.service.InstFundsAccountBucketService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.StatusEnumYN;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketCountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.infra.tool.lock.redisson.UredissonLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName InstFundsAccountBucketManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/11/24 16:39
 * @Version 1.0
 */
@Service
@Slf4j
public class InstFundsAccountBucketManageImpl implements InstFundsAccountBucketManage {

    @Resource
    UredissonLock uredissonLock;
    @Resource
    DingAlertClient dingAlertClient;
    @Resource
    ApplicationEventPublisher applicationEventPublisher;
    @Resource
    InstSubFundsAccountService instSubFundsAccountService;
    @Resource
    InstFundsAccountBucketService instFundsAccountBucketService;

    @NacosValue(value = "${multilevel-cache.redis.prefix}", autoRefreshed = true)
    private String prefix;

    @NacosValue(value = "#{${inst.funds.account.params-bucket.ding.map}}", autoRefreshed = true)
    private HashMap<String, Integer> paramsBucketDingMap;


    /**
     * 检查并填充生成子级资金账号需要使用的参数桶信息
     */
    @Override
    public void checkInstSubFundsAccountBucketId(RequestAccountDO requestAccountDO) {
        
        InstFundsAccountBucketEntity queryBucketEntity = new InstFundsAccountBucketEntity();
        queryBucketEntity.setAccountId(requestAccountDO.getInstFundsAccountEntity().getAccountId());
        queryBucketEntity.setStatus(StatusEnumYN.Y.getType());
        
        // 1. 检查子级资金账号是否已分配拓展参数，分配则查询并填充
        Long bucketId = requestAccountDO.getInstSubFundsAccountEntity().getBucketId();
        if (Objects.nonNull(bucketId)) {
            this.inputInstSubFundsAccountBucketId(requestAccountDO, queryBucketEntity, bucketId);
            return;
        }

        // 2. 子级资金账号未分配拓展参数，则检查是否需要分配
        List<InstFundsAccountBucketEntity> bucketsIdIsNullByAccountId = instFundsAccountBucketService.queryBucketsIdIsNullByAccountId(queryBucketEntity);
        requestAccountDO.setInstSubFundsAccountBucketsIsNullEntityList(bucketsIdIsNullByAccountId);

        // 3. 参数桶为空则不需要分配拓展参数
        if (CollectionUtil.isNotEmpty(bucketsIdIsNullByAccountId)) {

            // 4. 参数桶计数 total 为空说明不需要计数
            if (Objects.isNull(bucketsIdIsNullByAccountId.get(0).getTotal())) {  // NO_CHECK
                this.inputInstSubFundsAccountBucketId(requestAccountDO, queryBucketEntity, bucketsIdIsNullByAccountId.get(0).getId()); // NO_CHECK
                return;
            }

            // 5. 参数桶计数 total 不为空，则分配拓展参数，参数桶计数逻辑处理（分布式锁）
            this.inputInstSubFundsAccountBucketIdLock(requestAccountDO, bucketsIdIsNullByAccountId, queryBucketEntity);
        }
    }

    /**
     * 参数桶计数逻辑处理（分布式锁）
     */
    public void inputInstSubFundsAccountBucketIdLock(RequestAccountDO requestAccountDO, List<InstFundsAccountBucketEntity> bucketsIdIsNullByAccountId, InstFundsAccountBucketEntity queryBucketEntity) {

        long startTime = System.currentTimeMillis();
        String accountId = queryBucketEntity.getAccountId();
        // 分布式锁
        String key = prefix.concat(Thread.currentThread().getStackTrace()[1].getMethodName()).concat(":").concat(accountId);
        boolean locked = uredissonLock.lock(key, 5, TimeUnit.SECONDS);
        if (locked) {
            try {
                this.handelInstSubFundsAccountBucketId(requestAccountDO, bucketsIdIsNullByAccountId, queryBucketEntity);
            } catch (Exception e) {
                log.error("InstFundsAccountBucketManageImpl-inputInstSubFundsAccountBucketLock Exception:{}，costTime:{}", e, (System.currentTimeMillis() - startTime));
                throw e;
            } finally {
                uredissonLock.release(key);
            }
        } else {
            throw new BusinessException(ErrorCodeEnum.INST_ACCOUNT_PARAMS_BUCKET_EXCEPTIONS.getCode(), ErrorCodeEnum.INST_ACCOUNT_PARAMS_BUCKET_EXCEPTIONS.getMsg());
        }
    }

    /**
     * 参数桶计数逻辑处理
     */
    public void handelInstSubFundsAccountBucketId(RequestAccountDO requestAccountDO, List<InstFundsAccountBucketEntity> bucketsIdIsNullByAccountId, InstFundsAccountBucketEntity queryBucketEntity) {

        // 统计子级资金账号，各个桶使用情况，选择一个合适的桶数据
        InstSubFundsAccountQueryEntity subFundsAccountQueryEntity = new InstSubFundsAccountQueryEntity();
        subFundsAccountQueryEntity.setAccountId(queryBucketEntity.getAccountId());
        List<InstSubFundsAccountBucketCountEntity> countEntities = instSubFundsAccountService.queryBucketIdCountByAccountId(subFundsAccountQueryEntity);

        if (CollectionUtil.isNotEmpty(countEntities)) {
            // countEntities 不为空则检查使用情况
            Map<Long, Integer> map = countEntities.stream().collect(Collectors.toMap(InstSubFundsAccountBucketCountEntity::getBucketId, InstSubFundsAccountBucketCountEntity::getCount));

            for (InstFundsAccountBucketEntity bucketEntity : bucketsIdIsNullByAccountId) {
                if (!map.containsKey(bucketEntity.getId())) {
                    this.inputInstSubFundsAccountBucketId(requestAccountDO, queryBucketEntity, bucketEntity.getId());
                    break;
                }
                if (map.containsKey(bucketEntity.getId()) && Objects.nonNull(bucketEntity.getTotal()) && map.get(bucketEntity.getId()) < bucketEntity.getTotal()) {
                    this.inputInstSubFundsAccountBucketId(requestAccountDO, queryBucketEntity, bucketEntity.getId());
                    break;
                }
            }
            // 参数桶使用量异步检查
            applicationEventPublisher.publishEvent(new InstFunsAccountBucketsAlertEvent(this, requestAccountDO.getInstFundsAccountEntity(), bucketsIdIsNullByAccountId, map));
        } else {
            //countEntities 为空则取第一个桶用于计数
            Long bucketsId = bucketsIdIsNullByAccountId.get(0).getId(); // NO_CHECK
            this.inputInstSubFundsAccountBucketId(requestAccountDO, queryBucketEntity, bucketsId);
        }
    }

    /**
     * 填充机构资金账号子级资金账户开通拓展请求参数（指定参数桶ID）
     */
    public void inputInstSubFundsAccountBucketId(RequestAccountDO requestAccountDO, InstFundsAccountBucketEntity queryBucketEntity, Long bucketsId) {
        queryBucketEntity.setBucketsId(bucketsId);
        requestAccountDO.getInstSubFundsAccountEntity().setBucketId(bucketsId);
        List<InstFundsAccountBucketEntity> bucketsIdNotNullByAccountId = instFundsAccountBucketService.queryBucketsIdNotNullByBucketsId(queryBucketEntity);
        requestAccountDO.setInstSubFundsAccountBucketsNotNullEntityList(bucketsIdNotNullByAccountId);

    }

    /**
     * 监听机构账号拓展参数桶表数据
     */
    @Override
    @Async(CommonConstants.ASYNC_TASK_EXECUTOR_NAME)
    @EventListener
    public void onApplicationEvent(InstFunsAccountBucketsAlertEvent event) {
        InstFundsAccountEntity instFundsAccountEntity = event.getInstFundsAccountEntity();
        // 参数桶是否有告警配置
        if (paramsBucketDingMap.containsKey(instFundsAccountEntity.getAccountId())) {

            List<InstFundsAccountBucketEntity> list = event.getAccountBucketEntityList();
            Map<Long, Integer> subAccountCountMap = event.getSubAccountCountMap();
            list = list.stream().filter(t -> Objects.nonNull(t.getTotal())).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(list)) {
                long totalCount = 0;
                long usedCount = 1;

                for (InstFundsAccountBucketEntity accountBucket : list) {
                    totalCount += accountBucket.getTotal();
                    usedCount += subAccountCountMap.containsKey(accountBucket.getId()) ? subAccountCountMap.get(accountBucket.getId()): 0L;
                }

                // 剩余是否达到告警限制
                long remainder = totalCount - usedCount;
                if (remainder < paramsBucketDingMap.get(instFundsAccountEntity.getAccountId())) {
                    // 发送钉钉告警
                    String title = "机构账号【" + list.get(0).getKeyName() + "】" ; // NO_CHECK
                    String message = "";
                    if (remainder <= 0){
                        InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
                        queryEntity.setAccountId(instFundsAccountEntity.getAccountId());
                        int pendingCount = instSubFundsAccountService.queryCountBucketIdIsNullByAccountId(queryEntity);
                        title = title + "已用尽";
                        message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 国家：%s\n- 币种：%s\n- 使用类型：%s\n- 待处理申请数：%s个\n"
                                , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instFundsAccountEntity.getCountry(), instFundsAccountEntity.getCurrency(), instFundsAccountEntity.getUseType(), pendingCount);
                    } else {
                        title = title + "即将用尽";
                        message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 国家：%s\n- 币种：%s\n- 使用类型：%s\n- " + list.get(0).getKeyName() + "剩余使用数：%s个\n" // NO_CHECK
                                , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instFundsAccountEntity.getCountry(), instFundsAccountEntity.getCurrency(), instFundsAccountEntity.getUseType(), remainder);
                    }
                     dingAlertClient.sendMsgForExceptionGroupSubAccount(CommonConstants.PUSH_SUB_ACCOUNT_HANDLE_GROUP, title, message);
                }
            }
        }
    }
}
