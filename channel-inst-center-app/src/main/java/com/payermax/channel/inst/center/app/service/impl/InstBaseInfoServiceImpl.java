package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoDao;
import com.payermax.channel.inst.center.app.service.InstBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstBaseInfoServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 17:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstBaseInfoServiceImpl implements InstBaseInfoService {

    @Autowired
    private InstBaseInfoDao instBaseInfoDao;

    @Override
    public InstBaseInfoEntity queryByInstCode(String instCode) {
        Preconditions.checkArgument(instCode != null, "param instCode is mandatory");

        List<InstBaseInfoEntity> baseInfoEntities = instBaseInfoDao.selectByInstCode(instCode);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(baseInfoEntities), "instBaseInfo query by instCode resultSize is empty");
        Preconditions.checkArgument(baseInfoEntities.size() == 1, "instBaseInfo query by instCode resultSize must 1");

        return baseInfoEntities.get(0); //CHECKED
    }

    @Override
    public InstBaseInfoEntity queryById(Long instId) {
        Preconditions.checkArgument(instId != null, "param instId is mandatory");

        InstBaseInfoEntity queryEntity = new InstBaseInfoEntity();
        queryEntity.setInstId(instId);
        List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoDao.selectAll(queryEntity);
        if (CollectionUtils.isNotEmpty(instBaseInfoEntityList)) {
            return instBaseInfoEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public List<InstBaseInfoEntity> query(InstBaseInfoEntity instBaseInfoEntity) {
        Preconditions.checkArgument(instBaseInfoEntity != null, "param instBaseInfoEntity is mandatory");
        List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoDao.selectAll(instBaseInfoEntity);
        if (CollectionUtils.isNotEmpty(instBaseInfoEntityList)) {
            return instBaseInfoEntityList;
        }
        return null;
    }

    @Override
    public IPage<InstBaseInfoQueryEntity> queryAll(InstBaseInfoQueryEntity instBaseInfoQueryEntity, Long pageNum, Long pageSize) {
        Preconditions.checkArgument(instBaseInfoQueryEntity != null, "param instBaseInfoQueryEntity is mandatory");
        Page<InstBaseInfoQueryEntity> page = new Page<>(pageNum, pageSize);
        return instBaseInfoDao.selectAllInstInfo(page,instBaseInfoQueryEntity);
    }

    @Override
    public List<InstBaseInfoEntity> queryByBrandId(List<Long> instBrandIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instBrandIds), "param instBrandIds is mandatory");
        List<InstBaseInfoEntity> instBaseInfoEntities = instBaseInfoDao.selectByBrandId(instBrandIds);
        return instBaseInfoEntities;
    }

    @Override
    public int save(InstBaseInfoEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getInstId() == null) {
            // 新增
            result = instBaseInfoDao.insert(record);
        } else {
            // 更新
            result = instBaseInfoDao.updateByPrimaryKey(record);
        }
        return result;
    }
}
