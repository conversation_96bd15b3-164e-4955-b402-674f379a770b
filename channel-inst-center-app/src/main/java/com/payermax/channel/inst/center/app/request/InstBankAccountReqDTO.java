package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName InstBankAccountReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 21:17
 * @Version 1.0
 */
@Data
public class InstBankAccountReqDTO implements Serializable {

    private static final long serialVersionUID = 4639266154205127011L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 机构标识
     */
    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    /**
     * 机构唯一标识code
     */
    @ApiModelProperty(notes = "机构唯一标识code")
    private String instCode;

    /**
     * 地区
     */
    @ApiModelProperty(notes = "地区")
    private String country;

    /**
     * 币种
     */
    @ApiModelProperty(notes = "币种")
    private String currency;

    /**
     * 开户银行
     */
    @ApiModelProperty(notes = "开户银行")
    private String bankCode;

    /**
     * 开户银行名称
     */
    @ApiModelProperty(notes = "开户银行名称")
    private String bankName;

    /**
     * 开户名称
     */
    @ApiModelProperty(notes = "开户名称")
    private String accountName;

    /**
     * 银行账号
     */
    @ApiModelProperty(notes = "银行账号")
    private String accountNo;

    /**
     * 分支行
     */
    @ApiModelProperty(notes = "分支行")
    private String branch;

    /**
     * 分支行地址
     */
    @ApiModelProperty(notes = "分支行地址")
    private String branchAddress;

    /**
     * swift code
     */
    @ApiModelProperty(notes = "swiftCode")
    private String swiftCode;

    /**
     * iban
     */
    @ApiModelProperty(notes = "iban")
    private String iban;

    /**
     * 账户用途
     */
    @ApiModelProperty(notes = "账户用途")
    private String accountUse;

    /**
     * 我方，在渠道充值场景，要充值到机构的银行账户
     * eg:isDefaultRecharge 描述 是否 将当前账户 作为充值默认目标账户
     */
    @ApiModelProperty(notes = "是否作为默认的渠道充值支持账户")
    private Boolean isChannelRechargeDefault;

    /**
     * 状态(Y：启用，N：停用)
     */
    @ApiModelProperty(notes = "状态")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;
}
