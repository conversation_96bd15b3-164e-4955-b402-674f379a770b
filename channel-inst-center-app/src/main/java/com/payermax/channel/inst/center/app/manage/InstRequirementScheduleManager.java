package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.app.request.InstRequirementScheduleQueryReqDTO;
import com.payermax.channel.inst.center.app.request.InstRequirementScheduleReqDTO;
import com.payermax.channel.inst.center.app.response.InstRequirementScheduleQueryVO;
import com.payermax.channel.inst.center.app.response.InstRequirementScheduleVO;

/**
 * 集成需求排期Manager
 *
 * <AUTHOR>
 * @date 2022/6/15 17:17
 */
public interface InstRequirementScheduleManager {

    /**
     * 保存信息
     *
     * @param reqDto
     * @return
     */
    int save(InstRequirementScheduleReqDTO reqDto);

    /**
     * 分页查询
     *
     * @param reqDto
     * @return
     */
    PageResult<InstRequirementScheduleQueryVO> queryPageList(InstRequirementScheduleQueryReqDTO reqDto);

    /**
     * 根据排期ID查询信息
     *
     * @param id
     * @return
     */
    InstRequirementScheduleVO query(Long id);

    /**
     * 查询集成排期详情
     *
     * @param scheduleId
     * @param version
     * @return
     */
    InstRequirementScheduleVO queryScheduleDetail(Long scheduleId, String version);

}
