package com.payermax.channel.inst.center.app.manage.workflow;

import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */
@Slf4j
@Service
@DubboService(version = "1.0", timeout = 10000)
public class OmcWorkflowCallbackServiceImpl implements OmcWorkflowCallbackService {

    @Resource
    private WorkflowHandlerRegistry workflowHandlerRegistry;
    @Autowired
    private InstBusinessDraftRepository businessDraftRepository;

    /**
     * 流程回调
     * @param eventInfo 流程回调信息
     * @return 回调结果
     */
    @Override
    public Boolean callback(WfProcessEventInfo eventInfo) {
        String processId = eventInfo.getProcessInfo().getProcessId();
        String draftId = eventInfo.getProcessInfo().getBusinessKey();
        InstProcessStatusEnum processStatus = InstProcessStatusEnum.parse(eventInfo.getProcessInfo().getResult().name());
        log.info("workflow callback, processId:{}, draftId:{}, processStatus:{}", processId, draftId, processStatus);
        // 查询流程相关草稿
        InstBusinessDraft draft = InstBusinessDraftAssembler.INSTANCE.po2Domain((businessDraftRepository.getById(draftId)));
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(draft), ErrorCodeEnum.INNER_ERROR.getCode(), "draft is not exist");
        // 获取回调处理器
        String handleName = workflowHandlerRegistry.getHandlerName(draft.getBusinessType().name(), draft.getModuleName().name(), draft.getOperateType().name());
        AbstractWorkflowHandler handler = workflowHandlerRegistry.getHandler(handleName);
        // 执行回调
        return handler.callbackHandler(draft, processStatus, eventInfo);
    }

}
