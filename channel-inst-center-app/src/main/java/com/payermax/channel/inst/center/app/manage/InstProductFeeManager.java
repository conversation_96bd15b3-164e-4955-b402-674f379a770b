package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstProductFeeReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductFeeVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 0:02
 */
public interface InstProductFeeManager {
    /**
     * 保存产品费用信息
     *
     * @param productFeeReqDTOList
     * @return
     */
    int saveAll(List<InstProductFeeReqDTO> productFeeReqDTOList);

    /**
     * 查询产品费用信息
     *
     * @param productFeeReqDTO
     * @return
     */
    List<InstProductFeeVO> query(InstProductFeeReqDTO productFeeReqDTO);

    /**
     * 根据合同单号询产品费用信息
     *
     * @param contractNo
     * @return
     */
    List<InstProductFeeVO> queryAll(String contractNo);
}
