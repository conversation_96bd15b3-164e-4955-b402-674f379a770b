package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.event.InstSubFunsAccountBucketsAlertEvent;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;

/**
 * 机构资金账号预申请Manage
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstSubFundsAccountBucketManage {

    /**
     * 检查是否支持预申请，支持则通过预申请账号创建子级资金账号
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountRequestDO
     * @return
     */
    InstSubFundsAccountEntity checkAndCreateSubAccountByPreApplyBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO);

    /**
     * 检查是否支持预申请，支持则创建预申请账号
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountEntity
     * @return
     */
    InstSubFundsAccountBucketEntity buildAndCreatePreApplySubAccountBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity);
    
    /**
     * 查询机构账号下，预申请中可分配的
     * 但不存在于子级账号表且状态为申请中的
     * 预申请账号总数
     *
     * @param instFundsAccountEntity
     * @return
     */
    int queryAssignableCountByAccountId(InstFundsAccountEntity instFundsAccountEntity);
    
    /**
     * 预申请账号事件
     *
     * @param event
     * @return
     */
    void onApplicationEvent(InstSubFunsAccountBucketsAlertEvent event);
}
