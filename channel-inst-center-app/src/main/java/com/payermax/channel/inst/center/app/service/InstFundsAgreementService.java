package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.request.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstFundsAgreementQueryVO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 资金协议 Service
 */
public interface InstFundsAgreementService {

    /**
     * 保存资金协议
     * @param context 资金协议上下文
     * @return 资金协议
     */
    InstFundsAgreementContextDTO fundsAgreementSave(InstFundsAgreementContextDTO context);

    /**
     * 资金协议查询
     * @param pageNum 页码
     * @param pageSize 步长
     * @param request 查询参数
     * @return 资金协议VO 列表（资金协议、协议发起方、协议对手方）
     */
    List<InstFundsAgreementQueryVO> fundsAgreementQuery(int pageNum, int pageSize, InstFundsAgreementQueryRequestDTO request);

    /**
     * 根据 ID 查询资金协议
     * @param fundsAgreementNo 资金协议编号
     * @return 清算规则
     */
    InstFundsAgreementPO getFundsAgreementById(String fundsAgreementNo);

    /**
     * 修改业务协议
     * @param shareId 用户 ID
     * @param request 业务协议
     * @return 是否成功
     */
    Boolean bizAgreementModify(String shareId,InstFundsAgreementContextDTO.BizAgreement request);

    /**
     * 修改资金协议
     * @param shareId 用户 ID
     * @param request 资金协议
     * @return 是否成功
     */
    Boolean fundsAgreementModify(String shareId,InstFundsAgreementContextDTO.FundsAgreement request);

    /**
     * 资金协议状态修改
     * @param shareId 用户 ID
     * @param request 资金协议状态
     * @return 是否成功
     */
    Boolean fundsAgreementStatusChange(String shareId, InstFundsAgreementContextDTO.FundsAgreement request);


    /**
     * 新增清算规则
     * @param shareId 用户 ID
     * @param request 清算规则
     * @return 是否成功
     */
    Boolean settleRuleAdd(String shareId, InstFundsAgreementContextDTO.FundsSettleRule request);

    /**
     * 清算规则修改
     * @param shareId 用户 ID
     * @param request 清算规则
     * @return 是否成功
     */
    Boolean settleRuleModify(String shareId, InstFundsAgreementContextDTO.FundsSettleRule request);
}
