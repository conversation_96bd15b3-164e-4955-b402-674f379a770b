package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 机构DD操作请求DTO
 *
 * <AUTHOR>
 * @date 2022/5/15 22:59
 */
@Data
public class InstDdReqDTO implements Serializable {

    private static final long serialVersionUID = -2531185069465895608L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "机构标识")
    @NotNull(message = "[instId] is mandatory")
    private Long instId;

    @ApiModelProperty(notes = "申请单号")
    private String applyNo;

    private String registerName;

    private String registerNo;

    private Date registerDate;

    private String registerAddress;

    private String companyScale;

    private Date validityDate;

    private String corporateName;

    private Date corporateBirthDate;

    private String corporateAddress;

    private String website;

    private String businessScope;

    private String status;

    @ApiModelProperty(notes = "机构信息")
    private InstBaseInfoReqDTO instBaseInfo;

    @ApiModelProperty(notes = "调研问卷信息")
    private InstDdSurveyReqDTO ddSurvey;

    @ApiModelProperty(notes = "审核资料数据信息集合")
    private List<InstAuditDataReqDTO> auditDataList;

    @ApiModelProperty(notes = "审核结果")
    private InstAuditResultReqDTO auditResult;

}
