package com.payermax.channel.inst.center.app.dto.contract;

import com.payermax.channel.inst.center.facade.request.contract.InstContractBatchVerifyRequest;
import com.payermax.channel.inst.center.facade.response.InstContractBatchVerifyResponse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**

* <AUTHOR>
* @date 2025/4/9
* @DESC 机构合约批量校验上下文
*/
@Data
@Accessors(chain = true)
public class InstContractBatchVerifyContext {

    private InstContractBatchVerifyRequest request;

    private InstContractBatchVerifyResponse response;

    /**
     * 交易时间
     */
    private long transactionTime;

    /**
     * 校验列表
     */
    private CopyOnWriteArrayList<VerifyItemDto> verifyDataList;

    /**
     * 全部校验通过
     */
    private Boolean allVerifyPass;


    @Data
    public static class VerifyItemDto {

        /**
         * 机构编码
         */
        private String instCode;

        /**
         * 签约主体
         */
        private String entity;

        /**
         * 业务类型
         */
        private String bizType;

        /**
         * 支付方式类型
         */
        private String paymentMethodType;

        /**
         * 目标机构
         */
        private String targetOrg;

        /**
         * 目标卡组
         */
        private String cardOrg;

        /**
         * 币种列表
         */
        private List<String> ccyList;

        /**
         * 渠道商户号列表
         */
        private List<String> midList;

        /**
         * 有效 MID
         */
        private List<String> validMidList = new CopyOnWriteArrayList<>();

        /**
         * 无效 MID
         */
        private List<String> invalidMidList = new CopyOnWriteArrayList<>();

        /**
         * 全部校验通过
         */
        private Boolean allVerifyPass;

        /**
         * 校验失败信息
         */
        private List<String> verifyFailMsgList = new CopyOnWriteArrayList<>();

    }
}
