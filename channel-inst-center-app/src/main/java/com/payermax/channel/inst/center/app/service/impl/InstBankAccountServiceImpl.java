package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBankAccountDao;
import com.payermax.channel.inst.center.app.service.InstBankAccountService;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBankAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstBankAccountServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 14:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstBankAccountServiceImpl implements InstBankAccountService {

    @Autowired
    private InstBankAccountDao instBankAccountDao;

    @Autowired
    private InstBankAccountRepository instBankAccountRepository;


    @Override
    public List<InstBankAccountEntity> queryByCondition(InstBankAccountEntity queryCondition) {
        Preconditions.checkArgument(queryCondition.getInstId() != null, "param instId is mandatory");

        List<InstBankAccountEntity> instBankAccountEntities = instBankAccountDao.selectByCondition(queryCondition);

        if (CollectionUtils.isNotEmpty(instBankAccountEntities)) {
            return instBankAccountEntities;
        }
        return null;
    }

    @Override
    public int save(InstBankAccountEntity instBankAccountEntity) {
        Preconditions.checkArgument(instBankAccountEntity != null, "param instBankAccountEntity is mandatory");
        int result = 0;
        //主键不为空则根据主键更新信息
        if (null != instBankAccountEntity.getId()) {
            result = instBankAccountDao.updateByPrimaryKey(instBankAccountEntity);
        } else {
            result = instBankAccountDao.insert(instBankAccountEntity);
        }
        return result;
    }

    /**
     * 根据账户名称模糊查询银行账户
     * @param accountAlias 账户名称
     */
    @Override
    public List<InstBankAccountPO> queryBankByAccountAlias(String accountAlias){
        return instBankAccountRepository.queryDetailByAccountAlias(accountAlias);
    }
}
