package com.payermax.channel.inst.center.app.valid.annon.impl;

import com.payermax.channel.inst.center.common.constants.DictConstants;
import com.payermax.channel.inst.center.common.constrains.InterfaceValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.infrastructure.client.FintechBaseClientProxy;
import com.payermax.common.lang.util.StringUtil;
import com.ushareit.fintech.base.dto.DictItemDTO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CountryValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class CountryValid implements InterfaceValid<String, CountryValid.SimpleCountry> {

    @Resource
    private FintechBaseClientProxy baseClientProxy;

    @Override
    public boolean valid(String value, LangEnum lang) {
        if (StringUtil.isEmpty(value)) {
            return Boolean.TRUE;
        }
        return dataList().stream().anyMatch(item -> value.equals(item.getCode()));
    }

    @Override
    public List<SimpleCountry> queryData() {
        List<DictItemDTO> list = baseClientProxy.queryDict(DictConstants.DICT_ALL_COUNTRY);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(item -> new SimpleCountry().setCode(item.getItemName())).collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public class SimpleCountry {

        private String code;

        private String zhName;

        private String enName;
    }
}
