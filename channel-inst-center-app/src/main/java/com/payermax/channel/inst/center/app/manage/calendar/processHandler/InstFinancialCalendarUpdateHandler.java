package com.payermax.channel.inst.center.app.manage.calendar.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.HolidayAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.dto.calendar.HolidayChangeNotify;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContext;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContextBuilder;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarSaveRequest;
import com.payermax.channel.inst.center.app.rocketmq.producer.HolidayUpdateProducer;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.TemplateUtil;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.HolidayOperateEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialHolidaysUpdateLogPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialHolidayUpdateLogRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/18
 * @DESC
 */
@Slf4j
@Setter
@Component
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER, actionType = OperateTypeEnum.UPDATE)
public class InstFinancialCalendarUpdateHandler extends AbstractWorkflowHandler {


    @Resource
    private IDGenerator idGenerator;
    @Resource
    private InstFinancialCalendarContextBuilder calendarContextBuilder;
    @Resource
    private InstFinancialCalendarRepository calendarRepository;
    @Resource
    private InstFinancialCalendarHolidayRepository holidayRepository;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private InstFinancialHolidayUpdateLogRepository instFinancialHolidayUpdateLogRepository;

    @Resource
    private HolidayUpdateProducer holidayUpdateProducer;


    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarUpdateProcess.key:process_inst-center-financial-calendar-save-review}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarUpdateProcess.desc:金融日历修改审批}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarUpdateProcess.formMsgTemplate:日期：#!{holidayDate} | 是否工作日：#!{isWorkday} | 描述：#!{description} }", autoRefreshed = true)
    private String processFormMsgTemplate;


    /**
     * 初始化日历和节假日
     *
     * @param request 请求参数
     * @return 初始化结果
     */
    public Boolean startCalendarUpdate(InstFinancialCalendarSaveRequest request) {
        // 前置校验
        preCheck(request);

        // 校验数据并构建上下文
        log.info("InstFinancialCalendarUpdateHandler initCalendarAndHolidayDraft");
        InstFinancialCalendarContext context = calendarContextBuilder.validateAndBuildContext(request);

        // 发起流程
        log.info("InstFinancialCalendarUpdateHandler initCalendarAndHolidayDraft startProcess");
        InstBusinessDraft draft = InstFinancialCalendarAssembler.INSTANCE.calendarUpdateContext2Draft(context, idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.FINANCIAL_CALENDAR), request.getShareId());
        return super.startProcess(draft);
    }

    /**
     * 前置校验
     */
    private void preCheck(InstFinancialCalendarSaveRequest request) {
        // 查询日历
        List<InstFinancialCalendarPO> calendarList = calendarRepository.queryByConditions(InstFinancialCalendarPO.builder().calendarId(request.getCalendarId()).build());
        AssertUtil.isTrue(calendarList.size() == 1, ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "日历不存在，无法修改");
        AssertUtil.isTrue(StringUtils.isBlank(calendarList.get(0).getSourceCalendar()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "引用其他日历，无法修改");
    }


    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        processConfig.setProcessDefKey(processKey);
        processConfig.setApplicantShareId(draft.getOwner());
        processConfig.setProcessDesc(processDesc);
        // 草稿表单信息
        InstFinancialCalendarContext context = JSON.parseObject(draft.getDraftData(), InstFinancialCalendarContext.class);
        List<InstFinancialCalendarHoliday> addHolidayList = context.getHolidayList().stream()
                .filter(holiday -> holiday.getHolidayOperate().equals(HolidayOperateEnum.ADD)).collect(Collectors.toList());
        List<InstFinancialCalendarHoliday> cancelHolidayList = context.getHolidayList().stream()
                .filter(holiday -> holiday.getHolidayOperate().equals(HolidayOperateEnum.CANCEL)).collect(Collectors.toList());

        HashMap<String, Object> formMsgMap = new HashMap<>(4);
        formMsgMap.putAll(JSONObject.parseObject(JSONObject.toJSONString(context.getCalendar()), new TypeReference<HashMap<String, Object>>() {
        }));
        formMsgMap.put("businessKey", draft.getBusinessKey());
        formMsgMap.put("weekendListStr", String.join(",", context.getCalendar().getWeekendList()));
        formMsgMap.put("addHolidayList", composeHolidayFormMsg(addHolidayList, processFormMsgTemplate));
        formMsgMap.put("cancelHolidayList", composeHolidayFormMsg(cancelHolidayList, processFormMsgTemplate));
        processConfig.setFormInfoMap(formMsgMap);
        return processConfig;
    }

    /**
     * 构造节假日表单信息
     */
    private String composeHolidayFormMsg(List<InstFinancialCalendarHoliday> formMsgList, String template) {
        String formatedTemplate = template.replace("#!", "$!");
        return formMsgList.stream().map(item -> {
            Map<String, Object> params = JSON.parseObject(JSON.toJSONString(item), new TypeReference<Map<String, Object>>() {
            });
            return TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, formatedTemplate, params);
        }).collect(Collectors.joining(","));
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarUpdateHandler businessPassHandler");
        InstFinancialCalendarContext context = JSON.parseObject(draft.getDraftData(), InstFinancialCalendarContext.class);
        return transactionTemplate.execute(transactionStatus -> {
            List<InstFinancialCalendarHolidayPO> holidayPoList = context.getHolidayList().stream().map(InstFinancialCalendarAssembler.INSTANCE::domain2Po).collect(Collectors.toList());

            List<Long> holidayIds = new ArrayList<>();
            List<String> holidayDates = new ArrayList<>();
            List<String> calendarIds = new ArrayList<>();
            Map<String, InstFinancialCalendarHolidayPO> newMap = new HashMap<>();
            Map<String, InstFinancialCalendarHolidayPO> oldMap = new HashMap<>();
            Map<String, InstFinancialCalendarPO> id2CalendarMap = new HashMap<>();

            holidayPoList.forEach(item -> {
                if (item.getHolidayId() != null) {
                    holidayIds.add(item.getHolidayId());
                }
                holidayDates.add(item.getHolidayDate().toString());
                calendarIds.add(item.getCalendarId());
                newMap.put(item.getHolidayDate().toString(), item);
            });

            List<InstFinancialCalendarPO> calendarPOS = calendarRepository.queryByCalendarIds(calendarIds);
            for (InstFinancialCalendarPO calendarPO : calendarPOS) {
                id2CalendarMap.put(calendarPO.getCalendarId(), calendarPO);
            }

            List<InstFinancialCalendarHolidayPO> olds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(holidayIds)) {
                olds = holidayRepository.queryByHolidayIds(holidayIds);
            }

            olds.forEach(item -> oldMap.put(item.getHolidayDate().toString(), item));

            // 构建变更日志
            List<InstFinancialHolidaysUpdateLogPO> logs = buildUpdateLogPO(holidayDates, oldMap, newMap, id2CalendarMap);

            // 构建消息发送体
            HolidayChangeNotify notify = new HolidayChangeNotify();
            notify.setNotifyType("REALTIME");
            notify.setHolidayChangeItems(HolidayAssembler.holidayChangeItemsConvert(logs));

            // 发消息
            holidayUpdateProducer.sendHolidayUpdate(notify);


            // 保存修改记录
            instFinancialHolidayUpdateLogRepository.saveBatch(logs);

            // 保存节假日
            holidayRepository.saveOrUpdateBatch(holidayPoList);
            log.info("InstFinancialCalendarUpdateHandler businessPassHandler save calendar holiday");

            return Boolean.TRUE;
        });
    }

    private static List<InstFinancialHolidaysUpdateLogPO> buildUpdateLogPO(List<String> holidayDates, Map<String, InstFinancialCalendarHolidayPO> oldMap, Map<String, InstFinancialCalendarHolidayPO> newMap, Map<String, InstFinancialCalendarPO> id2CalendarMap) {
        List<InstFinancialHolidaysUpdateLogPO> logs = new ArrayList<>();
        for (String holidayDate : holidayDates) {
            InstFinancialHolidaysUpdateLogPO logPO = new InstFinancialHolidaysUpdateLogPO();
            // 老的是通过id去取的历史数据,如果原来不是节假日,那么这个是空的
            InstFinancialCalendarHolidayPO holidayOldPO = oldMap.get(holidayDate);
            // newMap带过来的是这次的变更,一定不为空
            InstFinancialCalendarHolidayPO holidayLatestPO = newMap.get(holidayDate);

            if (holidayOldPO == null && holidayLatestPO == null) {
                throw new IllegalArgumentException("新老都是空,数据有问题!");
            }

            InstFinancialCalendarPO calendarPO = id2CalendarMap.get(holidayLatestPO.getCalendarId());
            AssertUtil.notNull(calendarPO, "calendarPO is null:" + holidayLatestPO.getCalendarId());
            logPO.setHolidayDate(holidayDate);
            logPO.setCalendarType(calendarPO.getCalendarType());
            logPO.setCurrency(calendarPO.getCurrency());
            logPO.setCountry(calendarPO.getCountry());
            logPO.setHolidayName(holidayLatestPO.getDescription());
            if (holidayOldPO == null) {
                // 老的是空的,新的不为空,代表是非节假日改成了节假日
                InstFinancialCalendarHolidayPO holidayPO = new InstFinancialCalendarHolidayPO();
                holidayPO.setCalendarId(holidayLatestPO.getCalendarId());
                holidayPO.setHolidayDate(holidayLatestPO.getHolidayDate());
                // 老的如果不存在,一定是非节假日
                holidayPO.setIsWorkday(true);
                logPO.setOriginInfo(JSON.toJSONString(holidayPO));
            } else {
                // 都不为空,那就该怎么处理怎么处理
                logPO.setOriginInfo(JSON.toJSONString(holidayOldPO));
            }
            logPO.setLatestInfo(JSON.toJSONString(holidayLatestPO));
            logs.add(logPO);
        }
        return logs;
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarUpdateHandler businessRejectHandler");
        return Boolean.TRUE;
    }
}
