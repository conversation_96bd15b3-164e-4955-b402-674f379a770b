package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 23:24
 */
public interface InstContractService {
    /**
     * 保存合同
     *
     * @param record
     * @return
     */
    int save(InstContractEntity record);

    /**
     * 查询合同
     *
     * @param instContractEntity
     * @return
     */
    InstContractEntity getByEntity(InstContractEntity instContractEntity);

    /**
     * 查询机构合同列表
     *
     * @param instContractEntity
     * @return
     */
    List<InstContractEntity> getByEntityList(InstContractEntity instContractEntity);

    /**
     * 查询机构合同列表
     *
     * @param instIds
     * @return
     */
    List<InstContractEntity> getByInstIds(List<Long> instIds);

}
