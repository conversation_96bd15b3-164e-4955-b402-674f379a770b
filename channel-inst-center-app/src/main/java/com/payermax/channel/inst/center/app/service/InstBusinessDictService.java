package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:25
 */
public interface InstBusinessDictService {
    /**
     * 保存业务字典
     *
     * @param record
     * @return
     */
    int save(InstBusinessDictEntity record);

    /**
     * 保存业务字典
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstBusinessDictEntity> records);

    /**
     * 根据业务类型查询业务字典
     *
     * @param businessType
     * @return
     */
    List<InstBusinessDictEntity> getByBusinessType(String businessType);

    /**
     * 根据业务字典主键id删业务字典
     * @param id
     * @return
     */
    int deleteById(Long id);
}
