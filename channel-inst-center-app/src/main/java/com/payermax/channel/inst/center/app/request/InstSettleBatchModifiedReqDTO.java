package com.payermax.channel.inst.center.app.request;

import com.payermax.channel.inst.center.app.response.InstContractSettlementVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/4
 * @DESC
 */
@Data
public class InstSettleBatchModifiedReqDTO {

    /**
     * 结算信息编号列表
     */
    @ApiModelProperty(notes = "结算信息编号列表")
    private List<String> settleItemNoList;

    /**
     * 结算信息修改内容
     */
    @ApiModelProperty(notes = "结算信息修改内容")
    private InstContractSettlementVO settleData;

}
