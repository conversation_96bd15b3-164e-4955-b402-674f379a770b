package com.payermax.channel.inst.center.app.request.calendar;

import com.payermax.channel.inst.center.domain.enums.calendar.CalendarStatusEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/18
 * @DESC
 */
@Data
public class InstFinancialCalendarActivateRequest  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日历id
     */
    @NotBlank
    private String calendarId;

    /**
     * 状态
     */
    @NotNull
    private CalendarStatusEnum status;

    /**
     * 流程发起人
     */
    private String shareId;
}
