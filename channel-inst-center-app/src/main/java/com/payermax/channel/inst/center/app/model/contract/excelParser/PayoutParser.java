package com.payermax.channel.inst.center.app.model.contract.excelParser;

import com.payermax.channel.inst.center.app.dto.impl.AccumulatedInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayoutDTO;
import com.payermax.channel.inst.center.app.dto.impl.SettleInfoDTO;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstContractExcelEnum;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class PayoutParser extends InstContractParser {

    @PostConstruct
    public void init() {
        registryParser(ContractBizTypeEnum.O, this);
    }
    /**
     * 构建上下文
     */
    @Override
    public InstContractContext buildContext(InstContractParseRequestDTO request) {
        InstContractContext context = new InstContractContext();
        BeanUtils.copyProperties(request, context);
        return context;
    }

    /**
     * 解析 Excel，构造原始数据
     */
    @Override
    public void resolve(byte[] fileContent, InstContractContext context) {

        // 1. 解析 excel 文件 sheet 内容
        ExcelUtil.ExcelParseInfo<InstContractFeePayoutDTO> excelFeeInfo = ExcelUtil.readExcel(fileContent,
                InstContractFeePayoutDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_PAYOUT.getExcelSheetName());
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = ExcelUtil.readExcel(fileContent,
                AccumulatedInfoDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_PAYOUT.getExcelSheetName());

        // 2. 删除第一行数据（excel 中第二行，对列的解释）
        excelFeeInfo.getDataList().remove(0);
        excelAccumulatedInfo.getDataList().remove(0);

        // 3. 填入 Excel 中不携带的公共字段
        excelFeeInfo.getDataList().forEach(item -> BeanUtils.copyProperties(context,item));

        // 4. 费用信息、手续费信息写入上下文
        context.setFeeInfo(excelFeeInfo);
        context.setAccumulatedInfo(excelAccumulatedInfo);
    }

    /**
     * Excel 数据校验
     */
    @Override
    public void valid(InstContractContext context) {
        // 对数据行判空，没有数据抛出异常
        if(context.getFeeInfo().getDataList().isEmpty()){
            throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(),"Excel 数据不允许为空");
        }
        // 校验 Excel 内容, 校验失败信息包装到返回值中
        Map<String, ExportErrorInfo> errorInfoMap = new HashMap<>(16);
        errorInfoMap.put(InstContractExcelEnum.INST_CONTRACT_FEE_PAYOUT.getExcelSheetName(),commonExcelValid.valid(context.getFeeInfo()).getErrorInfo());
        // 根据校验结果判断有无校验错误
        context.setIsHasError(errorInfoMap.values().stream().anyMatch(Objects::nonNull));
        context.setErrorInfoMap(errorInfoMap);
    }

    /**
     * 数据处理
     */
    @Override
    public InstContractImportDTO dataHandle(InstContractContext context) {
        // 构造合同对象
        ExcelUtil.ExcelParseInfo<InstContractFeePayoutDTO> excelFeeInfo = (ExcelUtil.ExcelParseInfo<InstContractFeePayoutDTO>) context.getFeeInfo();
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = context.getAccumulatedInfo();
        // 1. 机构合同解析结果去重（简单去重，后续可完善去重逻辑）
        List<InstContractFeePayoutDTO> instContractFeeList = excelFeeInfo.getDataList().stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InstContractFeePayoutDTO::getInstContractKey))), ArrayList::new));

        // 2. 将解析到的手续费信息和业务唯一键进行对应，然后根据业务唯一键分组
        // 2.1 将业务唯一键和 Excel 所在行进行关联
        Map<Integer, String> excelRowBizKeyMap = excelFeeInfo.getDataList().stream()
                .collect(Collectors.toMap(InstContractFeePayoutDTO::getExcelRowNo, InstContractFeePayoutDTO::getInstContractKey));
        // 2.2 根据所在行对关联手续费和业务唯一键
        excelAccumulatedInfo.getDataList().forEach(item -> item.setInstContractKey(excelRowBizKeyMap.get(item.getExcelRowNo())));
        // 2.3 手续费分组
        Map<String, List<AccumulatedInfoDTO>> accumulatedInfo = excelAccumulatedInfo.getDataList().stream()
                .collect(Collectors.groupingBy(AccumulatedInfoDTO::getInstContractKey));

        // 3. 机构合同填充手续费信息、充值信息，可在此处对信息做特殊处理
        instContractFeeList.forEach( fee -> {
            fee.setAccumulatedInfoList(accumulatedInfo.get(fee.getInstContractKey()));
            fee.setSettleInfo(new SettleInfoDTO());
            // 将支付币种填入充值信息
            fee.getSettleInfo().setTransactionCurrency(fee.getTransactionCurrency());
            // 固定值 路透
            fee.setTargetFxSource("Reuters");
        });

        // 根据 Excel 行排序并返回结果
        InstContractImportDTO response = new InstContractImportDTO();
        BeanUtils.copyProperties(context,response);
        response.setDataList(instContractFeeList.stream().sorted(Comparator.comparingInt(InstContractFeePayoutDTO::getExcelRowNo)).collect(Collectors.toList()));
        return response;
    }


    /**
     * @param context
     * @return
     */
    @Override
    public InstContractImportDTO errorHandle(InstContractContext context) {

        // 返回解析异常信息
        InstContractImportDTO response = new InstContractImportDTO();
        BeanUtils.copyProperties(context,response);
        return response;
    }

}
