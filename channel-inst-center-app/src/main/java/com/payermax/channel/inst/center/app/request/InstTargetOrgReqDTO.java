package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstTargetOrgReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 12:58
 */
@Data
public class InstTargetOrgReqDTO implements Serializable {
    private static final long serialVersionUID = 8518310614808853149L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "目标机构编码")
    private String targetOrgCode;

    @ApiModelProperty(notes = "目标机构名称")
    private String targetOrgName;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;
}
