package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountBucketService;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstSubFundsAccountBucketDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机构子级资金账号预申请账号Service
 *
 * <AUTHOR>
 * @date 2022/10/20 13:59
 */
@Service
public class InstSubFundsAccountBucketServiceImpl implements InstSubFundsAccountBucketService {

    @Autowired
    InstSubFundsAccountBucketDao instSubFundsAccountBucketDao;

    @Override
    public List<InstSubFundsAccountBucketEntity> queryByQueryEntityWithoutMerchantNo(InstSubFundsAccountBucketQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getAccountId()), "param accountId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getSubUseType()), "param subUseType is mandatory");

        return instSubFundsAccountBucketDao.selectByQueryEntityWithoutMerchantNo(queryEntity);
    }
    
    @Override
    public List<InstSubFundsAccountBucketEntity> queryBindMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getAccountId()), "param accountId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getMerchantNo()), "param merchantNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getSubUseType()), "param subUseType is mandatory");

        return instSubFundsAccountBucketDao.selectBindMerchantAccountByQueryEntity(queryEntity);
    }

    @Override
    public List<InstSubFundsAccountBucketEntity> queryBindSubMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getAccountId()), "param accountId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getMerchantNo()), "param merchantNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getSubMerchantNo()), "param subMerchantNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getSubUseType()), "param subUseType is mandatory");

        return instSubFundsAccountBucketDao.selectBindSubMerchantAccountByQueryEntity(queryEntity);
    }

    @Override
    public List<InstSubFundsAccountBucketEntity> queryUnboundMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getAccountId()), "param accountId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryEntity.getSubUseType()), "param subUseType is mandatory");

        return instSubFundsAccountBucketDao.selectUnboundMerchantAccountByQueryEntity(queryEntity);
    }

    @Override
    public int updateBySubAccountId(InstSubFundsAccountBucketEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(record.getSubAccountId()), "param subAccountId is mandatory");

        return instSubFundsAccountBucketDao.updateBySubAccountId(record);
    }

    @Override
    public int insert(InstSubFundsAccountBucketEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(record.getSubAccountId()), "param subAccountId is mandatory");

        return instSubFundsAccountBucketDao.insert(record);
    }

    @Override
    public InstSubFundsAccountBucketEntity queryBySubAccountId(String subAccountId) {
        Preconditions.checkArgument(StringUtils.isNotBlank(subAccountId), "param subAccountId is mandatory");
        return instSubFundsAccountBucketDao.selectByPrimaryKey(subAccountId);
    }

    @Override
    public int queryAssignableCountByAccountId(String accountId) {
        Preconditions.checkArgument(accountId != null, "param accountId is mandatory");
        
        return instSubFundsAccountBucketDao.selectAssignableCountByAccountId(accountId);
    }

}
