package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductDao;
import com.payermax.channel.inst.center.app.service.InstProductService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstProductServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 14:52
 */
@Service
public class InstProductServiceImpl implements InstProductService {

    @Autowired
    private InstProductDao instProductDao;

    @Override
    public int save(InstProductEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (StringUtils.isBlank(record.getProductCode())) {
            record.setProductCode("PD"+System.currentTimeMillis());
            // 新增
            result = instProductDao.insert(record);
        } else {
            // 更新
            result = instProductDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public List<InstProductEntity> getByEntity(InstProductEntity instProductEntity) {
        Preconditions.checkArgument(instProductEntity != null, "param instProductEntity is mandatory");
        List<InstProductEntity> instProductEntities = instProductDao.selectAll(instProductEntity);
        return instProductEntities;
    }

    @Override
    public List<InstProductEntity> getByProductCodes(List<String> productCodes) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(productCodes), "param productCodes is mandatory");
        List<InstProductEntity> instProductEntities = instProductDao.selectByProductCodes(productCodes);
        return instProductEntities;
    }

    @Override
    public List<InstProductEntity> getByInstIds(List<Long> instIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instIds), "param instIds is mandatory");
        List<InstProductEntity> instProductEntities = instProductDao.selectByInstIds(instIds);
        return instProductEntities;
    }

    @Override
    public int deleteByProductCode(String productCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        int result = instProductDao.deleteByProductCode(productCode);
        return result;
    }
}
