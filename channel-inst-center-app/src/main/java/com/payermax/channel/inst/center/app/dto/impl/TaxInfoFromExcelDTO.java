package com.payermax.channel.inst.center.app.dto.impl;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * 用于绕过配置文件中的配置，读取自定义表头
 */
@Data
public class TaxInfoFromExcelDTO {

    /**
     * 税种
     */
    @JSONField(name="Tax \n税种")
    private String taxType;

    /**
     * 税率
     */
    @JSONField(name="Tax Ratio\n税率")
    private String taxRatio;

    /**
     * 计税方式
     */
    @JSONField(name="Tax Calculation Formula\n计税方式")
    private String taxCalculateType;

    /**
     * 税额是否可抵
     */
    @JSONField(name="Is tax deductible\n税费是否可抵扣")
    private String deductible;

    /**
     * 税务备注
     */
    @JSONField(name="Tax Memo\n税务备注")
    private String taxMemo;
}