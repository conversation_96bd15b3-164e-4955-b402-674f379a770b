package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InstContractParseRequestDTO {

    /**
     * 机构编码
     * */
    @NotBlank(message = "[instCode] is mandatory")
    private final String instCode;

    /**
     * 合同主体
     * */
    @NotBlank(message = "[contractEntity] is mandatory")
    private final String contractEntity;

    /**
     * 合作模式
     * */
    @NotBlank(message = "[cooperationMode] is mandatory")
    private final String cooperationMode;

    /**
     * 机构产品类型
     * */
    @NotBlank(message = "[productType] is mandatory")
    private final String productType;

    /**
     * 合同生效时间
     * */
    @NotNull(message = "[effectiveTime] is mandatory")
    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private final Date effectiveTime;

    /**
     * 合同费用文件地址
     * */
    @NotBlank(message = "[contractFeeFile] is mandatory")
    private final String contractFeeFile;

    /**
     * 合同编号
     * */
    private final String contractNo;

    /**
     * 合同影印件地址
     * */
    private final List<String> photocopyUrl;

    /**
     * 备注
     * */
    private final String remark;
}
