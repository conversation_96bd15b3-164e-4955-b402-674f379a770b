package com.payermax.channel.inst.center.app.factory;

import com.payermax.channel.inst.center.app.assembler.domain.InstFundsAgreementAssembler;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.common.enums.fundsagreement.InstBizAgreementTypeEnum;
import com.payermax.channel.inst.center.common.enums.fundsagreement.InstFundsAgreementStatusEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBizAgreementRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFundsAgreementRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @DESC
 */
@Component
@AllArgsConstructor
@Slf4j
public class InstFundsAgreementFactory {


    private final InstFundsAgreementRepository fundsAgreementRepository;
    private final InstBizAgreementRepository bizAgreementRepository;
    private final InstFundsAgreementAssembler fundsAgreementAssembler;
    private final VoucherAdapter voucherAdapter;

    /**
     * 生成业务协议
     * @param context 业务协议
     * @return 业务协议
     */
    public InstBizAgreementPO composeBizAgreement(InstFundsAgreementContextDTO.BizAgreement context) {
        // 1. 初始化业务协议
        InstBizAgreementPO bizAgreement = fundsAgreementAssembler.bizAgreementContext2Po(context);
        // 1.2 判断业务协议是否存在，存在则使用，不存在返回新的业务协议
        InstBizAgreementPO existBizAgreement = bizAgreementRepository.getByUniqueKey(bizAgreement);
        if(Objects.nonNull(existBizAgreement)) {
            return existBizAgreement;
        } else {
            // 1.3 判断协议类型，校验机构合约号是否必填，外部协议时必填
            if(!InstBizAgreementTypeEnum.ENTITY_FX.equals(context.getType()) && !InstBizAgreementTypeEnum.ENTITY_PAYMENT.equals(context.getType())) {
                AssertUtil.isTrue(StringUtils.isNotBlank(context.getContractNo()), "ERROR","外部协议时机构合约号必填");
            }
            // 生成业务协议名称
            String bizAgreementName = String.format("%s_%s_%s", context.getCounter(),context.getInitiator(),context.getType());
            bizAgreement.setName(bizAgreementName);
            // 1.5 生成业务协议编号
            String bizAgreementCode = voucherAdapter.getBizAgreementCode();
            bizAgreement.setBizAgreementNo(bizAgreementCode);
            bizAgreement.setNewlyCreated(true);
            context.setBizAgreementNo(bizAgreementCode);
            return bizAgreement;
        }
    }

    /**
     * 生成资金协议
     * @param context 资金协议
     * @return 资金协议
     */
    public InstFundsAgreementPO composeFundsAgreement(InstFundsAgreementContextDTO.FundsAgreement context) {
        // 1. 初始化资金协议
        InstFundsAgreementPO fundsAgreement = fundsAgreementAssembler.fundsAgreementContext2Po(context);
        // 默认未生效
        fundsAgreement.setStatus(InstFundsAgreementStatusEnum.INVALID.name());
        // 1.2 判断资金协议是否存在，存在则使用，不存在返回新的资金协议
        Optional<InstFundsAgreementPO> existFundsAgreement = fundsAgreementRepository.getByUniqueKey(fundsAgreement);
        if(existFundsAgreement.isPresent()) {
            return existFundsAgreement.get();
        } else {
            String fundsAgreementCode = voucherAdapter.getFundsAgreementCode();
            fundsAgreement.setFundsAgreementNo(fundsAgreementCode);
            fundsAgreement.setNewlyCreated(true);
            context.setFundsAgreementNo(fundsAgreementCode);
            return fundsAgreement;
        }
    }

    /**
     * 生成清算规则
     * @param fundsAgreementNo 资金协议编号
     * @param context 清算规则
     * @return 清算规则PO
     */
    public InstFundsSettleRulePO composeFundsSettleRule(String fundsAgreementNo, InstFundsAgreementContextDTO.FundsSettleRule context) {
        // 1. 初始化清算规则，唯一性校验由数据库完成
        InstFundsSettleRulePO fundsSettleRule = fundsAgreementAssembler.fundsSettleRuleContext2Po(context);
        fundsSettleRule.setFundsAgreementNo(fundsAgreementNo);
        fundsSettleRule.setSettleRuleNo(voucherAdapter.getFundsSettleRuleCode());
        context.setSettleRuleNo(fundsSettleRule.getSettleRuleNo());
        context.setFundsAgreementNo(fundsAgreementNo);
        return fundsSettleRule;
    }
}
