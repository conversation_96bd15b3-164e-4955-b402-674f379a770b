package com.payermax.channel.inst.center.app.manage.contract.settle.impl;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.manage.contract.settle.AbstractDslResolver;
import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundDefiniteTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundSignTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;

/**
 * <AUTHOR> 2023/6/21  3:31 PM
 */
@Component
@Slf4j
public class ComplexCombineTypeDslResolver extends AbstractDslResolver {

    @Override
    public void fillDslTransactionStartCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        throw new IllegalArgumentException("settle cycle transaction start date dsl resolve fail");
    }

    @Override
    public void fillDslTransactionEndCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        throw new IllegalArgumentException("settle cycle transaction end date dsl resolve fail");
    }

    @Override
    public void fillDslSettleCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String billDateDSL = settleDate.getBillDate();

        DSLEntity billDateDslEntity = getDslEntity(billDateDSL);

        roundDSLEntity.setBillDateDsl(billDateDslEntity);
    }

    @Override
    public void fillDslPaymentCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String paymentDate = settleDate.getPaymentDate();

        DSLEntity paymentDateDslEntity = getDslEntity(paymentDate);

        roundDSLEntity.setPaymentDateDsl(paymentDateDslEntity);
    }


    @Override
    public void fillDslArriveCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String arriveDate = settleDate.getArriveDate();

        DSLEntity arriveDateDslEntity = getDslEntity(arriveDate);

        roundDSLEntity.setArrivedDateDsl(arriveDateDslEntity);
    }


    @Override
    public void fillDslExchangeCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String exchangeDate = settleDate.getExchangeDate();

        DSLEntity exchangeDateDslEntity = getDslEntity(exchangeDate);

        roundDSLEntity.setExchangeDateDsl(exchangeDateDslEntity);

    }

    @Override
    public Set<DSLTypeEnum> supportedScenarios() {
        return Sets.newHashSet(DSLTypeEnum.COMPLEX_COMBINE);
    }


    private DSLEntity getDslEntity(String billDateDSL) {
        Matcher matherByDSL = getMatherByDSL(billDateDSL);

        // 1、eg: M/W
        RoundTypeEnum relativeInfoType = getRelativeInfoType(matherByDSL);
        AssertUtil.isTrue(Objects.nonNull(relativeInfoType), "", "relativeInfoType is null");

        // 2、eg: +/-
        RoundSignTypeEnum relativeInfoSign = getRelativeInfoSign(matherByDSL);
        AssertUtil.notNull(relativeInfoSign, "", "relativeInfoSign is null");

        // 3、eg: 1/2/3...等
        String relativeInfo = getRelativeInfo(matherByDSL);

        // 4、eg: +/-
        RoundSignTypeEnum definiteInfoSign = getDefiniteInfoSign(matherByDSL);
        AssertUtil.notNull(definiteInfoSign, "", "definiteInfoSign is null");

        // 5、eg: 1等
        String definiteInfo = getDefiniteInfo(matherByDSL);

        DSLEntity billDateDslEntity = new DSLEntity();
        billDateDslEntity.setRoundRelativeType(relativeInfoType);
        billDateDslEntity.setRoundRelativeSignType(relativeInfoSign);
        billDateDslEntity.setRoundRelativeOffset(Integer.parseInt(StringUtils.replaceChars(relativeInfo, RoundDefiniteTypeEnum.WD.name(), "")));
        billDateDslEntity.setRoundDefiniteSignType(definiteInfoSign);
        billDateDslEntity.setRoundDefiniteOffset(Integer.parseInt(StringUtils.replaceChars(definiteInfo, RoundDefiniteTypeEnum.WD.name(), "")));

        // 填充是否考虑节假日信息
        fillIsConsiderHoliday(definiteInfo, billDateDslEntity);
        return billDateDslEntity;
    }
}