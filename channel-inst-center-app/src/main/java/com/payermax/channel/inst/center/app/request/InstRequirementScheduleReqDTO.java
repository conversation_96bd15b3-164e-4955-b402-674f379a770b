package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/6 23:17
 */
@Data
public class InstRequirementScheduleReqDTO implements Serializable {

    private static final long serialVersionUID = 3096483999997565812L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "集成需求单ID")
    private Long requirementOrderId;

    @ApiModelProperty(notes = "PD标识")
    private String pdId;

    @ApiModelProperty(notes = "PD名称")
    private String pdName;

    @ApiModelProperty(notes = "是否启动 Y:是,N:否")
    private String isStart;

    @ApiModelProperty(notes = "优先级 P0:高,P1:中,P2:低")
    private String priority;

    @ApiModelProperty(notes = "TB链接")
    private String tbUrl;

    @ApiModelProperty(notes = "计划投产日期")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date planReleaseDate;

    @ApiModelProperty(notes = "备注")
    private String remark;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "机构产品")
    private InstProductReqDTO instProduct;

}
