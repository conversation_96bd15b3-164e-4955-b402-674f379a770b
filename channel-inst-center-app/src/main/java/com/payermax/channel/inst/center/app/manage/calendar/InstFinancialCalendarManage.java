package com.payermax.channel.inst.center.app.manage.calendar;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarInitTemplateDTO;
import com.payermax.channel.inst.center.app.request.calendar.*;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.infra.ionia.fs.dto.UploadResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC 金融日历管理
 */
public interface InstFinancialCalendarManage{


    /**
     * 查询日历列表
     * @param request 请求参数
     * @return 日历列表
     */
    Page<InstFinancialCalendarDTO> queryCalendarList(InstFinancialCalendarRequest request);

    /**
     * 根据 ID 查询日历
     * @param request 请求参数
     * @return 日历信息
     */
    InstFinancialCalendarDTO queryCalendarById(InstFinancialCalendarRequest request);

    /**
     * 查询节假日列表
     * @param request 请求参数
     * @return 节假日列表
     */
    List<InstFinancialCalendarHoliday> queryHolidayList(InstFinancialCalendarHolidayRequest request);

    /**
     * 初始化日历模板
     * @param request 初始化参数
     * @return 初始化结果
     */
    InstFinancialCalendarInitTemplateDTO initCalendarTemplate(InstFinancialCalendarInitTemplateRequest request);


    /**
     * 发起保存日历流程
     * @param request 日历信息
     * @return 保存结果
     */
    Boolean startCalendarSaveProcess(InstFinancialCalendarSaveRequest request);

    /**
     * 发起更新日历流程
     * @param request 日历信息
     * @return 更新结果
     */
    Boolean startCalendarUpdateProcess(InstFinancialCalendarSaveRequest request);

    /**
     * 发起日历上下线流程
     * @param request 日历信息
     * @return 更新结果
     */
    Boolean startCalendarActivateProcess(InstFinancialCalendarActivateRequest request);

    /**
     * 日历导出
     * @param calendarList 日历列表
     * @return 文件地址
     */
    UploadResponse calendarExport(List<String> calendarList);
}
