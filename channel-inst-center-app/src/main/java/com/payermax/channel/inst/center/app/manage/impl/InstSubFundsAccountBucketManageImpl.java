package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.event.InstSubFunsAccountBucketsAlertEvent;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountBucketService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.StatusEnumYN;
import com.payermax.channel.inst.center.common.enums.instcenter.BucketApplyTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.IsSupportYNEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.infra.tool.lock.redisson.UredissonLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName InstSubFundsAccountBucketManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/10/24 16:39
 * @Version 1.0
 */
@Service
@Slf4j
public class InstSubFundsAccountBucketManageImpl implements InstSubFundsAccountBucketManage {

    @Autowired
    UredissonLock uredissonLock;
    @Autowired
    ReqDoAssembler reqDoAssembler;
    @Autowired
    DingAlertClient dingAlertClient;
    @Autowired
    TransactionTemplate transactionTemplate;
    @Autowired
    StateMachineExecutor stateMachineExecutor;
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;
    @Autowired
    InstSubFundsAccountBucketService instSubFundsAccountBucketService;

    @NacosValue(value = "${multilevel-cache.redis.prefix}", autoRefreshed = true)
    private String prefix;

    @NacosValue(value = "#{${inst.funds.account.pre-apply.ding.map}}", autoRefreshed = true)
    private HashMap<String, Integer> preApplyDingMap;

    @Override
    public InstSubFundsAccountEntity checkAndCreateSubAccountByPreApplyBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {
        // 机构账号是否支持预申请判断
        if (!StringUtils.equals(instFundsAccountEntity.getIsSupportPreApply(), IsSupportYNEnum.Y.getType())) {
            return null;
        }
        // 保证预申请账号顺序分配（如果已有申请在等待预申请账号，则不进行预申请账号分配）
        InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
        queryEntity.setAccountId(instFundsAccountEntity.getAccountId());
        queryEntity.setMerchantNo(instSubFundsAccountRequestDO.getMerchantNo());
        queryEntity.setStatus(SubAccountStatusEnum.APPLY.getStatus());
        queryEntity.setUtcCreate(instSubFundsAccountRequestDO.getUtcCreate());
        int applyCount = instSubFundsAccountService.queryCountByAccountId(queryEntity);
        if (applyCount > 0) {
            return null;
        }

        return this.createSubAccountByPreApplyBucketLock(instFundsAccountEntity, instSubFundsAccountRequestDO);
    }


    /**
     * 通过预申请账号创建子级资金账号
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountRequestDO
     * @return
     */
    public InstSubFundsAccountEntity createSubAccountByPreApplyBucketLock(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {
        long startTime = System.currentTimeMillis();
        // 分布式锁
        String key = prefix.concat(Thread.currentThread().getStackTrace()[1].getMethodName()).concat(":").concat(instFundsAccountEntity.getAccountId());
        boolean locked = uredissonLock.lock(key, 5, TimeUnit.SECONDS);
        if (locked) {
            try {
                return this.createSubAccountByPreApplyBucket(instFundsAccountEntity, instSubFundsAccountRequestDO);
            } catch (Exception e) {
                log.error("InstSubFundsAccountBucketManageImpl-createSubAccountByPreApplyBucketLock Exception:{}，costTime:{}", e, (System.currentTimeMillis() - startTime));
                throw e;
            } finally {
                uredissonLock.release(key);
            }
        }
        throw new BusinessException(ErrorCodeEnum.TEMPLATE_CREATE_SUB_ACCOUNT.getCode(), ErrorCodeEnum.TEMPLATE_CREATE_SUB_ACCOUNT.getMsg());
    }

    /**
     * 通过预申请账号创建子级资金账号
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountRequestDO
     * @return
     */
    public InstSubFundsAccountEntity createSubAccountByPreApplyBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {
        // 查询可用预申请账号
        InstSubFundsAccountBucketEntity bucketEntity = queryAvailablePreApplyWithMerchantNo(instFundsAccountEntity, instSubFundsAccountRequestDO, true);

        if (Objects.nonNull(bucketEntity)) {
            // 预申请子级账号-》更新到子级资金账号表
            InstSubFundsAccountEntity subAccountEntity = this.updateSubAccountAndSubAccountBucket(instFundsAccountEntity, instSubFundsAccountRequestDO, bucketEntity);
            return subAccountEntity;
        }
        return null;
    }


    @Override
    public int queryAssignableCountByAccountId(InstFundsAccountEntity instFundsAccountEntity) {
        // 机构账号必须支持预申请
        if (Objects.equals(instFundsAccountEntity.getIsSupportPreApply(), IsSupportYNEnum.N.getType())) {
            return 0;
        }
        
        return instSubFundsAccountBucketService.queryAssignableCountByAccountId(instFundsAccountEntity.getAccountId());
    }

    public InstSubFundsAccountBucketEntity queryAvailablePreApplyWithMerchantNo(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO, boolean pushEvents) {
        // 使用预申请账号创建子级资金账号
        InstSubFundsAccountBucketQueryEntity instSubFundsAccountBucketQueryEntity = new InstSubFundsAccountBucketQueryEntity();
        instSubFundsAccountBucketQueryEntity.setAccountId(instFundsAccountEntity.getAccountId());
        instSubFundsAccountBucketQueryEntity.setSubUseType(instSubFundsAccountRequestDO.getSubUseType());
        instSubFundsAccountBucketQueryEntity.setMerchantNo(instSubFundsAccountRequestDO.getMerchantNo());
        instSubFundsAccountBucketQueryEntity.setSubMerchantNo(instSubFundsAccountRequestDO.getSubMerchantNo());
        instSubFundsAccountBucketQueryEntity.setSubAccountStatus(SubAccountStatusEnum.ACTIVATED.getStatus());
        instSubFundsAccountBucketQueryEntity.setStatus(StatusEnumYN.Y.getType());

        InstSubFundsAccountBucketEntity bucketEntity = null;
        if (Objects.nonNull(instSubFundsAccountRequestDO.getSubMerchantNo())) {
            List<InstSubFundsAccountBucketEntity> bindSubMerchantAccountBucketList = instSubFundsAccountBucketService.queryBindSubMerchantAccountByQueryEntity(instSubFundsAccountBucketQueryEntity);
            // 如果预申请账号中有绑定子商户的账号，则优先使用此账号
            if (CollectionUtil.isNotEmpty(bindSubMerchantAccountBucketList)) {
                bucketEntity = bindSubMerchantAccountBucketList.get(0); // NO_CHECK
            }
        } else if (Objects.nonNull(instSubFundsAccountRequestDO.getMerchantNo())) {
            List<InstSubFundsAccountBucketEntity> bindMerchantAccountBucketList = instSubFundsAccountBucketService.queryBindMerchantAccountByQueryEntity(instSubFundsAccountBucketQueryEntity);
            // 如果预申请账号中有绑定商户的账号，则优先使用此账号
            if (CollectionUtil.isNotEmpty(bindMerchantAccountBucketList)) {
                bucketEntity = bindMerchantAccountBucketList.get(0); // NO_CHECK
            }
        }

        // 如果预申请账号中没有绑定商户或子商户的账号，则查询未绑定商户的预申请账号进行分配
        if (Objects.isNull(bucketEntity)) {
            List<InstSubFundsAccountBucketEntity> unboundMerchantAccountBucketList = instSubFundsAccountBucketService.queryUnboundMerchantAccountByQueryEntity(instSubFundsAccountBucketQueryEntity);
            if (CollectionUtil.isNotEmpty(unboundMerchantAccountBucketList)) {
                bucketEntity = unboundMerchantAccountBucketList.get(0); // NO_CHECK
                // 未绑定商户的预申请账号，是否进行剩余告警
                if (pushEvents) {
                    applicationEventPublisher.publishEvent(new InstSubFunsAccountBucketsAlertEvent(this, instFundsAccountEntity, unboundMerchantAccountBucketList, instSubFundsAccountRequestDO));
                }
            }
        }

        return bucketEntity;
    }

    public InstSubFundsAccountBucketEntity queryAvailablePreApplyWithoutMerchantNo(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {
        InstSubFundsAccountBucketQueryEntity instSubFundsAccountBucketQueryEntity = new InstSubFundsAccountBucketQueryEntity();
        instSubFundsAccountBucketQueryEntity.setAccountId(instFundsAccountEntity.getAccountId());
        instSubFundsAccountBucketQueryEntity.setSubUseType(instSubFundsAccountRequestDO.getSubUseType());
        instSubFundsAccountBucketQueryEntity.setSubAccountStatus(SubAccountStatusEnum.ACTIVATED.getStatus());
        instSubFundsAccountBucketQueryEntity.setStatus(StatusEnumYN.Y.getType());
        
        List<InstSubFundsAccountBucketEntity> bucketEntityList = instSubFundsAccountBucketService.queryByQueryEntityWithoutMerchantNo(instSubFundsAccountBucketQueryEntity);
        if (CollectionUtil.isNotEmpty(bucketEntityList)){
            return bucketEntityList.get(0); // NO_CHECK
        }
        return null;
    }

    /**
     * 通过预申请补偿子级资金账号并更新预申请表
     *
     * @param instSubFundsAccountRequestDO
     * @param bucketEntity
     */
    private InstSubFundsAccountEntity updateSubAccountAndSubAccountBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO, InstSubFundsAccountBucketEntity bucketEntity) {
        InstSubFundsAccountEntity subAccount = reqDoAssembler.toInstSubFundsAccountEntity(instSubFundsAccountRequestDO);
        {
            subAccount.setSubAccountNo(bucketEntity.getSubAccountNo());
            subAccount.setBSubAccountNo(bucketEntity.getBSubAccountNo());
            subAccount.setBucketId(bucketEntity.getBucketId());
            if (StringUtil.isNotEmpty(bucketEntity.getSubAccountName())){
                subAccount.setSubAccountName(bucketEntity.getSubAccountName());
            }
            subAccount.setAccountJson(bucketEntity.getAccountJson());
            subAccount.setNumberSegmentNo(bucketEntity.getNumberSegmentNo());
            subAccount.setNumberSegmentId(bucketEntity.getNumberSegmentId());
        }
        StateRequest stateRequest = new StateRequest(instSubFundsAccountRequestDO.getStatus(), SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), subAccount);
        subAccount.setStatus(SubAccountStatusEnum.ACTIVATED.getStatus());

        InstSubFundsAccountEntity subFundsAccountEntity = transactionTemplate.execute(status -> {
            try {
                // 更新子级账号记录-》已激活
                stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
                // 更新原预申请表为已分配
                bucketEntity.setStatus(StatusEnumYN.N.getType());
                instSubFundsAccountBucketService.updateBySubAccountId(bucketEntity);
                return subAccount;
            } catch (BusinessException e) {
                status.setRollbackOnly();
                log.info("InstSubFundsAccountBucketManageImpl-updateSubAccountAndSubAccountBucket InstSubFundsAccountRequestDO:{},InstSubFundsAccountBucketEntity:{}, exception {}", JSON.toJSONString(instSubFundsAccountRequestDO), JSON.toJSONString(bucketEntity), e);
                throw e;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.info("InstSubFundsAccountBucketManageImpl-updateSubAccountAndSubAccountBucket InstSubFundsAccountRequestDO:{},InstSubFundsAccountBucketEntity:{}, exception {}", JSON.toJSONString(instSubFundsAccountRequestDO), JSON.toJSONString(bucketEntity), e);
                throw new BusinessException(ErrorCodeEnum.UPDATE_SUB_ACCOUNT_PRE_APPLY_FAIL.getCode(), ErrorCodeEnum.UPDATE_SUB_ACCOUNT_PRE_APPLY_FAIL.getMsg());
            }
        });
        return subFundsAccountEntity;
    }

    /**
     * 创建机构子级账号预申请记录
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountEntity
     */
    @Override
    public InstSubFundsAccountBucketEntity buildAndCreatePreApplySubAccountBucket(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity) {
        // 机构账号必须支持预申请
        if (Objects.equals(instFundsAccountEntity.getIsSupportPreApply(), IsSupportYNEnum.N.getType())) {
            return null;
        }

        // 检查是否重复创建
        InstSubFundsAccountBucketEntity bucketEntity = instSubFundsAccountBucketService.queryBySubAccountId(instSubFundsAccountEntity.getSubAccountId());
        if (Objects.isNull(bucketEntity)) {
            // todo 商户号和子商户号在请求明确表示不需要强绑定时置为null
            InstSubFundsAccountBucketEntity subAccountBucket = reqDoAssembler.toInstSubFundsAccountBucketEntity(instSubFundsAccountEntity);
            subAccountBucket.setApplyType(BucketApplyTypeEnum.MERCHANT_APPLY.name());
            instSubFundsAccountBucketService.insert(subAccountBucket);
            bucketEntity = subAccountBucket;
        }
        return bucketEntity;
    }

    /**
     * 监听预申请账号表数据
     */
    @Override
    @Async(CommonConstants.ASYNC_TASK_EXECUTOR_NAME)
    @EventListener
    public void onApplicationEvent(InstSubFunsAccountBucketsAlertEvent event) {
        InstFundsAccountEntity instFundsAccountEntity = event.getInstFundsAccountEntity();
        InstSubFundsAccountRequestDO instSubFundsAccountRequestDO = event.getInstSubFundsAccountRequestDO();
        // 预申请是否有告警配置
        if (preApplyDingMap.containsKey(instFundsAccountEntity.getAccountId())) {
            // 剩余是否达到告警限制
            List<InstSubFundsAccountBucketEntity> list = event.getAccountBucketEntityList();
            if (CollectionUtil.isEmpty(list) || list.size() < preApplyDingMap.get(instFundsAccountEntity.getAccountId())) {
                // 发送钉钉告警
                String title = "机构账号【预申请】即将用尽";
                String message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 国家：%s\n- 币种：%s\n- 商户号：%s\n- 子商户号：%s\n- 使用类型：%s\n- 剩余预申请账号：%s个\n"
                        , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instFundsAccountEntity.getCountry(), instFundsAccountEntity.getCurrency(), instSubFundsAccountRequestDO.getMerchantNo(), instSubFundsAccountRequestDO.getSubMerchantNo(), instFundsAccountEntity.getUseType(), list.size());
                dingAlertClient.sendMsgForExceptionGroupSubAccount(CommonConstants.PUSH_SUB_ACCOUNT_HANDLE_GROUP, title, message);
            }
        }
    }
}
