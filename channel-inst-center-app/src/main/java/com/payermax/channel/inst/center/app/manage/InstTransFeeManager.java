package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstTransFeeReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductTransFeeVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 23:19
 */
public interface InstTransFeeManager {
    /**
     * 保存产品费用信息
     *
     * @param productFeeReqDTOList
     * @return
     */
    int saveAll(InstTransFeeReqDTO productFeeReqDTOList);

    /**
     * 查询交易费用
     *
     * @param transFeeReqDTO
     * @return
     */
    List<InstProductTransFeeVO> queryAll(InstTransFeeReqDTO transFeeReqDTO);

    /**
     * 删除已配置交易费用
     * @param transFeeReqDTO
     * @return
     */
    int delete(InstTransFeeReqDTO transFeeReqDTO);
}
