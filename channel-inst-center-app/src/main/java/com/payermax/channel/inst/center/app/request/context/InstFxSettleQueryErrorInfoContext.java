package com.payermax.channel.inst.center.app.request.context;

import com.payermax.channel.inst.center.app.request.InstFxSettleReqDTO;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName InstFxSettleQueryErrorInfoContext
 * @Description
 * <AUTHOR>
 * @Date 2023/4/26 21:25
 */
@Data
@Builder
@Accessors(chain = true)
public class InstFxSettleQueryErrorInfoContext {
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    private String errMsg;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 结算换汇查询入参
     */
    private InstFxSettleReqDTO instFxSettleReqDTO;
    /**
     * 机构id
     */
    private Long instId;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品能力编码
     */
    private String capabilityCode;
}
