package com.payermax.channel.inst.center.app.manage.contract;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.model.contract.ContractContentEnum;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import com.payermax.channel.inst.center.app.model.contract.InstContractFactory;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.vo.contract.ContractProductBusinessKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/9 6:12 PM
 *
 * 机构交易成本校验
 **/
@Slf4j
@Component
public class ContractTradeCostProcessor extends AbstractContractTermsProcessor {

    @Resource
    private InstContractFactory instContractFactory;

    @Override
    public void assembleContractItems(ContractProcessByExcelContext context) {
        if (MapUtils.isEmpty(context.getStandardizedProductGroupFeeMap())
                && MapUtils.isEmpty(context.getUnStandardizedProductGroupFeeMap())) {
            log.warn("instCode:{} contractEntity:{} businessType:{} 费用信息为空，请关注！",
                    context.getInstCode(), context.getContractEntity(), context.getBusinessTypeEnum());
            return;
        }

        /**
         * 遍历所有机构原始产品，处理对应产品的成本条款
         */
        context.getStandardizedProductGroupFeeMap().entrySet().stream().forEach(entry ->
            context.addContractFeeItemsForInstProduct(entry.getKey() , processSingleBusinessUnit(entry))
        );
        context.getUnStandardizedProductGroupFeeMap().entrySet().stream().forEach(entry ->
            context.addContractFeeItemsForInstProduct(entry.getKey() , processSingleBusinessUnit(entry))
        );
    }


    private List<InstContractFeeItem> processSingleBusinessUnit(Map.Entry<ContractProductBusinessKey, List<InstContractFeeRowItemDTO>> entry) {

        /**
         * a. 获取excel 中原始条款信息
         */
        List<InstContractFeeRowItemDTO> excelFeeRowList = entry.getValue();

        /**
         * b. 构造处理后的费用条款领域实体列表
         */
        List<InstContractFeeItem> feeItemList = new ArrayList<>();

        /**
         * c. 遍历原始条款信息，构造费用条款领域实体
         *    businessKeyItemMap：
         *      -- key   见getContractItemBusinessKey()，
         *      -- value 列表存在多个对象，只可能是阶梯费用场景；不是阶梯费用，说明相同的业务key重复出现，需要报错
         */
        Map<String, List<InstContractFeeRowItemDTO>> businessKeyItemMap =
                excelFeeRowList.stream().collect(Collectors.groupingBy(InstContractFeeRowItemDTO::getContractItemBusinessKey));

        businessKeyItemMap.entrySet().stream().forEach(feeItemEntry -> {
            List<InstContractFeeRowItemDTO> feeItemRowList = feeItemEntry.getValue();

            // 此处只负责构造，业务校验在构造完成后进行
            InstContractFeeItem feeItem;
            if (CollectionUtils.size(feeItemRowList) == 1) {
                /**
                 * c.1. 单笔计算税费
                 */
                InstContractFeeRowItemDTO dtoLine = feeItemRowList.get(0); // CHECKED
                feeItem = instContractFactory.composePayInContractSingleFeeItem(dtoLine);
            } else {
                /**
                 * c.2. 阶梯计算税费
                 */
                feeItem = instContractFactory.composePayInContractStepFeeItem(feeItemRowList);
            }
            // 设置在excel中所属行号等 非业务信息
            feeItem.recordExcelRowList(feeItemRowList.stream().map(InstContractFeeRowItemDTO::getExcelRowNo).collect(Collectors.toList()));

            feeItemList.add(feeItem);
        });

        return feeItemList;
    }


    @Override
    public void businessValidateOnContractItems(ContractProcessByExcelContext context) {
        List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItemList = new ArrayList<>();

        context.getInstContractFeeMap().entrySet().forEach(entry -> {
            ContractProductBusinessKey businessKey = entry.getKey();
            // 标准化产品费用条款，进行业务逻辑自检，判断在业务逻辑上是否有异常
            entry.getValue().forEach(feeItem -> {
                List<ExportErrorInfo.ExportErrorInfoItem> errorItemList = feeItem.businessValidateOnContractItem();

                // 在错误信息上增加合同条款前缀，方便定位错误行
                String infoPrefix = String.format("instProductName: %s, paymentMethodType: %s, targetOrg: %s , cardOrg: %s ,ccy : %s",
                        businessKey.getInstProductName(), businessKey.getPaymentMethodType(), businessKey.getTargetOrg(), businessKey.getCardOrg(), feeItem.getPayCurrency());
                errorItemList.forEach(errorItem -> errorItem.setErrorMsg(infoPrefix.concat(errorItem.getErrorMsg())));

                errorInfoItemList.addAll(errorItemList);
            });
        });

        if (CollectionUtils.isNotEmpty(errorInfoItemList)) {
            context.setFeeErrorInfo(new ExportErrorInfo(true, null, errorInfoItemList));
        }
    }


    @Override
    public Set<ContractContentEnum> supportedScenarios() {
        return Sets.newHashSet(ContractContentEnum.INST_FEE_TAX_PAYIN,ContractContentEnum.INST_FEE_TAX_PAYOUT);
    }
}
