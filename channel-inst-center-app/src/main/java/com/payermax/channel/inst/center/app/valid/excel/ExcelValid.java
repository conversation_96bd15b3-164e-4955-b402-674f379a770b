package com.payermax.channel.inst.center.app.valid.excel;

import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.app.dto.common.ValidatorUtils;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * ExcelValid
 *
 * <AUTHOR>
 * @desc
 */
public interface ExcelValid<T extends ExcelUtil.BaseExcelRow, F> {

    /**
     * excel数据校验
     * @param excelParseInfo
     * @return
     */
    default Result<T> valid(ExcelUtil.ExcelParseInfo<T> excelParseInfo) {
        Result<T> result = new Result<T>().setSuccess(Boolean.TRUE).setData(excelParseInfo);
        if(Objects.isNull(excelParseInfo) || CollectionUtils.isEmpty(excelParseInfo.getDataList())) {
            return result;
        }
        ExportErrorInfo exportErrorInfo = ValidatorUtils.validateAll(excelParseInfo);
        if(exportErrorInfo.isHasErrors()) {
            result.setSuccess(Boolean.FALSE).setErrorInfo(exportErrorInfo);
        }
        return result;
    }

    /**
     * excel数据+表单数据校验
     * @param excelParseInfo
     * @param formData
     * @return
     */
    default Result<T> valid(ExcelUtil.ExcelParseInfo<T> excelParseInfo, F formData) {
        return valid(excelParseInfo);
    }

    @Data
    @Accessors(chain = true)
    class Result<T extends ExcelUtil.BaseExcelRow> {

        private boolean success;

        private ExportErrorInfo errorInfo;

        private ExcelUtil.ExcelParseInfo<T> data;
    }
}
