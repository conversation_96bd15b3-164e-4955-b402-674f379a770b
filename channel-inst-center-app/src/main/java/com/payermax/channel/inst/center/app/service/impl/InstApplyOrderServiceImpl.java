package com.payermax.channel.inst.center.app.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstApplyOrderDao;
import com.payermax.channel.inst.center.app.service.InstApplyOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstApplyOrderServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 23:06
 * @Version 1.0
 */
@Service
public class InstApplyOrderServiceImpl extends ServiceImpl<InstApplyOrderDao, InstApplyOrderEntity> implements InstApplyOrderService {

    @Autowired
    private InstApplyOrderDao instApplyOrderDao;

    @Override
    public int saveEntity(InstApplyOrderEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getApplyNo() == null) {
            record.setApplyNo("CH" + System.currentTimeMillis());
            // 新增
            result = instApplyOrderDao.insert(record);
        } else {
            // 更新
            result = instApplyOrderDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public int updateStageStatus(String applyNo, ApplyStageEnum applyStageEnum, String status) {
        Preconditions.checkArgument(StringUtils.isNotBlank(applyNo), "param applyNo is mandatory");
        Preconditions.checkArgument(applyStageEnum != null, "param applyStageEnum is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(status), "param status is mandatory");

        boolean isFinalStatus = applyStageEnum.isFinalStatus(status);
        String completeTime = null;
        if (isFinalStatus) {
            completeTime = DateUtil.now();
        }
        return instApplyOrderDao.updateStageStatus(applyNo, applyStageEnum.name(), status, completeTime);
    }

    @Override
    public IPage<ApplyOrderQueryEntity> queryPageList(ApplyOrderQueryEntity applyOrderQueryEntity, Long pageNum, Long pageSize) {
        Page<ApplyOrderQueryEntity> page = new Page<>(pageNum, pageSize);
        return instApplyOrderDao.selectApplyOrderList(page, applyOrderQueryEntity);
    }

    @Override
    public List<ApplyOrderQueryEntity> queryList(ApplyOrderQueryEntity applyOrderQueryEntity) {
        return instApplyOrderDao.selectApplyOrderList(applyOrderQueryEntity);
    }

    @Override
    public List<InstApplyOrderEntity> queryList(List<Long> instIds) {
        return instApplyOrderDao.selectApplyOrderListByInstIds(instIds);
    }

    @Override
    public ApplyOrderQueryEntity queryByApplyNo(String applyNo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(applyNo), "param applyNo is mandatory");
        ApplyOrderQueryEntity applyOrderQueryEntity = new ApplyOrderQueryEntity();
        applyOrderQueryEntity.setApplyNo(applyNo);
        List<ApplyOrderQueryEntity> applyOrderQueryEntities = instApplyOrderDao.selectApplyOrderList(applyOrderQueryEntity);
        if (CollectionUtils.isNotEmpty(applyOrderQueryEntities)) {
            return applyOrderQueryEntities.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }
}
