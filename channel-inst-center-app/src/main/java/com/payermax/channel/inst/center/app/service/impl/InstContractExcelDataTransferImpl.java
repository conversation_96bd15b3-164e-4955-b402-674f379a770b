package com.payermax.channel.inst.center.app.service.impl;

import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.config.DynamicExcelColumnsConfig;
import com.payermax.channel.inst.center.app.config.InstContractFeeParseConfig;
import com.payermax.channel.inst.center.app.config.InstContractSettleDateParseConfig;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.dto.impl.TaxInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.TaxInfoFromExcelDTO;
import com.payermax.channel.inst.center.app.service.InstContractExcelDataTransfer;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * InstContractExcelDataTransferImpl
 *
 * <AUTHOR>
 * @desc
 */
@Slf4j
@Service
public class InstContractExcelDataTransferImpl implements InstContractExcelDataTransfer {

    @Resource
    private InstContractFeeParseConfig instContractFeeParseConfig;

    @Resource
    private InstContractSettleDateParseConfig instContractSettleDateParseConfig;

    @Override
    public void instContractFeeRowItemDtoTransfer(InstContractFeeRowItemDTO instContractFeeRowItemDTO, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList) {
        if(Objects.isNull(instContractFeeRowItemDTO) || CollectionUtils.isEmpty(excelCellDataList)) {
            return;
        }
        instContractFeeRowItemDTO.setCardOrg(cardOrgTransfer(instContractFeeRowItemDTO.getCardOrg()));
        List<Map<String, Object>> dataList = getDefineParseDataList(instContractFeeRowItemDTO.getLang(), excelCellDataList, instContractFeeParseConfig);
        instContractFeeRowItemDTO.setTaxInfoList(JSON.parseArray(JSON.toJSONString(dataList), ExcelUtil.ExcelLangRow.langClassImpl(InstContractFeeRowItemDTO.TaxInfo.class, instContractFeeRowItemDTO.getLang())));
    }

    @Override
    public List<TaxInfoDTO> taxInfoListTransfer(LangEnum langEnum, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList){
        if(CollectionUtils.isEmpty(excelCellDataList)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> dataList = getDefineParseDataList(langEnum, excelCellDataList, instContractFeeParseConfig);
        List<TaxInfoFromExcelDTO> taxInfoFromExcelList = JSON.parseArray(JSON.toJSONString(dataList), TaxInfoFromExcelDTO.class);
        return taxInfoFromExcelList.stream().map(item -> {
            TaxInfoDTO taxInfo = new TaxInfoDTO();
            BeanUtils.copyProperties(item, taxInfo);
            return taxInfo;
        }).collect(Collectors.toList());
    }

    private String cardOrgTransfer(String cardOrg) {
        if(CommonConstants.CARD_PAY_CODE.equalsIgnoreCase(cardOrg)) {
            return CommonConstants.CARD_PAY_CODE;
        }
        return cardOrg;
    }

    private List<Map<String, Object>> getDefineParseDataList(LangEnum langEnum, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList, DynamicExcelColumnsConfig dynamicExcelSeparatorConfig) {
        boolean isStart = false;
        boolean isEnd = false;
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> dataMap = null;
        for(ExcelUtil.DefineExcelParser.ExcelCellData excelCellData : excelCellDataList) {
            if(excelCellData.getColumnName().equals(dynamicExcelSeparatorConfig.getStartSeparator())) {
                dataMap = new HashMap<>(8);
                isStart = true;
                isEnd = false;
                continue;
            }
            if(excelCellData.getColumnName().equals(dynamicExcelSeparatorConfig.getEndSeparator())) {
                if (MapUtils.isNotEmpty(dataMap)) {
                    dataList.add(dataMap);
                }

                isEnd = true;
                isStart = false;
                continue;
            }
            if(!isStart) {
               continue;
            }
            String fieldName = getFieldName(excelCellData.getColumnName(), langEnum, dynamicExcelSeparatorConfig);
            if(StringUtil.isEmpty(fieldName)) {
                continue;
            }
            Cell cellValue = excelCellData.getCellValue();
            if(Objects.isNull(cellValue) || StringUtil.isEmpty(((CellData) cellValue).getStringValue())) {
                continue;
            }
            dataMap.put(fieldName, ((CellData) cellValue).getStringValue());
        }
        if(isStart && !isEnd) {
            throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "dynamic columns format error");
        }
        return dataList;
    }

    @Override
    public void instContractSettleRowItemDtoTransfer(InstContractSettleRowItemDTO instContractSettleRowItemDTO, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList) {
        if(Objects.isNull(instContractSettleRowItemDTO) || CollectionUtils.isEmpty(excelCellDataList)) {
            return;
        }
        instContractSettleRowItemDTO.setCardOrg(cardOrgTransfer(instContractSettleRowItemDTO.getCardOrg()));
        List<Map<String, Object>> dataList = getDefineParseDataList(instContractSettleRowItemDTO.getLang(), excelCellDataList, instContractSettleDateParseConfig);
        instContractSettleRowItemDTO.setSettleDateInfoList(JSON.parseArray(JSON.toJSONString(dataList), ExcelUtil.ExcelLangRow.langClassImpl(InstContractSettleRowItemDTO.SettleDateInfo.class, instContractSettleRowItemDTO.getLang())));
    }

    private String getFieldName(String columnName, LangEnum langEnum, DynamicExcelColumnsConfig dynamicExcelColumnsConfig) {
        Map<String, String> fieldMapping = dynamicExcelColumnsConfig.getFieldMapping().get(LangEnum.code(langEnum));
        if(CollectionUtils.isEmpty(fieldMapping)) {
            return columnName;
        }
        for(Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            if(columnName.equals(entry.getValue())) {
                return entry.getKey();
            }
        }
        return columnName;
    }
}
