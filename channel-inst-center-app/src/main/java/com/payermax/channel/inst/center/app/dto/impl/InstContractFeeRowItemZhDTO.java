package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.valid.annon.impl.CompanyEntityValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.CountryValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.InstCodeValid;
import com.payermax.channel.inst.center.common.constrains.*;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.*;
import com.payermax.channel.inst.center.domain.enums.contract.management.*;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * InstContractFeeRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class InstContractFeeRowItemZhDTO extends InstContractFeeRowItemDTO {

    @ExcelProperty({ "", "录入" })
    private String enteredBy;

    @ExcelProperty({ "", "渠道签约主体全名" })
    private String instLegalName;

    @NotEmpty(message = "我方签约主体编码不能为空")
    @InterfaceAnnotation(clazz = CompanyEntityValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "我方签约主体编码" })
    private String contractEntity;

    @NotEmpty(message = "机构简称不能为空")
    @InterfaceAnnotation(clazz = InstCodeValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "机构简称" })
    private String instCode;

    @NotEmpty(message = "机构产品类型不能为空")
    @EnumAnnotation(values = ContractBusinessTypeEnum.class)
    @ExcelProperty({ "", "机构产品类型" })
    private String instProductType;

    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty({ "", "机构产品名称" })
    private String instProductName;

    @ExcelProperty({ "", "支付方式类型" })
    private String paymentMethodType;

    @ExcelProperty({ "", "目标机构" })
    private String targetOrg;

    @ExcelProperty({ "", "卡组" })
    private String cardOrg;

    @NotEmpty(message = "支付币种不能为空")
    @ExcelProperty({ "", "支付币种" })
    private String payCurrency;

    @ExcelProperty({ "", "卡片类型" })
    private String cardType;

    @InterfaceAnnotation(clazz = CountryValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "发卡国家" })
    private String cardIssueCountry;

    @InterfaceAnnotation(clazz = CountryValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "交易国家" })
    private String transactionCountry;

    @ExcelProperty({ "", "机构MID" })
    private String originMid;

    @ExcelProperty({ "", "机构SubMID" })
    private String subMid;

    @ExcelProperty({ "", "渠道商户号" })
    private String channelMerchantNo;

    @ExcelProperty({ "", "二级商户号" })
    private String subMerchantNo;

    @ExcelProperty({ "", "机构MCC" })
    private String originMcc;

    @ExcelProperty({ "", "标准化MCC" })
    private String standardMcc;

    @ExcelProperty({ "", "清算网络" })
    private String clearNetwork;


    //----- 大阶梯相关
    @EnumAnnotation(values = AccumulationCycle.class)
    @ExcelProperty({ "", "累计周期" })
    private String accumulationCycle;

    @EnumAnnotation(values = AccumulationType.class)
    @ExcelProperty({ "", "累计类型" })
    private String accumulationType;

    @EnumAnnotation(values = AccumulationMethod.class)
    @ExcelProperty({ "", "累计生效方法" })
    private String accumulationMethod;

    @EnumAnnotation(values = AccumulationRange.class)
    @ExcelProperty({ "", "累计生效范围" })
    private String accumulationRange;

    @EnumAnnotation(values = AccumulationDeductTime.class)
    @ExcelProperty({ "", "累计收费时机" })
    private String accumulationDeductTime;

    @ExcelProperty({ "", "是否参与累计" })
    private String accumulationJoin;

    @ExcelProperty({ "", "累计规则key" })
    private String accumulationKey;

    @ExcelProperty({ "", "累计规则模式" })
    private String accumulationMode;
    //----- 大阶梯相关

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "阶梯范围终点（不含）" })
    private String stepUpper;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "阶梯范围起点（含）" })
    private String stepLower;

    @CurrencyAnnotation
    @ExcelProperty({ "", "阶梯币种" })
    private String stepCurrency;

    @NotEmpty(message = "计费方式不能为空")
    @EnumAnnotation(values = FeeCalculateTypeEnum.class)
    @ExcelProperty({ "", "计费方式" })
    private String calculateType;

    @EnumAnnotation(values = FeeCalculateTimingEnum.class)
    @ExcelProperty({ "", "算费时点" })
    private String feeCalculateTime;

    @NotEmpty(message = "扣费币种不能为空")
    @CurrencyAnnotation
    @ExcelProperty({ "", "扣费币种" })
    private String chargeCurrency;

    @NotEmpty(message = "保留小数位数不能为空")
    @NumberAnnotation()
    @ExcelProperty({ "", "保留小数位数" })
    private String roundingScale;

    @NotEmpty(message = "保留小数算法不能为空")
    @EnumAnnotation(values = RoundingModeEnum.class)
    @ExcelProperty({ "", "保留小数算法" })
    private String roundingMode;

    @EnumAnnotation(values = FeeCalculateBaseModeEnum.class)
    @ExcelProperty({ "", "计费基准" })
    private String feeBasementMode;

    @PercentAnnotation
    @ExcelProperty({ "", "单笔比例" })
    private String singleStrokeRate;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "最低收费" })
    private String minCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "封顶收费" })
    private String maxCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "单笔固定金额" })
    private String singleFixedAmount;

    @CurrencyAnnotation
    @ExcelProperty({ "", "单笔固定币种" })
    private String singleFixedCurrency;

    @PercentAnnotation
    @ExcelProperty({ "", "退款单笔比例" })
    private String refundSingleStrokeRate;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "退款单笔固定金额" })
    private String refundSingleFixedAmount;

    @CurrencyAnnotation
    @ExcelProperty({ "", "退款单笔固定币种" })
    private String refundSingleFixedCurrency;

    @EnumAnnotation(values = FeeCalculateTypeEnum.class)
    @ExcelProperty({ "", "退款计费方式" })
    private String refundCalculateType;

    @EnumAnnotation(values = YesOrNoEnum.class)
    @ExcelProperty({ "", "退款时是否退费" })
    private String refundOrNot;

    @EnumAnnotation(values = CurrencyExchangeTimingEnum.class)
    @ExcelProperty({ "", "换汇时点" })
    private String currencyExchangeTime;

    @PercentAnnotation
    @ExcelProperty({ "", "FX加点" })
    private String fxSpread;

    @Data
    public static class TaxInfoZhImpl extends TaxInfo {

        /**
         * 税种
         */
        @NotEmpty(message = "税种不能为空")
        private String taxType;

        /**
         * 税率
         */
        @NotEmpty(message = "税率不能为空")
        private String taxRate;

        /**
         * 计税方式
         */
        @NotEmpty(message = "计税方式不能为空")
        @EnumAnnotation(values = TaxCalculateFormulaEnum.class)
        private String taxCalculateType;

        /**
         * 税额是否可抵
         */
        @NotEmpty(message = "是否可抵不能为空")
        @EnumAnnotation(values = YesOrNoEnum.class)
        private String deductible;
    }
}
