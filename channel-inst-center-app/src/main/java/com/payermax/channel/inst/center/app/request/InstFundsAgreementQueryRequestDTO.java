package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @DESC
 */
@Data
public class InstFundsAgreementQueryRequestDTO {


    @ApiModelProperty(notes = "资金协议类型")
    private String fundsAgreementType;

    @ApiModelProperty(notes = "资金协议名称")
    private String fundsAgreementName;

    @ApiModelProperty(notes = "协议发起方")
    private String initiator;

    @ApiModelProperty(notes = "协议对手方")
    private String counter;

    @ApiModelProperty(notes = "清算币种")
    private String clearingCurrency;

    @ApiModelProperty(notes = "资金协议状态")
    private String fundsAgreementStatus;
}
