package com.payermax.channel.inst.center.app.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeeRowItemZhDTO;
import com.payermax.channel.inst.center.app.service.InstContractExcelDataTransfer;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ApplicationUtils;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * InstContractFeeRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public abstract class InstContractFeeRowItemDTO extends AbstractInstContractRowItemDTO implements ExcelUtil.DefineExcelParser, ExcelUtil.ExcelLangRow {

    @ExcelIgnore
    private List<TaxInfo> taxInfoList;

    public static abstract class TaxInfo implements ExcelUtil.ExcelLangRow {

        /**
         * 税种
         *
         * @return
         */
        public abstract String getTaxType();

        /**
         * 税率
         *
         * @return
         */
        public abstract String getTaxRate();

        /**
         * 计税方式
         *
         * @return
         */
        public abstract String getTaxCalculateType();

        /**
         * 税额是否可抵
         *
         * @return
         */
        public abstract String getDeductible();

        @Override
        public Class<? extends ExcelUtil.ExcelLangRow> langClassImpl(LangEnum langEnum) {
            return InstContractFeeRowItemZhDTO.TaxInfoZhImpl.class;
        }
    }

    /**
     * 支付币种
     *
     * @return 支付币种
     */
    public abstract String getPayCurrency();

    /**
     * 卡片类型
     *
     * @return 卡片类型
     */
    public abstract String getCardType();

    /**
     * 发卡国家
     *
     * @return 发卡国家
     */
    public abstract String getCardIssueCountry();

    /**
     * 机构MID
     *
     * @return 机构MID
     */
    public abstract String getOriginMid();


    public abstract String getClearNetwork();

    public abstract String getChannelMerchantNo();

    /**
     * 机构MCC
     *
     * @return 机构MCC
     */
    public abstract String getOriginMcc();

    public abstract String getStandardMcc();

    public abstract String getSubMerchantNo();

    public abstract String getTransactionCountry();

    // ---- 大阶梯开始
    public abstract String getAccumulationCycle();

    public abstract String getAccumulationType();

    public abstract String getAccumulationMethod();

    public abstract String getAccumulationRange();

    public abstract String getAccumulationDeductTime();

    public abstract String getAccumulationJoin();

    public abstract String getAccumulationKey();

    public abstract String getAccumulationMode();
    // ---- 大阶梯结束

    /**
     * 阶梯范围起点（不含）, 需要数字校验
     *
     * @return 阶梯范围起点（不含）
     */
    public abstract String getStepUpper();

    /**
     * 阶梯范围终点（含）, 需要数字校验
     *
     * @return 阶梯范围终点（含）
     */
    public abstract String getStepLower();

    /**
     * 阶梯币种
     *
     * @return 阶梯币种
     */
    public abstract String getStepCurrency();

    /**
     * 计费方式
     *
     * @return 计费方式
     */
    public abstract String getCalculateType();

    /**
     * 算费时点
     *
     * @return 算费时点
     */
    public abstract String getFeeCalculateTime();

    /**
     * 扣费币种
     *
     * @return 扣费币种
     */
    public abstract String getChargeCurrency();

    /**
     * 保留小数位数, 需要数字校验
     *
     * @return 保留小数位数
     */
    public abstract String getRoundingScale();

    /**
     * 保留小数算法
     *
     * @return 保留小数算法
     */
    public abstract String getRoundingMode();

    /**
     * 计费基准
     *
     * @return 计费基准
     */
    public abstract String getFeeBasementMode();

    /**
     * 单笔比例
     *
     * @return 单笔比例
     */
    public abstract String getSingleStrokeRate();

    /**
     * 最低收费, 需要数字校验
     *
     * @return 最低收费
     */
    public abstract String getMinCharge();

    /**
     * 封顶收费, 需要数字校验
     *
     * @return 封顶收费
     */
    public abstract String getMaxCharge();

    /**
     * 单笔固定金额, 需要数字校验
     *
     * @return 单笔固定金额
     */
    public abstract String getSingleFixedAmount();

    /**
     * 单笔固定币种
     *
     * @return 单笔固定币种
     */
    public abstract String getSingleFixedCurrency();

    /**
     * 退款单笔比例
     *
     * @return 退款单笔比例
     */
    public abstract String getRefundSingleStrokeRate();

    /**
     * 退款单笔固定金额
     *
     * @return 退款单笔固定金额
     */
    public abstract String getRefundSingleFixedAmount();

    /**
     * 退款单笔固定币种
     *
     * @return 退款单笔固定币种
     */
    public abstract String getRefundSingleFixedCurrency();

    /**
     * 退款计费方式不能为空
     *
     * @return 退款计费方式不能为空
     */
    public abstract String getRefundCalculateType();

    /**
     * 退款时是否退税
     *
     * @return 退款时是否退税
     */
    public abstract String getRefundOrNot();

    /**
     * 换汇时机
     *
     * @return 换汇时机
     */
    public abstract String getCurrencyExchangeTime();

    /**
     * 外汇加点
     *
     * @return 外汇加点
     */
    public abstract String getFxSpread();

    @Override
    public void rowDataParse(List<ExcelCellData> excelCellDataList) {
        InstContractExcelDataTransfer instContractExcelDataTransfer = ApplicationUtils.getBean(InstContractExcelDataTransfer.class);
        instContractExcelDataTransfer.instContractFeeRowItemDtoTransfer(this, excelCellDataList);
    }

    @Override
    public Class<? extends InstContractFeeRowItemDTO> langClassImpl(LangEnum langEnum) {
        return InstContractFeeRowItemZhDTO.class;
    }

    /**
     * 每一个机构原始产品，会包含多维度的成本划分条款，以下字段拼接构成了描述条款的维度。
     * 维度businessKey相同，可认为是属于同一组条款（阶梯条款项）
     */
    public String getContractItemBusinessKey() {

        if (isStandardized()) {
            return StringUtils.join(getInstCode(), getContractEntity(), getInstProductType(), getPayCurrency(), getCardType(),
                    getCardIssueCountry(), getTransactionCountry(), getClearNetwork(), getChannelMerchantNo(), getStandardMcc(), getSubMerchantNo(), getCardOrg(), SymbolConstants.SYMBOL_STRIKE_LINE);

        } else {
            return StringUtils.join(getInstCode(), getContractEntity(), getInstProductType(), getInstProductName(), getPayCurrency(), getCardType(),
                    getCardIssueCountry(), getTransactionCountry(), getClearNetwork(), getOriginMid(), getOriginMcc(), getSubMerchantNo(), getCardOrg(), SymbolConstants.SYMBOL_STRIKE_LINE);
        }
    }
}
