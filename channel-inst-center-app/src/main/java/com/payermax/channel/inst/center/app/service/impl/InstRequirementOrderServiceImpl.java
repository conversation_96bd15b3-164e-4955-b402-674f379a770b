package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementOrderDao;
import com.payermax.channel.inst.center.app.service.InstRequirementOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 集成需求单Service实现
 *
 * <AUTHOR>
 * @date 2022/6/4 11:39
 */
@Service
public class InstRequirementOrderServiceImpl implements InstRequirementOrderService {

    @Autowired
    private InstRequirementOrderDao instRequirementOrderDao;

    @Override
    public int save(InstRequirementOrderEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instRequirementOrderDao.insert(record);
        } else {
            // 更新
            result = instRequirementOrderDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstRequirementOrderEntity get(String applyNo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(applyNo), "param applyNo is mandatory");

        InstRequirementOrderEntity record = new InstRequirementOrderEntity();
        record.setApplyNo(applyNo);
        List<InstRequirementOrderEntity> entityList = instRequirementOrderDao.selectAll(record);
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0); //NO_CHECK 申请单号唯一，只有一个
        }
        return null;
    }
}
