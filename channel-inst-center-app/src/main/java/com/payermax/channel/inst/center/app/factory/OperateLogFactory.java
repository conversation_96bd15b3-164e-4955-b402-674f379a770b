package com.payermax.channel.inst.center.app.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.payermax.channel.inst.center.common.enums.operatelog.*;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/26
 * @DESC
 */
@Component
public class OperateLogFactory {

    @Resource
    private IDGenerator idGenerator;

    public InstContractOperateLogPO composeDefaultOperateLog(BusinessTypeEnum businessType, OperateModuleEnum moduleName, String businessKey) {
        return InstContractOperateLogPO.builder()
                .logNo(idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.DEFAULT))
                .businessType(businessType)
                .moduleName(moduleName)
                .businessUniqueKey(businessKey)
                .operateTime(LocalDateTime.now(ZoneOffset.UTC))
                .build();
    }


    /**
     * 构造费用信息修改日志
     */
    public InstContractOperateLogPO composeFeeEditOperateLog(InstProcessDockPO processDock, LogOperateResTypeEnum operateRes, String auditComment) {
        // 构造日志信息
        String operator = processDock.getCreateUser();
        String businessUniqueKey = processDock.getBusinessId();
        String operateContent = String.format("用户 %s 修改费用信息，业务主键：%s，操作结果：%s，审批意见：%s", operator, businessUniqueKey, operateRes, auditComment);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",JSON.parseObject(processDock.getOriginalFormContent(), InstContractFeeItemPO.class));
            put("modifiedData",JSON.parseObject(processDock.getFormContent(), InstContractFeeItemPO.class));
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_FEE_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(operator)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(OperateTypeEnum.UPDATE);
    }

    /**
     * 构造费用信息修改日志-无工作流
     */
    public InstContractOperateLogPO composeFeeOperateByAdmin(String shareId, InstContractFeeItemPO originData, InstContractFeeItemPO modifiedData, LogOperateResTypeEnum operateRes, OperateTypeEnum operateType) {
        // 构造日志信息
        String businessUniqueKey = modifiedData.getInstContractFeeItemNo();
        String operateContent = String.format("管理员 %s 修改费用信息，业务主键：%s", shareId, businessUniqueKey);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_FEE_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(shareId)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(operateType);
    }

    /**
     * 构造结算信息修改日志
     */
    public InstContractOperateLogPO composeSettleEditOperateLog(InstProcessDockPO processDock, LogOperateResTypeEnum operateRes, String auditComment) {
        // 构造日志信息
        String operator = processDock.getCreateUser();
        String businessUniqueKey = processDock.getBusinessId();
        String operateContent = String.format("用户 %s 修改结算信息，业务主键：%s，操作结果：%s，审批意见：%s", operator, businessUniqueKey, operateRes, auditComment);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",JSON.parseObject(processDock.getOriginalFormContent(), InstContractSettlementItemPO.class));
            put("modifiedData",JSON.parseObject(processDock.getFormContent(), InstContractSettlementItemPO.class));
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_SETTLE_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(operator)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(OperateTypeEnum.UPDATE);
    }

    /**
     * 构造结算信息批量修改日志
     */
    public InstContractOperateLogPO composeSettleBatchModifiedOperateLog(InstProcessDockPO  processDock, LogOperateResTypeEnum operateRes, String auditComment) {
        // 构造日志信息
        String operator = processDock.getCreateUser();
        String businessUniqueKey = processDock.getBusinessId();
        String operateContent = String.format("用户 %s 批量修改结算信息，业务主键：[%s]，操作结果：%s，审批意见：%s", operator, businessUniqueKey, operateRes, auditComment);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",JSON.parseArray(processDock.getOriginalFormContent(), InstContractSettlementItemPO.class));
            put("modifiedData",JSON.parseArray(processDock.getFormContent(), InstContractSettlementItemPO.class));
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_SETTLE_BATCH_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(operator)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(OperateTypeEnum.UPDATE);
    }

    /**
     * 构造FX信息批量修改日志
     */
    public InstContractOperateLogPO composeFxBatchModifiedOperateLog(InstProcessDockPO  processDock, LogOperateResTypeEnum operateRes, String auditComment) {
        // 构造日志信息
        String operator = processDock.getCreateUser();
        String businessUniqueKey = processDock.getBusinessId();
        String operateContent = String.format("用户 %s 批量修改结算信息，业务主键：[%s]，操作结果：%s，审批意见：%s", operator, businessUniqueKey, operateRes, auditComment);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",JSON.parseArray(processDock.getOriginalFormContent(), InstContractFeeItemPO.class));
            put("modifiedData",JSON.parseArray(processDock.getFormContent(), InstContractFeeItemPO.class));
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_FX_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(operator)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(OperateTypeEnum.UPDATE);
    }

    /**
     * 构造结算信息修改日志-无工作流
     */
    public InstContractOperateLogPO composeSettleOperateByAdmin(String shareId, InstContractSettlementItemPO originData, InstContractSettlementItemPO modifiedData, LogOperateResTypeEnum operateRes, OperateTypeEnum operateType) {
        // 构造日志信息
        String businessUniqueKey = modifiedData.getInstContractSettlementItemNo();
        String operateContent = String.format("管理员 %s 修改结算信息，业务主键：%s", shareId, businessUniqueKey);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_SETTLE_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(shareId)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(operateType);
    }

    /**
     * 构造标准产品修改日志-无工作流
     */
    public InstContractOperateLogPO composeStandardProductOperateByAdmin(String shareId, InstContractStandardProductPO originData, InstContractStandardProductPO modifiedData, LogOperateResTypeEnum operateRes, OperateTypeEnum operateType) {
        // 构造日志信息
        String businessUniqueKey = modifiedData.getInstStandardProductNo();
        String operateContent = String.format("管理员 %s 修改标准化产品信息，业务主键：%s", shareId, businessUniqueKey);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_STANDARD_PRODUCT_MANAGER, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap, SerializerFeature.DisableCircularReferenceDetect))
                .setOperator(shareId)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(operateType);
    }

    /**
     * 构造修改日志-通用
     * @param shareId 用户 ID
     * @param businessUniqueKey 业务主键
     * @param originData 原始数据
     * @param modifiedData 修改后数据
     * @param module 模块名称
     * @param operateRes 操作结果
     * @param operateType 操作类型
     * @return 日志
     */
    public InstContractOperateLogPO composeOperateLog(String shareId, String businessUniqueKey, Object originData, Object modifiedData, OperateModuleEnum module, LogOperateResTypeEnum operateRes, OperateTypeEnum operateType) {
        // 构造日志信息
        String operateContent = String.format("用户 %s 修改数据，模块: %s，业务主键：%s", shareId, module, businessUniqueKey);
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = composeDefaultOperateLog(BusinessTypeEnum.INST_CENTER, module, businessUniqueKey);
        return logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(shareId)
                .setOperateContent(operateContent)
                .setOperateRes(operateRes)
                .setOperateType(operateType);
    }


}