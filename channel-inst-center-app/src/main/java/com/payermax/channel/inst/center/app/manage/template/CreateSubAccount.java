package com.payermax.channel.inst.center.app.manage.template;

import com.payermax.channel.inst.center.app.manage.template.mode.SubAccountModeTemplate;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;

import java.util.List;

/**
 * 子账号申请处理模版
 *
 * <AUTHOR> at 2022/10/9 10:26
 **/
public interface CreateSubAccount {

    /**
     * 子账号创建
     *
     * @param requestAccountDO
     */
    ResponseAccountDO createSubAccount(RequestAccountDO requestAccountDO);


    /**
     * 获取机构子级资金账户申请模式
     *
     * @param subAccountMode
     */
    SubAccountModeTemplate routeModeTemplate(String subAccountMode);

    /**
     * 优先级过滤规则
     *
     * @param instFundsAccountEntityList
     * @param instSubFundsAccountRequestDO
     */
    InstFundsAccountEntity routeInstAccount(List<InstFundsAccountEntity> instFundsAccountEntityList, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO);

    /**
     * 筛选可及时申请子级资金账号的机构账号的数据
     *
     * @param list
     * @param requestDO
     */
    List<InstFundsAccountEntity> filterInstAccount(List<InstFundsAccountEntity> list, InstSubFundsAccountRequestDO requestDO);
}
