package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.constants.DictConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.RequirementOrderStatusEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.RequirementScheduleStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.client.FintechBaseClientProxy;
import com.payermax.channel.inst.center.app.manage.InstRequirementOrderManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstAccountReqDTO;
import com.payermax.channel.inst.center.app.request.InstRequirementOrderReqDTO;
import com.payermax.channel.inst.center.app.response.InstAccountVO;
import com.payermax.channel.inst.center.app.response.InstRequirementOrderVO;
import com.payermax.channel.inst.center.app.service.*;
import com.ushareit.fintech.base.dto.DictItemDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集成需求单Manager实现
 *
 * <AUTHOR>
 * @date 2022/6/4 14:49
 */
@Service
public class InstRequirementOrderManagerImpl implements InstRequirementOrderManager {

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private FintechBaseClientProxy baseClientProxy;

    @Autowired
    private InstRequirementOrderService instRequirementOrderService;

    @Autowired
    private InstAccountService instAccountService;

    @Autowired
    private InstReportMerchantService instReportMerchantService;

    @Autowired
    private InstReconcileService instReconcileService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private InstContractService instContractService;

    @Autowired
    private InstContractProductService instContractProductService;
    
    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductCapabilityService instProductCapabilityService;

    @Autowired
    private InstRequirementScheduleService instRequirementScheduleService;

    @Autowired
    private InstRequirementScheduleDetailService instRequirementScheduleDetailService;

    @Override
    public int save(InstRequirementOrderReqDTO reqDto) {
        // 保存集成需求单
        InstRequirementOrderEntity orderEntity = reqDtoAssembler.toInstRequirementOrderEntity(reqDto);
        // 构建集成排期
        Map<String, InstRequirementScheduleEntity> scheduleMap = new HashMap<>();
        Map<String, List<InstRequirementScheduleDetailEntity>> scheduleDetailMap = new HashMap<>();
        this.buildScheduleEntity(orderEntity, scheduleMap, scheduleDetailMap);

        // 加入事务
        int result = transactionTemplate.execute(status -> {
            // 保存集成需求单信息
            int tmpResult = instRequirementOrderService.save(orderEntity);

            // 保存机构商户报备信息
            if (reqDto.getInstReportMerchant() != null) {
                InstReportMerchantEntity reportMerchantEntity = reqDtoAssembler.toInstReportMerchantEntity(reqDto.getInstReportMerchant());
                reportMerchantEntity.setRequirementOrderId(orderEntity.getId());
                reportMerchantEntity.setInstId(orderEntity.getInstId());
                instReportMerchantService.save(reportMerchantEntity);
            }
            // 保存机构对账信息
            if (reqDto.getInstReconcile() != null) {
                InstReconcileEntity reconcileEntity = reqDtoAssembler.toInstReconcileEntity(reqDto.getInstReconcile());
                reconcileEntity.setRequirementOrderId(orderEntity.getId());
                reconcileEntity.setInstId(orderEntity.getInstId());
                instReconcileService.save(reconcileEntity);
            }
            // 保存机构账号
            List<InstAccountReqDTO> accountReqList = reqDto.getAccountList();
            if (CollectionUtils.isNotEmpty(accountReqList)) {
                accountReqList.forEach((accountReq) -> {
                    InstAccountEntity accountEntity = reqDtoAssembler.toInstAccountEntity(accountReq);
                    accountEntity.setRequirementOrderId(orderEntity.getId());
                    accountEntity.setInstId(orderEntity.getInstId());
                    instAccountService.save(accountEntity);
                });
            }
            // 更新阶段状态
            if (StringUtils.isNotBlank(reqDto.getApplyNo())) {
                instApplyOrderService.updateStageStatus(reqDto.getApplyNo(), ApplyStageEnum.REQUIREMENT, orderEntity.getStatus());
            }
            // 保存集成排期信息
            scheduleMap.forEach((key, schedule) -> {
                // 保存集成排期
                schedule.setRequirementOrderId(orderEntity.getId());
                schedule.setStatus(RequirementScheduleStatusEnum.INIT.name());
                instRequirementScheduleService.save(schedule);
                // 保存集成排期详情
                List<InstRequirementScheduleDetailEntity> scheduleDetailList = scheduleDetailMap.get(key);
                scheduleDetailList.forEach(scheduleDetail -> {
                    scheduleDetail.setRequirementScheduleId(schedule.getId());
                    instRequirementScheduleDetailService.add(scheduleDetail);
                });
            });
            // 生成产研排期
            return tmpResult;
        });

        return result;
    }

    @Override
    public InstRequirementOrderVO query(String applyNo) {
        // 查询需求单
        InstRequirementOrderEntity orderEntity = instRequirementOrderService.get(applyNo);
        InstRequirementOrderVO orderVo = respVoAssembler.toInstRequirementOrderVO(orderEntity);
        if (orderVo == null) {
            return null;
        }

        // 查询机构商户报备信息
        InstReportMerchantEntity reportMerchantQueryEntity = new InstReportMerchantEntity();
        reportMerchantQueryEntity.setRequirementOrderId(orderEntity.getId());
        InstReportMerchantEntity reportMerchantEntity = instReportMerchantService.query(reportMerchantQueryEntity);
        orderVo.setInstReportMerchant(respVoAssembler.toInstReportMerchantVO(reportMerchantEntity));
        // 查询机构对账信息
        InstReconcileEntity reconcileQueryEntity = new InstReconcileEntity();
        reconcileQueryEntity.setRequirementOrderId(orderEntity.getId());
        InstReconcileEntity reconcileEntity = instReconcileService.query(reconcileQueryEntity);
        orderVo.setInstReconcile(respVoAssembler.toInstReconcileVO(reconcileEntity));
        // 查询机构账号
        InstAccountEntity accountQueryEntity = new InstAccountEntity();
        accountQueryEntity.setRequirementOrderId(orderEntity.getId());
        List<InstAccountEntity> accountEntityList = instAccountService.queryList(accountQueryEntity);
        List<InstAccountVO> accountVoList = respVoAssembler.toInstAccountVOList(accountEntityList);
        orderVo.setAccountList(accountVoList);

        return orderVo;
    }

    /**
     * 构建集成排期
     *
     * @param orderEntity
     * @param scheduleMap
     * @param scheduleDetailMap
     */
    private void buildScheduleEntity(InstRequirementOrderEntity orderEntity, Map<String, InstRequirementScheduleEntity> scheduleMap, Map<String, List<InstRequirementScheduleDetailEntity>> scheduleDetailMap) {
        if (orderEntity == null || StringUtils.isBlank(orderEntity.getApplyNo())) {
            return;
        }
        // 校验集成需求单状态
        if (!RequirementOrderStatusEnum.PD_ANALYSIS.name().equals(orderEntity.getStatus())) {
            return;
        }
        // 根据申请单号查询合同
        InstContractEntity queryContractEntity = new InstContractEntity();
        queryContractEntity.setApplyNo(orderEntity.getApplyNo());
        queryContractEntity.setInstId(orderEntity.getInstId());
        InstContractEntity instContract = instContractService.getByEntity(queryContractEntity);
        if (instContract == null) {
            return;
        }
        // 根据合同号查询合同产品映射关系
        List<InstContractProductEntity> instContractProductList = instContractProductService.getByContractNo(instContract.getContractNo());
        if (CollectionUtils.isEmpty(instContractProductList)) {
            return;
        }

        // 根据产品编码查询产品信息
        List<String> productCodes = instContractProductList.stream().map(InstContractProductEntity::getInstProductCode).distinct().collect(Collectors.toList());
        List<InstProductEntity> productEntityList = instProductService.getByProductCodes(productCodes);
        if (CollectionUtils.isEmpty(productEntityList)) {
            return;
        }
        Map<String, InstProductEntity> productEntityMap = productEntityList.stream().collect(Collectors.toMap(InstProductEntity::getProductCode, Function.identity()));

        // 根据产品能力编码查询产品能力
        List<String> capabilityCodeList = instContractProductList.stream().map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList());
        List<InstProductCapabilityEntity> productCapabilityList = instProductCapabilityService.getByCapabilityCodes(capabilityCodeList);
        if (CollectionUtils.isEmpty(productCapabilityList)) {
            return;
        }

        // 调用基础服务查询产品和国家关系字典项
        List<DictItemDTO> itemDTOList = baseClientProxy.queryDict(DictConstants.DICT_IC_PD_COUNTRY_MAPPING);
        Map<String, JSONObject> pdCountryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach((item) -> {
                if (StringUtils.isBlank(item.getExtraInfo())) {
                    return;
                }
                pdCountryMap.put(item.getItemValue(), JSONObject.parseObject(item.getExtraInfo()));
            });
        }
        // 构建产品排期和排期详情对象
        productCapabilityList.forEach((prodCapa) -> {
            String mapKey = String.format("%s_%s", prodCapa.getInstProductCode(), prodCapa.getCountry());
            if (scheduleMap.get(mapKey) == null) {
                InstRequirementScheduleEntity scheduleEntity = new InstRequirementScheduleEntity();
                scheduleEntity.setRequirementOrderId(orderEntity.getId());
                InstProductEntity instProductEntity = productEntityMap.get(prodCapa.getInstProductCode());
                // 设置PD
                pdCountryMap.entrySet().stream().filter(entry->getPD(entry,prodCapa.getCountry(),instProductEntity.getChannelType()))
                        .sorted(Comparator.comparing(entry->{
                            JSONObject jsonObject = entry.getValue();
                            return jsonObject.getIntValue("sort");
                        })).findFirst().ifPresent((entry) -> { //NO_CHECK 方法未被调用
                    scheduleEntity.setPdId(entry.getKey());
                });
                scheduleMap.put(mapKey, scheduleEntity);
            }
            List<InstRequirementScheduleDetailEntity> scheduleDetailList = scheduleDetailMap.get(mapKey);
            if (scheduleDetailList == null) {
                scheduleDetailList = new ArrayList<>();
                scheduleDetailMap.put(mapKey, scheduleDetailList);
            }
            InstRequirementScheduleDetailEntity scheduleDetailEntity = new InstRequirementScheduleDetailEntity();
            scheduleDetailEntity.setInstProductCode(prodCapa.getInstProductCode());
            scheduleDetailEntity.setInstProductCapabilityCode(prodCapa.getCapabilityCode());
            scheduleDetailList.add(scheduleDetailEntity);
        });
    }

    private boolean getPD(Map.Entry<String, JSONObject> entry,String country,String chanelType) {
        JSONObject jsonObject = entry.getValue();
        if (jsonObject == null) {
            return false;
        }
        JSONArray countryList = jsonObject.getJSONArray("country");
        return (chanelType.equalsIgnoreCase(jsonObject.getString("channelType")) && countryList.contains(country))
            || countryList.contains(CommonConstants.ALL);
    }
}
