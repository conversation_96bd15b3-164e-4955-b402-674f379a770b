package com.payermax.channel.inst.center.app.manage.template;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.manage.template.mode.SubAccountModeTemplate;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.facade.enums.SupportSubAccountTypeEnum;
import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.util.MigrationAlgorithmUtils;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2022/10/9 13:59
 **/
@Service
@Slf4j
public class CreateSubAccountImpl implements CreateSubAccount {

    @Autowired
    ReqDoAssembler reqDoAssembler;
    @Autowired
    SubAccountModeTemplate apiModeTemplate;
    @Autowired
    SubAccountModeTemplate offlineModeTemplate;
    @Autowired
    SubAccountModeTemplate numberSegmentModeTemplate;
    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;
    @Autowired
    InstSubFundsAccountBucketManage instSubFundsAccountBucketManage;

    @Override
    public ResponseAccountDO createSubAccount(RequestAccountDO requestAccountDO) {

        ResponseAccountDO responseAccountDO = new ResponseAccountDO();

        // 1.获取上游传递创建子级资金-请求参数
        InstSubFundsAccountRequestDO instSubFundsAccountRequestDO = requestAccountDO.getInstSubFundsAccountRequestDO();

        // 2.筛选过滤选择一个机构账号
        InstFundsAccountEntity instFundsAccountEntity = this.routeInstAccount(requestAccountDO.getInstFundsAccountEntityList(), instSubFundsAccountRequestDO);
        responseAccountDO.setInstFundsAccountEntity(instFundsAccountEntity);
        requestAccountDO.setInstFundsAccountEntity(instFundsAccountEntity);

        // 3.获取机构子级资金账户申请模式
        SubAccountModeTemplate modeTemplate = this.routeModeTemplate(instFundsAccountEntity.getSubAccountMode());

        // 4.子级资金账号记录-初始化
        InstSubFundsAccountEntity instSubFundsAccountEntity = modeTemplate.buildCreateSubAccountRecord(requestAccountDO);
        responseAccountDO.setInstSubFundsAccountEntity(instSubFundsAccountEntity);
        requestAccountDO.setInstSubFundsAccountEntity(instSubFundsAccountEntity);

        boolean chackSubAccountFromDb = false;
        try {
            // 申请开关，数据源切换后补偿
            if (MigrationAlgorithmUtils.getMigrateStart()) {
               throw new BusinessException("Migration Database!");
            }
            // 5.子级资金账号预申请判断
            InstSubFundsAccountRequestDO requestDO = reqDoAssembler.toInstSubFundsAccountRequestDO(instSubFundsAccountEntity);
            InstSubFundsAccountEntity preApplyAccountBucketEntity = instSubFundsAccountBucketManage.checkAndCreateSubAccountByPreApplyBucket(instFundsAccountEntity, requestDO);
            if (Objects.nonNull(preApplyAccountBucketEntity)) {
                responseAccountDO.setInstSubFundsAccountEntity(preApplyAccountBucketEntity);
                requestAccountDO.setInstSubFundsAccountEntity(preApplyAccountBucketEntity);
                return responseAccountDO;
            }
            // 6.子级资金账号获取
            modeTemplate.getSubAccountNo(requestAccountDO);
            // 7.对子级资金账户激活或进行其他处理
            modeTemplate.handleActivationOrOthersAction(requestAccountDO);
        } catch (Exception e) {
            chackSubAccountFromDb = true;
            // 出现异常请求机构定时任务重试（1.申请重试 状态为初始化的数据，重走申请模版，2.激活重试 API类 状态为待激活的数据）
            log.error("CreateSubAccountImpl-createSubAccount Exception!", e);
        } finally {
            if (chackSubAccountFromDb) {
                // 异常情况下确保返回子级账号数据与数据库子级账号信息一致
                InstSubFundsAccountQueryEntity querySubEntity = new InstSubFundsAccountQueryEntity();
                querySubEntity.setSubAccountId(instSubFundsAccountEntity.getSubAccountId());
                InstSubFundsAccountEntity subFundsAccount = instSubFundsAccountService.queryById(querySubEntity);
                responseAccountDO.setInstSubFundsAccountEntity(subFundsAccount);
            }
        }

        return responseAccountDO;
    }

    /**
     * 根据子级资金账户申请模式获取一个处理模版
     **/
    @Override
    public SubAccountModeTemplate routeModeTemplate(String subAccountMode) {
        if (StringUtils.isEmpty(subAccountMode)) {
            return null;
        }
        SubAccountModeTemplate modeTemplate;
        switch (SubAccountModeEnum.getByType(subAccountMode)) {
            case NUMBER_SEGMENT:
                modeTemplate = numberSegmentModeTemplate;
                break;
            case API:
                modeTemplate = apiModeTemplate;
                break;
            case OFFLINE:
                modeTemplate = offlineModeTemplate;
                break;
            default:
                throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_MODE_NOT_SUPPORT.getCode(), ErrorCodeEnum.SUB_ACCOUNT_MODE_NOT_SUPPORT.getMsg());
        }

        return modeTemplate;
    }

    /**
     * 优先级过滤规则（后续可新增路由筛选规则）
     **/
    @Override
    public InstFundsAccountEntity routeInstAccount(List<InstFundsAccountEntity> instFundsAccountEntityList, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {

        // 获取机构账号仅一个
        if (instFundsAccountEntityList.size() == 1) {
            return instFundsAccountEntityList.get(0);// CHECKED
        }

        // 获取有优先级配置的机构账号
        List<InstFundsAccountEntity> havePriority = instFundsAccountEntityList.stream().filter(t -> Objects.nonNull(t.getPriority())).collect(Collectors.toList());

        // 若都无优先级配置
        if (CollectionUtil.isEmpty(havePriority)) {
            return instFundsAccountEntityList.get(0);// NO_CHECK
        }

        // 获取有优先级配置的机构账号仅一个
        if (havePriority.size() == 1) {
            return havePriority.get(0);// CHECKED
        }

        List<InstFundsAccountEntity> highPriorityDoList = new ArrayList<>(havePriority.size());

        // 有优先级，根据优先级对机构进行排序
        havePriority.sort(Comparator.comparingInt(InstFundsAccountEntity::getPriority).reversed());
        // 遍历机构路由
        Integer maxHighPriority = Integer.MIN_VALUE;
        Integer weightTotal = 0;
        for (InstFundsAccountEntity accountEntity : instFundsAccountEntityList) {
            Integer priority = accountEntity.getPriority();
            // 如果筛选后存在该机构 && 此机构路由优先级更高
            if (maxHighPriority <= priority) {
                highPriorityDoList.add(accountEntity);
                // 设置最高优先级
                maxHighPriority = priority;
                // 计算总权重
                if (accountEntity.getWeight() > 0) {
                    weightTotal = weightTotal + accountEntity.getWeight();
                }
            }
        }

        // 若高优先级有1个，直接返回
        if (CollectionUtil.isNotEmpty(highPriorityDoList) && highPriorityDoList.size() == 1) {
            return highPriorityDoList.get(0);// CHECKED
        }

        // 若高优先级有多个个，权重分流
        int randomVal = RandomUtils.nextInt(weightTotal);
        int tmpTotal = 0;
        for (InstFundsAccountEntity highPriorityEntity : highPriorityDoList) {
            if (highPriorityEntity.getWeight() <= 0) {
                continue;
            }
            // 如果随机数在临时变量和机构权重之间，则选择此机构
            if (randomVal >= tmpTotal && randomVal < tmpTotal + highPriorityEntity.getWeight()) {
                return highPriorityEntity;
            }
            tmpTotal += highPriorityEntity.getWeight();
        }

        // 所有规则都不符合,则获取高优先级第一个
        return highPriorityDoList.get(0);// NO_CHECK
    }

    /**
     * 筛选可及时申请子级资金账号的机构账号的数据
     *
     * @param list      预筛选可用机构账户
     * @param requestDO 号段模式 需要支持号段类型
     */
    @Override
    public List<InstFundsAccountEntity> filterInstAccount(List<InstFundsAccountEntity> list, InstSubFundsAccountRequestDO requestDO) {

        List<InstFundsAccountEntity> resultList = new ArrayList<>(list);

        // 循环筛选机构账号
        for (InstFundsAccountEntity account : list) {

            // 构账号下生成的子级账号是否已超过最大数
            Long subAccountLimit = account.getSubAccountLimit();
            if (Objects.nonNull(subAccountLimit)) {
                InstSubFundsAccountQueryEntity subFundsAccountQueryEntity = new InstSubFundsAccountQueryEntity();
                subFundsAccountQueryEntity.setAccountId(account.getAccountId());
                // 机构账号下生成的子级账号是否已超过最大数
                int subAccountUsed = instSubFundsAccountService.queryCountByAccountId(subFundsAccountQueryEntity);
                if (subAccountLimit <= ((long) (subAccountUsed))) {
                    resultList.remove(account);
                    continue;
                }
            }

            // 检查机构账号是否支持该场景
            if (StringUtils.isNotBlank(requestDO.getScenes()) && ObjectUtil.notEqual(account.getScenes(),"*")) {
                List<String> scenesList = Arrays.asList(account.getScenes().split(","));
                // 检查不包含则不可用
                if (!scenesList.contains(requestDO.getScenes())) {
                    resultList.remove(account);
                    continue;
                }
            }

            // 检查机构账号是够支持此VA用途
            if (StringUtils.isNotBlank(requestDO.getSubUseType())) {
                List<String> subAccountTypeList = Arrays.asList(account.getSupportSubAccountType().split(","));
                switch (UseTypeEnum.valueOf(requestDO.getSubUseType())) {
                    case RECHARGE:
                        // 检查不包含则不可用
                        if (!subAccountTypeList.contains(SupportSubAccountTypeEnum.VA_RECHARGE.name())) {
                            resultList.remove(account);
                        }
                        break; 
                    case RECEIVE_PAY:
                        // 检查不包含则不可用
                        if (!subAccountTypeList.contains(SupportSubAccountTypeEnum.VA_RECEIVE_PAY.name())) {
                            resultList.remove(account);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        return resultList;
    }


}
