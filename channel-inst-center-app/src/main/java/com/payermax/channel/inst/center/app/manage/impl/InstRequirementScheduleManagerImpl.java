package com.payermax.channel.inst.center.app.manage.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstProductCapabilityQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleDetailQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity;
import com.payermax.channel.inst.center.app.manage.InstRequirementScheduleManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstProductCapabilityReqDTO;
import com.payermax.channel.inst.center.app.request.InstProductReqDTO;
import com.payermax.channel.inst.center.app.request.InstRequirementScheduleQueryReqDTO;
import com.payermax.channel.inst.center.app.request.InstRequirementScheduleReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductCapabilityVO;
import com.payermax.channel.inst.center.app.response.InstProductVO;
import com.payermax.channel.inst.center.app.response.InstRequirementScheduleQueryVO;
import com.payermax.channel.inst.center.app.response.InstRequirementScheduleVO;
import com.payermax.channel.inst.center.app.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 集成需求排期Manager实现
 *
 * <AUTHOR>
 * @date 2022/6/15 17:23
 */
@Service
public class InstRequirementScheduleManagerImpl implements InstRequirementScheduleManager {

    @Autowired
    private InstRequirementScheduleService instRequirementScheduleService;

    @Autowired
    private InstRequirementScheduleDetailService instRequirementScheduleDetailService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductCapabilityService instProductCapabilityService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Override
    public int save(InstRequirementScheduleReqDTO reqDto) {
        // 构建产品能力entity对象
        List<InstProductCapabilityEntity> prodCapaList = new ArrayList<>();
        List<InstRequirementScheduleDetailEntity> scheduleDetailList = new ArrayList<>();
        this.buildProductCapability(reqDto, prodCapaList, scheduleDetailList);

        // 通过事务执行更新操作
        int result = transactionTemplate.execute(status -> {
            InstRequirementScheduleEntity scheduleEntity = reqDtoAssembler.toInstRequirementScheduleEntity(reqDto);
            int tmpResult = instRequirementScheduleService.save(scheduleEntity);

            // 更新产品能力信息
            prodCapaList.forEach(prodCapa -> {
                instProductCapabilityService.update(prodCapa);
            });
            // 更新集成排期详情
            scheduleDetailList.forEach(scheduleDetail -> {
                instRequirementScheduleDetailService.update(scheduleDetail);
            });
            return tmpResult;
        });

        return result;
    }

    @Override
    public PageResult<InstRequirementScheduleQueryVO> queryPageList(InstRequirementScheduleQueryReqDTO reqDto) {
        InstRequirementScheduleQueryEntity queryEntity = reqDtoAssembler.toInstRequirementScheduleQueryEntity(reqDto);
        // 分页查询数据
        IPage<InstRequirementScheduleQueryEntity> page = instRequirementScheduleService.queryPageList(queryEntity, reqDto.getPageNum(), reqDto.getPageSize());
        // 转换数据
        List<InstRequirementScheduleQueryEntity> records = page.getRecords();

        // 构建结果
        PageResult<InstRequirementScheduleQueryVO> voPage = new PageResult<>();
        voPage.setTotal(page.getTotal());
        voPage.setRows(new ArrayList<>());
        // 如果结果为空，则返回
        if (CollectionUtils.isEmpty(records)) {
            return voPage;
        }

        // 遍历集成排期结果，构建数据
        List<Long> scheduleIds = new ArrayList<>();
        Map<Long, InstRequirementScheduleQueryVO> scheduleVoMap = new HashMap<>();
        records.forEach(record -> {
            InstRequirementScheduleQueryVO scheduleQueryVO = respVoAssembler.toInstRequirementScheduleQueryVO(record);

            scheduleIds.add(scheduleQueryVO.getId());
            voPage.getRows().add(scheduleQueryVO);
            scheduleVoMap.put(scheduleQueryVO.getId(), scheduleQueryVO);
        });

        // 查询集成排期详情
        InstRequirementScheduleDetailQueryEntity detailQueryEntity = new InstRequirementScheduleDetailQueryEntity();
        detailQueryEntity.setRequirementScheduleIds(scheduleIds);
        List<InstRequirementScheduleDetailEntity> scheduleDetailList = instRequirementScheduleDetailService.queryList(detailQueryEntity);
        if (CollectionUtils.isEmpty(scheduleDetailList)) {
            return voPage;
        }

        // 获取详情对应的产品和产品能力
        Map<String, List<Long>> productScheduleMap = new HashMap<>();
        Map<String, InstRequirementScheduleDetailEntity> productCapaScheduleMap = new HashMap<>();
        scheduleDetailList.forEach(detail -> {
            List<Long> productScheduleIdList = productScheduleMap.get(detail.getInstProductCode());
            if (productScheduleIdList == null) {
                productScheduleIdList = new ArrayList<>();
                productScheduleMap.put(detail.getInstProductCode(), productScheduleIdList);
            }
            productScheduleIdList.add(detail.getRequirementScheduleId());

            productCapaScheduleMap.put(detail.getInstProductCapabilityCode(), detail);
        });
        // 查询产品
        List<InstProductEntity> productList = instProductService.getByProductCodes(new ArrayList<>(productScheduleMap.keySet()));
        productList.forEach(product -> {
            List<Long> productScheduleIdList = productScheduleMap.get(product.getProductCode());
            if (CollectionUtils.isEmpty(productScheduleIdList)) {
                return;
            }
            InstProductVO productVo = respVoAssembler.toInstProductVo(product);
            productScheduleIdList.forEach(tmpScheduleId -> {
                InstRequirementScheduleQueryVO scheduleQueryVO = scheduleVoMap.get(tmpScheduleId);
                if (scheduleQueryVO == null) {
                    return;
                }
                scheduleQueryVO.setInstProduct(productVo);
            });
        });
        // 查询产品能力
        List<InstProductCapabilityEntity> capabilityList = instProductCapabilityService.getByCapabilityCodes(new ArrayList<>(productCapaScheduleMap.keySet()));
        capabilityList.forEach(capa -> {
            InstRequirementScheduleDetailEntity capaScheduleDetail = productCapaScheduleMap.get(capa.getCapabilityCode());
            if (capaScheduleDetail == null) {
                return;
            }
            InstRequirementScheduleQueryVO scheduleQueryVO = scheduleVoMap.get(capaScheduleDetail.getRequirementScheduleId());
            if (scheduleQueryVO == null) {
                return;
            }
            List<InstProductCapabilityVO> capabilityVoList = scheduleQueryVO.getProductCapabilityList();
            if (capabilityVoList == null) {
                capabilityVoList = new ArrayList<>();
                scheduleQueryVO.setProductCapabilityList(capabilityVoList);
            }
            InstProductCapabilityVO productCapaVo = respVoAssembler.toInstProductCapabilityVo(capa);
            productCapaVo.setVersion(capaScheduleDetail.getVersion());
            capabilityVoList.add(productCapaVo);
        });

        return voPage;
    }

    @Override
    public InstRequirementScheduleVO query(Long id) {
        InstRequirementScheduleEntity instRequirementScheduleEntity = instRequirementScheduleService.getById(id);
        InstRequirementScheduleVO instRequirementScheduleVO = respVoAssembler.toInstRequirementScheduleVo(instRequirementScheduleEntity);
        return instRequirementScheduleVO;
    }

    @Override
    public InstRequirementScheduleVO queryScheduleDetail(Long scheduleId, String version) {
        InstRequirementScheduleVO scheduleVO = new InstRequirementScheduleVO();
        scheduleVO.setId(scheduleId);
        // 查询集成排期详情
        InstRequirementScheduleDetailQueryEntity queryEntity = new InstRequirementScheduleDetailQueryEntity();
        queryEntity.setRequirementScheduleId(scheduleId);
        List<InstRequirementScheduleDetailEntity> scheduleDetailList = instRequirementScheduleDetailService.queryList(queryEntity);
        if (CollectionUtils.isEmpty(scheduleDetailList)) {
            return scheduleVO;
        }

        // 获取机构产品编码（一个集成排期只会有一个产品）
        String productCode = scheduleDetailList.get(0).getInstProductCode(); //NO_CHECK 方法未被调用
        // 查询机构产品
        InstProductEntity queryProductEntity = new InstProductEntity();
        queryProductEntity.setProductCode(productCode);
        List<InstProductEntity> instProductList = instProductService.getByEntity(queryProductEntity);
        if (CollectionUtils.isEmpty(instProductList)) {
            return scheduleVO;
        }
        InstProductVO instProductVO = respVoAssembler.toInstProductVo(instProductList.get(0)); //NO_CHECK 方法未被调用
        scheduleVO.setInstProduct(instProductVO);

        // 获取机构产品能力编码
        List<String> capabilityCodeList = new ArrayList<>();
        Map<String, String> capabilityVersionMap = new HashMap<>();
        scheduleDetailList.forEach(scheduleDetail -> {
            capabilityCodeList.add(scheduleDetail.getInstProductCapabilityCode());
            capabilityVersionMap.put(scheduleDetail.getInstProductCapabilityCode(), scheduleDetail.getVersion());
        });
        // 查询产品能力
        List<InstProductCapabilityEntity> capabilityList = instProductCapabilityService.getByCapabilityCodes(capabilityCodeList);

        List<String> unConfigTargetOrgList = new ArrayList<>();
        List<String> unConfigCardOrgList = new ArrayList<>();
        Map<String, InstProductCapabilityVO> prodCapaVOMap = new HashMap<>();
        // 遍历&构建已配置的产品能力VO
        capabilityList.forEach(prodCapa -> {
            String realVersion = capabilityVersionMap.get(prodCapa.getCapabilityCode());
            // 版本不存在，说明未进行配置
            if (StringUtils.isBlank(realVersion)) {
                if (StringUtils.isNotBlank(prodCapa.getTargetOrg())) {
                    unConfigTargetOrgList.add(prodCapa.getTargetOrg());
                }
                if (StringUtils.isNotBlank(prodCapa.getCardOrg())) {
                    unConfigCardOrgList.add(prodCapa.getCardOrg());
                }
                return;
            }
            InstProductCapabilityVO prodCapaVO = prodCapaVOMap.get(realVersion);
            if (prodCapaVO == null) {
                prodCapaVO = this.buildProductCapabilityVO(prodCapa);
                prodCapaVO.setVersion(realVersion);
                prodCapaVOMap.put(realVersion, prodCapaVO);
            }
            // 获取同版本的目标机构
            if (StringUtils.isNotBlank(prodCapa.getTargetOrg())) {
                prodCapaVO.setTargetOrgs(StringUtils.isBlank(prodCapaVO.getTargetOrgs()) ? prodCapa.getTargetOrg() :
                        String.format("%s,%s", prodCapaVO.getTargetOrgs(), prodCapa.getTargetOrg()));
            }
            // 获取同版本的卡组织
            if (StringUtils.isNotBlank(prodCapa.getCardOrg())) {
                prodCapaVO.setCardOrgs(StringUtils.isBlank(prodCapaVO.getCardOrgs()) ? prodCapa.getCardOrg() :
                        String.format("%s,%s", prodCapaVO.getCardOrgs(), prodCapa.getCardOrg()));
            }
        });
        // 设置机构产品信息
        instProductVO.setCountrys(capabilityList.get(0).getCountry()); //NO_CHECK 方法未被调用
        instProductVO.setTargetOrgs(unConfigTargetOrgList.stream().collect(Collectors.joining(",")));
        instProductVO.setCardOrgs(unConfigCardOrgList.stream().collect(Collectors.joining(",")));

        List<InstProductCapabilityVO> productCapabilityVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(version)) {
            InstProductCapabilityVO tmpProdCapaVO = prodCapaVOMap.get(version);
            if (tmpProdCapaVO != null) {
                productCapabilityVOList.add(tmpProdCapaVO);
            }
        } else {
            productCapabilityVOList.addAll(prodCapaVOMap.values());
        }
        instProductVO.setProductCapabilityList(productCapabilityVOList);

        return scheduleVO;
    }

    /**
     * 构建InstProductCapabilityVO
     *
     * @param prodCapa
     * @return
     */
    private InstProductCapabilityVO buildProductCapabilityVO(InstProductCapabilityEntity prodCapa) {
        InstProductCapabilityVO prodCapaVO = respVoAssembler.toInstProductCapabilityVo(prodCapa);
        return prodCapaVO;
    }

    /**
     * 构建产品能力
     *
     * @param reqDto
     * @param prodCapaList
     * @param scheduleDetailList
     */
    private void buildProductCapability(InstRequirementScheduleReqDTO reqDto, List<InstProductCapabilityEntity> prodCapaList, List<InstRequirementScheduleDetailEntity> scheduleDetailList) {
        if (reqDto == null || prodCapaList == null || scheduleDetailList == null) {
            return;
        }
        InstProductReqDTO instProductReq = reqDto.getInstProduct();
        // 存在产品能力
        if (instProductReq == null || CollectionUtils.isEmpty(instProductReq.getProductCapabilityList())) {
            return;
        }

        // 获取产品能力配置
        List<InstProductCapabilityReqDTO> productCapabilityReqList = instProductReq.getProductCapabilityList();
        InstProductCapabilityReqDTO prodCapaReq = productCapabilityReqList.get(0); //NO_CHECK 方法未被调用

        Preconditions.checkArgument(!(StringUtils.isNotBlank(prodCapaReq.getTargetOrgs()) && StringUtils.isNotBlank(prodCapaReq.getCardOrgs())),
                "param targetOrgs and cardOrgs cannot exist at the same time");

        // 构建产品能力查询对象
        InstProductCapabilityQueryEntity queryEntity = new InstProductCapabilityQueryEntity();
        queryEntity.setInstProductCode(prodCapaReq.getInstProductCode());
        queryEntity.setCountry(prodCapaReq.getCountry());
        // 解析目标机构或卡组织
        List<String> orgList = new ArrayList<>();
        if (StringUtils.isNotBlank(prodCapaReq.getTargetOrgs())) {
            orgList = Arrays.asList(StringUtils.split(prodCapaReq.getTargetOrgs()));
            queryEntity.setTargetOrgs(orgList);
        } else if (StringUtils.isNotBlank(prodCapaReq.getCardOrgs())) {
            orgList = Arrays.asList(StringUtils.split(prodCapaReq.getCardOrgs()));
            queryEntity.setCardOrgs(orgList);
        }
        // 查询产品能力
        List<InstProductCapabilityEntity> productCapabilityList = instProductCapabilityService.getList(queryEntity);
        // 构建产品能力编码Map
        Map<String, String> capabilityCodeMap = new HashMap<>();
        productCapabilityList.forEach(prodCapa -> {
            String mapKey = prodCapa.getTargetOrg();
            if (StringUtils.isNotBlank(prodCapa.getCardOrg())) {
                mapKey = prodCapa.getCardOrg();
            }
            if (StringUtils.isBlank(mapKey)) {
                return;
            }
            capabilityCodeMap.put(mapKey, prodCapa.getCapabilityCode());
        });

        // 遍历目标机构或卡组织，构建entity对象
        String version = String.valueOf(System.currentTimeMillis());
        orgList.forEach(org -> {
            String capabilityCode = capabilityCodeMap.get(org);
            if (StringUtils.isBlank(capabilityCode)) {
                return;
            }
            // 构建产品能力Entity
            InstProductCapabilityEntity prodCapaEntity = reqDtoAssembler.toInstProductCapabilityEntity(prodCapaReq);
            prodCapaEntity.setCapabilityCode(capabilityCode);
            prodCapaList.add(prodCapaEntity);
            // 构建集成排期详情
            InstRequirementScheduleDetailEntity scheduleDetailEntity = new InstRequirementScheduleDetailEntity();
            scheduleDetailEntity.setRequirementScheduleId(reqDto.getId());
            scheduleDetailEntity.setInstProductCode(prodCapaReq.getInstProductCode());
            scheduleDetailEntity.setInstProductCapabilityCode(capabilityCode);
            scheduleDetailEntity.setVersion(version);
            scheduleDetailList.add(scheduleDetailEntity);
        });
    }

}
