package com.payermax.channel.inst.center.app.dto;

import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.domain.vo.contract.ContractBusinessKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> at 2023/6/26 11:24 AM
 *
 * 机构合约录入Excel，不同录入内容（sheet）公共字段，表示不同DTO类型公共父类
 **/
@Slf4j
public abstract class AbstractInstContractRowItemDTO extends ExcelUtil.BaseExcelRow {

    /**
     * 录入人
     * @return 录入人
     */
    public abstract String getEnteredBy();

    /**
     * 机构legal name
     * @return 机构legal name
     */
    protected abstract String getInstLegalName();

    /**
     * 我方主体
     * @return 我方主体
     */
    public abstract String getContractEntity();

    /**
     * 机构简称
     * @return 机构简称
     */
    public abstract String getInstCode();

    /**
     * 机构产品类型
     * @return 机构产品类型
     */
    public abstract String getInstProductType();

    /**
     * 机构产品名称
     * @return 机构产品名称
     */
    public abstract String getInstProductName();

    /**
     * 支付方式类型
     *
     * @return 支付方式类型
     */
    public abstract String getPaymentMethodType();

    /**
     * 目标机构
     *
     * @return 目标机构
     */
    public abstract String getTargetOrg();

    /**
     * 卡组
     *
     * @return 卡组
     */
    public abstract String getCardOrg();

    /**
     * 设置卡组
     * @param cardOrg 卡组
     */
    public abstract void setCardOrg(String cardOrg);

    /**
     * 该条记录是否已经标准化
     */
    public boolean isStandardized() {
        return StringUtils.isNotBlank(getPaymentMethodType())
                && (StringUtils.isNotBlank(getTargetOrg()) || StringUtils.isNotBlank(getCardOrg()));
    }

    /**
     * 签约主体，机构标识，签约业务类型 构成唯一机构合约
     */
    public ContractBusinessKey getContractBusinessKey() {
        return new ContractBusinessKey(getInstCode(), getContractEntity(), getInstProductType());
    }

    /**
     * 每一份机构合约内 会描述多个机构原始产品，需要依次处理多个产品
     */
    public String getOriginProductName() {
        return getInstProductName();
    }
}
