package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstProductReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 15:54
 */
public interface InstProductManager {
    /**
     * 保存产品信息(只有产品信息，没有产品能力)
     * @param productReqDTO
     * @return
     */
    int save(InstProductReqDTO productReqDTO);

    /**
     * 保存产品信息(产品基本信息+产品能力)
     *
     * @param productReqDTO
     * @return
     */
    int saveAll(InstProductReqDTO productReqDTO);

    /**
     * 查询产品信息(只有产品信息，没有产品能力)
     *
     * @param productReqDTO
     * @return
     */
    List<InstProductVO> query(InstProductReqDTO productReqDTO);

    /**
     * 根据合同单号询产品信息(产品基本信息+产品能力)
     *
     * @param contractNo
     * @return
     */
    List<InstProductVO> queryAll(String contractNo);

    /**
     * 根据产品编码和操作version查询产品信息(产品基本信息+产品能力)
     *
     * @param productCode
     * @return
     */
    InstProductVO query(String productCode,String version);

    /**
     * 根据产品编码和操作version删除产品信息
     * @param productCode
     * @return
     */
    int delete(String productCode,String version);
}
