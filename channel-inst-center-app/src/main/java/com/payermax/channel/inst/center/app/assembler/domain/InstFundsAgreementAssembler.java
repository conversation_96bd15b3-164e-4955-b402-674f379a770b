package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstFundsAgreementQueryVO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class})
public interface InstFundsAgreementAssembler {

    /**
     * 业务协议上下文转业务协议PO
     * @param context 业务协议上下文
     * @return 业务协议PO
     */
    InstBizAgreementPO bizAgreementContext2Po(InstFundsAgreementContextDTO.BizAgreement context);


    /**
     * 资金协议上下文转为资金协议PO
     * @param context 资金协议上下文
     * @return 资金协议PO
     */
    @Mappings({
            @Mapping(target = "bizAgreement", ignore = true),
            @Mapping(target = "settleRules", ignore = true)
    })
    InstFundsAgreementPO fundsAgreementContext2Po(InstFundsAgreementContextDTO.FundsAgreement context);

    /**
     * 结算规则上下文转结算规则PO
     * @param context 结算规则上下文
     * @return 结算规则PO
     */
    InstFundsSettleRulePO fundsSettleRuleContext2Po(InstFundsAgreementContextDTO.FundsSettleRule context);

    /**
     * 查询参数转资金协议PO
     * @param request 查询参数
     * @return 资金协议PO
     */
    @Mappings({
            @Mapping(target = "type", source = "request.fundsAgreementType"),
            @Mapping(target = "name", source = "request.fundsAgreementName"),
            @Mapping(target = "clearingCcy", source = "request.clearingCurrency"),
            @Mapping(target = "status", source = "request.fundsAgreementStatus")
    })
    InstFundsAgreementPO request2FundsPo(InstFundsAgreementQueryRequestDTO request);

    /**
     * 资金协议 PO 转 VO
     * @param po 资金协议 PO
     * @return  资金协议 VO
     */
    InstFundsAgreementQueryVO fundsPo2QueryVo(InstFundsAgreementPO po);

    /**
     * 资金协议复制
     * @param po 资金协议 PO
     * @return 资金协议 PO
     */
    InstFundsAgreementPO fundsAgreementCopy(InstFundsAgreementPO po);
}
