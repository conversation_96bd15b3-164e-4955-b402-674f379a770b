package com.payermax.channel.inst.center.app.config;

import java.util.Map;

/**
 * DynamicExcelSeparatorConfig
 *
 * <AUTHOR>
 * @desc
 */
public interface DynamicExcelColumnsConfig {

    /**
     * 获取起始分割符
     * @return 起始分割符
     */
    String getStartSeparator();

    /**
     * 设置起始分割符
     * @param startSeparator 起始分割符
     */
    void setStartSeparator(String startSeparator);

    /**
     * 获取结束分割符
     * @return 结束分割符
     */
    String getEndSeparator();

    /**
     * 设置结束分割符
     * @param endSeparator 结束分割符
     */
    void setEndSeparator(String endSeparator);

    /**
     * 获取字段映射MAP
     * @return 字段映射MAP
     */
    Map<String, Map<String, String>> getFieldMapping();

    /**
     * 设置字段映射MAP
     * @param fieldMapping 字段映射MAP
     */
    void setFieldMapping(Map<String, Map<String, String>> fieldMapping);
}
