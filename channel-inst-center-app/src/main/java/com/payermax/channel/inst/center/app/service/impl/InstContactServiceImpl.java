package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContactDao;
import com.payermax.channel.inst.center.app.service.InstContactService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstContactServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 13:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstContactServiceImpl implements InstContactService {

    @Autowired
    private InstContactDao instContactDao;

    @Override
    public List<InstContactEntity> query(InstContactEntity instContactEntity) {
        Preconditions.checkArgument(instContactEntity.getInstId() != null, "param instId is mandatory");

        List<InstContactEntity> instContactEntityList = instContactDao.selectAll(instContactEntity);
        if (CollectionUtils.isNotEmpty(instContactEntityList)) {
            return instContactEntityList;
        }
        return null;
    }

    @Override
    public List<InstContactEntity> query(List<Long> instIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instIds), "param instIds is mandatory");
        List<InstContactEntity> instContactEntities = instContactDao.selectByInstIds(instIds);
        return instContactEntities;
    }

    @Override
    public int save(InstContactEntity instContactEntity) {
        Preconditions.checkArgument(instContactEntity != null, "param instContactEntity is mandatory");
        int result = 0;
        //主键不为空则根据主键更新信息
        if (null != instContactEntity.getId()) {
            result = instContactDao.updateByPrimaryKey(instContactEntity);
        } else {
            result = instContactDao.insert(instContactEntity);
        }
        return result;
    }
}
