package com.payermax.channel.inst.center.app.manage.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.AuditBusiTypeEnum;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity;
import com.payermax.channel.inst.center.app.manage.InstDdManage;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.*;
import com.payermax.channel.inst.center.app.response.InstDdQueryVO;
import com.payermax.channel.inst.center.app.response.InstDdVO;
import com.payermax.channel.inst.center.app.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 机构DD manage实现
 *
 * <AUTHOR>
 * @date 2022/5/15 23:02
 */
@Service
public class InstDdManageImpl implements InstDdManage {

    @Autowired
    private InstDdService instDdService;

    @Autowired
    private InstBaseInfoService instBaseInfoService;

    @Autowired
    private InstDdSurveyService instDdSurveyService;

    @Autowired
    private InstAuditDataService instAuditDataService;

    @Autowired
    private InstAuditResultService instAuditResultService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(InstDdReqDTO ddReq) {
        // 保存DD信息
        InstDdEntity instDdEntity = reqDtoAssembler.toInstDdEntity(ddReq);
        int result = instDdService.save(instDdEntity);

        // 更新阶段状态
        instApplyOrderService.updateStageStatus(instDdEntity.getApplyNo(), ApplyStageEnum.DD, instDdEntity.getStatus());

        // 保存机构信息
        InstBaseInfoReqDTO instBaseInfo = ddReq.getInstBaseInfo();
        if (instBaseInfo != null) {
            InstBaseInfoEntity instBaseInfoEntity = new InstBaseInfoEntity();
            instBaseInfoEntity.setInstId(instBaseInfo.getInstId());
            instBaseInfoEntity.setInstTypes(instBaseInfo.getInstTypes());
            instBaseInfoEntity.setEntityCountry(instBaseInfo.getEntityCountry());
            instBaseInfoEntity.setIsFatfMember(instBaseInfo.getIsFatfMember());
            instBaseInfoService.save(instBaseInfoEntity);
        }
        // 保存DD调研报告
        InstDdSurveyReqDTO ddSurvey = ddReq.getDdSurvey();
        if (ddSurvey != null) {
            InstDdSurveyEntity instDdSurveyEntity = reqDtoAssembler.toInstDdSurveyEntity(ddSurvey);
            instDdSurveyEntity.setDdId(instDdEntity.getId());
            instDdSurveyService.save(instDdSurveyEntity);
        }
        // 保存审核资料数据
        List<InstAuditDataReqDTO> auditDataList = ddReq.getAuditDataList();
        if (CollectionUtils.isNotEmpty(auditDataList)) {
            // 保存审核资料数据
            auditDataList.forEach(auditData -> {
                InstAuditDataEntity auditDataEntity = reqDtoAssembler.toInstAuditDataEntity(auditData);
                auditDataEntity.setBusinessType(AuditBusiTypeEnum.DD.name());
                auditDataEntity.setBusinessNo(String.valueOf(instDdEntity.getId()));
                instAuditDataService.save(auditDataEntity);
            });
        }
        // 保存审核结果
        InstAuditResultReqDTO auditResult = ddReq.getAuditResult();
        if (auditResult != null) {
            InstAuditResultEntity auditResultEntity = reqDtoAssembler.toInstAuditResultEntity(auditResult);
            auditResultEntity.setBusinessType(AuditBusiTypeEnum.DD.name());
            auditResultEntity.setBusinessNo(String.valueOf(instDdEntity.getId()));
            instAuditResultService.save(auditResultEntity);
        }
        return result;
    }

    @Override
    public InstDdVO query(Long instId) {
        InstDdEntity instDdEntity = instDdService.getByInstId(instId);
        if (instDdEntity == null) {
            return null;
        }
        // DD VO转换
        InstDdVO instDdVO = respVoAssembler.toInstDdVo(instDdEntity);

        // 查询机构信息
        InstBaseInfoEntity instBaseInfoEntity = instBaseInfoService.queryById(instDdEntity.getInstId());
        instDdVO.setInstBaseInfo(respVoAssembler.toInstBaseInfoVo(instBaseInfoEntity));

        // 查询DD调研问卷
        InstDdSurveyEntity instDdSurveyEntity = instDdSurveyService.getByDdId(instDdEntity.getId());
        instDdVO.setDdSurvey(respVoAssembler.toInstDdSurveyVo(instDdSurveyEntity));

        // 查询审核资料数据
        InstAuditDataEntity queryAuditDataEntity = new InstAuditDataEntity();
        queryAuditDataEntity.setBusinessType(AuditBusiTypeEnum.DD.name());
        queryAuditDataEntity.setBusinessNo(String.valueOf(instDdEntity.getId()));
        List<InstAuditDataEntity> instAuditDataEntities = instAuditDataService.queryList(queryAuditDataEntity);
        instDdVO.setAuditDataList(respVoAssembler.toInstAuditDataVo(instAuditDataEntities));

        // 查询审核结果
        InstAuditResultEntity queryAuditResultEntity = new InstAuditResultEntity();
        queryAuditResultEntity.setBusinessType(AuditBusiTypeEnum.DD.name());
        queryAuditResultEntity.setBusinessNo(String.valueOf(instDdEntity.getId()));
        InstAuditResultEntity instAuditResultEntity = instAuditResultService.query(queryAuditResultEntity);
        instDdVO.setAuditResult(respVoAssembler.toInstAuditResultVo(instAuditResultEntity));

        return instDdVO;
    }

    @Override
    public InstDdQueryVO queryInstDdByInstIds(List<Long> instIds) {
        List<InstDdQueryEntity> instDdQueryEntities = instDdService.getByInstIds(instIds);
        if (CollectionUtils.isNotEmpty(instDdQueryEntities)) {
            if (instDdQueryEntities.size() == 1) {
                return respVoAssembler.toInstDdQueryVo(instDdQueryEntities.get(0)); //NO_CHECK 方法未被调用
            }
            List<InstDdQueryEntity> auditAgresDds = instDdQueryEntities.stream().filter(obj -> "AUDIT_AGREE".equalsIgnoreCase(obj.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(auditAgresDds)) {
                return respVoAssembler.toInstDdQueryVo(auditAgresDds.stream().findFirst().get()); //NO_CHECK 方法未被调用
            }
            return respVoAssembler.toInstDdQueryVo(instDdQueryEntities.stream().findFirst().get()); //NO_CHECK 方法未被调用
        }
        return null;
    }
}
