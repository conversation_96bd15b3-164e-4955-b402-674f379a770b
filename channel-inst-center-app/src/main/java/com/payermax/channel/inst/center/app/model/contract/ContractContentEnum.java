package com.payermax.channel.inst.center.app.model.contract;

import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/9 5:55 PM
 **/
@AllArgsConstructor
@Getter
public enum ContractContentEnum {

    INST_FEE_TAX_PAYIN("institution trade cost", ContractBusinessTypeEnum.I),

    INST_FEE_TAX_PAYOUT("institution trade cost", ContractBusinessTypeEnum.O),

    INST_ACCUMULATED_FEE_TAX_PAYIN("institution accumulated cost", ContractBusinessTypeEnum.I),

    INST_ACCUMULATED_FEE_TAX_PAYOUT("institution accumulated cost", ContractBusinessTypeEnum.O),

    INST_SETTLEMENT_PAYIN("institution settlement info", ContractBusinessTypeEnum.I),

    INST_SETTLEMENT_PAYOUT("institution settlement info", ContractBusinessTypeEnum.O),
    ;

    private String excelSheetName;

    private ContractBusinessTypeEnum businessType;

    /**
     * 根据业务类型 获取需解析的合同内容模板
     */
    public static List<ContractContentEnum> getContractContentByBusinessType(ContractBusinessTypeEnum businessTypeEnum) {
        return Arrays.stream(values()).filter(item -> item.getBusinessType() == businessTypeEnum).collect(Collectors.toList());
    }
}
