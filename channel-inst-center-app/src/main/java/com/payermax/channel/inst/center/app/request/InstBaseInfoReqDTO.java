package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstBaseInfoReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 21:17
 * @Version 1.0
 */
@Data
public class InstBaseInfoReqDTO implements Serializable {

    private static final long serialVersionUID = -8908172987562204559L;
    private long pageNum;
    private long pageSize;
    /**
     * 机构标识
     */
    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    /**
     * 机构编码
     */
    @ApiModelProperty(notes = "机构编码")
    @Size(max = 64, message = "[instCode] maximum 64 length")
    private String instCode;

    /**
     * 机构品牌标识
     */
    @ApiModelProperty(notes = "机构品牌标识")
    private Long instBrandId;

    /**
     * 机构品牌名称
     */
    @ApiModelProperty(notes = "机构品牌名称")
    private String instBrandName;

    /**
     * 机构品牌编码
     */
    @ApiModelProperty(notes = "机构品牌编码")
    @Size(max = 64, message = "[instBrandCode] maximum 64 length")
    private String instBrandCode;

    /**
     * 机构名称
     */
    @ApiModelProperty(notes = "机构名称")
    @Size(max = 64, message = "[instName] maximum 64 length")
    private String instName;

    /**
     * 机构类型，可多个
     */
    @ApiModelProperty(notes = "机构类型")
    private String instTypes;

    /**
     * 主体所在地
     */
    @ApiModelProperty(notes = "主体所在地")
    private String entityCountry;

    /**
     * 是否FATF成员国 Y:是 N:否
     */
    @ApiModelProperty(notes = "是否FATF成员国")
    private String isFatfMember;

    /**
     * 简介
     */
    @ApiModelProperty(notes = "简介")
    @Size(max = 256, message = "[remark] maximum 256 length")
    private String remark;

    /**
     * 状态 Y：启用，N：停用
     */
    @ApiModelProperty(notes = "状态")
    private String status;

    /**
     * 渠道类型
     */
    @ApiModelProperty(notes = "渠道类型")
    private String channelType;

    /**
     * 支付方式类型
     */
    @ApiModelProperty(notes = "支付方式类型")
    private String paymentMethodType;

    /**
     * BD负责人
     */
    @ApiModelProperty(notes = "BD负责人")
    private String bdId;

    /**
     * AM负责人
     */
    @ApiModelProperty(notes = "AM负责人")
    private String amId;

    /**
     * BD负责人
     */
    @ApiModelProperty(notes = "BD负责人")
    private List<String> bdList;

    /**
     * AM负责人
     */
    @ApiModelProperty(notes = "AM负责人")
    private List<String> amList;

    /**
     * bdAndAm
     */
    @ApiModelProperty(notes = "BD、AM负责人")
    private String bdAndAmId;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    private Date utcCreate;

    /**
     * 公司注册号
     */
    @ApiModelProperty(notes = "公司注册号")
    private String registerNo;
}
