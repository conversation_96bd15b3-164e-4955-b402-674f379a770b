package com.payermax.channel.inst.center.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity;

import java.util.List;

/**
 * 机构信息相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
public interface InstBaseInfoService{

    /**
     * 根据机构code查询记录
     *
     * @return
     */
    InstBaseInfoEntity queryByInstCode(String instCode);

    /**
     * 更加机构ID查询机构信息
     *
     * @param instId
     * @return
     */
    InstBaseInfoEntity queryById(Long instId);

    /**
     * 查询机构信息
     *
     * @return
     */
    List<InstBaseInfoEntity> query(InstBaseInfoEntity instBaseInfoEntity);

    /**
     * 查询机构信息
     *
     * @return
     */
    IPage<InstBaseInfoQueryEntity> queryAll(InstBaseInfoQueryEntity instBaseInfoQueryEntity, Long pageNum, Long pageSize);

    /**
     * 查询机构信息
     *
     * @return
     */
    List<InstBaseInfoEntity> queryByBrandId(List<Long> instBrandIds);

    /**
     * 保存机构信息
     *
     * @return
     */
    int save(InstBaseInfoEntity instBaseInfoEntity);


}
