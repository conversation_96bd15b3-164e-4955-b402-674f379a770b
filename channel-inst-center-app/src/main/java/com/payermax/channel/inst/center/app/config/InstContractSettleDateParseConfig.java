package com.payermax.channel.inst.center.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * InstContractFeeParseConfig
 *
 * <AUTHOR>
 * @desc
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "excel.import.inst-contract.parse.settle-date")
public class InstContractSettleDateParseConfig implements DynamicExcelColumnsConfig {

    private String startSeparator;

    private String endSeparator;

    private Map<String, Map<String, String>> fieldMapping;
}
