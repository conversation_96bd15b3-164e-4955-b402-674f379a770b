package com.payermax.channel.inst.center.app.manage.contract;

import com.payermax.channel.inst.center.app.model.contract.ContractContentEnum;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.common.utils.ISupportedScenarios;

/**
 * <AUTHOR> at 2023/6/9 5:58 PM
 **/
public abstract class AbstractContractTermsProcessor<T extends ExcelUtil.BaseExcelRow> implements ISupportedScenarios<ContractContentEnum> {

    public void processOnContractContent(ContractProcessByExcelContext context, ContractContentEnum contentEnum) {

        // 1. 组装成内部领域对象
        assembleContractItems(context);

        // 2. 业务层面校验
        businessValidateOnContractItems(context);
    }

    /**
     * 根据excel原始内容 构造内部领域实体
     */
    public abstract void assembleContractItems(ContractProcessByExcelContext context);


    /**
     * 在内部领域实体上 做业务逻辑校验
     */
    public abstract void businessValidateOnContractItems(ContractProcessByExcelContext context);
}
