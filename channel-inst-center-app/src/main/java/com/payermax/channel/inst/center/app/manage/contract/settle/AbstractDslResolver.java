package com.payermax.channel.inst.center.app.manage.contract.settle;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.utils.ISupportedScenarios;
import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.*;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.common.lang.util.AssertUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2023/6/18  11:40 PM
 */
public abstract class AbstractDslResolver implements ISupportedScenarios<DSLTypeEnum> {

    protected static final Map<String, Pattern> PATTERN_CACHE = new HashMap<>();

    /**
     * 填充 - 交易周期开始信息
     */
    public abstract void fillDslTransactionStartCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    /**
     * 填充 - 交易周期结束信息
     */
    public abstract void fillDslTransactionEndCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    /**
     * 填充 - 结算账单日信息
     */
    public abstract void fillDslSettleCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    /**
     * 填充 - 结算账单日信息
     */
    public abstract void fillDslPaymentCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    /**
     * 填充 - 结算账单日信息
     */
    public abstract void fillDslArriveCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    /**
     * 填充 - 结算换汇日信息
     */
    public abstract void fillDslExchangeCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity);

    public AbstractDslResolver() {
        Set<DSLTypeEnum> dslTypeEnums = supportedScenarios();
        dslTypeEnums.forEach(item -> PATTERN_CACHE.put(item.getRegexExpression(), Pattern.compile(item.getRegexExpression())));
    }

    /**
     * 获取DSL的类型
     *
     * @return 详见DSLTypeEnum
     */
    public DSLTypeEnum getDslType() {
        Set<DSLTypeEnum> dslTypeEnums = supportedScenarios();
        AssertUtil.isTrue(dslTypeEnums.size() == 1, "", "supported dsl must be 1");
        return dslTypeEnums.toArray(new DSLTypeEnum[0])[0];
    }

    /**
     * @param dsl 原始DSL表达式
     * @return 获取正则解析后的matcher对象
     */
    public Matcher getMatherByDSL(String dsl) {
        DSLTypeEnum dslType = getDslType();
        String regexExpression = dslType.getRegexExpression();
        Pattern compileRegex = PATTERN_CACHE.get(regexExpression);
        Preconditions.checkArgument(compileRegex != null, dsl + " is illegal,not match regex");
        Matcher matcher = compileRegex.matcher(dsl);
        Preconditions.checkArgument(matcher.find(), dsl + " is illegal,not match regex");
        return matcher;
    }

    /**
     * 获取周期相对信息类型
     *
     * @param matherByDSL 原始DSL表达式
     * @return 详见-RoundTypeEnum
     */
    public RoundTypeEnum getRelativeInfoType(Matcher matherByDSL) {
        String relativeInfoType = matherByDSL.group(DslKeywordEnum.RELATIVE_INFO_TYPE.getDesc());
        AssertUtil.isTrue(StringUtils.isNotBlank(relativeInfoType), "", "");
        return RoundTypeEnum.getEnumByDesc(relativeInfoType);
    }

    /**
     * 获取周期相对信息的偏移方向
     *
     * @param matherByDSL 原始DSL表达式
     * @return 相对信息的偏移方向，详见 - RoundSignTypeEnum，+/-
     */
    public RoundSignTypeEnum getRelativeInfoSign(Matcher matherByDSL) {
        try {
            String relativeInfoSign = matherByDSL.group(DslKeywordEnum.RELATIVE_INFO_SIGN.getDesc());
            return RoundSignTypeEnum.getRoundSignEnumBySign(relativeInfoSign);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取周期相对信息的偏移值
     *
     * @param matherByDSL 原始DSL表达式
     * @return eg:数字
     */
    public String getRelativeInfo(Matcher matherByDSL) {
        try {
            return matherByDSL.group(DslKeywordEnum.RELATIVE_INFO.getDesc());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取周期绝对信息的偏移方向
     *
     * @param matherByDSL 原始DSL表达式
     * @return 详见 - RoundSignTypeEnum，+/-
     */
    public RoundSignTypeEnum getDefiniteInfoSign(Matcher matherByDSL) {
        String definiteInfoSign = matherByDSL.group(DslKeywordEnum.DEFINITE_INFO_SIGN.getDesc());
        return StringUtils.isBlank(definiteInfoSign) ? RoundSignTypeEnum.ADD : RoundSignTypeEnum.getRoundSignEnumBySign(definiteInfoSign);
    }

    /**
     * 获取周期绝对信息的偏移值
     *
     * @param matherByDSL 原始DSL表达式
     * @return eg:1、1WD
     */
    public String getDefiniteInfo(Matcher matherByDSL) {
        return matherByDSL.group(DslKeywordEnum.DEFINITE_INFO.getDesc());
    }

    /**
     * 填充是否考虑节假日信息
     *
     * @param relativeInfo 相对信息的偏移值
     * @param dslEntity    DSL领域实体
     */
    public void fillIsConsiderHoliday(String relativeInfo, DSLEntity dslEntity) {
        if (relativeInfo.contains(RoundDefiniteTypeEnum.WD.name())) {
            dslEntity.setIsConsiderHoliday(Boolean.TRUE);
        } else {
            dslEntity.setIsConsiderHoliday(Boolean.FALSE);
        }
    }

}