package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleDetailQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementScheduleDetailDao;
import com.payermax.channel.inst.center.app.service.InstRequirementScheduleDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 集成需求单排期详情Service实现
 *
 * <AUTHOR>
 * @date 2022/6/15 17:48
 */
@Service
public class InstRequirementScheduleDetailServiceImpl implements InstRequirementScheduleDetailService {

    @Autowired
    private InstRequirementScheduleDetailDao instRequirementScheduleDetailDao;

    @Override
    public int add(InstRequirementScheduleDetailEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        return instRequirementScheduleDetailDao.insert(record);
    }

    @Override
    public int update(InstRequirementScheduleDetailEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        return instRequirementScheduleDetailDao.updateByPrimaryKey(record);
    }

    @Override
    public List<InstRequirementScheduleDetailEntity> queryList(InstRequirementScheduleDetailQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");
        return instRequirementScheduleDetailDao.selectAll(queryEntity);
    }
}
