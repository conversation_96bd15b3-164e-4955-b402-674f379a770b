package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstNdaEntity;

/**
 * NDA相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/13 22:32
 */
public interface InstNdaService {

    /**
     * 查询机构NDA信息
     *
     * @return
     */
    InstNdaEntity query(InstNdaEntity instNdaEntity);

    /**
     * 保存机构NDA信息
     *
     * @return
     */
    int save(InstNdaEntity instNdaEntity);


}
