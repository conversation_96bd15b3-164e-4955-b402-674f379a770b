package com.payermax.channel.inst.center.app.model.contract;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.vo.contract.ContractProductBusinessKey;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/12 8:56 PM
 *
 * 单份机构合约（签约主体，机构标识，签约业务类型 构成唯一机构合约）的合约条款处理
 **/
@Data
public class ContractProcessByExcelContext {
    /**
     * ###############################
     * ###############################
     * excel原始内容 start
     * ###############################
     * ###############################
     */
    private Map<ContractProductBusinessKey, List<InstContractFeeRowItemDTO>>    standardizedProductGroupFeeMap = new HashMap<>();
    private Map<ContractProductBusinessKey, List<InstContractFeeRowItemDTO>>    unStandardizedProductGroupFeeMap = new HashMap<>();

    private Map<ContractProductBusinessKey, List<InstContractSettleRowItemDTO>> standardizedProductGroupSettleMap = new HashMap<>();
    private Map<ContractProductBusinessKey, List<InstContractSettleRowItemDTO>> unStandardizedProductGroupSettleMap = new HashMap<>();


    /**
     * 当前处理的合同 所属机构标识
     */
    private String instCode;
    /**
     * 当前处理的合同 所属机构的签约主体
     */
    private String contractEntity;
    /**
     * 业务类型，代收/代付
     */
    private ContractBusinessTypeEnum businessTypeEnum;
    /**
     * 当前语言环境
     */
    private LangEnum langEnum;
    /**
     * 本次变动开始生效时间
     */
    private LocalDateTime effectStartTime;


    /**
     * ###############################
     * ###############################
     * 校验结果内容 start
     * ###############################
     * ###############################
     */
    private ExportErrorInfo feeErrorInfo;

    private ExportErrorInfo settleErrorInfo;

    private ExportErrorInfo accumulateFeeErrorInfo;


    /**
     * 正常处理流程，处理之后的新版本合约内容
     *
     */
    private Map<ContractProductBusinessKey, List<InstContractFeeItem>>          instContractFeeMap = new HashMap<>();

    private Map<ContractProductBusinessKey, List<InstContractSettlementItem>>   instContractSettlementMap = new HashMap<>();


    public ContractProcessByExcelContext(String instCode, String contractEntity, ContractBusinessTypeEnum businessTypeEnum,
                                         List<InstContractFeeRowItemDTO> feeParseInfo,
                                         List<InstContractSettleRowItemDTO> settleParseInfo, LangEnum langEnum) {

        // 费用信息处理
        for (InstContractFeeRowItemDTO feeItem : feeParseInfo) {
            boolean isStandardized = feeItem.isStandardized();
            ContractProductBusinessKey businessKey = new ContractProductBusinessKey(isStandardized, feeItem.getInstProductName(),
                    feeItem.getPaymentMethodType(), feeItem.getTargetOrg(), feeItem.getCardOrg());

            // 标准化的 和 未经标准化的，分类处理
            if (isStandardized) {
                List<InstContractFeeRowItemDTO> standardizedList
                        = standardizedProductGroupFeeMap.computeIfAbsent(businessKey, a -> Lists.newArrayList());
                standardizedList.add(feeItem);
            } else {
                List<InstContractFeeRowItemDTO> unStandardizedList
                        = unStandardizedProductGroupFeeMap.computeIfAbsent(businessKey, a -> Lists.newArrayList());
                unStandardizedList.add(feeItem);
            }
        }

        // 结算信息处理
        if (CollectionUtils.isNotEmpty(settleParseInfo)){
            for (InstContractSettleRowItemDTO settleItem : settleParseInfo) {
                boolean isStandardized = settleItem.isStandardized();
                ContractProductBusinessKey businessKey = new ContractProductBusinessKey(isStandardized, settleItem.getInstProductName(),
                        settleItem.getPaymentMethodType(), settleItem.getTargetOrg(), settleItem.getCardOrg());

                // 标准化的 和 未经标准化的，分类处理
                if (isStandardized) {
                    List<InstContractSettleRowItemDTO> standardizedList
                            = standardizedProductGroupSettleMap.computeIfAbsent(businessKey, a -> Lists.newArrayList());
                    standardizedList.add(settleItem);
                } else {
                    List<InstContractSettleRowItemDTO> unStandardizedList
                            = unStandardizedProductGroupSettleMap.computeIfAbsent(businessKey, a -> Lists.newArrayList());
                    unStandardizedList.add(settleItem);
                }
            }
        }

        this.instCode = instCode;
        this.contractEntity = contractEntity;
        this.businessTypeEnum = businessTypeEnum;
        this.langEnum = langEnum;
    }

    /**
     * 将构造完成的 合同费用信息加入 费用结果中
     */
    public void addContractFeeItemsForInstProduct(ContractProductBusinessKey originProduct, List<InstContractFeeItem> feeItemList) {

        instContractFeeMap.put(originProduct, feeItemList);
    }

    /**
     * 将构造完成的 合同结算信息加入 费用结果中
     */
    public void addContractSettlementItemsForInstProduct(ContractProductBusinessKey originProduct, List<InstContractSettlementItem> settlementItemList) {

        instContractSettlementMap.put(originProduct, settlementItemList);
    }

    public boolean hasBusinessError() {
        return (feeErrorInfo != null && feeErrorInfo.isHasErrors()) ||
                (settleErrorInfo != null && settleErrorInfo.isHasErrors()) ||
                (accumulateFeeErrorInfo != null && accumulateFeeErrorInfo.isHasErrors());
    }
}
