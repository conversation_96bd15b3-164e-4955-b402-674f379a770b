package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.app.dto.InstContractAccumulateRowItemDTO;
import com.payermax.channel.inst.center.app.valid.annon.impl.CompanyEntityValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.InstCodeValid;
import com.payermax.channel.inst.center.common.constrains.InterfaceAnnotation;
import com.payermax.channel.inst.center.common.constrains.NumberAnnotation;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * InstContractAccumulateRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class InstContractAccumulateRowItemZhDTO extends InstContractAccumulateRowItemDTO {

    @ExcelProperty({ "", "录入" })
    private String enteredBy;

    @ExcelProperty({ "", "机构legal name" })
    private String instLegalName;

    @NotEmpty(message = "我方签约主体编码不能为空")
    @InterfaceAnnotation(clazz = CompanyEntityValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "我方签约主体编码" })
    private String contractEntity;

    @NotEmpty(message = "机构简称不能为空")
    @InterfaceAnnotation(clazz = InstCodeValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "机构简称" })
    private String instCode;

    @ExcelProperty({ "", "机构产品类型" })
    private String instProductType;

    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty({ "", "机构产品名称" })
    private String instProductName;

    @ExcelProperty({ "", "支付方式类型" })
    private String paymentMethodType;

    @ExcelProperty({ "", "目标机构" })
    private String targetOrg;

    @ExcelProperty({ "", "卡组" })
    private String cardOrg;

    @ExcelProperty({ "", "支付币种" })
    private String payCurrency;

    @ExcelProperty({ "", "卡片类型" })
    private String cardType;

    @ExcelProperty({ "", "发卡国家" })
    private String cardIssueCountry;

    @ExcelProperty({ "", "机构MID" })
    private String originMid;

    @ExcelProperty({ "", "机构MCC" })
    private String originMcc;

    @ExcelProperty({ "", "累计周期" })
    private String accumulateCycle;

    @ExcelProperty({ "", "累计类型" })
    private String accumulateType;

    @ExcelProperty({ "", "累计生效方法" })
    private String accumulateEffectMethod;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "累计阶梯上限（不含）" })
    private String accumulateStepUpper;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "累计阶梯下限（含）" })
    private String accumulateStepLower;

    @ExcelProperty({ "", "阶梯单位" })
    private String accumulateStepUnit;

    @ExcelProperty({ "", "计费方式" })
    private String calculateType;

    @ExcelProperty({ "", "算费时点" })
    private String feeCalculateTime;

    @ExcelProperty({ "", "算费币种" })
    private String calculateFeeCurrency;

    @ExcelProperty({ "", "扣费币种" })
    private String chargeCurrency;

    @NumberAnnotation()
    @ExcelProperty({ "", "保留小数位数" })
    private String roundingScale;

    @ExcelProperty({ "", "保留小数算法" })
    private String roundingMode;

    @ExcelProperty({ "", "计费基准" })
    private String feeBasementMode;

    @ExcelProperty({ "", "单笔比例" })
    private String singleRate;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "最低收费" })
    private String minCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "封顶收费" })
    private String maxCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "单笔固定金额" })
    private String singleFixedAmount;

    @ExcelProperty({ "", "单笔固定币种" })
    private String singleFixedCurrency;
}
