package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity;

import java.util.List;

/**
 * 机构联系人信息相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/17 22:32
 */
public interface InstContactService {

    /**
     * 查询机构联系人信息
     *
     * @return
     */
    List<InstContactEntity> query(InstContactEntity instContactEntity);

    /**
     * 查询机构联系人信息
     *
     * @return
     */
    List<InstContactEntity> query(List<Long> instIds);

    /**
     * 保存机构联系人信息
     *
     * @return
     */
    int save(InstContactEntity instContactEntity);


}
