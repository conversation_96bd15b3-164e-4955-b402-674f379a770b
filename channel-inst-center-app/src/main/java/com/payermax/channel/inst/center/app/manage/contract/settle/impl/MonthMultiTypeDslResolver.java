package com.payermax.channel.inst.center.app.manage.contract.settle.impl;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.manage.contract.settle.AbstractDslSimpleTypeResolver;
import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;

/**
 * <AUTHOR> 2023/6/21  2:32 PM
 */
@Component
@Slf4j
public class MonthMultiTypeDslResolver extends AbstractDslSimpleTypeResolver {

    @Override
    public Set<DSLTypeEnum> supportedScenarios() {
        return Sets.newHashSet(DSLTypeEnum.MONTH_MULTI);
    }

    @Override
    public RoundTypeEnum getRoundType() {
        return RoundTypeEnum.MONTH_MULTI;
    }

    @Override
    public void fillDslTransactionStartCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String startDateDSL = settleDate.getTransactionStartDate();
        fillTransactionCycleType(roundDSLEntity, startDateDSL);
        roundDSLEntity.setTransactionCycleStart(0);
    }

    @Override
    public void fillDslTransactionEndCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        String endDateDSL = settleDate.getTransactionEndDate();
        fillTransactionCycleType(roundDSLEntity, endDateDSL);
        roundDSLEntity.setTransactionCycleEnd(0);
    }

    @Override
    public void fillDslSettleCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        DSLEntity dslEntity = buildSimpleDayDslEntity(settleDate.getBillDate(), roundDSLEntity);
        roundDSLEntity.setBillDateDsl(dslEntity);
    }

    @Override
    public void fillDslPaymentCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        DSLEntity dslEntity = buildSimpleDayDslEntity(settleDate.getPaymentDate(), roundDSLEntity);
        roundDSLEntity.setPaymentDateDsl(dslEntity);
    }

    @Override
    public void fillDslExchangeCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        DSLEntity dslEntity = buildSimpleDayDslEntity(settleDate.getExchangeDate(), roundDSLEntity);
        roundDSLEntity.setExchangeDateDsl(dslEntity);
    }


    @Override
    public void fillDslArriveCycle(SettleDate settleDate, SettleRoundDSLEntity roundDSLEntity) {
        DSLEntity dslEntity = buildSimpleDayDslEntity(settleDate.getArriveDate(), roundDSLEntity);
        roundDSLEntity.setArrivedDateDsl(dslEntity);
    }

    private DSLEntity buildSimpleDayDslEntity(String dataDsl, SettleRoundDSLEntity roundDSLEntity) {

        Matcher matherByDSL = getMatherByDSL(dataDsl);

        // 1、MM
        RoundTypeEnum relativeInfoType = getRelativeInfoType(matherByDSL);
        AssertUtil.isTrue(Objects.nonNull(relativeInfoType) && RoundTypeEnum.MONTH_MULTI.equals(relativeInfoType)
                , "ERROR", "must be MONTH_MULTI!==" + dataDsl);


        String definiteInfo = getDefiniteInfo(matherByDSL);

        DSLEntity dslEntity = new DSLEntity();
        dslEntity.setRoundRelativeType(relativeInfoType);
        dslEntity.setDefiniteOffset(definiteInfo);

        // 填充是否考虑节假日信息
        fillIsConsiderHoliday(definiteInfo, dslEntity);
        return dslEntity;
    }

    private void fillTransactionCycleType(SettleRoundDSLEntity roundDSLEntity, String startDateDSL) {
        Matcher matherByDSL = getMatherByDSL(startDateDSL);
        RoundTypeEnum relativeInfoType = getRelativeInfoType(matherByDSL);
        AssertUtil.isTrue(RoundTypeEnum.MONTH.equals(relativeInfoType), "", "");
        roundDSLEntity.setTransactionCycleType(relativeInfoType);
    }

}