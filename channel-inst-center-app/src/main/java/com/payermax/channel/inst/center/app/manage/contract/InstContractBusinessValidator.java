package com.payermax.channel.inst.center.app.manage.contract;

import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOriginProductRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstProcessDockRepository;
import com.payermax.common.lang.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstContractBusinessValidator{

    private final InstContractBaseInfoRepository instContractBaseInfoRepository;
    private final InstContractOriginProductRepository contractOriginProductRepository;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final InstProcessDockRepository processDockRepository;

    /**
     * FX 批量修改预检查, 返回业务唯一键
     */
    public String fxBatchModifyPreCheck(List<InstContractFeeItemPO> feeItemList) {

        // 校验是否单个机构批量修改
        List<String> originProductNoList = feeItemList.stream().map(InstContractFeeItemPO::getInstOriginProductNo).collect(Collectors.toList());
        InstContractBaseInfoPO baseInfo = isMultiContract(originProductNoList);

        // 检查是否存在版本升级审批流程
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(originProductNoList), "ERROR", "originProductNoList is empty");
        contractVersionUpgradeProcessCheck(originProductNoList.get(0)); // CHECKED 校验机构版本升级流程，仅需使用第一个原始产品号即可

        // 检查是否存在审批流程
        String businessKey = ListUtils.quickBuildUnderlineKey(baseInfo.getInstProductType(), baseInfo.getInstCode(), baseInfo.getContractEntity(), feeItemList.get(0).getPayCurrency()); // CHECKED 使用第一个变更信息的支付币种

        // 校验流程唯一性
        AssertUtil.isTrue(processDockRepository.isProcessExistByBusinessIdAndStatus(businessKey, InstProcessStatusEnum.PROCESSING),"error","已存在审核中的批量修改流程");

        return businessKey;
    }

    /**
     * 校验是否多份合约修改
     */
    public InstContractBaseInfoPO isMultiContract(List<String> originProductNoList) {
        // 原始产品编号去重
        originProductNoList = originProductNoList.stream().distinct().collect(Collectors.toList());
        // 查询原始产品列表
        List<InstContractOriginProductPO> originProductList = originProductNoList.stream().map(contractOriginProductRepository::queryByOriginProductNo).collect(Collectors.toList());
        // 查询合同列表并校验是否同一合约
        Map<String, InstContractBaseInfoPO> baseInfoMap = originProductList.stream().map(product -> instContractBaseInfoRepository.queryOneByNo(product.getContractNo()))
                .collect(Collectors.toMap(InstContractBaseInfoPO::getContractNo, Function.identity()
                        , (v1, v2) -> v1));
        AssertUtil.isTrue(baseInfoMap.size()==1 , "ERROR", "仅支持单份合约批量修改");
        return baseInfoMap.entrySet().stream().findFirst().get().getValue();
    }


    /**
     * 合约子项变更流程检查
     * 用于检查子项变更时是否存在合约版本变更
     * 费用、外汇、结算
     */
    public InstContractBaseInfoPO contractVersionUpgradeProcessCheck(String instOriginProductNo){
        // 查询原始产品
        InstContractOriginProductPO originProduct = contractOriginProductRepository.queryByOriginProductNo(instOriginProductNo);
        // 查询合同
        InstContractBaseInfoPO contractBaseInfo = instContractBaseInfoRepository.queryOneByNo(originProduct.getContractNo());
        AssertUtil.isTrue(Objects.nonNull(contractBaseInfo), "ERROR", "合同不存在");
        List<InstBusinessDraftPO> processingList = businessDraftRepository.getProcessingList(InstBusinessDraftPO.builder()
                .businessKey(contractBaseInfo.getContractNo())
                .businessType(BusinessTypeEnum.INST_CENTER.name())
                .moduleName(OperateModuleEnum.INST_CENTER_CONTRACT_VERSION_MANAGER.name())
                .operateType(OperateTypeEnum.UPDATE.name())
                .build());
        AssertUtil.isTrue(CollectionUtils.isEmpty(processingList), "ERROR"
                , String.format("当前有合约版本变更流程正在处理中，流程发起人【%s】"
                        , processingList.stream().map(InstBusinessDraftPO::getOwner).collect(Collectors.joining(CommonConstants.COMMA))));
        return contractBaseInfo;
    }
}
