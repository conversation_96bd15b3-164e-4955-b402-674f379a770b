package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.lang.UUID;
import com.payermax.channel.inst.center.common.enums.OmcEnum;
import com.payermax.channel.inst.center.common.exception.CustomException;
import com.payermax.channel.inst.center.infrastructure.entity.AttachEntity;
import com.payermax.channel.inst.center.infrastructure.client.FintechBaseClientProxy;
import com.payermax.channel.inst.center.app.manage.AttachManage;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.response.AttachVO;
import com.payermax.channel.inst.center.app.service.AttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件附件Manage实现
 *
 * <AUTHOR>
 * @date 2022/5/15 17:32
 */
@Service
public class AttachManageImpl implements AttachManage {

    @Autowired
    private AttachService attachService;

    @Autowired
    private FintechBaseClientProxy baseClientProxy;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public List<AttachVO> queryAttachs(List<Long> attachIdList) {
        List<AttachVO> attachVOList = new ArrayList<>();
        // 查询文件附件
        List<AttachEntity> attachEntityList = attachService.getByIdList(attachIdList);
        // 响应转换
        attachEntityList.forEach(entity -> {
            AttachVO attachVO = respVoAssembler.toAttachVo(entity);
            attachVOList.add(attachVO);
        });
        return attachVOList;
    }

    @Override
    public AttachVO save(MultipartFile file) {
        AttachEntity attachEntity = new AttachEntity();
        try {
            // 文件原始名称
            String fileName = file.getOriginalFilename();
            attachEntity.setAttachName(fileName);
            // 文件名称加时间戳，放在文件名重复
            fileName = String.format("%s_%s", UUID.randomUUID().toString().replaceAll("-", "").substring(0, 16), fileName);
            // 上传到S3
            String url = baseClientProxy.uploadToAws(null, fileName, file.getBytes());
            // 保存附件
            attachEntity.setAttachUrl(url);
            attachService.save(attachEntity);
        } catch (Exception e) {
            throw new CustomException(OmcEnum.SysEnum.FAILED.getCode(), e.getMessage());
        }
        return respVoAssembler.toAttachVo(attachEntity);
    }
}
