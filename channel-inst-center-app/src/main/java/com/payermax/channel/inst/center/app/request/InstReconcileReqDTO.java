package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 机构对账DTO
 *
 * <AUTHOR>
 * @date 2022/6/4 17:33
 */
@Data
public class InstReconcileReqDTO implements Serializable {

    private static final long serialVersionUID = 7771301765547808302L;

    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String invoiceProvider;

    private String reconcileMethod;

    private String reconcileTemplate;

}
