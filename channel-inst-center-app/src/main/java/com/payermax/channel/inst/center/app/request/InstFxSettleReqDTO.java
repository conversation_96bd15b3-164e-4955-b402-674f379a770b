package com.payermax.channel.inst.center.app.request;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-20 11:05 AM
 */
@Getter
@Setter
public class InstFxSettleReqDTO {

    /**
     * 渠道编码
     */
    private String chanelCode;

    /**
     *渠道商户编码
     */
    private String channelMerchantCode;

    /**
     * 支付方式编码
     */
    private String channelMethodCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 卡组织
     */
    private String cardOrg;

    /**
     * 支付币种
     */
    private String paymentCcy;
}
