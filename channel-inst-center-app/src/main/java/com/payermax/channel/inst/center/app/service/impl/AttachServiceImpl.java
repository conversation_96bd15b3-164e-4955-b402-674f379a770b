package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.AttachEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.AttachQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.AttachDao;
import com.payermax.channel.inst.center.app.service.AttachService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件附件Service实现
 *
 * <AUTHOR>
 * @date 2022/5/15 20:54
 */
@Service
public class AttachServiceImpl implements AttachService {

    @Autowired
    private AttachDao attachDao;

    @Override
    public int save(AttachEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        return attachDao.insert(record);
    }

    @Override
    public List<AttachEntity> getByIdList(List<Long> idList) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(idList), "param idList is mandatory");

        AttachQueryEntity queryEntity = new AttachQueryEntity();
        queryEntity.setIdList(idList);
        return attachDao.selectAll(queryEntity);
    }
}
