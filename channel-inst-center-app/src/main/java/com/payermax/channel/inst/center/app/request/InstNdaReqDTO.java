package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstNdaReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/15 21:17
 * @Version 1.0
 */
@Data
public class InstNdaReqDTO implements Serializable {

    private static final long serialVersionUID = -2107223416256618550L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 机构标识
     */
    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    /**
     * 申请单号
     */
    @ApiModelProperty(notes = "申请单号")
    private String applyNo;

    /**
     * 我司主体
     */
    @ApiModelProperty(notes = "我司主体")
    private String shareitEntity;

    /**
     * NDA版本
     */
    @ApiModelProperty(notes = "版本")
    private String version;

    /**
     * NDA原始文件
     */
    @ApiModelProperty(notes = "NDA原始文件")
    private String ndaAttachId;

    /**
     * 机构是否已签署
     */
    @ApiModelProperty(notes = "机构是否已签署")
    private String instSignFlag;

    /**
     * 我司是否已签署
     */
    @ApiModelProperty(notes = "我司是否已签署")
    private String shareitSignFlag;

    /**
     * 双签文件
     */
    @ApiModelProperty(notes = "双签文件")
    private String pairSignAttachId;

    /**
     * 机构单签文件
     */
    @ApiModelProperty(notes = "机构单签文件")
    private String instSignAttachId;

    /**
     * 我司单签文件
     */
    @ApiModelProperty(notes = "我司单签文件")
    private String shareitSignAttachId;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 状态
     */
    @ApiModelProperty(notes = "状态")
    private String status;
}
