package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBrandDao;
import com.payermax.channel.inst.center.app.service.InstBrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstBrandServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 13:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstBrandServiceImpl implements InstBrandService {

    @Autowired
    private InstBrandDao instBrandDao;

    @Override
    public List<InstBrandEntity> query(InstBrandEntity instBrandEntity) {
        Preconditions.checkArgument(instBrandEntity != null, "param instBrandEntity is mandatory");
        List<InstBrandEntity> instBrandEntityList = instBrandDao.selectAll(instBrandEntity);
        if (CollectionUtils.isNotEmpty(instBrandEntityList)) {
            return instBrandEntityList;
        }
        return null;
    }

    @Override
    public List<InstBrandEntity> queryByBdIds(List<String> bdIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(bdIds), "param bdIds is mandatory");
        List<InstBrandEntity> instBrandEntityList = instBrandDao.selectByBdIds(bdIds);
        if (CollectionUtils.isNotEmpty(instBrandEntityList)) {
            return instBrandEntityList;
        }
        return null;
    }

    @Override
    public int save(InstBrandEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getBrandId() == null) {
            // 新增
            result = instBrandDao.insert(record);
        } else {
            // 更新
            result = instBrandDao.updateByPrimaryKey(record);
        }
        return result;
    }
}
