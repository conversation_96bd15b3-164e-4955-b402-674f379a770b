package com.payermax.channel.inst.center.app.manage.account.processHandler;

import com.payermax.channel.inst.center.app.assembler.domain.InstBankAccountAssembler;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.account.InstBankAccount;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBankAccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstAccountCommonUtils {

    private final InstBankAccountRepository bankAccountRepository;


    /**
     * 代理银行账户优先级校验
     */
    public void bankAccountPriorityCheck(InstBankAccount instBankAccount) {
        InstBankAccount query = new InstBankAccount().setInstId(instBankAccount.getInstId());
        List<InstBankAccountPO> instbankList = bankAccountRepository.queryByConditions(InstBankAccountAssembler.INSTANCE.domain2Po(query));
        Map<String, List<InstBankAccountPO>> groupByOrder = instbankList.stream()
                .filter(item -> !item.getId().equals(instBankAccount.getId()))
                .collect(Collectors.groupingBy(InstBankAccountPO::getRechargeUseOrder));

        String order = instBankAccount.getRechargeUseOrder();
        AssertUtil.isTrue(!groupByOrder.containsKey(order), ErrorCodeEnum.INST_BANK_ACCOUNT_VALIDATE_ERROR.getCode(),
                String.format("账户优先级重复，请重新设置，当前已存在优先级【%s】", String.join(CommonConstants.COMMA, groupByOrder.keySet())));
    }
}
