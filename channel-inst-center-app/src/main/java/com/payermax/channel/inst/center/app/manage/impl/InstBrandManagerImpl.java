package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.app.manage.InstBrandManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstBrandReqDTO;
import com.payermax.channel.inst.center.app.response.InstBrandVO;
import com.payermax.channel.inst.center.app.service.InstBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstBrandManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 10:28
 * @Version 1.0
 */
@Service
@Deprecated
public class InstBrandManagerImpl implements InstBrandManager {

    @Autowired
    private InstBrandService instBrandService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public List<InstBrandVO> query(InstBrandReqDTO instBrandReqDTO) {
        //请求转换
        InstBrandEntity instBrandEntity = reqDtoAssembler.toInstBrandEntity(instBrandReqDTO);
        //响应转换
        List<InstBrandEntity> instBrandEntityList = instBrandService.query(instBrandEntity);
        return respVoAssembler.toInstBrandVo(instBrandEntityList);
    }

    @Override
    public int save(InstBrandReqDTO instBrandReqDTO) {
        //请求转换
        InstBrandEntity instBrandEntity = reqDtoAssembler.toInstBrandEntity(instBrandReqDTO);
        return instBrandService.save(instBrandEntity);

    }
}
