package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.excel.util.StringUtils;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.manage.InstAccountManager;
import com.payermax.channel.inst.center.app.request.InstAccountQueryReqDTO;
import com.payermax.channel.inst.center.app.request.InstAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstAccountQueryVO;
import com.payermax.channel.inst.center.app.response.InstAccountVO;
import com.payermax.channel.inst.center.app.service.InstAccountService;
import com.payermax.channel.inst.center.app.service.InstRequirementOrderService;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity;
import com.payermax.common.lang.util.AssertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstAccountManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 11:07
 */
@Service
@Deprecated
public class InstAccountManagerImpl implements InstAccountManager {

    @Autowired
    private InstRequirementOrderService instRequirementOrderService;

    @Autowired
    private InstAccountService instAccountService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public Integer saveAccount(InstAccountQueryReqDTO instAccountQueryReqDTO) {
        AssertUtil.isTrue(false, "ERROR", "方法已废弃，不可调用");
        return null;
        // List<InstAccountReqDTO> instAccountReqDTOs = instAccountQueryReqDTO.getInstAccountReqDTOS();
        // if(CollectionUtils.isEmpty(instAccountReqDTOs)){
        //     return 0;
        // }
        // List<Long> ids = instAccountReqDTOs.stream().filter(obj -> obj.getId() != null).map(InstAccountReqDTO::getId).collect(Collectors.toList());
        // if(!CollectionUtils.isEmpty(ids)){
        //     InstAccountReqDTO instAccountReqDTO = instAccountReqDTOs.get(0); //NO_CHECK 方法未被调用
        //     instAccountService.delete(instAccountReqDTO.getRequirementOrderId(),instAccountReqDTO.getInstId(),instAccountReqDTO.getEnv(),ids);
        // }
        // AtomicInteger count = new AtomicInteger();
        // instAccountReqDTOs.forEach((accountReq) -> {
        //     InstAccountEntity accountEntity = reqDtoAssembler.toInstAccountEntity(accountReq);
        //     Integer result = instAccountService.save(accountEntity);
        //     count.getAndAdd(result);
        // });
        // InstRequirementOrderEntity record = new InstRequirementOrderEntity();
        // record.setId(instAccountReqDTOs.get(0).getRequirementOrderId()); //NO_CHECK 方法未被调用
        // record.setProdPlatformUrl(instAccountQueryReqDTO.getProdPlatformUrl());
        // instRequirementOrderService.save(record);
        // return count.get();
    }

    @Override
    public InstAccountQueryVO queryAccount(InstAccountQueryReqDTO instAccountQueryReqDTO) {
        InstAccountEntity entity = new InstAccountEntity();
        entity.setRequirementOrderId(instAccountQueryReqDTO.getRequirementOrderId());
        entity.setInstId(instAccountQueryReqDTO.getInstId());
        List<InstAccountEntity> instAccountEntities = instAccountService.queryList(entity);
        List<InstAccountVO> instAccountVOS = respVoAssembler.toInstAccountVOList(instAccountEntities);

        InstRequirementOrderEntity instRequirementOrderEntity = instRequirementOrderService.get(instAccountQueryReqDTO.getApplyNo());

        InstAccountQueryVO instAccountQueryVO = new InstAccountQueryVO();
        instAccountQueryVO.setProdPlatformUrl(instRequirementOrderEntity !=null ? instRequirementOrderEntity.getProdPlatformUrl() : StringUtils.EMPTY);
        instAccountQueryVO.setInstAccountVOS(instAccountVOS);
        return instAccountQueryVO;
    }

    @Override
    public List<InstAccountVO> queryMids(InstAccountReqDTO instAccountReqDTO) {
        InstAccountEntity entity = reqDtoAssembler.toInstAccountEntity(instAccountReqDTO);
        List<InstAccountEntity> instAccountEntities = instAccountService.queryList(entity);
        List<InstAccountVO> instAccountVOS = respVoAssembler.toInstAccountVOList(instAccountEntities);
        return instAccountVOS;
    }
}
