package com.payermax.channel.inst.center.app.dto.workflow;

import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/2/20
 * @DESC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InstContractSettleFormMsgDTO extends InstContractBaseFormMsgDTO {


    /**
     * settleItem 编号
     */
    private String settleItemNo;

    /**
     * 原始信息
     */
    private InstContractSettlementItemPO originData;


    /**
     * 修改后信息
     */
    private InstContractSettlementItemPO modifiedData;
}
