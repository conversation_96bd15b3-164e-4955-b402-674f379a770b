package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstBankAccountEntity;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;

import java.util.List;

/**
 * 机构银行卡信息相关服务Service
 *
 * <AUTHOR>
 * @date 2022/5/17 14:32
 */
public interface InstBankAccountService {

    /**
     * 基于指定条件，查询机构银行账户
     *
     * @param queryCondition 查询条件
     * @return
     */
    List<InstBankAccountEntity> queryByCondition(InstBankAccountEntity queryCondition);


    /**
     * 保存机构银行卡信息
     *
     * @return
     */
    int save(InstBankAccountEntity instBankAccountEntity);


    /**
     * 根据账户名称模糊查询银行账户
     * @param accountAlias 账户名称
     */
    List<InstBankAccountPO> queryBankByAccountAlias(String accountAlias);


}
