package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstProductCapabilityService;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstProductCapabilityQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductCapabilityDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstProductCapabilityServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 20:41
 */
@Service
@Slf4j
public class InstProductCapabilityServiceImpl implements InstProductCapabilityService {

    @Autowired
    private InstProductCapabilityDao instProductCapabilityDao;

    @Override
    public int save(InstProductCapabilityEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(record.getInstProductCode()), "param instProductCode is mandatory");

        //TODO 产品能力编码生成规则
        record.setCapabilityCode("PC" + System.currentTimeMillis());
        int result = instProductCapabilityDao.insert(record);

        return result;
    }

    @Override
    public int saveBatch(List<InstProductCapabilityEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        int result = instProductCapabilityDao.insertBatch(records);
        return result;
    }

    @Override
    public int update(InstProductCapabilityEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(record.getInstProductCode()), "param instProductCode is mandatory");

        return instProductCapabilityDao.updateByPrimaryKey(record);
    }

    @Override
    public List<InstProductCapabilityEntity> getList(InstProductCapabilityQueryEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");

        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityDao.selectAll(queryEntity);
        return instProductCapabilityEntities;
    }

    @Override
    public List<InstProductCapabilityEntity> getByProductCodes(List<String> productCodes) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(productCodes), "param productCodes is mandatory");

        InstProductCapabilityQueryEntity queryEntity = new InstProductCapabilityQueryEntity();
        queryEntity.setProductCodes(productCodes);
        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityDao.selectAll(queryEntity);
        return instProductCapabilityEntities;
    }

    @Override
    public List<InstProductCapabilityEntity> getByProductCodeAndVersion(String productCode, String version) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(version), "param version is mandatory");
        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityDao.selectByProductCodeAndVersion(productCode, version);
        return instProductCapabilityEntities;
    }

    @Override
    public List<InstProductCapabilityEntity> getByProductCodeAndCapabilityCodes(String productCode, List<String> capabilityCodes) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityDao.selectByProductCodeAndCapabilityCode(productCode, capabilityCodes);
        return instProductCapabilityEntities;
    }

    @Override
    public List<InstProductCapabilityEntity> getByCapabilityCodes(List<String> capabilityCodes) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        List<InstProductCapabilityEntity> instProductCapabilityEntities = instProductCapabilityDao.selectByCapabilityCodes(capabilityCodes);
        return instProductCapabilityEntities;
    }

    @Override
    public int deleteByProductCode(String productCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        int result = instProductCapabilityDao.deleteByProductCode(productCode);
        return result;
    }

    @Override
    public int deleteByProductCodeAndVersion(String productCode, String version) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
//        Preconditions.checkArgument(StringUtils.isNotBlank(version), "param version is mandatory");
        int result = instProductCapabilityDao.deleteByProductCodeAndVersion(productCode, version);
        return result;
    }

    @Override
    public int deleteByProductCodeAndCapabilityCode(String productCode, List<String> capabilityCodes) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        int result = instProductCapabilityDao.deleteByProductCodeAndCapabilityCode(productCode, capabilityCodes);
        return result;
    }
}
