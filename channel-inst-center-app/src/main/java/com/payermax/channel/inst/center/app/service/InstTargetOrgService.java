package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 13:04
 */
public interface InstTargetOrgService {
    /**
     * 保存目标机构
     *
     * @param record
     * @return
     */
    int save(InstTargetOrgEntity record);

    /**
     * 保存目标机构
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstTargetOrgEntity> records);

    /**
     * 根据产品编码查询产品能力
     *
     * @param record
     * @return
     */
    List<InstTargetOrgEntity> getByEntity(InstTargetOrgEntity record);
}
