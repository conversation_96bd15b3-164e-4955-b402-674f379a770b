package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstAuditDataReqDTO;
import com.payermax.channel.inst.center.app.response.InstAuditDataVO;

/**
 * 审核资料相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
public interface InstAuditDataManager {

    /**
     * 查询审核信息
     *
     * @return
     */
    InstAuditDataVO query(InstAuditDataReqDTO instAuditDataReqDTO);

    /**
     * 保存审核信息
     *
     * @return
     */
    int save(InstAuditDataReqDTO instAuditDataReqDTO);
}
