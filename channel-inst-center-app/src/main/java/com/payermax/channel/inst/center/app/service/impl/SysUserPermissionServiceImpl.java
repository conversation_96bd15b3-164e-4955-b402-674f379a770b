package com.payermax.channel.inst.center.app.service.impl;

import com.payermax.channel.inst.center.app.model.contract.desensitizer.SensitiveType;
import com.payermax.channel.inst.center.app.service.SysUserPermissionService;
import com.payermax.channel.inst.center.common.enums.instcontract.InstContractRoleEnum;
import com.payermax.operating.upms.context.PermissionDomainService;
import com.payermax.operating.upms.context.RoleDomainService;
import com.payermax.operating.upms.context.UpmsDomainService;
import com.ushareit.pay.upms.req.UserPermissionReq;
import com.ushareit.pay.upms.resp.SysAdminRoleQueryResp;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @DESC
 */
@Log4j2
@Service
public class SysUserPermissionServiceImpl implements SysUserPermissionService {

    private final PermissionDomainService permissionDomainService;
    private final RoleDomainService roleDomainService;
    private final UpmsDomainService upmsDomainService;

    @Autowired
    public SysUserPermissionServiceImpl(PermissionDomainService permissionDomainService, RoleDomainService roleDomainService
            , UpmsDomainService upmsDomainService){
        this.permissionDomainService = permissionDomainService;
        this.roleDomainService = roleDomainService;
        this.upmsDomainService = upmsDomainService;
    }

    /**
     * 根据 shareId 和 角色判断用户是否有权限
     * @param shareId
     * @param roleEnum
     */
    @Override
    public Boolean hasPermissions(String shareId, InstContractRoleEnum roleEnum) {
        // 查询用户角色列表
        Optional<List<SysAdminRoleQueryResp>> userRole = roleDomainService.queryRolesByShareId(shareId);
        return userRole.isPresent() && userRole.get().stream().anyMatch(item -> item.getRoleName().equals(roleEnum.getRoleName()));
    }

    /**
     * 根据 shardID 判断是否拥有费用信息权限
     *
     * @param shareId
     * @param sensitiveType
     */
    @Override
    public Boolean hasValuePermission(String shareId, SensitiveType sensitiveType) {
        // 查询用户权限
        UserPermissionReq userPermissionReq = new UserPermissionReq();
        userPermissionReq.setShareId(shareId);
        userPermissionReq.setCode(sensitiveType.getAuthCode());
        Optional<Boolean> filter = upmsDomainService.filter(userPermissionReq);
        return filter.orElse(false);
    }


}
