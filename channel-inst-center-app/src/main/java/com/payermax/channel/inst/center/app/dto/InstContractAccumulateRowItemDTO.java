package com.payermax.channel.inst.center.app.dto;

import com.payermax.channel.inst.center.app.dto.impl.InstContractAccumulateRowItemZhDTO;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;

/**
 * InstContractAccumulateRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
public abstract class InstContractAccumulateRowItemDTO extends AbstractInstContractRowItemDTO implements ExcelUtil.ExcelLangRow {

    /**
     * 支付币种
     * @return 支付币种
     */
    public abstract String getPayCurrency();

    /**
     * 卡片类型
     * @return 卡片类型
     */
    public abstract String getCardType();

    /**
     * 发卡国家
     * @return 发卡国家
     */
    public abstract String getCardIssueCountry();

    /**
     * 机构MID
     * @return 机构MID
     */
    public abstract String getOriginMid();

    /**
     * 机构MCC
     * @return 机构MCC
     */
    public abstract String getOriginMcc();

    /**
     * 累计周期
     * @return 累计周期
     */
    public abstract String getAccumulateCycle();

    /**
     * 累计类型
     * @return 累计类型
     */
    public abstract String getAccumulateType();

    /**
     * 累计生效方法
     * @return 累计生效方法
     */
    public abstract String getAccumulateEffectMethod();

    /**
     * 累计阶梯上限（不含）, 需要数字校验
     * @return 累计阶梯上限（不含）
     */
    public abstract String getAccumulateStepUpper();

    /**
     * 累计阶梯下限（含）, 需要数字校验
     * @return 累计阶梯下限（含）
     */
    public abstract String getAccumulateStepLower();

    /**
     * 阶梯单位
     * @return 阶梯单位
     */
    public abstract String getAccumulateStepUnit();

    /**
     * 计费方式
     * @return 计费方式
     */
    public abstract String getCalculateType();

    /**
     * 算费时点
     * @return 算费时点
     */
    public abstract String getFeeCalculateTime();

    /**
     * 算费币种
     * @return 算费币种
     */
    public abstract String getCalculateFeeCurrency();

    /**
     * 扣费币种
     * @return 扣费币种
     */
    public abstract String getChargeCurrency();

    /**
     * 保留小数位数, 需要数组校验
     * @return 保留小数位数
     */
    public abstract String getRoundingScale();

    /**
     * 保留小数算法
     * @return 保留小数算法
     */
    public abstract String getRoundingMode();

    /**
     * 计费基准
     * @return 计费基准
     */
    public abstract String getFeeBasementMode();

    /**
     * 单笔比例
     * @return 单笔比例
     */
    public abstract String getSingleRate();

    /**
     * 最低收费, 需要数字校验
     * @return 最低收费
     */
    public abstract String getMinCharge();

    /**
     * 封顶收费, 需要数字校验
     * @return 封顶收费
     */
    public abstract String getMaxCharge();

    /**
     * 单笔固定金额, 需要数字校验
     * @return 单笔固定金额
     */
    public abstract String getSingleFixedAmount();

    /**
     * 单笔固定币种
     * @return 单笔固定币种
     */
    public abstract String getSingleFixedCurrency();

    @Override
    public Class<? extends InstContractAccumulateRowItemDTO> langClassImpl(LangEnum langEnum) {
        return InstContractAccumulateRowItemZhDTO.class;
    }
}
