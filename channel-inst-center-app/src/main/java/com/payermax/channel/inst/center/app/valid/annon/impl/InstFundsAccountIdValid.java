package com.payermax.channel.inst.center.app.valid.annon.impl;

import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.common.constrains.InterfaceValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.common.lang.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * InstFundsAccountIdValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class InstFundsAccountIdValid implements InterfaceValid<String, InstFundsAccountIdValid.SimpleInstFundsAccount> {

    @Resource
    private InstFundsAccountService instFundsAccountService;

    @Override
    public boolean valid(String value, LangEnum lang) {
        if(StringUtil.isEmpty(value)) {
            return Boolean.TRUE;
        }
        return dataList().stream().anyMatch(item -> value.equals(item.getAccountId()));
    }

    @Override
    public List<SimpleInstFundsAccount> queryData() {
        List<InstFundsAccountEntity> list = instFundsAccountService.queryListByQueryEntity(new InstFundsAccountQueryEntity());
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(item -> new SimpleInstFundsAccount().setAccountId(item.getAccountId())).collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public class SimpleInstFundsAccount {

        private String accountId;
    }
}
