package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstContactReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/15 21:17
 * @Version 1.0
 */
@Data
public class InstContactReqDTO implements Serializable {

    private static final long serialVersionUID = 2483030019341517976L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 机构标识
     */
    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(notes = "联系人姓名")
    private String name;

    /**
     * 联系人职能
     */
    @ApiModelProperty(notes = "联系人职能")
    private String position;

    /**
     * 联系人头衔
     */
    @ApiModelProperty(notes = "联系人头衔")
    private String title;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(notes = "联系人邮箱")
    private String email;

    /**
     * 联系人电话
     */
    @ApiModelProperty(notes = "联系人电话")
    private String phoneNo;

    /**
     * 联系人IM
     */
    @ApiModelProperty(notes = "联系人IM")
    private String imNo;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 状态(Y：启用，N：停用)
     */
    @ApiModelProperty(notes = "状态")
    private String status;
}
