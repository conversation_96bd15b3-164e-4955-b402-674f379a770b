package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.constants.DictConstants;
import com.payermax.channel.inst.center.common.enums.AlertEnum;
import com.payermax.channel.inst.center.common.enums.CountryEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.DdStatusEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstCharacterEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.StatusEnum;
import com.payermax.channel.inst.center.common.result.BaseResult;
import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.common.utils.JsonUtils;
import com.payermax.channel.inst.center.infrastructure.alert.AlarmPlatformUtil;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstInfoPushEntity;
import com.payermax.channel.inst.center.infrastructure.client.FintechBaseClientProxy;
import com.payermax.channel.inst.center.app.manage.InstBaseInfoManager;
import com.payermax.channel.inst.center.app.manage.InstContractManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstBaseInfoReqDTO;
import com.payermax.channel.inst.center.app.request.InstBrandReqDTO;
import com.payermax.channel.inst.center.app.request.InstContractReqDTO;
import com.payermax.channel.inst.center.app.request.InstInfoReqDTO;
import com.payermax.channel.inst.center.app.response.*;
import com.payermax.channel.inst.center.app.service.*;
import com.ushareit.fintech.base.dto.DictItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InstBaseInfoManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 18:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstBaseInfoManagerImpl implements InstBaseInfoManager {

    private static final String PUSH_INST_INFO_GROUP = "push-inst-info-exceptionNotify";

    /**
     * 1.开启:监控机构信息正常推送日志;
     */
    private static final String NORMAL_MONITOR_ENABLE = "1";

    @Autowired
    private InstBaseInfoService instBaseInfoService;

    @Autowired
    private InstBrandService instBrandService;

    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductCapabilityService instProductCapabilityService;

    @Autowired
    private InstDdService instDdService;

    @Autowired
    private FintechBaseClientProxy baseClientProxy;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private DingAlertClient dingAlertClient;

    @NacosValue(value = "${group.contractSystem.url}", autoRefreshed = true)
    private String url;

    @NacosValue(value = "${group.contractSystem.timeout}", autoRefreshed = true)
    private int timeout;

    @NacosValue(value = "${group.contractSystem.normalLogMonitor:0}", autoRefreshed = true)
    private String normalLogMonitor;

    @Autowired
    private Environment env;

    @Autowired
    private InstContactService instContactService;

    @Autowired
    private InstContractManager instContractManager;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Override
    public List<InstBaseInfoVO> query(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        //请求转换
        InstBaseInfoEntity instBaseInfoEntity = reqDtoAssembler.toInstBaseInfoEntity(instBaseInfoReqDTO);
        //响应转换
        List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoService.query(instBaseInfoEntity);
        Map<Long, List<InstDdQueryEntity>> instDdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(instBaseInfoEntityList)) {
            List<Long> instIds = instBaseInfoEntityList.stream().map(InstBaseInfoEntity::getInstId).distinct().collect(Collectors.toList());
            List<InstDdQueryEntity> instDdQueryEntityList = instDdService.getByInstIds(instIds);
            if (CollectionUtils.isNotEmpty(instDdQueryEntityList)) {
                instDdMap = instDdQueryEntityList.stream().filter(obj -> StringUtils.isNotBlank(obj.getRegisterName())).collect(Collectors.groupingBy(InstDdQueryEntity::getInstId));
            }
        }
        List<InstBaseInfoVO> instBaseInfoVOS = respVoAssembler.toInstBaseInfoVos(instBaseInfoEntityList);
        if (MapUtils.isNotEmpty(instDdMap)) {
            for (InstBaseInfoVO instBaseInfoVO : instBaseInfoVOS) {
                List<InstDdQueryEntity> instDdQueryEntities = instDdMap.get(instBaseInfoVO.getInstId());
                if (CollectionUtils.isNotEmpty(instDdQueryEntities)) {
                    instBaseInfoVO.setRegisterName(instDdQueryEntities.get(0).getRegisterName()); //NO_CHECK 方法未被调用
                }
            }
        }
        return instBaseInfoVOS;
    }

    @Override
    public PageResult<InstBaseInfoVO> queryAll(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        //请求转换
        InstBaseInfoQueryEntity instBaseInfoQueryEntity = reqDtoAssembler.toInstBaseInfoQueryEntity(instBaseInfoReqDTO);
        //响应转换
        IPage<InstBaseInfoQueryEntity> instBaseInfoQueryEntityPage = instBaseInfoService.queryAll(instBaseInfoQueryEntity, instBaseInfoReqDTO.getPageNum(), instBaseInfoReqDTO.getPageSize());
        List<InstBaseInfoQueryEntity> records = instBaseInfoQueryEntityPage.getRecords();
        List<InstBaseInfoVO> instBaseInfoVOS = respVoAssembler.toInstBaseInfoQueryVos(records);
        if (CollectionUtils.isNotEmpty(instBaseInfoVOS)) {
            //负责AM
            //调用基础服务查询渠道BD和渠道AM关系字典项
            List<DictItemDTO> itemDTOList = baseClientProxy.queryDict(DictConstants.DICT_IC_DATA_MAPPING);
            Map<String, String> bdAmMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(itemDTOList)) {
                Optional<DictItemDTO> dictItemDTO = itemDTOList.stream().filter(obj -> DictConstants.DICT_IC_BD_AM_MAPPING.equalsIgnoreCase(obj.getItemValue())).findFirst(); //NO_CHECK 方法未被调用
                if (dictItemDTO.isPresent()) {
                    bdAmMap = JsonUtils.jsonToPojo(dictItemDTO.get().getExtraInfo(), Map.class);
                }
            }
            if (MapUtils.isNotEmpty(bdAmMap)) {
                Map<String, String> finalBdAmMap = bdAmMap;
                instBaseInfoVOS = instBaseInfoVOS.stream()
                        .map(obj -> {
                            if (StringUtils.isNotBlank(obj.getBdId()) && StringUtils.isBlank(obj.getAmId())) {
                                obj.setAmId(StringUtils.defaultIfBlank(finalBdAmMap.get(obj.getBdId()), StringUtils.EMPTY));
                            }
                            return obj;
                        }).collect(Collectors.toList());
            }
            List<Long> instIds = instBaseInfoVOS.stream().map(InstBaseInfoVO::getInstId).distinct().collect(Collectors.toList());
            //机构产品
            List<InstProductEntity> instProductEntityList = instProductService.getByInstIds(instIds);
            if (CollectionUtils.isNotEmpty(instProductEntityList)) {
                List<InstProductVO> instProductVOS = respVoAssembler.toInstProductVos(instProductEntityList);
                Map<String, List<InstProductVO>> instProductMap = instProductVOS.stream().collect(Collectors.groupingBy(InstProductVO::getInstId));
                instBaseInfoVOS.stream().forEach(instBaseInfoVO -> {
                    List<InstProductVO> instProductVOList = instProductMap.get(String.valueOf(instBaseInfoVO.getInstId()));
                    if (CollectionUtils.isNotEmpty(instProductVOList)) {
                        instBaseInfoVO.setInstProductVOS(instProductVOList);
                        List<InstProductCapabilityEntity> instProductCapabilityEntityList = instProductCapabilityService.getByProductCodes(instProductVOList.stream().map(InstProductVO::getProductCode).distinct().collect(Collectors.toList()));
                        instBaseInfoVO.setCountrys(instProductCapabilityEntityList.stream().map(InstProductCapabilityEntity::getCountry).distinct().collect(Collectors.joining(",")));
                    }
                });
            }
            //机构联系人
            List<InstContactEntity> instContactEntityList = instContactService.query(instIds);
            if (CollectionUtils.isNotEmpty(instContactEntityList)) {
                List<InstContactVO> instContactVOs = respVoAssembler.toInstContactVo(instContactEntityList);
                Map<Integer,ContactDictItemVO> positionMap = getContactDictItem(DictConstants.DICT_CONTACT_POSITION);
                Map<Integer,ContactDictItemVO> titleMap = getContactDictItem(DictConstants.DICT_CONTACT_TITLE);
                Map<Long, List<InstContactVO>> instContactMap = instContactVOs.stream()
                        .map(instContactVO -> {
                            String position = instContactVO.getPosition();
                            String title = instContactVO.getTitle();
                            if(MapUtils.isNotEmpty(positionMap) && StringUtils.isNotBlank(position)){
                                ContactDictItemVO contactDictItemVO = positionMap.get(Integer.valueOf(position));
                                instContactVO.setPositionZhName(Optional.ofNullable(contactDictItemVO).map(ContactDictItemVO::getZhName).orElse(""));
                                instContactVO.setPositionEnName(Optional.ofNullable(contactDictItemVO).map(ContactDictItemVO::getEnName).orElse(""));
                            }
                            if(MapUtils.isNotEmpty(titleMap) && StringUtils.isNotBlank(title)){
                                ContactDictItemVO contactDictItemVO = titleMap.get(Integer.valueOf(title));
                                instContactVO.setTitleZhName(Optional.ofNullable(contactDictItemVO).map(ContactDictItemVO::getZhName).orElse(""));
                                instContactVO.setTitleEnName(Optional.ofNullable(contactDictItemVO).map(ContactDictItemVO::getEnName).orElse(""));
                            }
                            return instContactVO;
                        })
                        .collect(Collectors.groupingBy(InstContactVO::getInstId));
                instBaseInfoVOS.stream().forEach(instBaseInfoVO -> {
                    instBaseInfoVO.setInstContactVOS(instContactMap.get(instBaseInfoVO.getInstId()));
                });
            }
            //机构合同
            InstContractReqDTO instContractReqDTO = new InstContractReqDTO();
            instContractReqDTO.setInstIds(instIds);
            List<InstContractVO> instContractVOs = instContractManager.list(instContractReqDTO);
            if (CollectionUtils.isNotEmpty(instContractVOs)) {
                Map<Long, List<InstContractVO>> instContractMap = instContractVOs.stream().collect(Collectors.groupingBy(InstContractVO::getInstId));
                instBaseInfoVOS.stream().forEach(instBaseInfoVO -> {
                    instBaseInfoVO.setInstContractVOS(instContractMap.get(instBaseInfoVO.getInstId()));
                });
            }
            //申请单
            List<InstApplyOrderEntity> instApplyOrderEntities = instApplyOrderService.queryList(instIds);
            if (CollectionUtils.isNotEmpty(instApplyOrderEntities)) {
                List<InstApplyOrderVO> instApplyOrderVOS = respVoAssembler.toInstApplyOrdersVo(instApplyOrderEntities);
                Map<Long, List<InstApplyOrderVO>> instApplyOrderMap = instApplyOrderVOS.stream().collect(Collectors.groupingBy(InstApplyOrderVO::getInstId));
                instBaseInfoVOS.stream().forEach(instBaseInfoVO -> {
                    List<InstApplyOrderVO> instApplyOrderList = instApplyOrderMap.get(instBaseInfoVO.getInstId());
                    if (CollectionUtils.isNotEmpty(instApplyOrderList)) {
                        instBaseInfoVO.setApplyNos(instApplyOrderList.stream().map(InstApplyOrderVO::getApplyNo).distinct().collect(Collectors.toList()));
                    }
                });
            }
        }
        PageResult<InstBaseInfoVO> voPage = new PageResult<>();
        voPage.setTotal(instBaseInfoQueryEntityPage.getTotal());
        voPage.setRows(instBaseInfoVOS);
        return voPage;
    }

    /**
     * 获取机构联系人职位、头衔
     * @param dictCode
     * @return
     */
    private Map<Integer, ContactDictItemVO> getContactDictItem(String dictCode) {
        List<DictItemDTO> dictItemDTOList = baseClientProxy.queryDict(dictCode);
        List<ContactDictItemVO> contactDictItemVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dictItemDTOList)) {
            Optional<DictItemDTO> dictItemDTO = dictItemDTOList.stream().filter(obj -> dictCode.equalsIgnoreCase(obj.getItemName()) || dictCode.equalsIgnoreCase(obj.getItemValue())).findFirst(); //NO_CHECK 方法未被调用
            if (dictItemDTO.isPresent()) {
                contactDictItemVOS = JsonUtils.jsonToList(dictItemDTO.get().getExtraInfo(),ContactDictItemVO.class);
            }
            if (CollectionUtils.isNotEmpty(contactDictItemVOS)) {
                Map<Integer, ContactDictItemVO> map = contactDictItemVOS.stream().collect(Collectors.toMap(ContactDictItemVO::getId, Function.identity()));
                return map;
            }
        }
        return null;
    }

    @Override
    public List<InstBaseInfoVO> queryByBdId(String bdId) {
        Preconditions.checkArgument(StringUtils.isNotBlank(bdId), "param bdId is mandatory");
        InstBrandEntity brandEntity = new InstBrandEntity();
        brandEntity.setBdId(bdId);
        brandEntity.setStatus(StatusEnum.Y.getValue());
        List<InstBrandEntity> brandEntityList = instBrandService.query(brandEntity);
        if (CollectionUtils.isNotEmpty(brandEntityList)) {
            List<Long> brandIds = brandEntityList.stream().map(InstBrandEntity::getBrandId).distinct().collect(Collectors.toList());
            List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoService.queryByBrandId(brandIds);
            List<InstBaseInfoVO> instBaseInfoVOS = respVoAssembler.toInstBaseInfoVos(instBaseInfoEntityList);
            return instBaseInfoVOS;
        }
        return null;
    }

    @Override
    public List<InstBaseInfoVO> queryByBdIds(List<String> bdIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(bdIds), "param bdIds is mandatory");
        List<InstBrandEntity> brandEntityList = instBrandService.queryByBdIds(bdIds);
        if (CollectionUtils.isNotEmpty(brandEntityList)) {
            List<Long> brandIds = brandEntityList.stream().map(InstBrandEntity::getBrandId).distinct().collect(Collectors.toList());
            List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoService.queryByBrandId(brandIds);
            Map<Long, List<InstDdQueryEntity>> instDdMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(instBaseInfoEntityList)) {
                List<Long> instIds = instBaseInfoEntityList.stream().map(InstBaseInfoEntity::getInstId).distinct().collect(Collectors.toList());
                List<InstDdQueryEntity> instDdQueryEntityList = instDdService.getByInstIds(instIds);
                if (CollectionUtils.isNotEmpty(instDdQueryEntityList)) {
                    instDdMap = instDdQueryEntityList.stream().filter(obj -> StringUtils.isNotBlank(obj.getRegisterName())).collect(Collectors.groupingBy(InstDdQueryEntity::getInstId));
                }
            }
            List<InstBaseInfoVO> instBaseInfoVOS = respVoAssembler.toInstBaseInfoVos(instBaseInfoEntityList);
            if (MapUtils.isNotEmpty(instDdMap)) {
                for (InstBaseInfoVO instBaseInfoVO : instBaseInfoVOS) {
                    List<InstDdQueryEntity> instDdQueryEntities = instDdMap.get(instBaseInfoVO.getInstId());
                    if (CollectionUtils.isNotEmpty(instDdQueryEntities)) {
                        instBaseInfoVO.setRegisterName(instDdQueryEntities.get(0).getRegisterName()); //NO_CHECK 方法未被调用
                    }
                }
            }
            return instBaseInfoVOS;
        }
        return null;
    }

    @Override
    public int save(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        //请求转换
        InstBaseInfoEntity instBaseInfoEntity = reqDtoAssembler.toInstBaseInfoEntity(instBaseInfoReqDTO);
        return instBaseInfoService.save(instBaseInfoEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateInstCode(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        int result = 0;
        //推送机构信息
        Map<String, String> msgMap = this.pushInstInfo(instBaseInfoReqDTO);
        if (MapUtils.isNotEmpty(msgMap) && (NORMAL_MONITOR_ENABLE.equalsIgnoreCase(normalLogMonitor) || StringUtils.isNotBlank(msgMap.get("error")))) {
            String title = "机构信息推送集团合同系统-环境["+String.join("", Arrays.asList(env.getActiveProfiles()))+"]";
            String message = String.format("## **%s**\n- 机构名称：%s\n- 机构编码：%s\n- 请求信息：%s\n- 响应信息：%s\n- 异常信息：%s"
                    , title, instBaseInfoReqDTO.getInstName(), instBaseInfoReqDTO.getInstCode(), msgMap.get("request"), msgMap.get("response"), msgMap.get("error"));
            dingAlertClient.sendMsg(PUSH_INST_INFO_GROUP, title, message);
            AlarmPlatformUtil.sendMessageByOperationType(AlertEnum.PUSH_INST_INFO_GROUP, message,title);
        }
        if (MapUtils.isNotEmpty(msgMap) && StringUtils.isBlank(msgMap.get("error"))) {
            //请求转换
            InstBaseInfoEntity instBaseInfoEntity = new InstBaseInfoEntity();
            instBaseInfoEntity.setInstId(instBaseInfoReqDTO.getInstId());
            instBaseInfoEntity.setInstCode(instBaseInfoReqDTO.getInstCode());
            result = instBaseInfoService.save(instBaseInfoEntity);

            Long brandId = instBaseInfoReqDTO.getInstBrandId();
            String brandCode = instBaseInfoReqDTO.getInstBrandCode();
            // 更新
            if (brandId != null && StringUtils.isNotBlank(brandCode)) {
                InstBrandEntity instBrandEntity = new InstBrandEntity();
                instBrandEntity.setBrandId(brandId);
                instBrandEntity.setBrandCode(brandCode);
                instBrandService.save(instBrandEntity);
            }
        }
        return result;
    }

    /**
     * 推送机构信息到集团合同系统
     *
     * @param instBaseInfoReqDTO
     */
    public Map<String, String> pushInstInfo(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put("error", StringUtils.EMPTY);
        try {
            InstInfoPushEntity instInfo = assembleInstInfo(instBaseInfoReqDTO);
            String request = JSONObject.toJSONString(Arrays.asList(instInfo));
            msgMap.put("request", request);
            log.info("推送机构信息请求：{}", request);
            String response = HttpRequest.post(url).body(request).timeout(timeout).execute().body();
            msgMap.put("response", response);
            log.info("推送机构信息响应：{}", response);
            if (StringUtils.isBlank(response)) {
                msgMap.put("error", BaseResult.ResponseEnum.INST_CENTER_PUSH_RESPONSE_EMPTY.getMsg());
                return msgMap;
            }
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject == null) {
                msgMap.put("error", BaseResult.ResponseEnum.INST_CENTER_PUSH_RESPONSE_EMPTY.getMsg());
                return msgMap;
            }
            if (!BaseResult.ResponseEnum.OK.getCode().equalsIgnoreCase(jsonObject.getString("code"))) {
                msgMap.put("error", response);
                return msgMap;
            }
        } catch (Exception e) {
            log.warn("推送机构信息异常：", e);
            msgMap.put("error", ExceptionUtil.getMessage(e));
            return msgMap;
        }
        return msgMap;
    }

    /**
     * 组装待推送的机构信息
     *
     * @param instBaseInfoReqDTO
     * @return
     */
    private InstInfoPushEntity assembleInstInfo(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        InstBaseInfoEntity instBaseInfoEntity = instBaseInfoService.queryById(instBaseInfoReqDTO.getInstId());
        InstBrandEntity instBrandEntity = new InstBrandEntity();
        instBrandEntity.setBrandId(instBaseInfoEntity.getInstBrandId());
        List<InstBrandEntity> instBrandEntityList = instBrandService.query(instBrandEntity);
        InstBrandEntity brandEntity = instBrandEntityList.get(0); //NO_CHECK 方法未被调用
        InstDdEntity ddEntity = instDdService.getByInstId(instBaseInfoEntity.getInstId());
        InstInfoPushEntity instInfoPushEntity = InstInfoPushEntity.builder()
                .fullName(ddEntity.getRegisterName())
                .thirdId(instBaseInfoReqDTO.getInstCode())
                .oppositeCharacter(CountryEnum.CN.getCountryCode().equalsIgnoreCase(instBaseInfoEntity.getEntityCountry()) ? InstCharacterEnum.DOMESTIC_ORGANIZATION.getValue() : InstCharacterEnum.OVERSEAS_ORGANIZATION.getValue())
                .tinCode(ddEntity.getRegisterNo())
                .countryName(instBaseInfoEntity.getEntityCountry())
                .legalPersonName(ddEntity.getCorporateName())
                .creatorAccount(brandEntity.getBdId())
                .creationTime(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                .build();
        return instInfoPushEntity;
    }

    @Override
    public List<InstBaseInfoVO> queryInstNameList(InstBrandReqDTO instBrandReqDTO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(instBrandReqDTO.getBrandName()), "param brandName is mandatory");

        InstBrandEntity instBrandEntity = reqDtoAssembler.toInstBrandEntity(instBrandReqDTO);
        List<InstBrandEntity> instBrandEntities = instBrandService.query(instBrandEntity);
        if (CollectionUtils.isNotEmpty(instBrandEntities)) {
            Long brandId = instBrandEntities.get(0).getBrandId(); //NO_CHECK 方法未被调用
            InstBaseInfoEntity baseInfoEntity = new InstBaseInfoEntity();
            baseInfoEntity.setInstBrandId(brandId);
            List<InstBaseInfoEntity> instBaseInfoEntityList = instBaseInfoService.query(baseInfoEntity);
            return respVoAssembler.toInstBaseInfoVos(instBaseInfoEntityList);
        }
        return null;
    }

    @Override
    public int add(InstInfoReqDTO instInfoReqDTO) {
        //请求转换
        InstBaseInfoEntity instBaseInfoEntity = reqDtoAssembler.toInstInfoEntity(instInfoReqDTO);
        InstBrandEntity instBrandEntity = reqDtoAssembler.toInstBrandEntity(instInfoReqDTO.getBrandDto());
        InstDdEntity instDdEntity = reqDtoAssembler.toInstDdEntity(instInfoReqDTO.getDdInfoDto());
        int result = transactionTemplate.execute(status -> {
            instBrandEntity.setStatus(StatusEnum.Y.getValue());
            instBrandService.save(instBrandEntity);
            instBaseInfoEntity.setInstBrandId(instBrandEntity.getBrandId());
            instBaseInfoEntity.setStatus(StatusEnum.Y.getValue());
            int rows = instBaseInfoService.save(instBaseInfoEntity);
            instDdEntity.setInstId(instBaseInfoEntity.getInstId());
            instDdEntity.setStatus(DdStatusEnum.INIT.name());
            instDdService.save(instDdEntity);
            return rows;
        });
        return result;
    }

    @Override
    public int check(InstBaseInfoReqDTO instBaseInfoReqDTO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(instBaseInfoReqDTO.getEntityCountry()), "param entityCountry is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instBaseInfoReqDTO.getRegisterNo()), "param registerNo is mandatory");
        InstBaseInfoEntity instBaseInfoEntity = reqDtoAssembler.toInstBaseInfoEntity(instBaseInfoReqDTO);
        List<InstBaseInfoEntity> instBaseInfoEntities = instBaseInfoService.query(instBaseInfoEntity);
        if (CollectionUtils.isEmpty(instBaseInfoEntities)) {
            return 0;
        }
        List<Long> instIds = instBaseInfoEntities.stream().map(InstBaseInfoEntity::getInstId).distinct().collect(Collectors.toList());
        List<InstDdQueryEntity> instDdQueryEntityList = instDdService.getByInstIds(instIds);
        if (CollectionUtils.isEmpty(instDdQueryEntityList)) {
            return 0;
        }
        Optional<InstDdQueryEntity> any = instDdQueryEntityList.stream().filter(obj -> instBaseInfoReqDTO.getRegisterNo().equalsIgnoreCase(obj.getRegisterNo())).findAny();
        return any.isPresent() ? instDdQueryEntityList.size() : 0;
    }
}
