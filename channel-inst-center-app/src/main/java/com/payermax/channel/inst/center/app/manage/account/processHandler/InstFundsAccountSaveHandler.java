package com.payermax.channel.inst.center.app.manage.account.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.DiffUtil;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.infrastructure.adapter.InstFundsAccountSaveAdapter;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFundsAccountRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.omc.channel.exchange.facade.request.InstFundsAccountSaveRequest;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import com.payermax.operating.omc.portal.workflow.facade.common.em.ProcessResult;
import com.payermax.workflow.server.dubbo.api.domain.WorkflowCallback;
import com.payermax.workflow.server.dubbo.api.em.ProcessStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.INST_CENTER_FUNDS_ACCOUNT_MANAGER, actionType = OperateTypeEnum.UPDATE)
public class InstFundsAccountSaveHandler extends AbstractWorkflowHandler {

    private final InstFundsAccountRepository fundsAccountRepository;
    private final InstBaseInfoRepository baseInfoRepository;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final IDGenerator idGenerator;
    private final InstFundsAccountSaveAdapter fundsAccountSaveAdapter;

    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.key:process_inst-center-funds-account-approve}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.desc:新增/变更渠道账户}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.formMsgTemplate:}", autoRefreshed = true)
    private String processFormMsgTemplate;

    /**
     * 账户信息修改时重点提示字段、排除字段列表
     */
    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.warning-fields:useType,bankAbility}", autoRefreshed = true)
    private Set<String> accountWarningFields;
    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.excluding-fields:}", autoRefreshed = true)
    private Set<String> accountExcludingFields;

    /**
     * 业务场景
     */
    private final BusinessTypeEnum BUSINESS_TYPE = BusinessTypeEnum.INST_CENTER;
    private final OperateTypeEnum OPERATE_TYPE = OperateTypeEnum.UPDATE;
    private final OperateModuleEnum MODULE_NAME = OperateModuleEnum.INST_CENTER_FUNDS_ACCOUNT_MANAGER;
    private final LogScenesTypeEnum LOG_SCENES_TYPE = LogScenesTypeEnum.FUNDS_ACCOUNT;


    /**
     * 新增/修改渠道账户
     */
    public Boolean startSaveProcess(InstFundsAccountSaveRequest request, String shareId){
        InstBusinessDraft draft = buildDraft(request, shareId);
        return super.startProcess(draft);
    }


    /**
     * 业务流程审批发起
     */
    @Override
    protected Boolean workflowProcessStart(InstBusinessDraft draft){
        try{
            return super.workflowProcessStart(draft);
        } catch (BusinessException e){
            // 当明确是业务异常时，才进行撤销操作
            log.error("发起流程失败，撤销账户变更草稿：{},", e.getMessage());
            // 撤回 omc-channel-exchange 账户草稿
            revokeOmcDraft(draft);
            throw new BusinessException(ErrorCodeEnum.WORKFLOW_START_ERROR.getCode(), "工作流发起失败，撤销账户变更草稿");
        } catch (Exception e){
            log.error("发起流程失败：{}", e.getMessage());
            throw new BusinessException(ErrorCodeEnum.WORKFLOW_START_ERROR.getCode(), "工作流发起失败");
        }
    }

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        processConfig.setProcessDefKey(processKey);
        processConfig.setProcessDesc(processDesc);
        // 审批表单信息
        processConfig.setFormInfoMap(buildFormMsg(draft));
        return processConfig;
    }

    @Override
    protected InstBusinessDraft businessDraftSave(InstBusinessDraft draft){
        try{
            // 调用 omc-channel-exchange 账户保存接口，获取草稿ID
            InstFundsAccountSaveRequest modifiedData = JSON.parseObject(JSON.parseObject(draft.getDraftData()).getString("modifiedData"), InstFundsAccountSaveRequest.class);
            // 新工作流标志，及流程关联 ID，确保兼容旧逻辑
            modifiedData.setNewWorkflow(true);
            modifiedData.setRelatedProcessId(draft.getDraftId());
            // 调用
            fundsAccountSaveAdapter.saveWithProcess(draft.getOwner(), modifiedData);

            businessDraftRepository.save(InstBusinessDraftAssembler.INSTANCE.domain2Po(draft));

        }catch (BizException | IllegalArgumentException e){
            log.warn("保存修改信息失败",e);
            throw new BusinessException(ErrorCodeEnum.INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALL_ERROR.getCode(), e.getMessage());
        }catch (Exception e){
            log.error("保存修改信息失败",e);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "draft save fail");
        }
        return draft;
    }



    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        // 构造审批通过回调信息
        log.info("流程审批通过，调用回调方法，草稿 ID :{}", draft.getDraftId());
        return remoteCallback(draft, eventInfo);
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("流程审批拒绝，业务数据回滚，草稿 ID :{}", draft.getDraftId());
        // 旧OA无失败逻辑，需手动处理
        return remoteCallback(draft, eventInfo);
    }

    // --------------------------------------------------------------------------------------------------

    /**
     * 构造草稿
     */
    private InstBusinessDraft buildDraft(InstFundsAccountSaveRequest request, String shareId) {

        // 查询账户信息
        InstFundsAccountEntity fundsAccount = null;
        if(StringUtils.isNotBlank(request.getAccountId())){
            fundsAccount = fundsAccountRepository.queryByAccountId(request.getAccountId());
        }

        // 草稿内容
        Map<String, Object> draftData = new HashMap<>(4);
        draftData.put("originData", fundsAccount);
        draftData.put("modifiedData", request);

        // 构造草稿
        return InstBusinessDraftAssembler.INSTANCE.buildDefaultDraft(
                idGenerator.generateIdAccordingToSystem(BUSINESS_TYPE, LOG_SCENES_TYPE), MODULE_NAME, OPERATE_TYPE,
                String.valueOf(request.getInstId()), JSON.toJSONString(draftData, SerializerFeature.DisableCircularReferenceDetect), shareId
        );
    }

    /**
     * 撤回 omc-channel-exchange 账户草稿
     */
    private Boolean revokeOmcDraft(InstBusinessDraft draft){
        log.info("回滚 omc-channel-exchange 账户草稿，草稿ID：{}", draft.getDraftId());
        WorkflowCallback workflowCallback = new WorkflowCallback();
        workflowCallback.setProcessId(draft.getDraftId());
        workflowCallback.setProcessStatus(ProcessStatus.REVOKE);
        return fundsAccountSaveAdapter.callbackHandler(workflowCallback);
    }

    /**
     * 远程回调
     */
    public Boolean remoteCallback(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        WorkflowCallback workflowCallback = new WorkflowCallback();
        workflowCallback.setProcessId(draft.getDraftId());
        workflowCallback.setProcessStatus(processStatusConvert(eventInfo.getProcessInfo().getResult()));
        return fundsAccountSaveAdapter.callbackHandler(workflowCallback);
    }

    /**
     * 流程回调状态转换
     */
    private ProcessStatus processStatusConvert(ProcessResult processResult) {
        switch (processResult){
            case PASS:
                return ProcessStatus.YES;
            case REJECT:
                return ProcessStatus.NO;
            case REVOKE:
                return ProcessStatus.REVOKE;
            default:
                throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "processStatusConvert fail");
        }
    }


    /**
     * 构建审批表单信息
     */
    private Map<String, Object> buildFormMsg(InstBusinessDraft draft) {
        try{
            HashMap<String, Object> formMsg = new HashMap<>(4);

            // 获取机构信息
            InstBaseInfo instBaseInfo = baseInfoRepository.getById(draft.getBusinessKey());
            formMsg.put("instName", instBaseInfo.getInstName());
            formMsg.put("instCode", instBaseInfo.getInstCode());

            // 账户信息
            Optional<String> originData = Optional.ofNullable(JSON.parseObject(draft.getDraftData()).getString("originData"));
            String modifiedDataStr = JSON.parseObject(draft.getDraftData()).getString("modifiedData");
            InstFundsAccountEntity modifiedData = JSON.parseObject(JSONObject.parseObject(draft.getDraftData()).getString("modifiedData"), InstFundsAccountEntity.class);
            formMsg.putAll(JSON.parseObject(originData.orElse(modifiedDataStr)));
            formMsg.put("isNewAccount", String.valueOf(StringUtils.isBlank(modifiedData.getAccountId())));

            // 进行 DIFF，并转换成表单信息
            TreeMap<String, Map<String,Object>> diffMsg = DiffUtil.diff2Map(originData.orElse(JSON.toJSONString(new Object())), modifiedDataStr);

            // 过滤掉无差异数据以及变更为 null/空字符串 的数据
            diffMsg.entrySet().removeIf(entry -> {
                Map<String, Object> valueMap = entry.getValue();
                String left = String.valueOf(valueMap.get("left"));
                String right = String.valueOf(valueMap.get("right"));
                return (StringUtils.isBlank(left) && StringUtils.isBlank(right)) || ObjectUtils.isEmpty(valueMap.get("right"));
            });

            formMsg.put("warningMsg",warningMsgBuilder(diffMsg));
            formMsg.put("normalMsg",normalMsgBuilder(diffMsg));

            return formMsg;
        }catch (Exception e){
            log.error("构建审批表单信息失败",e);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 重要信息
     */
    private String warningMsgBuilder(TreeMap<String, Map<String,Object>> differenceMap){
        return differenceMap.entrySet().stream()
                // 过滤非重要字段
                .filter(entry -> accountWarningFields.contains(entry.getKey()))
                // 处理字符串
                .map(entry -> {
                    JSONObject valueJson = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                    return String.format("【%s】: %s -> %s ", entry.getKey(),
                            StringUtils.defaultIfBlank(valueJson.getString("left"),"【】"),
                            StringUtils.defaultIfBlank(valueJson.getString("right") ,"【】"));
                })
                .collect(Collectors.joining("\n"));
    }

    /**
     * 一般信息
     */
    private String normalMsgBuilder(TreeMap<String, Map<String,Object>> differenceMap){
        return differenceMap.entrySet().stream()
                // 过滤重要字段及忽略字段
                .filter(entry -> !accountWarningFields.contains(entry.getKey()) && !accountExcludingFields.contains(entry.getKey()))
                // 处理字符串
                .map(entry -> {
                    JSONObject valueJson = JSON.parseObject(JSON.toJSONString(entry.getValue()));
                    return String.format("【%s】: %s -> %s ", entry.getKey(),
                            StringUtils.defaultIfBlank(valueJson.getString("left"),"【】"),
                            StringUtils.defaultIfBlank(valueJson.getString("right") ,"【】"));
                })
                .collect(Collectors.joining("\n"));
    }

}
