package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstBusinessDictEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBusinessDictDao;
import com.payermax.channel.inst.center.app.service.InstBusinessDictService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstBusinessDictServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:28
 */
@Service
public class InstBusinessDictServiceImpl implements InstBusinessDictService {

    @Autowired
    private InstBusinessDictDao instBusinessDictDao;

    @Override
    public int save(InstBusinessDictEntity record) {
        Preconditions.checkArgument(record != null ,"record is mandatory");
        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instBusinessDictDao.insert(record);
        } else {
            // 更新
            result = instBusinessDictDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public int saveBatch(List<InstBusinessDictEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        int result = instBusinessDictDao.insertBatch(records);
        return result;
    }

    @Override
    public List<InstBusinessDictEntity> getByBusinessType(String businessType) {
        Preconditions.checkArgument(StringUtils.isNotBlank(businessType), "param businessType is mandatory");
        List<InstBusinessDictEntity> instBusinessDictEntities = instBusinessDictDao.selectByBusinessType(businessType);
        return instBusinessDictEntities;
    }

    @Override
    public int deleteById(Long id) {
        Preconditions.checkArgument(id != null, "param businessType is mandatory");
        int result = instBusinessDictDao.delete(id);
        return result;
    }
}
