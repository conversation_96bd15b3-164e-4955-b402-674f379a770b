package com.payermax.channel.inst.center.app.dto.contract;

import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13
 * @DESC
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InstContractVersionUpgradeDto extends InstContractBaseInfo {

    /**
     * 开始生效时间
     */
    private LocalDateTime effectiveStartTime;

    /**
     * 当前版本信息
     */
    private InstContractVersionInfo currentContractVersion;

    /**
     * 即将生效版本信息
     */
    private InstContractVersionInfo comingContractVersion;

    /**
     * 版本升级的费用信息
     */
    private List<InstContractFeeItemRequestDTO> feeList;

}
