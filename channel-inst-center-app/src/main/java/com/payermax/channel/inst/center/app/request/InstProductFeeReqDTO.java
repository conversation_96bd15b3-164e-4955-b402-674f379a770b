package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstProductFeeReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 0:03
 */
@Data
public class InstProductFeeReqDTO implements Serializable {
    private static final long serialVersionUID = -7383967054052804022L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "合同单号")
    private String contractNo;

    @ApiModelProperty(notes = "产品编码")
    private String instProductCode;

    @ApiModelProperty(notes = "汇率基准")
    private String baseRate;

    @ApiModelProperty(notes = "汇率时间类型")
    private String rateTimeType;

    @ApiModelProperty(notes = "汇率时间")
    private String rateTime;

    @ApiModelProperty(notes = "汇率基准来源")
    private String baseRateSource;

    @ApiModelProperty(notes = "是否使用NDF")
    private String isUseNdf;

    @ApiModelProperty(notes = "NDF币种")
    private String ndfCurrency;

    @ApiModelProperty(notes = "是否有接入费")
    private String hasAccessFee;

    @ApiModelProperty(notes = "接入费用详情")
    private String accessFeeDetail;

    @ApiModelProperty(notes = "是否后收费")
    private String isChargeAfterward;

    @ApiModelProperty(notes = "后收费详情")
    private String chargeAfterwardDetail;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;
}
