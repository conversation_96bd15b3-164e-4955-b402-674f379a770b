package com.payermax.channel.inst.center.app.manage.calendar;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarSaveRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/11
 * @DESC
 */
@Slf4j
@Component
public class InstFinancialCalendarContextBuilder {


    @Autowired
    private InstFinancialCalendarRepository calendarRepository;
    @Autowired
    private InstFinancialCalendarHolidayRepository holidayRepository;
    @Autowired
    private InstBusinessDraftRepository businessDraftRepository;
    @NacosValue(value = "${inst.financialCalendar.bank.allow.set}", autoRefreshed = true)
    private Set<String> bankCalendarAllowSet;

    /**
     * 校验并构建金融日历保存流程上下文数据
     * @param request 请求参数
     * @return 金融日历上下文对象
     */
    public InstFinancialCalendarContext validateAndBuildContext(InstFinancialCalendarSaveRequest request) {

        // 构建上下文
        InstFinancialCalendarContext context = contextBuild(request);

        // 流程校验
        processValid(context);

        // 校验日历
        calendarValid(context);

        // 校验并填充节假日列表
        context.setHolidayList(holidayValidAndFill(context));
        return context;
    }

    /**
     * 校验并填充已存在的节假日列表
     */
    private List<InstFinancialCalendarHoliday> holidayValidAndFill(InstFinancialCalendarContext context) {
        InstFinancialCalendar calendar = context.getCalendar();
        List<InstFinancialCalendarHoliday> holidayList = context.getHolidayList();

        // 查询已存在的节假日列表
        Map<LocalDate, InstFinancialCalendarHoliday> existHolidayMap = holidayRepository.queryByConditions(InstFinancialCalendarHolidayPO.builder().calendarId(calendar.getCalendarId()).build(), null, null)
                .stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain)
                .collect(Collectors.toMap(InstFinancialCalendarHoliday::getHolidayDate, Function.identity()));

        // 节假日列表校验
        AssertUtil.isTrue(holidayList.stream().allMatch(holiday -> Objects.nonNull(holiday.getHolidayDate())), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "节假日时间不能为空");

        // 填充已存在的节假日 ID 并判断是否存在变更
        holidayList.forEach(holiday -> Optional.ofNullable(existHolidayMap.get(holiday.getHolidayDate()))
                .ifPresent( existHoliday -> {
                    holiday.setIsChanged(holidayIsChanged(holiday, existHoliday));
                    holiday.setHolidayId(existHoliday.getHolidayId());
                }));
        // 过滤掉无变更的数据
        holidayList = holidayList.stream().filter(InstFinancialCalendarHoliday::getIsChanged).collect(Collectors.toList());

        // 根据日期排序
        return holidayList.stream().sorted(Comparator.comparing(InstFinancialCalendarHoliday::getHolidayDate)).collect(Collectors.toList());
    }

    /**
     * 判断节假日是否发生变更
     */
    private Boolean holidayIsChanged(InstFinancialCalendarHoliday holiday, InstFinancialCalendarHoliday originHoliday){
        // 所有条件都为 true 时，isChanged 为 false
        return !Stream.of(
                Objects.equals(holiday.getDescription(), (originHoliday.getDescription())),
                Objects.equals(holiday.getIsWorkday(), (originHoliday.getIsWorkday())),
                Objects.equals(holiday.getHolidayOperate(), (originHoliday.getHolidayOperate()))
        ).allMatch(isEqual -> isEqual);
    }


    /**
     * 构建上下文
     */
    private InstFinancialCalendarContext contextBuild(InstFinancialCalendarSaveRequest request) {
        InstFinancialCalendar calendar = InstFinancialCalendarAssembler.INSTANCE.request2Calendar(request);
        List<InstFinancialCalendarHoliday> holidayList = request.getHolidayList().stream()
                .map( dto -> InstFinancialCalendarAssembler.INSTANCE.dto2Holiday(dto, calendar))
                .collect(Collectors.toList());

        return InstFinancialCalendarContext.builder().holidayDTOList(request.getHolidayList())
                .holidayList(holidayList).calendar(calendar)
                .generateRelatedCountry(request.getGenerateRelatedCountry()).relatedCountry(request.getRelatedCountry())
                .generateRelatedCurrency(request.getGenerateRelatedCurrency()).relatedCurrency(request.getRelatedCurrency())
                .build();
    }

    /**
     * 流程校验
     */
    private void processValid(InstFinancialCalendarContext context) {
        log.info("流程唯一校验，业务主键：{}", context.getCalendar().getCalendarId());
        // 流程唯一校验
        InstBusinessDraft draftDomain = InstBusinessDraft.builder().businessType(BusinessTypeEnum.INST_CENTER).moduleName(OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER)
                .businessKey(context.getCalendar().getCalendarId()).status(InstProcessStatusEnum.PROCESSING).build();
        List<InstBusinessDraftPO> processingDraftList = businessDraftRepository.queryByConditions(InstBusinessDraftAssembler.INSTANCE.domain2Po(draftDomain));
        AssertUtil.isTrue(CollectionUtils.isEmpty(processingDraftList), ErrorCodeEnum.MORE_THAN_ONE.getCode(), "存在正在处理的业务，请稍后再试");
    }

    /**
     * 日历校验
     */
    private void calendarValid(InstFinancialCalendarContext context) {
        InstFinancialCalendar calendar = context.getCalendar();
        AssertUtil.isTrue(Objects.nonNull(calendar.getCalendarYear()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "年度不能为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(calendar.getWeekendList()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "周末列表不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(calendar.getOwner()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "负责人不能为空");
        if(context.getGenerateRelatedCurrency()){
            AssertUtil.isTrue(StringUtils.isNotBlank(context.getRelatedCurrency()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "生成关联日历时，关联币种不能为空");
        }
        if(context.getGenerateRelatedCountry()){
            AssertUtil.isTrue(StringUtils.isNotBlank(context.getRelatedCountry()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "生成关联日历时，关联国家不能为空");
        }
        calendarTypeValid(calendar, true);
    }

    /**
     * 日历类型校验
     */
    public void calendarTypeValid(InstFinancialCalendar calendar, boolean bankAllowCheck){
        // 日历类型校验
        switch (calendar.getCalendarType()) {
            case COUNTRY:
                AssertUtil.isTrue(StringUtils.isNotBlank(calendar.getCountry()) && !CommonConstants.STAR.equals(calendar.getCountry()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "国家节假日，必须设定国家，且国家不能为 * ");
                AssertUtil.isTrue(CommonConstants.STAR.equals(calendar.getCurrency()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "国家节假日，币种必须为 * 或不设置");
                break;
            case CURRENCY:
                AssertUtil.isTrue(StringUtils.isNotBlank(calendar.getCurrency()) && !CommonConstants.STAR.equals(calendar.getCurrency()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "币种节假日，必须设定币种，且币种不能为 * ");
                AssertUtil.isTrue(CommonConstants.STAR.equals(calendar.getCountry()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "币种节假日，国家必须为 * 或不设置");
                break;
            case BANK:
                AssertUtil.isTrue(StringUtils.isNotBlank(calendar.getCountry()) && StringUtils.isNotBlank(calendar.getCurrency()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "银行节假日，必须设定国家+币种");
                AssertUtil.isTrue(!CommonConstants.STAR.equals(calendar.getCountry()) && !CommonConstants.STAR.equals(calendar.getCurrency()), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "银行节假日，必须设定国家+币种");
                // 是否检查银行节假日允许列表
                if(bankAllowCheck){
                    log.info("银行节假日，允许币种：{}", bankCalendarAllowSet);
                    AssertUtil.isTrue(bankCalendarAllowSet.contains(String.format("%s-%s", calendar.getCountry(), calendar.getCurrency())), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "银行节假日，不在允许的列表中");
                }
                break;
            default:
                throw new BizException(ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR, "不支持的日历类型");
        }

    }


    /**
     * 根据年份、国家、币种生成日历ID
     */
    public String generateCalendarId(String year, String country, String currency) {
        InstFinancialCalendarPO po = InstFinancialCalendarPO.builder().calendarYear(year).country(country).currency(currency).build();
        List<InstFinancialCalendarPO> calendarList = calendarRepository.queryByConditions(po);
        AssertUtil.isTrue(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(calendarList),ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "日历已存在");

        // 生成 ID
        return Stream.of(year, country, currency).map(item -> StringUtils.isNotBlank(item) ? item : CommonConstants.STAR).collect(Collectors.joining("-"));
    }
}
