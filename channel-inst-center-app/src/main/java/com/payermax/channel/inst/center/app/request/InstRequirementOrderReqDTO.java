package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 集成需求单请求
 *
 * <AUTHOR>
 * @date 2022/6/4 14:36
 */
@Data
public class InstRequirementOrderReqDTO implements Serializable {

    private static final long serialVersionUID = 748898332867060723L;

    private Long id;

    private String applyNo;

    private Long instId;

    private String apiDocId;

    private String apiDocUrl;

    private String platformUrl;

    private String prodPlatformUrl;

    private String payDocId;

    private String payDocUrl;

    private String refundDocId;

    private String refundDocUrl;

    private String disputeDocId;

    private String disputeDocUrl;

    private String billDocId;

    private String billDocUrl;

    private String problemHandleDocId;

    private String problemHandleDocUrl;

    private String releaseRequireDocId;

    private String releaseRequireDocUrl;

    @Size(max = 256, message = "[remark] maximum 256 length")
    private String remark;

    private String status;

    @ApiModelProperty(notes = "机构账号集合")
    @Valid
    private List<InstAccountReqDTO> accountList;

    @ApiModelProperty(notes = "机构商户报备信息")
    @Valid
    private InstReportMerchantReqDTO instReportMerchant;


    @ApiModelProperty(notes = "机构对账信息")
    @Valid
    private InstReconcileReqDTO instReconcile;
}
