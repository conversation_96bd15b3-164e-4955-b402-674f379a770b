package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstAuditResultReqDTO;
import com.payermax.channel.inst.center.app.response.InstAuditResultVO;

/**
 * 审核结果相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/19 9:47
 */
public interface InstAuditResultManager {

    /**
     * 查询审核结果
     *
     * @return
     */
    InstAuditResultVO query(InstAuditResultReqDTO instAuditResultReqDTO);

    /**
     * 保存审核结果
     *
     * @return
     */
    int save(InstAuditResultReqDTO instAuditResultReqDTO);
}
