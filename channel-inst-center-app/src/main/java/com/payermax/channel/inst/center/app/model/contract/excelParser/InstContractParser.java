package com.payermax.channel.inst.center.app.model.contract.excelParser;

import com.payermax.channel.inst.center.app.assembler.domain.InstContractDraftAssembler;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.app.valid.excel.impl.CommonExcelValid;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractStandardProductRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public abstract class InstContractParser{


    @Resource
    InstContractDraftAssembler instContractDraftAssembler;

    @Resource
    InstContractBaseInfoRepository instContractBaseInfoRepository;

    @Resource
    InstContractStandardProductRepository instContractStandardProductRepository;

    @Resource
    protected CommonExcelValid commonExcelValid;

    /**
     * 解析策略
     */
    public static Map<ContractBizTypeEnum,InstContractParser> PARSER_MAP = new ConcurrentHashMap<>();

    /**
     * 注册解析器
     */
    public static void registryParser(ContractBizTypeEnum contractBizType, InstContractParser instContractParser){
        log.info("registry contract parser type: {}, strategy: {}", contractBizType, instContractParser);
        AssertUtil.isTrue(Objects.nonNull(contractBizType), ErrorCodeEnum.INST_CENTER_FEE_FINDER_STRATEGY_ERROR.getCode(), "合约查询器类型应为枚举值");
        AssertUtil.isTrue(Objects.nonNull(instContractParser), ErrorCodeEnum.INST_CENTER_FEE_FINDER_STRATEGY_ERROR.getCode(), "合约查询器不能为空");
        PARSER_MAP.putIfAbsent(contractBizType,instContractParser);
    }

    /**
     * 获取解析器
     */
    public static InstContractParser getParser(ContractBizTypeEnum contractBizType){
        log.info("get contract parser type: {}", contractBizType);
        AssertUtil.isTrue(PARSER_MAP.containsKey(contractBizType), ErrorCodeEnum.INNER_ERROR.getCode(), "未注册的策略");
        return PARSER_MAP.get(contractBizType);
    }

    /**
     * 构建上下文
     */
    public abstract InstContractContext buildContext(InstContractParseRequestDTO request);

    /**
     * 解析 Excel，构造原始数据
     */
    public abstract void resolve(byte[] fileContent, InstContractContext context);

    /**
     * Excel 数据校验
     */
    public abstract void valid(InstContractContext context);

    /**
     * 校验费用信息的唯一性
     */
    public void feeMsgUniqueCheck(InstContractImportDTO instContractImportDTO){
        // TODO: 2023/6/9 校验费用信息的唯一性
    }

    /**
     * Excel 数据校验正常时进行数据处理
     */
    public abstract InstContractImportDTO dataHandle(InstContractContext context);

    /**
     * Excel 数据校验异常时处理逻辑
     */
    public abstract InstContractImportDTO errorHandle(InstContractContext context);

    /**
     * 根据入参及合同文件解析并返回数据
     * @param request
     * @param fileContent
     */
    public InstContractImportDTO parser(InstContractParseRequestDTO request, byte[] fileContent){


        // 构建上下文
        InstContractContext context = buildContext(request);

        // 1. 解析
        resolve(fileContent,context);

        // 2. 校验
        valid(context);

        // 3. 校验不通过时返回错误信息
        if(context.getIsHasError()){
            return errorHandle(context);
        }

        // 4. 校验通过时数据处理并返回
        InstContractImportDTO instContractImportDTO = dataHandle(context);

        feeMsgUniqueCheck(instContractImportDTO);

        return instContractImportDTO;

    }
}
