package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;

import java.util.List;

/**
 * 机构银行卡信息相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/17 22:32
 */
public interface InstBankAccountManager {


    /**
     * 基于指定条件，查询机构银行账户
     * 备注：这里单写一个是因为其它类似selectAll传参不灵活，比如in条件场景下，希望传list，但是实体是String，因此新开一个
     *
     * @return
     */
    List<InstBankAccountVO> queryByCondition(InstBankAccountReqDTO instBankAccountReqDTO);

    /**
     * 保存机构银行卡信息
     *
     * @return
     */
    int save(InstBankAccountReqDTO instBankAccountReqDTO);

    /**
     * 保存机构银行卡信息
     *
     * @return
     */
    int saveBatch(List<InstBankAccountReqDTO> instBankAccountReqDTO);

    /**
     * 删除机构银行卡信息
     *
     * @return
     */
    int delete(InstBankAccountReqDTO instBankAccountReqDTO);
}
