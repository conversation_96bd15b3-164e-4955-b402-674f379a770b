package com.payermax.channel.inst.center.app.model.contract.desensitizer;

import com.alibaba.fastjson.serializer.ValueFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/1/11
 * @DESC
 */
@Slf4j
public class FeeValueDesensitizeFilter implements ValueFilter {


    private final SensitiveType filterType;

    public FeeValueDesensitizeFilter(SensitiveType filterType) {
        this.filterType = filterType;
    }

    @Override
    public Object process(Object object, String name, Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return value;
        }

        try {
            // HashMap 无法转换为实体判断，只能扫描所有值判断，先行忽略，布尔值无法重写为字符串，忽略
            if (object instanceof HashMap || object instanceof Boolean) {
                return value;
            }
            Field field = object.getClass().getDeclaredField(name);
            // 获取实体类上的注解
            SensitiveInfo sensitiveInfo = field.getAnnotation(SensitiveInfo.class);
            if (ObjectUtils.isEmpty(sensitiveInfo)) {
                return value;
            }
            SensitiveType sensitiveType = sensitiveInfo.value();
            // 判断过滤器类型
            if(StringUtils.isNotBlank(filterType.name()) && !SensitiveType.ALL_VALUE.equals(filterType)){
                // 替换数据
                if(sensitiveType.equals(filterType)){
                    return AuthConstant.NO_PERMISSION;
                }else{
                    return value;
                }
            }else{
                // 兜底逻辑
                switch (sensitiveType){
                    case FEE_VALUE:
                    case TAX_VALUE:
                    case FX_VALUE:
                    case SETTLE_VALUE:
                    default:
                        return AuthConstant.NO_PERMISSION;
                }
            }
        } catch (NoSuchFieldException e) {
            log.error("当前数据类型为{}, 属性名 {},值为{}", object.getClass(), name, value);
        }
        return AuthConstant.NO_PERMISSION;
    }
}