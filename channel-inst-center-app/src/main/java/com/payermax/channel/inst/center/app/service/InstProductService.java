package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;

import java.util.List;

/**
 * @Description 机构产品 Service
 * <AUTHOR>
 * @Date 2022/5/18 14:47
 */
public interface InstProductService {
    /**
     * 保存产品
     *
     * @param record
     * @return
     */
    int save(InstProductEntity record);

    /**
     * 查询产品
     *
     * @param instProductEntity
     * @return
     */
    List<InstProductEntity> getByEntity(InstProductEntity instProductEntity);

    /**
     * 根据产品编码查询产品
     *
     * @param productCodes
     * @return
     */
    List<InstProductEntity> getByProductCodes(List<String> productCodes);

    /**
     * 根据机构id查询产品
     *
     * @param instIds
     * @return
     */
    List<InstProductEntity> getByInstIds(List<Long> instIds);

    /**
     * 根据产品编码删除产品信息
     * @param productCode
     * @return
     */
    int deleteByProductCode(String productCode);
}
