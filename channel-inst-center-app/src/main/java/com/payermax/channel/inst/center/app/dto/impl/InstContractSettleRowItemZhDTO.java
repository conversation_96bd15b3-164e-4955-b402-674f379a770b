package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.valid.annon.impl.CompanyEntityValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.CountryValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.InstCodeValid;
import com.payermax.channel.inst.center.app.valid.annon.impl.InstFundsAccountIdValid;
import com.payermax.channel.inst.center.common.constrains.*;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * InstContractSettleRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class InstContractSettleRowItemZhDTO extends InstContractSettleRowItemDTO {

    @ExcelProperty({ "", "录入" })
    private String enteredBy;

    @ExcelProperty({ "", "渠道签约主体全名" })
    private String instLegalName;

    @NotEmpty(message = "机构简称不能为空")
    @InterfaceAnnotation(clazz = InstCodeValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "机构简称" })
    private String instCode;

    @NotEmpty(message = "我方签约主体编码不能为空")
    @InterfaceAnnotation(clazz = CompanyEntityValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "我方签约主体编码" })
    private String contractEntity;

    @NotEmpty(message = "机构产品类型不能为空")
    @EnumAnnotation(values = ContractBusinessTypeEnum.class)
    @ExcelProperty({ "", "机构产品类型" })
    private String instProductType;

    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty({ "", "机构产品名称" })
    private String instProductName;

    @ExcelProperty({ "", "支付方式类型" })
    private String paymentMethodType;

    @ExcelProperty({ "", "目标机构" })
    private String targetOrg;

    @ExcelProperty({ "", "卡组" })
    private String cardOrg;

    @NotEmpty(message = "支付币种不能为空")
    @CurrencyAnnotation
    @ExcelProperty({ "", "支付币种" })
    private String payCurrency;

    @ExcelProperty({ "", "卡片类型" })
    private String cardType;

    @InterfaceAnnotation(clazz = CountryValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "发卡国家" })
    private String cardIssueCountry;

    @ExcelProperty({ "", "机构MID" })
    private String originMid;
    @ExcelProperty({ "", "渠道商户号" })
    private String channelMerchantNo;

    @ExcelProperty({ "", "机构MCC" })
    private String originMcc;
    @ExcelProperty({ "", "标准化MCC" })
    private String standardMcc;

    @NotEmpty(message = "结算币种不能为空")
    @CurrencyAnnotation
    @ExcelProperty({ "", "结算币种" })
    private String settleCurrency;

    @EnumAnnotation(values = FeeCalculateTypeEnum.class)
    @ExcelProperty({ "", "计费方式" })
    private String calculateType;

    @PercentAnnotation
    @ExcelProperty({ "", "单笔比例" })
    private String singleRate;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "最低收费" })
    private String minCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "封顶收费" })
    private String maxCharge;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "单笔固定金额" })
    private String singleFixedAmount;

    @CurrencyAnnotation
    @ExcelProperty({ "", "单笔固定币种" })
    private String singleFixedCurrency;

    @NumberAnnotation(isInteger = false)
    @ExcelProperty({ "", "起结金额" })
    private String minSettleAmount;

    @InterfaceAnnotation(clazz = InstFundsAccountIdValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "我方收款账户" })
    private String accountId;

    @NotEmpty(message = "提现收款方式不能为空")
    @EnumAnnotation(values = WithdrawMethodEnum.class)
    @ExcelProperty({ "", "提现收款方式" })
    private String withdrawMethod;

    @InterfaceAnnotation(clazz = InstFundsAccountIdValid.class, lang = LangEnum.ZH)
    @ExcelProperty({ "", "充值帐户" })
    private String chargeAccountId;

    @ExcelProperty({ "", "机构银行帐户" })
    private String instBankAccountId;

    @CurrencyAnnotation
    @ExcelProperty({ "", "充值币种" })
    private String chargeCurrency;

    @Data
    public static class SettleDateInfoZhImpl extends SettleDateInfo {

        @NotEmpty(message = "账单日不能为空")
        private String billDate;

        @NotEmpty(message = "交易开始日期不能为空")
        private String transactionStartDate;

        @NotEmpty(message = "交易结束日期不能为空")
        private String transactionEndDate;

        @NotEmpty(message = "打款日不能为空")
        private String paymentDate;

        @NotEmpty(message = "到账日不能为空")
        private String arriveDate;
    }
}
