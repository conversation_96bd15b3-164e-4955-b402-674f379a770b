package com.payermax.channel.inst.center.app.manage.calendar.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContext;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContextBuilder;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarSaveRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.TemplateUtil;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.HolidayOperateEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/27
 */
@Slf4j
@Component
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER, actionType = OperateTypeEnum.ADD)
public class InstFinancialCalendarSaveHandler extends AbstractWorkflowHandler {


    @Resource
    private IDGenerator idGenerator;
    @Resource
    private InstFinancialCalendarContextBuilder calendarContextBuilder;
    @Resource
    private InstFinancialCalendarRepository calendarRepository;
    @Resource
    private InstFinancialCalendarHolidayRepository holidayRepository;
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarSaveProcess.key:process_inst-center-financial-calendar-save-review}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarSaveProcess.desc:金融日历保存审批}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarSaveProcess.formMsgTemplate:日期：#!{holidayDate} | 是否工作日：#!{isWorkday} | 描述：#!{description} }", autoRefreshed = true)
    private String processFormMsgTemplate;


    /**
     * 初始化日历和节假日
     * @param request 请求参数
     * @return 初始化结果
     */
    public Boolean startCalendarSave(InstFinancialCalendarSaveRequest request) {

        // 前置校验
        preCheck(request);

        // 校验数据并构建上下文
        log.info("InstFinancialCalendarSaveHandler initCalendarAndHolidayDraft");
        InstFinancialCalendarContext context = calendarContextBuilder.validateAndBuildContext(request);

        // 发起流程
        log.info("InstFinancialCalendarSaveHandler initCalendarAndHolidayDraft startProcess");
        InstBusinessDraft draft = InstFinancialCalendarAssembler.INSTANCE.calendarSaveContext2Draft(context, idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.FINANCIAL_CALENDAR), request.getShareId());
        return super.startProcess(draft);
    }

    /**
     * 前置校验
     */
    private void preCheck(InstFinancialCalendarSaveRequest request) {
        InstFinancialCalendarPO calendarPo = calendarRepository.getById(request.getCalendarId());
        AssertUtil.isTrue(ObjectUtils.isNull(calendarPo), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_CHECK_ERROR.getCode(), "日历已存在，无法新增");
    }


    /* ---------------------------------------  流程相关  ---------------------------------------*/

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        processConfig.setProcessDefKey(processKey);
        processConfig.setProcessDesc(processDesc);
        // 草稿表单信息
        InstFinancialCalendarContext context = JSON.parseObject(draft.getDraftData(), InstFinancialCalendarContext.class);
        List<InstFinancialCalendarHoliday> addHolidayList  = context.getHolidayList().stream()
                .filter(holiday -> holiday.getHolidayOperate().equals(HolidayOperateEnum.ADD)).collect(Collectors.toList());
        List<InstFinancialCalendarHoliday> cancelHolidayList  = context.getHolidayList().stream()
                .filter(holiday -> holiday.getHolidayOperate().equals(HolidayOperateEnum.CANCEL)).collect(Collectors.toList());

        HashMap<String, Object> formMsgMap = new HashMap<>(4);
        formMsgMap.putAll(JSONObject.parseObject(JSONObject.toJSONString(context.getCalendar()), new TypeReference<HashMap<String, Object>>() {}));
        formMsgMap.put("businessKey", draft.getBusinessKey());
        formMsgMap.put("weekendListStr", String.join(",", context.getCalendar().getWeekendList()));
        formMsgMap.put("addHolidayList", composeHolidayFormMsg(addHolidayList, processFormMsgTemplate));
        formMsgMap.put("cancelHolidayList", composeHolidayFormMsg(cancelHolidayList, processFormMsgTemplate));
        formMsgMap.put("generateRelatedCalendar", String.valueOf(context.getGenerateRelatedCountry() || context.getGenerateRelatedCurrency()));
        if(context.getGenerateRelatedCurrency()){
            formMsgMap.put("relatedMsg", context.getRelatedCurrency());
        }
        if(context.getGenerateRelatedCountry()){
            formMsgMap.put("relatedMsg", context.getRelatedCountry());
        }
        processConfig.setFormInfoMap(formMsgMap);
        return processConfig;
    }


    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarSaveHandler businessPassHandler");
        InstFinancialCalendarContext context = JSON.parseObject(draft.getDraftData(), InstFinancialCalendarContext.class);
        return transactionTemplate.execute( transactionStatus -> {
            // 生成并保存关联日历
            String relatedCalendarId = generateRelatedCalendar(context);
            context.getCalendar().setTargetCalendar(relatedCalendarId);
            // 保存日历
            calendarRepository.save(InstFinancialCalendarAssembler.INSTANCE.domain2Po(context.getCalendar()));
            log.info("InstFinancialCalendarSaveHandler businessPassHandler save calendar");
            // 保存节假日
            List<InstFinancialCalendarHolidayPO> holidayPoList = context.getHolidayList().stream().map(InstFinancialCalendarAssembler.INSTANCE::domain2Po).collect(Collectors.toList());
            holidayRepository.saveBatch(holidayPoList);
            log.info("InstFinancialCalendarSaveHandler businessPassHandler save calendar holiday");
            return Boolean.TRUE;
        });
    }


    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarSaveHandler businessRejectHandler");
        return Boolean.TRUE;
    }

    /**
     * 生成关联日历信息
     */
    private String generateRelatedCalendar(InstFinancialCalendarContext context) {
        String calendarId = null;
        if(context.getGenerateRelatedCurrency() || context.getGenerateRelatedCountry()) {
            InstFinancialCalendar calendar = InstFinancialCalendarAssembler.INSTANCE.deepcopy(context.getCalendar());
            if (context.getGenerateRelatedCountry()) {
                calendar.setCountry(context.getRelatedCountry());
                calendar.setCurrency(CommonConstants.STAR);
            }
            if (context.getGenerateRelatedCurrency()) {
                calendar.setCountry(CommonConstants.STAR);
                calendar.setCurrency(context.getRelatedCurrency());
            }
            calendar.setSourceCalendar(context.getCalendar().getCalendarId());
            calendarId = calendarContextBuilder.generateCalendarId(String.valueOf(calendar.getCalendarYear()), calendar.getCountry(), calendar.getCurrency());
            calendar.setCalendarId(calendarId);
            calendarRepository.save(InstFinancialCalendarAssembler.INSTANCE.domain2Po(calendar));
        }
        return calendarId;
    }

    /**
     * 构造节假日表单信息
     */
    private String composeHolidayFormMsg(List<InstFinancialCalendarHoliday> formMsgList, String template){
        String formatedTemplate = template.replace("#!", "$!");
        return formMsgList.stream().map(item -> {
            Map<String, Object> params = JSON.parseObject(JSON.toJSONString(item), new TypeReference<Map<String, Object>>() {});
            return TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, formatedTemplate, params);
        }).collect(Collectors.joining(","));
    }

}
