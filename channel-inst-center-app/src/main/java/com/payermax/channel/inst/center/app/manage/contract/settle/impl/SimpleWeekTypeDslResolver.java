package com.payermax.channel.inst.center.app.manage.contract.settle.impl;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.manage.contract.settle.AbstractDslSimpleTypeResolver;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR> 2023/6/21  3:28 PM
 */
@Component
@Slf4j
public class SimpleWeekTypeDslResolver extends AbstractDslSimpleTypeResolver {

    @Override
    public Set<DSLTypeEnum> supportedScenarios() {
        return Sets.newHashSet(DSLTypeEnum.SIMPLE_WEEK);
    }

    @Override
    public RoundTypeEnum getRoundType() {
        return RoundTypeEnum.WEEK;
    }
}
