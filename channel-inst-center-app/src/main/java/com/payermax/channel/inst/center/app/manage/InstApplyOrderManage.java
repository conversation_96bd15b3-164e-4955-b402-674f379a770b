package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.app.request.InstApplyOrderReqDTO;
import com.payermax.channel.inst.center.app.response.ApplyOrderQueryVO;

import java.util.List;

/**
 * @Description 机构申请单 manage
 * <AUTHOR>
 * @Date 2022/5/17 23:12
 */
public interface InstApplyOrderManage {
    /**
     * 查所有申请单
     * @param instApplyOrderReqDTO
     * @return
     */
    PageResult<ApplyOrderQueryVO> queryApplyOrders(InstApplyOrderReqDTO instApplyOrderReqDTO);

    /**
     * 保存申请单相关信息
     * @param instApplyOrderReqDTO
     */
    String save(InstApplyOrderReqDTO instApplyOrderReqDTO);

    /**
     * 根据申请单号查询申请单信息
     * @param applyNo
     * @return
     */
    ApplyOrderQueryVO query(String applyNo);

    /**
     * 根据品牌+机构+接入国家/地区+接入产品
     * 查询已存在的申请单
     * @param instApplyOrderReqDTO
     * @return
     */
    List<ApplyOrderQueryVO> queryExistedApplyOrders(InstApplyOrderReqDTO instApplyOrderReqDTO);
}
