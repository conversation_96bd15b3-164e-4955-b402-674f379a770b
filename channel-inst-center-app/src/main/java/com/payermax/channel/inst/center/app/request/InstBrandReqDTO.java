package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstBrandReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 21:17
 * @Version 1.0
 */
@Data
public class InstBrandReqDTO implements Serializable {

    private static final long serialVersionUID = -8098626094120609765L;
    /**
     * 品牌标识
     */
    @ApiModelProperty(notes = "品牌标识")
    private Long brandId;

    /**
     * 品牌编码
     */
    @ApiModelProperty(notes = "品牌编码")
    private String brandCode;

    /**
     * 品牌名称
     */
    @ApiModelProperty(notes = "品牌名称")
    private String brandName;

    /**
     * BD负责人
     */
    @ApiModelProperty(notes = "BD负责人")
    private String bdId;

    /**
     * BD负责人名称
     */
    @ApiModelProperty(notes = "BD负责人名称")
    private String bdName;

    /**
     * 状态 Y：启用，N：停用
     */
    @ApiModelProperty(notes = "状态")
    private String status;
}
