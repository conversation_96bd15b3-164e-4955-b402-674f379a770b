package com.payermax.channel.inst.center.app.manage.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity;
import com.payermax.channel.inst.center.app.manage.InstProductFeeManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstProductFeeReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductFeeVO;
import com.payermax.channel.inst.center.app.service.InstContractProductService;
import com.payermax.channel.inst.center.app.service.InstProductFeeService;
import com.payermax.channel.inst.center.app.service.InstProductService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InstProductFeeManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 0:12
 */
@Service
public class InstProductFeeManagerImpl implements InstProductFeeManager {

    @Autowired
    private InstContractProductService instContractProductService;

    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductFeeService instProductFeeService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;
    
    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public int saveAll(List<InstProductFeeReqDTO> productFeeReqDTOList) {
        //TODO 检查合同签约产品信息
        List<InstProductFeeEntity> instProductFeeEntities = reqDtoAssembler.toInstProductFeeEntities(productFeeReqDTOList);
        int result = instProductFeeService.saveAll(instProductFeeEntities);
        return result;
    }

    @Override
    public List<InstProductFeeVO> query(InstProductFeeReqDTO productFeeReqDTO) {
        Preconditions.checkArgument(productFeeReqDTO != null && StringUtils.isNotBlank(productFeeReqDTO.getContractNo()) && StringUtils.isNotBlank(productFeeReqDTO.getInstProductCode()),"contractNo & productCode is mandatory");
        List<String> productCodes = Arrays.asList(productFeeReqDTO.getInstProductCode());
        List<InstProductEntity> instProductEntityList = instProductService.getByProductCodes(productCodes);
        if(CollectionUtils.isEmpty(instProductEntityList)){
            return null;
        }
        List<InstProductFeeVO> instProductFeeVOList = instProductEntityList.stream().map(product -> {
            InstProductFeeVO productFeeVO = new InstProductFeeVO();
            productFeeVO.setContractNo(productFeeReqDTO.getContractNo());
            productFeeVO.setInstProductCode(product.getProductCode());
            productFeeVO.setChannelType(product.getChannelType());
            productFeeVO.setPaymentMethodType(product.getPaymentMethodType());
            productFeeVO.setProductName(product.getProductName());
            return productFeeVO;
        }).collect(Collectors.toList());
        List<InstProductFeeEntity> instProductFeeEntityList = instProductFeeService.getByContractNoAndProductCodes(productFeeReqDTO.getContractNo(), productCodes);
        if(CollectionUtils.isNotEmpty(instProductFeeEntityList)){
            instProductFeeVOList = instProductFeeVOList.stream().map(productFeeVO -> {
                InstProductFeeEntity productFeeEntity = instProductFeeEntityList.get(0); //NO_CHECK 方法未被调用
                InstProductFeeVO instProductFeeVO = respVoAssembler.toInstProductFeeVo(productFeeEntity);
                instProductFeeVO.setContractNo(productFeeVO.getContractNo());
                instProductFeeVO.setInstProductCode(productFeeVO.getInstProductCode());
                instProductFeeVO.setChannelType(productFeeVO.getChannelType());
                instProductFeeVO.setPaymentMethodType(productFeeVO.getPaymentMethodType());
                instProductFeeVO.setProductName(productFeeVO.getProductName());
                return instProductFeeVO;
            }).collect(Collectors.toList());
        }
        return instProductFeeVOList;
    }

    @Override
    public List<InstProductFeeVO> queryAll(String contractNo) {
        InstContractProductEntity contractProductEntity = new InstContractProductEntity();
        contractProductEntity.setContractNo(contractNo);
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(contractProductEntity);
        if(CollectionUtils.isEmpty(instContractProductEntities)){
            return null;
        }
        List<String> productCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCode).distinct().collect(Collectors.toList());
        List<InstProductEntity> instProductEntities = instProductService.getByProductCodes(productCodes);
        if(CollectionUtils.isEmpty(instProductEntities)){
            return null;
        }
        List<InstProductFeeVO> instProductFeeVOList = instProductEntities.stream()
                .sorted(Comparator.comparing(InstProductEntity::getChannelType)).map(product -> {
            InstProductFeeVO productFeeVO = new InstProductFeeVO();
            productFeeVO.setContractNo(contractNo);
            productFeeVO.setInstProductCode(product.getProductCode());
            productFeeVO.setChannelType(product.getChannelType());
            productFeeVO.setPaymentMethodType(product.getPaymentMethodType());
            productFeeVO.setProductName(product.getProductName());
            return productFeeVO;
        }).collect(Collectors.toList());
        List<InstProductFeeEntity> instProductFeeEntities = instProductFeeService.getByContractNoAndProductCodes(contractNo, productCodes);
        if(CollectionUtils.isNotEmpty(instProductFeeEntities)){
            Map<String, InstProductFeeEntity> productFeeMap = instProductFeeEntities.stream().collect(Collectors.toMap(InstProductFeeEntity::getInstProductCode, Function.identity()));
            instProductFeeVOList = instProductFeeVOList.stream().map(productFeeVO -> {
                InstProductFeeEntity productFeeEntity = productFeeMap.get(productFeeVO.getInstProductCode());
                if (productFeeEntity != null) {
                    BeanUtils.copyProperties(productFeeEntity, productFeeVO);
                }
                return productFeeVO;
            }).collect(Collectors.toList());
        }
        return instProductFeeVOList;
    }
}
