package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContractDao;
import com.payermax.channel.inst.center.app.service.InstContractService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstContractServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 23:32
 */
@Service
public class InstContractServiceImpl implements InstContractService {
    @Autowired
    private InstContractDao instContractDao;

    @Override
    public int save(InstContractEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getContractNo() == null) {
            record.setContractNo("HT"+System.currentTimeMillis());
            // 新增
            result = instContractDao.insert(record);
        } else {
            // 更新
            result = instContractDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstContractEntity getByEntity(InstContractEntity instContractEntity) {
        Preconditions.checkArgument(instContractEntity != null, "param instContractEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instContractEntity.getContractNo()) || (StringUtils.isNotBlank(instContractEntity.getApplyNo()) && instContractEntity.getInstId() != null), "param contractNo or applyNo & instId is mandatory");

        List<InstContractEntity> instContractEntities = instContractDao.selectAll(instContractEntity);
        if (CollectionUtils.isNotEmpty(instContractEntities)) {
            return instContractEntities.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public List<InstContractEntity> getByEntityList(InstContractEntity entity) {
        Preconditions.checkArgument(entity != null && entity.getInstId() != null, "param instId is mandatory");
        List<InstContractEntity> instContractEntities = instContractDao.selectAll(entity);
        return instContractEntities;
    }

    @Override
    public List<InstContractEntity> getByInstIds(List<Long> instIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instIds), "param instIds is mandatory");
        List<InstContractEntity> instContractEntities = instContractDao.selectByInstIds(instIds);
        return instContractEntities;
    }
}
