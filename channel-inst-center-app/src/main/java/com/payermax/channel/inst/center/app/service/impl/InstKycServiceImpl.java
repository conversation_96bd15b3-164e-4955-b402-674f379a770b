package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstKycEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstKycDao;
import com.payermax.channel.inst.center.app.service.InstKycService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * KYC service实现
 *
 * <AUTHOR>
 * @date 2022/5/15 22:45
 */
@Service
public class InstKycServiceImpl implements InstKycService {

    @Autowired
    private InstKycDao instKycDao;

    @Override
    public int save(InstKycEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            if (StringUtils.isBlank(record.getStatus())) {
                record.setStatus("INIT");
            }
            result = instKycDao.insert(record);
        } else {
            // 更新
            result = instKycDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstKycEntity getByInstId(Long instId) {
        Preconditions.checkArgument(instId != null, "param instId is mandatory");

        InstKycEntity queryEntity = new InstKycEntity();
        queryEntity.setInstId(instId);
        List<InstKycEntity> instKycEntityList = instKycDao.selectAll(queryEntity);
        if (CollectionUtils.isNotEmpty(instKycEntityList)) {
            return instKycEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }
}
