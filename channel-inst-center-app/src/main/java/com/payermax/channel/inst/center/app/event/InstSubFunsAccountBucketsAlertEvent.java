package com.payermax.channel.inst.center.app.event;

import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 机构预申请账号
 *
 * <AUTHOR> at 2022/10/26 13:50
 **/
@Getter
public class InstSubFunsAccountBucketsAlertEvent extends ApplicationEvent {

    /**
     * 查询到的预申请账号(不超过500)
     **/
    private List<InstSubFundsAccountBucketEntity> accountBucketEntityList;

    /**
     * 预申请机构信息
     **/
    private InstFundsAccountEntity instFundsAccountEntity;

    /**
     * 申请子级资金账号请求参数
     **/
    private InstSubFundsAccountRequestDO instSubFundsAccountRequestDO;

    public InstSubFunsAccountBucketsAlertEvent(Object source, InstFundsAccountEntity instFundsAccountEntity, List<InstSubFundsAccountBucketEntity> accountBucketEntityList, InstSubFundsAccountRequestDO instSubFundsAccountRequestDO) {
        super(source);
        this.instFundsAccountEntity = instFundsAccountEntity;
        this.accountBucketEntityList = accountBucketEntityList;
        this.instSubFundsAccountRequestDO = instSubFundsAccountRequestDO;
    }
}
