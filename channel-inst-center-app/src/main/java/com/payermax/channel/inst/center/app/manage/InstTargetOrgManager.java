package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstTargetOrgReqDTO;
import com.payermax.channel.inst.center.app.response.InstTargetOrgVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 13:01
 */
public interface InstTargetOrgManager {

    int save(InstTargetOrgReqDTO record);

    int saveAll(List<String> targetOrgs);

    List<InstTargetOrgVO> query(InstTargetOrgReqDTO record);
}
