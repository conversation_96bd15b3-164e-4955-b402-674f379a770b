package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstTargetOrgDao;
import com.payermax.channel.inst.center.app.service.InstTargetOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstTargetOrgServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 13:40
 */
@Service
public class InstTargetOrgServiceImpl implements InstTargetOrgService {

    @Autowired
    private InstTargetOrgDao instTargetOrgDao;

    @Override
    public int save(InstTargetOrgEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instTargetOrgDao.insert(record);
        } else {
            // 更新
            result = instTargetOrgDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public int saveBatch(List<InstTargetOrgEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        int result = instTargetOrgDao.insertBatch(records);
        return result;
    }

    @Override
    public List<InstTargetOrgEntity> getByEntity(InstTargetOrgEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        List<InstTargetOrgEntity> instTargetOrgEntities = instTargetOrgDao.selectAll(record);
        return instTargetOrgEntities;
    }
}
