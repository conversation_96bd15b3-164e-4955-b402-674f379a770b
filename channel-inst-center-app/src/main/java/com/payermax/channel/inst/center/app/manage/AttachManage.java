package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.response.AttachVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件附件Manage
 *
 * <AUTHOR>
 * @date 2022/5/15 17:30
 */
public interface AttachManage {

    /**
     * 查询文件附件
     *
     * @param attachIdList
     * @return
     */
    List<AttachVO> queryAttachs(List<Long> attachIdList);

    /**
     * 保存文件附件
     *
     * @param file
     * @return
     */
    AttachVO save(MultipartFile file);

}
