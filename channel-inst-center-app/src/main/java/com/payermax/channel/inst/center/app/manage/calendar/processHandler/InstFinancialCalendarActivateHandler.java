package com.payermax.channel.inst.center.app.manage.calendar.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContextBuilder;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarActivateRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.TemplateUtil;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/18
 * @DESC
 */
@Slf4j
@Component
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER, actionType = OperateTypeEnum.INVALID)
public class InstFinancialCalendarActivateHandler extends AbstractWorkflowHandler {


    @Resource
    private IDGenerator idGenerator;
    @Resource
    private InstFinancialCalendarContextBuilder calendarContextBuilder;
    @Resource
    private InstFinancialCalendarRepository calendarRepository;
    @Resource
    private InstFinancialCalendarHolidayRepository holidayRepository;
    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarActivateProcess.key:process_inst-center-financial-calendar-save-review}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarActivateProcess.desc:金融日历上下线审批}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instFinancialCalendarActivateProcess.formMsgTemplate: #!{originStatus} -> #!{modifyStatus} }", autoRefreshed = true)
    private String processFormMsgTemplate;


    /**
     * 初始化日历和节假日
     * @param request 请求参数
     * @return 初始化结果
     */
    public Boolean startCalendarActivate(InstFinancialCalendarActivateRequest request) {
        log.info("InstFinancialCalendarActivateHandler initCalendarInvalidDraft");
        // 查询日历
        List<InstFinancialCalendar> calendarList = calendarRepository.queryByConditions(InstFinancialCalendarPO.builder().calendarId(request.getCalendarId()).build())
                        .stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());
        AssertUtil.isTrue(calendarList.size() == 1, ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "日历不存在");
        InstFinancialCalendar originCalendar = calendarList.get(0); //CHECKED
        AssertUtil.isTrue(!originCalendar.getStatus().equals(request.getStatus()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "日历状态无需变更");

        // 构造草稿
        InstFinancialCalendar modifyCalendar = InstFinancialCalendarAssembler.INSTANCE.deepcopy(originCalendar);
        modifyCalendar.setStatus(request.getStatus());
        InstBusinessDraft draft = InstFinancialCalendarAssembler.INSTANCE.calendarActivateDraftInit(originCalendar, idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.FINANCIAL_CALENDAR), request.getShareId());
        draft.setDraftData(JSON.toJSONString(new HashMap<String, InstFinancialCalendar>(2) {{
            put("originData", originCalendar);
            put("modifyData", modifyCalendar);
        }}));


        // 发起流程
        log.info("InstFinancialCalendarActivateHandler startProcess");
        return super.startProcess(draft);
    }


    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        // 流程定义信息
        processConfig.setProcessDefKey(processKey);
        processConfig.setProcessDesc(processDesc);
        // 草稿表单信息
        HashMap<String, Object> formMsgMap = new HashMap<>(4);
        JSONObject draftData = JSON.parseObject(draft.getDraftData());
        InstFinancialCalendar calendar = JSON.parseObject(JSON.toJSONString(draftData.get("originData")), InstFinancialCalendar.class);
        formMsgMap.putAll(JSONObject.parseObject(JSONObject.toJSONString(calendar), new TypeReference<HashMap<String, Object>>() {}));
        formMsgMap.put("businessKey", draft.getBusinessKey());
        formMsgMap.put("weekendListStr", String.join(",", calendar.getWeekendList()));
        formMsgMap.put("statusChange", composeHolidayFormMsg(draftData, processFormMsgTemplate));
        processConfig.setFormInfoMap(formMsgMap);
        return processConfig;
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarUpdateHandler businessPassHandler");
        JSONObject draftData = JSON.parseObject(draft.getDraftData());
        return transactionTemplate.execute( transactionStatus -> {
            InstFinancialCalendar calendar = JSON.parseObject(JSON.toJSONString(draftData.get("modifyData")), InstFinancialCalendar.class);
            calendarRepository.updateById(InstFinancialCalendarAssembler.INSTANCE.domain2Po(calendar));
            return Boolean.TRUE;
        });
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstFinancialCalendarActivateHandler businessRejectHandler");
        return Boolean.TRUE;
    }

    /**
     * 构造节假日表单信息
     */
    private String composeHolidayFormMsg(JSONObject draftData, String template){
        String formatedTemplate = template.replace("#!", "$!");
        InstFinancialCalendar modifyData = JSON.parseObject(JSON.toJSONString(draftData.get("modifyData")), InstFinancialCalendar.class);
        InstFinancialCalendar originData = JSON.parseObject(JSON.toJSONString(draftData.get("originData")), InstFinancialCalendar.class);
        Map<String, Object> params = new HashMap<String,Object>(4) {{
            put("originStatus", originData.getStatus());
            put("modifyStatus", modifyData.getStatus());
        }};

        return TemplateUtil.render(TemplateUtil.TemplateType.VELOCITY, formatedTemplate, params);
    }
}
