package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstContactReqDTO;
import com.payermax.channel.inst.center.app.response.InstContactVO;

import java.util.List;

/**
 * 机构联系人信息相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/17 22:32
 */
public interface InstContactManager {

    /**
     * 查询机构联系人信息
     *
     * @return
     */
    List<InstContactVO> query(InstContactReqDTO instContactReqDTO);

    /**
     * 保存机构联系人信息
     *
     * @return
     */
    int save(InstContactReqDTO instContactReqDTO);

    /**
     * 保存机构联系人信息
     *
     * @return
     */
    int saveBatch(List<InstContactReqDTO> instContactReqDTOs);
}
