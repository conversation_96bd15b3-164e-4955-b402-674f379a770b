package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 机构账号请求
 *
 * <AUTHOR>
 * @date 2022/6/4 14:37
 */
@Data
public class InstAccountReqDTO implements Serializable {

    private static final long serialVersionUID = -3814044532390761955L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    @ApiModelProperty(notes = "集成需求单ID")
    private Long requirementOrderId;

    @ApiModelProperty(notes = "账号类型")
    private String type;

    @ApiModelProperty(notes = "账号环境")
    private String env;

    @ApiModelProperty(notes = "账号")
    private String account;

    @ApiModelProperty(notes = "账号密钥类型")
    private String keyType;

    @ApiModelProperty(notes = "账号密钥值")
    private String keyValue;

    @ApiModelProperty(notes = "加密方式")
    private String encryptType;

    @ApiModelProperty(notes = "备注")
    private String remark;

    @ApiModelProperty(hidden = true)
    private String status;

    @ApiModelProperty(notes = "商户密钥")
    private List<InstAccountKeyReqDTO> instAccountKeyReqDTOList;

}
