package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName InstKycReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 21:17
 * @Version 1.0
 */
@Data
public class InstKycReqDTO implements Serializable {

    private static final long serialVersionUID = -5674442108280966525L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 机构标识
     */
    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    /**
     * 申请单号
     */
    @ApiModelProperty(notes = "申请单号")
    private String applyNo;

    /**
     * 水印文字
     */
    @ApiModelProperty(notes = "水印文字")
    private String watermarkWord;

    /**
     * 调研问卷文件
     */
    @ApiModelProperty(notes = "调研问卷文件")
    private String surveyAttachId;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;

    /**
     * 状态 INIT:初始态 DATA_COMPLETED:已提供材料 AUDITING:机构审核中 AUDIT_RETURN:机构审核驳回-待追加材料 AUDIT_AGREE:机构审批通过 COMPLETED:完成
     */
    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "审核资料数据信息集合")
    private List<InstAuditDataReqDTO> auditDataList;
    
}
