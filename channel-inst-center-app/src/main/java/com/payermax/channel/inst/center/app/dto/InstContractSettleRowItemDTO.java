package com.payermax.channel.inst.center.app.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.payermax.channel.inst.center.app.dto.impl.InstContractSettleRowItemZhDTO;
import com.payermax.channel.inst.center.app.service.InstContractExcelDataTransfer;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ApplicationUtils;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * InstContractSettleRowItemDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public abstract class InstContractSettleRowItemDTO extends AbstractInstContractRowItemDTO implements ExcelUtil.DefineExcelParser, ExcelUtil.ExcelLangRow {

    @ExcelIgnore
    private List<SettleDateInfo> settleDateInfoList;

    @Data
    public static abstract class SettleDateInfo implements ExcelUtil.ExcelLangRow {

        /**
         * 账单日
         * @return
         */
        public abstract String getBillDate();

        /**
         * 交易开始日期
         * @return
         */
        public abstract String getTransactionStartDate();

        /**
         * 交易结束日期
         * @return
         */
        public abstract String getTransactionEndDate();

        /**
         * 打款日
         * @return
         */
        public abstract String getPaymentDate();

        /**
         * 到账日
         * @return
         */
        public abstract String getArriveDate();

        @Override
        public Class<? extends ExcelUtil.ExcelLangRow> langClassImpl(LangEnum langEnum) {
            return InstContractSettleRowItemZhDTO.SettleDateInfoZhImpl.class;
        }
    }

    /**
     * 支付币种
     *
     * @return 支付币种
     */
    public abstract String getPayCurrency();

    /**
     * 卡片类型
     *
     * @return 卡片类型
     */
    public abstract String getCardType();

    /**
     * 发卡国家
     *
     * @return 发卡国家
     */
    public abstract String getCardIssueCountry();

    /**
     * 机构MID
     *
     * @return 机构MID
     */
    public abstract String getOriginMid();

    public abstract String getChannelMerchantNo();

    /**
     * 机构MCC
     *
     * @return 机构MCC
     */
    public abstract String getOriginMcc();

    public abstract String getStandardMcc();

    /**
     * 结算币种
     *
     * @return 结算币种
     */
    public abstract String getSettleCurrency();

    /**
     * 计费方式
     *
     * @return 计费方式
     */
    public abstract String getCalculateType();

    /**
     * 单笔比例
     *
     * @return 单笔比例
     */
    public abstract String getSingleRate();

    /**
     * 最低收费, 需要数字校验
     *
     * @return 最低收费
     */
    public abstract String getMinCharge();

    /**
     * 封顶收费, 需要数字校验
     *
     * @return 封顶收费
     */
    public abstract String getMaxCharge();

    /**
     * 单笔固定金额, 需要数字校验
     *
     * @return 单笔固定金额
     */
    public abstract String getSingleFixedAmount();

    /**
     * 单笔固定币种
     *
     * @return 单笔固定币种
     */
    public abstract String getSingleFixedCurrency();

    /**
     * 起结金额
     *
     * @return 起结金额
     */
    public abstract String getMinSettleAmount();

    /**
     * 我方收款账户
     *
     * @return 我方收款账户
     */
    public abstract String getAccountId();

    /**
     * 提现收款方式
     *
     * @return 提现收款方式
     */
    public abstract String getWithdrawMethod();

    @Override
    public void rowDataParse(List<ExcelCellData> excelCellDataList) {
        InstContractExcelDataTransfer instContractExcelDataTransfer = ApplicationUtils.getBean(InstContractExcelDataTransfer.class);
        instContractExcelDataTransfer.instContractSettleRowItemDtoTransfer(this, excelCellDataList);
    }

    @Override
    public Class<? extends InstContractSettleRowItemDTO> langClassImpl(LangEnum langEnum) {
        return InstContractSettleRowItemZhDTO.class;
    }


    /**
     * 每一个机构原始产品，会包含多维度的成本划分条款，以下字段拼接构成了描述条款的维度。
     * 维度businessKey相同，可认为是属于同一组条款（阶梯条款项）
     */
    public String getContractItemBusinessKey() {

        if (isStandardized()) {
            return StringUtils.join(getInstCode(), getContractEntity(), getInstProductType(), getPayCurrency(), getCardType(),
                    getCardIssueCountry(), getChannelMerchantNo(), getStandardMcc(), getCardOrg(), SymbolConstants.SYMBOL_STRIKE_LINE);

        } else {
            return StringUtils.join(getInstCode(), getContractEntity(), getInstProductType(), getInstProductName(), getPayCurrency(), getCardType(),
                    getCardIssueCountry(), getOriginMid(), getOriginMcc(), getCardOrg(), SymbolConstants.SYMBOL_STRIKE_LINE);
        }
    }
}
