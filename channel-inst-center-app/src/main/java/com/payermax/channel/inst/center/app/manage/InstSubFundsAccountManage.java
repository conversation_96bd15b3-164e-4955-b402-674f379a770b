package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubAccountDetailByIdRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubFundsAccountRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.CloseSubAccountResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QuerySubAccountDetailByIdResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QuerySubFundsAccountResponseDO;

import java.util.List;

/**
 * 机构资金账号Manage
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstSubFundsAccountManage {

    /**
     * 查询可用机构资金账号列表
     *
     * @param queryAccountDetailRequestDO
     * @return
     */
    List<QuerySubFundsAccountResponseDO> queryAccountDetailAndSubList(QuerySubFundsAccountRequestDO queryAccountDetailRequestDO);


    /**
     * 查询可用机构资金账号
     *
     * @param querySubAccountDetailRequestDO
     * @return
     */
    QuerySubAccountDetailByIdResponseDO querySubAccountDetailById(QuerySubAccountDetailByIdRequestDO querySubAccountDetailRequestDO);

    /**
     * 关闭子级资金账号
     *
     * @param businessKey
     * @return
     */
    CloseSubAccountResponseDO closeSubAccountDetailById(String businessKey);

    /**
     * 检查账号是否重复
     *
     * @param subAccountNo 子级账号
     * @param accountId 当前子级账号记录的ID
     * @param accountId 主账号ID
     * 
     * @return
     */
    boolean checkSubAccountNoDuplication(String subAccountNo, String subAccountId, String accountId);

}
