package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstKycReqDTO;
import com.payermax.channel.inst.center.app.response.InstKycVO;

/**
 * KYC相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/13 22:32
 */
public interface InstKycManager {

    /**
     * 查询机构KYC信息
     *
     * @return
     */
    InstKycVO query(Long instId);

    /**
     * 保存机构KYC信息
     *
     * @return
     */
    int save(InstKycReqDTO instKycReqDTO);
}
