package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 机构合同费用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstContractFeePayoutDTO extends AbstractInstContractFeeDTO{

    /**
     * 到账国家
     */
    @ExcelProperty("Country\n到账国家")
    private String country;



    /**
     * 机构+我方主体+合作模式+机构产品类型+机构产品名称+支付币种+国家+行业分类+资金来源+【发卡区域 +卡类型+持卡人类型+卡组织 +卡等级】+ 清算网络 + 费用承担方 +生效时间
     * 构成唯一机构合约
     */
    public String getInstContractKey(){
        List<String> keyList = Stream.of(getInstCode(), getContractEntity(), getCooperationMode(), getProductType(), getInstProductName(),
                        getTransactionCurrency(), getCountry(), getClearNetWork(), getFeeBearer(), String.valueOf(getEffectiveTime().getTime()))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, SymbolConstants.SYMBOL_STRIKE_LINE);
    }

}
