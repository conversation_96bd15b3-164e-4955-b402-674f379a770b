package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstProductFeeService;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductFeeDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ClassName InstProductFeeServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/31 10:31
 */
@Service
public class InstProductFeeServiceImpl implements InstProductFeeService {
    @Autowired
    private InstProductFeeDao instProductFeeDao;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAll(List<InstProductFeeEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        AtomicInteger result = new AtomicInteger(0);
        for (InstProductFeeEntity record : records) {
            int count = 0;
            if (record.getId() == null) {
                // 新增
                count = instProductFeeDao.insert(record);
            } else {
                // 更新
                count = instProductFeeDao.updateByPrimaryKey(record);
            }
            result.addAndGet(count);
        }
        return result.get();
    }

    @Override
    public List<InstProductFeeEntity> getByContractNoAndProductCodes(String contractNo,List<String> productCodes) {
        Preconditions.checkArgument(StringUtils.isNotBlank(contractNo),"param contractNo is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(productCodes),"param productCodes is mandatory");
        List<InstProductFeeEntity> instProductFeeEntities = instProductFeeDao.selectByContractNoAndProductCodes(contractNo,productCodes);
        return instProductFeeEntities;
    }

    @Override
    public int deleteByContractNoAndProductCode(String contractNo,String productCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(contractNo),"param contractNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        InstProductFeeEntity productFeeEntity = new InstProductFeeEntity();
        productFeeEntity.setContractNo(contractNo);
        productFeeEntity.setInstProductCode(productCode);
        int result = instProductFeeDao.deleteByEntity(productFeeEntity);
        return result;
    }
}
