package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/31 10:25
 */
public interface InstTransFeeService {
    /**
     * 保存费用明细
     *
     * @param record
     * @return
     */
    int save(InstTransFeeEntity record);

    /**
     * 保存费用明细
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstTransFeeEntity> records);

    /**
     * 根据费用分组id查费用明细
     *
     * @param feeGroupIds
     * @return
     */
    List<InstTransFeeEntity> getByFeeGroupIds(@Param("feeGroupIds") List<String> feeGroupIds);

    /**
     * 根据费用分组id删除费用明细
     * @param feeGroupId
     * @return
     */
    int deleteByFeeGroupIds(List<String> feeGroupId);
}
