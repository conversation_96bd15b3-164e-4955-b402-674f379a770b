package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity;

/**
 * DD调研报告Service
 *
 * <AUTHOR>
 * @date 2022/5/18 16:02
 */
public interface InstDdSurveyService {

    /**
     * 保存DD
     *
     * @param record
     * @return
     */
    int save(InstDdSurveyEntity record);

    /**
     * 根据DD ID查询调研报告
     *
     * @param ddId
     * @return
     */
    InstDdSurveyEntity getByDdId(Long ddId);

}
