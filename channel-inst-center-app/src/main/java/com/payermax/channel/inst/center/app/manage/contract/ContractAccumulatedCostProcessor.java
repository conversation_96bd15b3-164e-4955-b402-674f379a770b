package com.payermax.channel.inst.center.app.manage.contract;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.model.contract.ContractContentEnum;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR> at 2023/6/9 6:14 PM
 **/
@Component
public class ContractAccumulatedCostProcessor extends AbstractContractTermsProcessor {

    @Override
    public void assembleContractItems(ContractProcessByExcelContext context) {

    }

    @Override
    public void businessValidateOnContractItems(ContractProcessByExcelContext context) {

    }

    @Override
    public Set<ContractContentEnum> supportedScenarios() {
        return Sets.newHashSet(ContractContentEnum.INST_ACCUMULATED_FEE_TAX_PAYIN, ContractContentEnum.INST_ACCUMULATED_FEE_TAX_PAYOUT);
    }
}
