package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstContractReqDTO;
import com.payermax.channel.inst.center.app.response.InstContractVO;

import java.util.List;

/**
 * @ClassName InstContractManager
 * @Description
 * <AUTHOR>
 * @Date 2022/5/25 23:51
 */
public interface InstContractManager {
    /**
     * 保存合同信息
     *
     * @param contractReqDTO
     * @return
     */
    int save(InstContractReqDTO contractReqDTO);

    /**
     * 查询合同信息
     *
     * @param contractReqDTO
     * @return
     */
    InstContractVO query(InstContractReqDTO contractReqDTO);

    /**
     * 查询机构合同列表
     *
     * @param contractReqDTO
     * @return
     */
    List<InstContractVO> list(InstContractReqDTO contractReqDTO);
}
