package com.payermax.channel.inst.center.app.manage.workflow;

import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */


@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface WorkflowCallbackHandler {
    BusinessTypeEnum businessType();
    OperateModuleEnum moduleName();
    OperateTypeEnum actionType();
}
