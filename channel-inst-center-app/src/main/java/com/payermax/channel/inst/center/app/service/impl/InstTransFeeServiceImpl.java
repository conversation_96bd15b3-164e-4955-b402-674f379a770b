package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstTransFeeDao;
import com.payermax.channel.inst.center.app.service.InstTransFeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstTransFeeServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/31 10:32
 */
@Service
public class InstTransFeeServiceImpl implements InstTransFeeService {

    @Autowired
    private InstTransFeeDao instTransFeeDao;

    @Override
    public int save(InstTransFeeEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");
        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instTransFeeDao.insert(record);
        } else {
            // 更新
            result = instTransFeeDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public int saveBatch(List<InstTransFeeEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        int result = instTransFeeDao.insertBatch(records);
        return result;
    }

    @Override
    public List<InstTransFeeEntity> getByFeeGroupIds(List<String> feeGroupIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(feeGroupIds), "param feeGroupIds is mandatory");
        List<InstTransFeeEntity> instTransFeeEntities = instTransFeeDao.selectAll(feeGroupIds);
        return instTransFeeEntities;
    }

    @Override
    public int deleteByFeeGroupIds(List<String> feeGroupIds) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(feeGroupIds), "param feeGroupIds is mandatory");
        int result = instTransFeeDao.deleteByFeeGroupIds(feeGroupIds);
        return result;
    }
}
