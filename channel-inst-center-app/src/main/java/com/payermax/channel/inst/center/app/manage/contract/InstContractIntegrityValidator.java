package com.payermax.channel.inst.center.app.manage.contract;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.request.contract.config.FxConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.SettlementConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.content.CurrencyExchangeTiming;
import com.payermax.channel.inst.center.facade.request.contract.config.content.FeeType;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleAccount;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/10
 * @DESC 机构合约完整性校验
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstContractIntegrityValidator {

    @NacosValue(value = "${inst.contract.validate.white-list.fee:}", autoRefreshed = true)
    private Set<String> feeWhiteList;
    @NacosValue(value = "${inst.contract.validate.white-list.fx:}", autoRefreshed = true)
    private Set<String> fxWhiteList;
    @NacosValue(value = "${inst.contract.validate.white-list.settlement:}", autoRefreshed = true)
    private Set<String> settlementWhiteList;
    @NacosValue(value = "${inst.contract.validate.owner.fee:}", autoRefreshed = true)
    private List<String> feeOwnerList;
    @NacosValue(value = "${inst.contract.validate.owner.fx:}", autoRefreshed = true)
    private List<String> fxOwnerList;
    @NacosValue(value = "${inst.contract.validate.owner.settlement:}", autoRefreshed = true)
    private List<String> settlementOwnerList;



    public Boolean validate(InstInfoQueryResponse contractInfo) {
        log.info("合约校验-合约完整性校验开始: {}-{}, 合约编号: {}, 费用编号: {}, 支付币种: {}", contractInfo.getBizType(), contractInfo.getInstCode()
                , contractInfo.getContractNo(), contractInfo.getInstContractFeeItemNo(), contractInfo.getPayCurrency());

        // 费用校验
        feeValidate(contractInfo);

        // 外汇信息校验
        fxValidate(contractInfo);

        // 结算信息校验
        settlementValidate(contractInfo);

        log.info("合约校验-合约完整性校验通过");

        return Boolean.TRUE;
    }


    /**
     * 费用信息校验
     */
    private void feeValidate(InstInfoQueryResponse contractInfo) {
        log.info("合约校验-费用信息校验开始");
        // 白名单，跳过校验
        if (feeWhiteList.contains(contractInfo.getInstCode())){
            log.info("合约校验-费用信息白名单配置，跳过校验: {}", contractInfo.getInstCode());
            return;
        }
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(contractInfo.getFeeConfigMap()),
                ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("费用校验-费用配置为空", feeOwnerList));
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(contractInfo.getFeeConfigMap().get(FeeType.TRADE.name())),
                ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("费用校验-交易费用未配置", feeOwnerList));
        log.info("合约校验-费用信息校验通过");
    }

    /**
     * 外汇信息校验
     */
    private void fxValidate(InstInfoQueryResponse contractInfo) {
        log.info("合约校验-外汇信息校验开始");
        // 白名单，跳过校验
        if (fxWhiteList.contains(contractInfo.getInstCode())){
            log.info("合约校验-FX信息白名单配置，跳过校验: {}", contractInfo.getInstCode());
            return;
        }
        FxConfig fxConfig = contractInfo.getFxConfig();
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(fxConfig), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("FX信息未配置", fxOwnerList));
        AssertUtil.isTrue(EnumUtils.isValidEnum(CurrencyExchangeTiming.class, fxConfig.getCurrencyExchangeTime()),
                ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("FX换汇时机配置异常", fxOwnerList));

        // 是否需要换汇
        boolean isExchange = !CurrencyExchangeTiming.NO_EXCHANGE.name().equalsIgnoreCase(fxConfig.getCurrencyExchangeTime());

        // 需要换汇时校验FX加点
        if(isExchange){
            AssertUtil.isTrue(ObjectUtils.isNotEmpty(fxConfig.getFxSpread()),
                    ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("FX加点配置异常", fxOwnerList));
        }

        // 入款 且 换汇时机!=不换汇 时，结算周期必须配置换汇日
        if(ContractBizTypeEnum.I.name().equalsIgnoreCase(contractInfo.getBizType()) && isExchange){
            exchangeDateValidate(contractInfo);
        }
        log.info("合约校验-外汇信息校验通过");
    }

    /**
     * 结算信息校验
     */
    private void settlementValidate(InstInfoQueryResponse contractInfo) {
        log.info("合约校验-结算信息校验开始");
        // 白名单，跳过校验
        if (settlementWhiteList.contains(contractInfo.getInstCode())){
            log.info("合约校验-结算信息白名单配置，跳过校验: {}", contractInfo.getInstCode());
            return;
        }
        // 出款渠道无结算配置，跳过校验
        if(ContractBizTypeEnum.O.name().equals(contractInfo.getBizType())){
            return;
        }
        SettlementConfig settlementConfig = contractInfo.getSettlementConfig();
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(settlementConfig),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算信息未配置", settlementOwnerList));
        AssertUtil.isTrue(StringUtils.isNotBlank(settlementConfig.getSettleCurrency()),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算币种配置异常", settlementOwnerList));

        // 结算周期校验
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(settlementConfig.getSettleDates()),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算周期未配置", settlementOwnerList));
        AssertUtil.isTrue(settlementConfig.getSettleDates().size() == 1,
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算周期配置异常-存在多个结算周期", settlementOwnerList));
        SettleDate settleDate = settlementConfig.getSettleDates().get(0);
        settleExpressionValidate(settleDate.getBillDate(), "billDate");
        settleExpressionValidate(settleDate.getPaymentDate(), "paymentDate");
        settleExpressionValidate(settleDate.getArriveDate(), "arriveDate");

        // 结算账户校验
        SettleAccount settleAccount = settlementConfig.getSettleAccount();
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(settleAccount),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算账户未配置", settlementOwnerList));
        AssertUtil.isTrue(StringUtils.isNotBlank(settleAccount.getAccountId()),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算账户账号异常", settlementOwnerList));

        log.info("合约校验-结算信息校验通过");

    }

    /**
     * 换汇日校验
     */
    private void exchangeDateValidate(InstInfoQueryResponse contractInfo) {
        SettlementConfig settlementConfig = contractInfo.getSettlementConfig();
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(settlementConfig.getSettleDates()),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算周期配置异常", settlementOwnerList));
        AssertUtil.isTrue(settlementConfig.getSettleDates().size() == 1,
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), exceptionMsgWrapping("结算周期配置异常-存在多个结算周期", settlementOwnerList));
        SettleDate settleDate = settlementConfig.getSettleDates().get(0); // CHECKED
        // 换汇日表达式校验
        settleExpressionValidate(settleDate.getExchangeDate(), "exchangeDate");
    }


    /**
     * 周期表达式校验
     */
    private void settleExpressionValidate(String expression, String type){
        log.info("结算周期表达式校验: {}, {}", expression, type);
        AssertUtil.isTrue(StringUtils.isNotBlank(expression),
                ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(), String.format("结算周期配置异常-表达式为空: %s", type));
        // 表达式校验
        try {
            DSLTypeEnum.mateDslType(expression);
        }catch (Exception e){
            log.error("结算周期表达式校验-结算周期表达式异常: {}", expression);
            throw new BusinessException(ErrorCodeEnum.INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR.getCode(),
                    String.format("结算周期配置异常 -【%s】表达式异常: %s", type, expression));
        }
    }
    
    /**
     * 异常消息包装
     */
    private String exceptionMsgWrapping(String msg, List<String> owners){
        return String.format("%s, 负责人员:【%s】", msg, String.join(",", owners));
    }

}
