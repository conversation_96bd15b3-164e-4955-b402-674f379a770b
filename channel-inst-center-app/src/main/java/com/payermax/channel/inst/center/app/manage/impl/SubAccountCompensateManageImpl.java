package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.manage.SubAccountCompensateManage;
import com.payermax.channel.inst.center.app.manage.template.CreateSubAccount;
import com.payermax.channel.inst.center.app.manage.template.activation.ApiActivationInquiryTemplate;
import com.payermax.channel.inst.center.app.manage.template.activation.SubAccountActivationTemplate;
import com.payermax.channel.inst.center.app.manage.template.mode.ApiModeInquiryTemplate;
import com.payermax.channel.inst.center.app.manage.template.mode.SubAccountModeTemplate;
import com.payermax.channel.inst.center.app.rocketmq.producer.InstCenterRocketProducer;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.enums.instcenter.IsSupportYNEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/16 16:15
 **/
@Service
@Slf4j
public class SubAccountCompensateManageImpl implements SubAccountCompensateManage {

    @Autowired
    ReqDoAssembler reqDoAssembler;
    @Autowired
    CreateSubAccount createSubAccount;
    @Autowired
    ApiModeInquiryTemplate apiModeInquiryTemplate;
    @Autowired
    InstFundsAccountService instFundsAccountService;
    @Autowired
    InstCenterRocketProducer instCenterRocketProducer;
    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;
    @Autowired
    ApiActivationInquiryTemplate apiActivationInquiryTemplate;
    @Autowired
    InstSubFundsAccountBucketManage instSubFundsAccountBucketManage;


    @Override
    public void subAccountCompensate(InstSubFundsAccountEntity subFundsAccount, Boolean compensateRetry) {
        InstFundsAccountQueryEntity accountQueryEntity = new InstFundsAccountQueryEntity();
        accountQueryEntity.setAccountId(subFundsAccount.getAccountId());

        // 查询结构账号信息
        InstFundsAccountEntity fundsAccount = instFundsAccountService.queryById(accountQueryEntity);

        InstSubFundsAccountEntity orgSubFundsAccount = ObjectUtil.clone(subFundsAccount);

        RequestAccountDO requestAccountDO = new RequestAccountDO();
        requestAccountDO.setInstFundsAccountEntity(fundsAccount);
        requestAccountDO.setInstSubFundsAccountEntity(subFundsAccount);

        // 获取机构子级资金账户申请模式
        SubAccountModeTemplate modeTemplate = createSubAccount.routeModeTemplate(requestAccountDO.getInstFundsAccountEntity().getSubAccountMode());
        SubAccountActivationTemplate accountActivationTemplate = null;
        if (ObjectUtil.equal(fundsAccount.getIsNeedActivation(), IsSupportYNEnum.Y.getType())) {
            // 获取激活处理模版
            accountActivationTemplate = modeTemplate.routeActivationTemplate(fundsAccount.getActivationMode());
        }
        
        InstSubFundsAccountRequestDO requestDO;
        InstSubFundsAccountEntity subAccountByPreApplyAccount;
        
        switch (Objects.requireNonNull(SubAccountStatusEnum.getSubAccountStatusByStatus(subFundsAccount.getStatus()))) {
            case INITIATE:
                // 【初始化】先预申请处理，无预申请走原有申请逻辑
                requestDO = reqDoAssembler.toInstSubFundsAccountRequestDO(subFundsAccount);
                subAccountByPreApplyAccount = instSubFundsAccountBucketManage.checkAndCreateSubAccountByPreApplyBucket(fundsAccount, requestDO);
                if (Objects.nonNull(subAccountByPreApplyAccount)) {
                    requestAccountDO.setInstSubFundsAccountEntity(subAccountByPreApplyAccount);
                    break;
                }
                // 子级资金账号获取
                modeTemplate.getSubAccountNo(requestAccountDO);
                modeTemplate.handleActivationOrOthersAction(requestAccountDO);
                break;
            case APPLY:
                // 【申请中】非重发
                if (Boolean.FALSE.equals(compensateRetry)){
                    requestDO = reqDoAssembler.toInstSubFundsAccountRequestDO(subFundsAccount);
                    subAccountByPreApplyAccount = instSubFundsAccountBucketManage.checkAndCreateSubAccountByPreApplyBucket(fundsAccount, requestDO);
                    if (Objects.nonNull(subAccountByPreApplyAccount)) {
                        requestAccountDO.setInstSubFundsAccountEntity(subAccountByPreApplyAccount);
                    } else {
                        // 【API模式】则需要补偿查询外部渠道
                        if (StringUtils.equals(fundsAccount.getSubAccountMode(), SubAccountModeEnum.API.getType()) && Boolean.FALSE.equals(compensateRetry)) {
                            apiModeInquiryTemplate.getSubAccountNo(requestAccountDO);
                            InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
                            queryEntity.setSubAccountId(subFundsAccount.getSubAccountId());
                            requestAccountDO.setInstSubFundsAccountEntity(instSubFundsAccountService.queryById(queryEntity));
                        }
                    }
                } else {
                    //【申请中】重发
                    modeTemplate.getSubAccountNo(requestAccountDO);
                }
                break;
            case TO_BE_ACTIVATED:
                // 【待激活】
                if (Objects.nonNull(accountActivationTemplate)) {
                    accountActivationTemplate.activation(requestAccountDO);
                }
                break;
            case ACTIVE:
                // 【激活中】非重发
                if (Boolean.FALSE.equals(compensateRetry)){
                    // API模式【激活中]，则需要补偿查询外部渠道
                    if (StringUtils.equals(fundsAccount.getSubAccountMode(), SubAccountModeEnum.API.getType())) {
                        apiActivationInquiryTemplate.activation(requestAccountDO);
                        InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
                        queryEntity.setSubAccountId(subFundsAccount.getSubAccountId());
                        requestAccountDO.setInstSubFundsAccountEntity(instSubFundsAccountService.queryById(queryEntity));
                    }
                } else if (Objects.nonNull(accountActivationTemplate)) {
                    //【激活中】重发
                    accountActivationTemplate.activation(requestAccountDO);
                }
                break;
            default:
                return;
        }

        if (ObjectUtil.notEqual(requestAccountDO.getInstSubFundsAccountEntity().getStatus(), orgSubFundsAccount.getStatus())) {
            ResponseAccountDO responseAccountDO = new ResponseAccountDO();
            responseAccountDO.setInstFundsAccountEntity(requestAccountDO.getInstFundsAccountEntity());
            responseAccountDO.setInstSubFundsAccountEntity(requestAccountDO.getInstSubFundsAccountEntity());
            SendResult sendResult = instCenterRocketProducer.sendAccountStaticChangeNotify(responseAccountDO);

            log.info("BatchSubAccountCompensateJobHandler-sendResult:{}", JSON.toJSONString(sendResult));
        }
    }

    @Override
    public Result<Integer> compensateSubAccountNoMq(List<String> subAccountIds) {
        int i = 0;
        for (int j = 0; j < subAccountIds.size(); j++) {
            InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
            queryEntity.setSubAccountId(subAccountIds.get(j));
            InstSubFundsAccountEntity subFundsAccount = instSubFundsAccountService.queryById(queryEntity);
            if (Objects.isNull(subFundsAccount)) {
                continue;
            }

            InstFundsAccountQueryEntity accountQueryEntity = new InstFundsAccountQueryEntity();
            accountQueryEntity.setAccountId(subFundsAccount.getAccountId());
            // 查询结构账号信息
            InstFundsAccountEntity fundsAccount = instFundsAccountService.queryById(accountQueryEntity);
            if (Objects.isNull(fundsAccount)) {
                continue;
            }

            ResponseAccountDO responseAccountDO = new ResponseAccountDO();
            responseAccountDO.setInstFundsAccountEntity(fundsAccount);
            responseAccountDO.setInstSubFundsAccountEntity(subFundsAccount);
            SendResult sendResult = instCenterRocketProducer.sendAccountStaticChangeNotify(responseAccountDO);
            log.info("SubAccountCompensateManageImpl-activeSubAccountNo:{}", JSON.toJSONString(sendResult));
            if (sendResult.getSendStatus().equals(SendStatus.SEND_OK)){
                i++;
            }
        }
        return new Result<>(i);

    }
}
