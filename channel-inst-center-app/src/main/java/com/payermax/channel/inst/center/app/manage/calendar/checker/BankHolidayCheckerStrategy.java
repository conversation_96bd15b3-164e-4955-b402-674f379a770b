package com.payermax.channel.inst.center.app.manage.calendar.checker;

import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BankHolidayCheckerStrategy extends AbstractHolidayCheckerStrategy {

    public BankHolidayCheckerStrategy() {
        registry(CalendarTypeEnum.BANK, this);
    }

    @Override
    List<InstFinancialCalendar> calendarFilter(List<InstFinancialCalendar> calendars, String country, String currency) {
        log.info("BankHolidayChecker country:{} currency:{}", country, currency);
        AssertUtil.notEmpty(currency, "ERROR", "currency can not be empty");
        AssertUtil.notEmpty(country, "ERROR", "country can not be empty");

        com.payermax.common.lang.util.AssertUtil.notEmpty(currency, "ERROR", "currency can not be empty");
        // 查询银行日历，优先精确匹配
        List<InstFinancialCalendar> filteredCalendars = calendarPreciseFilter(calendars, country, currency);
        log.info("BankHolidayChecker calendarPreciseFilter result size:{}", filteredCalendars.size());
        if (CollectionUtils.isEmpty(filteredCalendars)) {
            // 精准匹配无数据时进行模糊匹配
            filteredCalendars = calendarFuzzyFilter(calendars, country, currency);
            log.info("BankHolidayChecker calendarPreciseFilter result size:{}", filteredCalendars.size());
        }
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(filteredCalendars), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "查询不到对应银行日历");

        return filteredCalendars;
    }

    /**
     * 日历精准匹配
     */
    @Override
    public List<InstFinancialCalendar> calendarPreciseFilter(List<InstFinancialCalendar> calendars, String country, String currency) {
        List<InstFinancialCalendar> filteredCalendar = calendars.stream()
                .filter(calendar -> calendar.getCountry().equals(country)
                        && calendar.getCurrency().equals(currency) && calendar.getCalendarType() == CalendarTypeEnum.BANK)
                .collect(Collectors.toList());
        AssertUtil.isTrue(filteredCalendar.size() <= 1, ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "错误匹配: 匹配到多个日历");
        // 填入节假日
        filteredCalendar.forEach(calendar -> calendar.setHolidays(getHolidays(calendar)));
        return filteredCalendar;
    }

    /**
     * 日历模糊匹配
     */
    private List<InstFinancialCalendar> calendarFuzzyFilter(List<InstFinancialCalendar> calendars, String country, String currency) {
        return calendars.stream().filter(calendar -> (calendar.getCountry().equals(country) && calendar.getCurrency().equals(CommonConstants.STAR))
                        || (calendar.getCountry().equals(CommonConstants.STAR) && calendar.getCurrency().equals(currency)))
                // 填入节假日
                .peek(calendar -> calendar.setHolidays(getHolidays(calendar))).collect(Collectors.toList());
    }

}
