package com.payermax.channel.inst.center.app.manage.account.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBankAccountAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.request.account.InstBankAccountSaveOrUpdateRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.DiffUtil;
import com.payermax.channel.inst.center.domain.entity.account.InstBankAccount;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBankAccountRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/9/27
 * @DESC 银行账户修改流程处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.INST_CENTER_BANK_ACCOUNT_MANAGER, actionType = OperateTypeEnum.UPDATE)
public class InstBankAccountUpdateHandler extends AbstractWorkflowHandler {

    private final InstBankAccountRepository bankAccountRepository;
    private final InstBaseInfoRepository baseInfoRepository;
    private final InstAccountCommonUtils accountCommonUtils;
    private final IDGenerator idGenerator;

    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instBankAccount.update.key:process_inst-center-bank-account-approve}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instBankAccount.update.desc:修改银行账户}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instBankAccount.update.formMsgTemplate:}", autoRefreshed = true)
    private String processFormMsgTemplate;

    /**
     * 业务场景
     */
    private final BusinessTypeEnum BUSINESS_TYPE = BusinessTypeEnum.INST_CENTER;
    private final OperateTypeEnum OPERATE_TYPE = OperateTypeEnum.UPDATE;
    private final OperateModuleEnum MODULE_NAME = OperateModuleEnum.INST_CENTER_BANK_ACCOUNT_MANAGER;
    private final LogScenesTypeEnum LOG_SCENES_TYPE = LogScenesTypeEnum.BANK_ACCOUNT;



    /**
     * 修改银行账户
     */
    public Boolean startUpdateProcess(InstBankAccountSaveOrUpdateRequest request, String shareId){
        InstBusinessDraft draft = buildDraft(request, shareId);
        return super.startProcess(draft);
    }

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        processConfig.setProcessDefKey(processKey);
        processConfig.setProcessDesc(processDesc);
        // 审批表单信息
        processConfig.setFormInfoMap(buildFormMsg(draft));
        return processConfig;
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstBankAccountUpdateHandler businessPassHandler");
        InstBankAccount bankAccount = JSON.parseObject(String.valueOf(JSON.parseObject(draft.getDraftData()).get("modifiedData")), InstBankAccount.class);
        return bankAccountRepository.updateById(InstBankAccountAssembler.INSTANCE.domain2Po(bankAccount));
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstBankAccountUpdateHandler businessRejectHandler");
        return Boolean.TRUE;
    }

    /**
     * 构造草稿对象
     */
    private InstBusinessDraft buildDraft(InstBankAccountSaveOrUpdateRequest request, String shareId) {
        // 对象转换
        InstBankAccount instBankAccount = InstBankAccountAssembler.INSTANCE.request2Domain(request);
        // 查询原始数据
        InstBankAccount originData = InstBankAccountAssembler.INSTANCE.po2Domain(bankAccountRepository.getById(instBankAccount.getId()));
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(originData), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "账户不存在");

        // 优先级校验
        accountCommonUtils.bankAccountPriorityCheck(instBankAccount);

        // 草稿内容
        Map<String, InstBankAccount> draftData = new HashMap<String, InstBankAccount>(4){{
            put("originData", originData);
            put("modifiedData", instBankAccount);
        }};

        // 构造草稿
        return InstBusinessDraftAssembler.INSTANCE.buildDefaultDraft(
                idGenerator.generateIdAccordingToSystem(BUSINESS_TYPE, LOG_SCENES_TYPE), MODULE_NAME,OPERATE_TYPE,
                String.valueOf(instBankAccount.getInstId()), JSON.toJSONString(draftData, SerializerFeature.DisableCircularReferenceDetect), shareId
        );
    }


    /**
     * 构造表单信息
     */
    private HashMap<String, Object> buildFormMsg(InstBusinessDraft draft) {

        HashMap<String, Object> formMsgMap = new HashMap<>(4);
        // 获取机构信息
        InstBaseInfo instBaseInfo = baseInfoRepository.getById(draft.getBusinessKey());
        formMsgMap.put("instName", instBaseInfo.getInstName());
        formMsgMap.put("instCode", instBaseInfo.getInstCode());

        // 获取账户变更信息
        String modifiedData = JSON.parseObject(draft.getDraftData()).getString("modifiedData");
        String originData = JSON.parseObject(draft.getDraftData()).getString("originData");
        formMsgMap.putAll(JSONObject.parseObject(modifiedData));

        // 进行变更 DIFF，并转换成表单信息
        TreeMap<String, Map<String, Object>> diffMsg = DiffUtil.diff2Map(originData, modifiedData);
        diffMsg.forEach((key, value) -> formMsgMap.put(key,
                String.format("%s -> %s",
                StringUtils.defaultIfBlank(String.valueOf(value.get("left")),"【】"),
                StringUtils.defaultIfBlank(String.valueOf(value.get("right")),"【】"))));
        return formMsgMap;
    }
}
