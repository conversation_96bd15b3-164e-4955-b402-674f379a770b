package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.fin.operating.log.dto.WriteLogDto;
import com.payermax.fin.operating.log.enums.OperatingTypeEnum;
import com.payermax.fin.operating.log.util.LogUserThreadLocal;
import com.payermax.channel.inst.center.common.constants.DictConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.StatusEnum;
import com.payermax.channel.inst.center.common.result.BaseResult;
import com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity;
import com.payermax.channel.inst.center.infrastructure.client.FinOperatingLogClient;
import com.payermax.channel.inst.center.app.manage.InstContactManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstContactReqDTO;
import com.payermax.channel.inst.center.app.response.InstContactVO;
import com.payermax.channel.inst.center.app.service.InstContactService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * @ClassName InstContactManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 10:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstContactManagerImpl implements InstContactManager {

    @Autowired
    private InstContactService instContactService;

    @Autowired
    private FinOperatingLogClient finOperatingLogClient;

    @Value("${spring.application.name}")
    private String appId;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public List<InstContactVO> query(InstContactReqDTO instContactReqDTO) {
        //请求转换
        InstContactEntity instContactReqEntity = reqDtoAssembler.toInstContactEntity(instContactReqDTO);
        //响应转换
        List<InstContactEntity> instContactEntityList = instContactService.query(instContactReqEntity);
        return respVoAssembler.toInstContactVo(instContactEntityList);
    }

    @Override
    public int save(InstContactReqDTO instContactReqDTO) {
        this.recordOperatingLog(instContactReqDTO);
        //请求转换
        InstContactEntity instContactEntity = reqDtoAssembler.toInstContactEntity(instContactReqDTO);
        return instContactService.save(instContactEntity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<InstContactReqDTO> instContactReqDTOs) {
        //请求转换
        List<InstContactEntity> instContactEntities = reqDtoAssembler.toInstContactEntities(instContactReqDTOs);
        List<Long> ids = instContactEntities.stream().filter(obj -> obj.getId() != null).map(InstContactEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(ids)){
            InstContactEntity instContactEntity = new InstContactEntity();
            instContactEntity.setInstId(instContactReqDTOs.get(0).getInstId()); //NO_CHECK 方法未被调用
            List<InstContactEntity> instContactEntityList = instContactService.query(instContactEntity);
            List<InstContactEntity> deleteInstContactList = instContactEntityList.stream().filter(obj -> !ids.contains(obj.getId())).map(obj -> {
                obj.setStatus(StatusEnum.N.getValue());
                return obj;
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(deleteInstContactList)){
                instContactEntities.addAll(deleteInstContactList);
            }
        }
        AtomicInteger result = new AtomicInteger(0);
        instContactEntities.forEach(instContactEntity -> {
            int count = instContactService.save(instContactEntity);
            result.addAndGet(count);
        });
        return result.get();
    }
    @Async
    public void recordOperatingLog(InstContactReqDTO instContactReqDTO){
        try{
            WriteLogDto writeLogDto = new WriteLogDto();
            writeLogDto.setUserId(LogUserThreadLocal.getUserId());
            writeLogDto.setAppId(appId);
            writeLogDto.setAppName(appId);
            writeLogDto.setLinkId(instContactReqDTO.getInstId() != null ? String.valueOf(instContactReqDTO.getInstId()) : String.valueOf(instContactReqDTO.getId()));
            writeLogDto.setModuleId(DictConstants.DICT_INST_CONTACTS_MODULE_ID);
            writeLogDto.setModuleName(DictConstants.DICT_INST_CONTACTS_MODULE_NAME);
            writeLogDto.setActionType(getOperatingType(instContactReqDTO));
            writeLogDto.setActionName(DictConstants.DICT_INST_CONTACTS_MODULE_NAME);
            writeLogDto.setActionContent(JSONObject.toJSONString(instContactReqDTO));
            writeLogDto.setApiName("com.payermax.omc.channel.exchange.client.controller.instcenter.InstContactController#saveInstContact");
            log.info("记录操作日志请求：{}",writeLogDto);
            BaseResult<Object> objectBaseResult = finOperatingLogClient.recordOperatingLog(writeLogDto);
            log.info("记录操作日志响应：{}",objectBaseResult);
        }catch (Exception e){
            log.warn("记录操作日志异常：",e);
        }
    }

    /**
     * 获取操作类型
     * @param instContactReqDTO
     * @return
     */
    private Integer getOperatingType(InstContactReqDTO instContactReqDTO) {
        if(instContactReqDTO.getId() == null){
            return OperatingTypeEnum.ADD.getOperationType();
        }
        if(StatusEnum.Y.getValue().equalsIgnoreCase(instContactReqDTO.getStatus())){
            return OperatingTypeEnum.MODIFY.getOperationType();
        }
        if(StatusEnum.N.getValue().equalsIgnoreCase(instContactReqDTO.getStatus())){
            return OperatingTypeEnum.DEL.getOperationType();
        }
        return OperatingTypeEnum.DEFAULT.getOperationType();
    }
}
