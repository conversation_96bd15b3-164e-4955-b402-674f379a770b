package com.payermax.channel.inst.center.app.service.impl;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.InstContractAccumulateRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayinDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayoutDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeeVaDTO;
import com.payermax.channel.inst.center.app.manage.contract.AbstractContractTermsProcessor;
import com.payermax.channel.inst.center.app.model.contract.ContractContentEnum;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import com.payermax.channel.inst.center.app.model.contract.InstContractFactory;
import com.payermax.channel.inst.center.app.model.contract.dataParser.FeeItemConvertUtils;
import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductDataParser;
import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.model.contract.excelParser.InstContractParser;
import com.payermax.channel.inst.center.app.request.*;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportResponseDTO;
import com.payermax.channel.inst.center.app.response.InstContractInitResponseVo;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.app.service.InstContractDraftService;
import com.payermax.channel.inst.center.app.service.InstContractServiceV2;
import com.payermax.channel.inst.center.app.valid.annon.impl.CompanyEntityValid;
import com.payermax.channel.inst.center.app.valid.excel.ExcelValid;
import com.payermax.channel.inst.center.app.valid.excel.impl.CommonExcelValid;
import com.payermax.channel.inst.center.app.valid.excel.impl.InstContractFeeExcelValid;
import com.payermax.channel.inst.center.common.constrains.InterfaceValid;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.enums.PaymentTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ActionType;
import com.payermax.channel.inst.center.common.enums.instcenter.InstProductTypeEnum;
import com.payermax.channel.inst.center.common.utils.ApplicationUtils;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.common.utils.ExecuteUtil;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractOriginProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractStandardProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.domain.vo.contract.ContractBusinessKey;
import com.payermax.channel.inst.center.infrastructure.adapter.FileDownloadService;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.FundingChannelProductInfo;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.FundingChannelProductInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractVersionInfoRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * InstContractServiceV2Impl
 *
 * <AUTHOR>
 * @desc
 */
@Slf4j
@Service
public class InstContractServiceV2Impl implements InstContractServiceV2 {

    @Resource
    private InstContractBaseInfoRepository instContractBaseInfoRepository;

    @Resource
    private InstContractVersionInfoRepository instContractVersionInfoRepository;

    @Resource
    private InstContractFactory instContractFactory;

    @Resource
    private ContractVersionPOAssembler contractVersionPOAssembler;

    @Resource
    private CompanyEntityValid companyEntityValid;

    @Resource
    private InstContractFeeExcelValid instContractFeeExcelValid;

    @Resource
    private CommonExcelValid commonExcelValid;

    @Resource
    private FileDownloadService fileDownloadService;

    @Resource
    private InstContractDraftService instContractDraftService;

    @Resource
    private InstContractDraftRepository instContractDraftRepository;

    @Resource
    private InstContractDraftAssembler instContractDraftAssembler;

    @Resource
    private FundingChannelProductInfoRepository fundingChannelProductInfoRepository;

    @Resource
    private InstProductDataParser instProductDataParser;


    @Validated
    @Override
    @Deprecated
    public InstContractImportResponseDTO initInstContract(InstContractImportRequestDTO instContractInitRequest) {
        // 1. 读取原始 excel 文件内容
        byte[] fileContent = fileDownloadService.downloadContentByUrl(instContractInitRequest.getContractFileUrl());
        LangEnum langEnum = LangEnum.parse(instContractInitRequest.getLang());

        // 2. 解析 excel文件sheet内容
        ExcelUtil.ExcelParseInfo<InstContractFeeRowItemDTO> feeExcelParseInfo = ExcelUtil.readExcel(fileContent
                , InstContractFeeRowItemDTO.class, ContractContentEnum.INST_FEE_TAX_PAYIN.getExcelSheetName(), instContractInitRequest.getLang());

        ExcelUtil.ExcelParseInfo<InstContractSettleRowItemDTO> settleExcelParseInfo = ExcelUtil.readExcel(fileContent
                , InstContractSettleRowItemDTO.class, ContractContentEnum.INST_SETTLEMENT_PAYIN.getExcelSheetName(), instContractInitRequest.getLang());

        ExcelUtil.ExcelParseInfo<InstContractAccumulateRowItemDTO> accumulateExcelParseInfo = ExcelUtil.readExcel(fileContent
                , InstContractAccumulateRowItemDTO.class, ContractContentEnum.INST_ACCUMULATED_FEE_TAX_PAYIN.getExcelSheetName(), instContractInitRequest.getLang());

        try {
            LangEnum.setLangEnumThreadLocal(langEnum);
            InterfaceValid.initThreadValidDataCache();
            // 3. 数据校验
            InstContractImportResponseDTO validResult = buildValidInfo(feeExcelParseInfo, settleExcelParseInfo, accumulateExcelParseInfo);
            if (validResult.isHasErrors()) {
                return validResult;
            }

            // 4. 领域对象转换
            // 4.1. 筛选出本次excel录入包含哪些机构：key为机构标识，我方签约主体，签约产品类型 共同组成的合同唯一编码，value为对应合同的条款
            Map<ContractBusinessKey, List<InstContractFeeRowItemDTO>> contractFeeItemMap
                    = feeExcelParseInfo.getDataList().stream().collect(Collectors.groupingBy(InstContractFeeRowItemDTO::getContractBusinessKey));
            Map<ContractBusinessKey, List<InstContractSettleRowItemDTO>> contractSettleItemMap
                    = settleExcelParseInfo.getDataList().stream().collect(Collectors.groupingBy(InstContractSettleRowItemDTO::getContractBusinessKey));

            Set<ContractBusinessKey> allContractSet = new HashSet<>();
            allContractSet.addAll(contractFeeItemMap.keySet());
            allContractSet.addAll(contractSettleItemMap.keySet());

            // 4.2. 遍历所有机构合同
            List<InstContractImportResponseDTO> list = new ArrayList<>();
            allContractSet.forEach(contractBusinessKey -> {
                List<InstContractFeeRowItemDTO> tempContractFeeItems = contractFeeItemMap.get(contractBusinessKey);
                List<InstContractSettleRowItemDTO> tempContractSettleItems = contractSettleItemMap.get(contractBusinessKey);
                ContractBusinessTypeEnum businessTypeEnum = ConvertUtils.getEnumByDesc(ContractBusinessTypeEnum.class, contractBusinessKey.getInstProduct());

                InstContractImportResponseDTO contractResponse = processOnSingleContractInput(
                        contractBusinessKey.getInstCode(), contractBusinessKey.getContractEntity(), businessTypeEnum,
                        tempContractFeeItems, tempContractSettleItems, langEnum);
                if (contractResponse != null) {
                    list.add(contractResponse);
                }
            });
            return InstContractImportResponseDTO.merge(list);
        } finally {
            ExecuteUtil.executeNonException(() -> LangEnum.invalidLangEnumThreadLocal(), () -> InterfaceValid.clearThreadValidDataCache());
        }
    }


    @Deprecated
    private InstContractImportResponseDTO buildValidInfo(ExcelUtil.ExcelParseInfo<InstContractFeeRowItemDTO> feeExcelParseInfo,
                                                         ExcelUtil.ExcelParseInfo<InstContractSettleRowItemDTO> settleExcelParseInfo,
                                                         ExcelUtil.ExcelParseInfo<InstContractAccumulateRowItemDTO> accumulateExcelParseInfo) {
        ExcelValid.Result<InstContractFeeRowItemDTO> feeValidResult;
        ExcelValid.Result<InstContractSettleRowItemDTO> settleValidResult;
        ExcelValid.Result<InstContractAccumulateRowItemDTO> accumulateValidResult;
        feeValidResult = instContractFeeExcelValid.valid(feeExcelParseInfo);
        settleValidResult = commonExcelValid.valid(settleExcelParseInfo);
        accumulateValidResult = commonExcelValid.valid(accumulateExcelParseInfo);
        InstContractImportResponseDTO result = new InstContractImportResponseDTO();
        if (!feeValidResult.isSuccess()) {
            result.setFeeErrorInfo(feeValidResult.getErrorInfo());
        }
        if (!settleValidResult.isSuccess()) {
            result.setSettleErrorInfo(settleValidResult.getErrorInfo());
        }
        if (!accumulateValidResult.isSuccess()) {
            result.setAccumulateErrorInfo(accumulateValidResult.getErrorInfo());
        }
        return result;
    }

    /**
     * 此处处理 单份逻辑合同内容
     */
    @Deprecated
    private InstContractImportResponseDTO processOnSingleContractInput(String instCode, String contractEntity, ContractBusinessTypeEnum businessTypeEnum,
                                                                       List<InstContractFeeRowItemDTO> feeParseInfo,
                                                                       List<InstContractSettleRowItemDTO> settleParseInfo,
                                                                       LangEnum langEnum) {
        try {
            // 1. 构造合约处理上下文对象，将集团主体编码转化成我方主体编码（在上层已做过基础校验，此处必定能获取到）
            String entityBizCode = convertEntityBizCode(contractEntity);
            ContractProcessByExcelContext context = new ContractProcessByExcelContext(instCode, entityBizCode,
                    businessTypeEnum, feeParseInfo, settleParseInfo, langEnum);

            // 2. 依次处理
            List<ContractContentEnum> contentEnums = ContractContentEnum.getContractContentByBusinessType(businessTypeEnum);

            for (ContractContentEnum contentEnum : contentEnums) {
                // 2.1. 调用对应sheet处理器，执行具体业务逻辑处理
                AbstractContractTermsProcessor processor = ApplicationUtils.getBean(AbstractContractTermsProcessor.class, contentEnum);
                processor.processOnContractContent(context, contentEnum);
            }

            // 2.2. 如果业务校验 存在异常，在这边直接返回
            if (context.hasBusinessError()) {
                return InstContractImportResponseDTO.builder()
                        .feeErrorInfo(context.getFeeErrorInfo())
                        .settleErrorInfo(context.getSettleErrorInfo())
                        .accumulateErrorInfo(context.getAccumulateFeeErrorInfo())
                        .build();
            }

            // 3. 构造合约，版本合约对象
            Pair<InstContractBaseInfo, InstContractVersionInfo> contractPair = getExistedOrComposeInstContractBaseInfo(context);

            InstContractBaseInfo contractBaseInfo = contractPair.getKey();
            InstContractVersionInfo contractVersionInfo = contractPair.getValue();

            Pair<List<InstContractOriginProduct>, List<InstContractStandardProduct>> productPair =
                    instContractFactory.composeInstProducts(context, contractBaseInfo.getContractNo(), contractVersionInfo.getContractVersion());
            contractVersionInfo.setOriginProducts(productPair.getKey());
            contractVersionInfo.setStandardProducts(productPair.getValue());

            // 4. 入库持久化
            InstContractBaseInfoPO baseInfoPO = contractVersionPOAssembler.convertBaseInfoPO(contractBaseInfo);
            InstContractVersionInfoPO versionInfoPO = contractVersionPOAssembler.convertVersionInfoPO(contractVersionInfo);
            instContractBaseInfoRepository.signNewInstContract(baseInfoPO, versionInfoPO);

        } catch (Exception e) {
            String errorInfo = String.format("handler contract error, importType[%s], instCode[%s], companyEntity[%s]"
                    , businessTypeEnum.getEnglishDesc(), instCode, contractEntity);
            log.error(errorInfo, e);
            return InstContractImportResponseDTO.builder()
                    .errorMsgList(Arrays.asList(errorInfo))
                    .build();
        }
        return null;
    }

    @Deprecated
    private Pair<InstContractBaseInfo, InstContractVersionInfo> getExistedOrComposeInstContractBaseInfo(ContractProcessByExcelContext context) {
        InstContractBaseInfoPO baseInfoPO = instContractBaseInfoRepository.queryActiveBaseInfo(context.getInstCode(), context.getContractEntity(),
                context.getBusinessTypeEnum().name(), ContractStatusEnum.ACTIVATED.name());

        InstContractBaseInfo contractBaseInfo;
        InstContractVersionInfo contractVersionInfo;
        if (baseInfoPO == null) {
            // 还未存在，则构造新的合同基本信息
            contractBaseInfo = instContractFactory.composeInstContractBase(context.getInstCode(), context.getContractEntity(),
                    context.getBusinessTypeEnum());

            contractVersionInfo = instContractFactory.composeInstContractNewVersion(context, contractBaseInfo);

        } else {
            // 已存在，则直接获取 合同基准信息和当前版本
            contractBaseInfo = contractVersionPOAssembler.convertBaseInfoDomain(baseInfoPO);

            InstContractVersionInfoPO versionInfoPO = instContractVersionInfoRepository.queryActiveVersion(contractBaseInfo.getContractNo());
            AssertUtil.notNull(versionInfoPO, "error", "contract version not exist!");
            contractVersionInfo = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoPO);
        }

        return new Pair<>(contractBaseInfo, contractVersionInfo);
    }

    @Deprecated
    private String convertEntityBizCode(String groupEntityCode) {
        String entityBizCode = companyEntityValid.dataList().stream().filter(item -> StringUtils.equalsIgnoreCase(item.getGroupEntityCode(), groupEntityCode))
                .findFirst().map(CompanyEntityValid.SimpleCompanyEntity::getEntityBizCode).orElse(null); //NO_CHECK 匹配我司主体 CODE，结果符合预期

        if (StringUtils.isBlank(entityBizCode)) {
            log.warn("集团主体编码:{} 对应的我方主体编码不存在", groupEntityCode);
            entityBizCode = groupEntityCode;
        }

        return entityBizCode;
    }


    /**
     * 解析机构合同 Excel 信息
     *
     * @param request
     */
    @Override
    public InstContractImportDTO instContractParse(InstContractParseRequestDTO request) {

        // 下载 Excel
        byte[] fileContent = fileDownloadService.downloadContentByUrl(request.getContractFeeFile());
        // 读取本地文件进行解析
        // byte[] fileContent = readExcelFileLocal();

        // 根据产品种类走对应解析逻辑
        // Payout
        if (InstProductTypeEnum.PAYOUT.getValue().equals(request.getProductType())) {
            return InstContractParser.getParser(ContractBizTypeEnum.O).parser(request, fileContent);
        }
        // Payin || 技术服务费
        if (InstProductTypeEnum.PAYIN_WITH_APMS.getValue().equals(request.getProductType())
                || InstProductTypeEnum.TECH_SERVICE.getValue().equals(request.getProductType())) {
            return InstContractParser.getParser(ContractBizTypeEnum.I).parser(request, fileContent);
        }
        // VA
        if (InstProductTypeEnum.VA.getValue().equals(request.getProductType())) {
            return InstContractParser.getParser(ContractBizTypeEnum.VA).parser(request, fileContent);
        }

        throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "暂不支持的产品类型");
    }

    /**
     * 根据 shareId 查询草稿列表
     *
     * @param shareId
     */
    @Override
    public List<InstProductItemVO> queryDraftList(String shareId) {
        return instContractDraftService.queryList(shareId);
    }

    /**
     * 根据条件查询草稿列表
     *
     * @param request
     */
    @Override
    public List<InstProductItemVO> queryDraftByConditions(InstProductQueryRequestDTO request) {
        InstContractDraft draft = instContractDraftAssembler.request2Draft(request);
        Date start = request.getEntryTimeRange().get(0); // NO_CHECK 列表中第一个值表示开始，结果符合预期
        Date end = request.getEntryTimeRange().get(1);
        return instContractDraftService.queryByConditions(draft, start, end);
    }

    /**
     * 根据 ID 查询草稿
     *
     * @param draftId
     */
    @Override
    public InstContractDraft queryDraftByDraftId(String draftId) {
        return instContractDraftRepository.queryById(draftId);
    }

    /**
     * 根据 ID 删除草稿
     *
     * @param draftId
     */
    @Override
    public Boolean delDraftById(String draftId) {
        return instContractDraftRepository.removeById(draftId);
    }

    /**
     * 根据支付类型查询支付方式
     *
     * @param paymentType 支付类型
     */
    @Override
    public List<FundingChannelProductInfo> queryPaymentMethodByType(String paymentType) {
        if (paymentType.equals(InstProductTypeEnum.PAYOUT.getValue())) {
            return fundingChannelProductInfoRepository.queryByPaymentType(PaymentTypeEnum.PAYOUT.getValue());
        }
        if (paymentType.equals(InstProductTypeEnum.PAYIN_WITH_APMS.getValue())
                || paymentType.equals(InstProductTypeEnum.PAYIN_WITH_CARD.getValue())
                || paymentType.equals(InstProductTypeEnum.PAYIN.getValue())
                || paymentType.equals(InstProductTypeEnum.TECH_SERVICE.getValue())
                || paymentType.equals(InstProductTypeEnum.VA.getValue())) {
            return fundingChannelProductInfoRepository.queryByPaymentType(PaymentTypeEnum.PAYMENT.getValue());

        }
        throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "暂不支持的支付类型");
    }

    /**
     * 保存产品结算信息到草稿
     *
     * @param request 结算信息
     */
    @Override
    public Boolean saveSettlementInfoByDraftId(InstContractStandardRequestDTO.StandardSettleInfo request) {
        // 1. 根据 ID 查询草稿数据
        InstContractDraft instContractDraft = instContractDraftRepository.queryById(request.getDraftId());
        // TODO 2. 修改内容进行 diff 写入操作日志
        // 3. 保存数据;
        AbstractInstContractFeeDTO feeDto = draftData2Dto(instContractDraft);
        feeDto.setSettleInfo(request.getSettleInfo());
        instContractDraft.setDraftData(JSON.toJSONString(feeDto));
        return instContractDraftRepository.updateById(instContractDraft);
    }

    /**
     * 保存产品标准化信息到草稿
     *
     * @param request 标准化信息
     */
    @Override
    public Boolean saveStandardProductInfoByDraftId(InstContractStandardRequestDTO.StandardProductInfo request) {
        // 1. 根据 ID 查询草稿数据
        InstContractDraft instContractDraft = instContractDraftRepository.queryById(request.getDraftId());
        AbstractInstContractFeeDTO feeDto = draftData2Dto(instContractDraft);
        // TODO 2. 修改内容进行 diff 写入操作日志
        // 3. 保存数据;
        feeDto.setStandardProductInfo(request.getProductInfo());
        instContractDraft.setDraftData(JSON.toJSONString(feeDto));
        return instContractDraftRepository.updateById(instContractDraft);
    }

    /**
     * 保存换汇标准化信息到草稿
     *
     * @param request 标准化信息
     */
    @Override
    public Boolean saveStandardFxInfo(InstContractStandardRequestDTO.StandardFxInfo request) {
        // 1. 根据 ID 查询草稿数据
        InstContractDraft instContractDraft = instContractDraftRepository.queryById(request.getDraftId());
        // TODO 2. 修改内容进行 diff 写入操作日志
        AbstractInstContractFeeDTO feeDto = draftData2Dto(instContractDraft);
        // 3. 保存数据;
        feeDto.setTargetFxSource(request.getTargetFxSource());
        feeDto.setFxMarkup(request.getFxMarkup());
        feeDto.setConvertedFxMarkup(request.getConvertedFxMarkup());
        instContractDraft.setDraftData(JSON.toJSONString(feeDto));
        return instContractDraftRepository.updateById(instContractDraft);
    }

    /**
     * 机构产品提交复核
     * @param draftId
     * @param status
     */
    @Override
    public boolean productDraftStatusChange(String draftId, String status) {
        // 1. 根据 ID 查询草稿数据
        InstContractDraft instContractDraft = instContractDraftRepository.queryById(draftId);
        AssertUtil.notNull(instContractDraft, "error", "product draft not exist!");
        AssertUtil.isTrue(FeeItemConvertUtils.isStandardized(instContractDraft), "error", "产品标准化未完成");
        // 2. 更新草稿状态
        instContractDraft.setActionType(status);
        instContractDraft.setStatus(status);
        return instContractDraftRepository.updateById(instContractDraft);
    }

    /**
     * 机构产品提交审核
     */
    @Override
    public InstProductParseContext productAuditSubmit(String draftId) {
        // 1. 根据 ID 查询草稿数据
        InstContractDraft instContractDraft = instContractDraftRepository.queryById(draftId);
        AssertUtil.notNull(instContractDraft, "error", "product draft not exist!");

        // 2. 数据解析、拆分
        InstProductParseContext context = instProductDataParser.process(instContractDraft);

        // 3. TODO 数据校验兜底，大部分校验应在录入、标准化时完成

        // 4. 写入数据库
        instContractDraftService.draftParseResultSaving(context);

        // 5. 更新草稿状态
        instContractDraft.setActionType(ActionType.AUDIT_AGREE.name());
        instContractDraft.setStatus(ActionType.AUDIT_AGREE.name());
        instContractDraftRepository.updateById(instContractDraft);
        // TODO 6. 写入操作日志
        return context;
    }



    /**
     * 根据产品类型转换 DTO
     *
     * @param instContractDraft 草稿数据
     */
    private AbstractInstContractFeeDTO draftData2Dto(InstContractDraft instContractDraft) {
        if (instContractDraft.getBusinessType().equals(InstProductTypeEnum.PAYIN.getValue())
                || instContractDraft.getBusinessType().equals(InstProductTypeEnum.PAYIN_WITH_APMS.getValue())
                || instContractDraft.getBusinessType().equals(InstProductTypeEnum.PAYIN_WITH_CARD.getValue())
                || instContractDraft.getBusinessType().equals(InstProductTypeEnum.TECH_SERVICE.getValue())) {
            return JSON.parseObject(instContractDraft.getDraftData(), InstContractFeePayinDTO.class);
        }
        if (instContractDraft.getBusinessType().equals(InstProductTypeEnum.PAYOUT.getValue())) {
            return JSON.parseObject(instContractDraft.getDraftData(), InstContractFeePayoutDTO.class);
        }
        if (instContractDraft.getBusinessType().equals(InstProductTypeEnum.VA.getValue())){
            return JSON.parseObject(instContractDraft.getDraftData(), InstContractFeeVaDTO.class);
        }
        throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "暂不支持的支付类型");
    }


    /**
     * 将机构合同保存到草稿表
     *
     * @param request
     */
    @Override
    public InstContractInitResponseVo instContractListSave(InstContractInitRequestDTO request) {
        return instContractDraftService.initInstContract(request);
    }


    /**
     * 本地调试时直接读取本地文件进行解析
     */
    private static byte[] readExcelFileLocal() {

        // 直接读取本地文件
        String filePath = "";
        FileInputStream fis = null;
        byte[] fileBytes = null;

        try {
            File file = new File(filePath);
            fis = new FileInputStream(file);

            // 创建一个与文件大小相同的字节数组
            fileBytes = new byte[(int) file.length()];

            // 将文件内容读取到字节数组中
            fis.read(fileBytes);
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return fileBytes;
    }
}
