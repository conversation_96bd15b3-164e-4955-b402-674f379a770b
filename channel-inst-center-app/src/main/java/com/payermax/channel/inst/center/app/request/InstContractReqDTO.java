package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstContractReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 0:06
 */
@Data
public class InstContractReqDTO implements Serializable {
    @ApiModelProperty(notes = "合同单号")
    private String contractNo;

    @ApiModelProperty(notes = "合同号")
    private String realContractNo;

    @ApiModelProperty(notes = "机构id")
    private Long instId;

    @ApiModelProperty(notes = "申请单号")
    private String applyNo;

    @ApiModelProperty(notes = "资金结算机构")
    private String fundsSettleInst;

    @ApiModelProperty(notes = "服务采购方")
    private String servicePurchaser;

    @ApiModelProperty(notes = "服务提供方")
    private String serviceProvider;

    @ApiModelProperty(notes = "开始时间")
    private Date startDate;

    @ApiModelProperty(notes = "结束时间")
    private Date endDate;

    @ApiModelProperty(notes = "合同签署情况")
    private String signFlag;

    @ApiModelProperty(notes = "合同影印件")
    private String contractAttachId;

    @ApiModelProperty(notes = "备注")
    private String remark;

    @ApiModelProperty(notes = "状态")
    private String status;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "机构ids")
    private List<Long> instIds;
}
