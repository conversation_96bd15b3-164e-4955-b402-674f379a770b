package com.payermax.channel.inst.center.app.manage.contract.settle;

import com.payermax.channel.inst.center.common.utils.ApplicationUtils;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR> 2023/6/21  2:54 PM
 */
public class DSLResolveProcessor {

    /**
     * 解析DSL表达式到实体
     */
    public static SettleRoundDSLEntity resolveDslToEntity(SettleDate instSettleDateConfig) {
        AssertUtil.notNull(instSettleDateConfig, "", "instSettleDateConfig must not null");
        SettleRoundDSLEntity roundDSLEntity = new SettleRoundDSLEntity();

        // 1、交易开始时间DSL解析
        AssertUtil.isTrue(StringUtils.isNotBlank(instSettleDateConfig.getTransactionStartDate()), "",
                "instSettleDateConfig transactionStartDate must not null");
        resolveDealStartDate(instSettleDateConfig, roundDSLEntity);

        // 2、交易结束时间DSL解析
        AssertUtil.isTrue(StringUtils.isNotBlank(instSettleDateConfig.getTransactionEndDate()), "",
                "instSettleDateConfig transactionEndDate must not null");
        resolveDealEndDate(instSettleDateConfig, roundDSLEntity);

        // 3、账单日DSL解析
        AssertUtil.isTrue(StringUtils.isNotBlank(instSettleDateConfig.getBillDate()), "",
                "instSettleDateConfig billDate must not null");
        resolveBillDate(instSettleDateConfig, roundDSLEntity);

        // 4、打款日DSL解析
        if (StringUtils.isNotBlank(instSettleDateConfig.getPaymentDate())) {
            resolvePaymentDate(instSettleDateConfig, roundDSLEntity);
        }

        // 5、到账日DSL解析
        if (StringUtils.isNotBlank(instSettleDateConfig.getArriveDate())) {
            resolveArrivedDate(instSettleDateConfig, roundDSLEntity);
        }

        // 6、换汇日DSL解析
        if(StringUtils.isNotBlank(instSettleDateConfig.getExchangeDate())) {
            resolveExchangeDate(instSettleDateConfig, roundDSLEntity);
        }

        return roundDSLEntity;
    }

    /**
     * 结算周期条款 - 交易时间开始解析
     */
    private static void resolveDealStartDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String dealStartDateDSLExp = instSettleDateConfig.getTransactionStartDate();
        if (StringUtils.isNotBlank(dealStartDateDSLExp)) {
            DSLTypeEnum dealStartDSLType = DSLTypeEnum.mateDslType(dealStartDateDSLExp);
            AbstractDslResolver dealStartDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, dealStartDSLType);
            dealStartDSLResolver.fillDslTransactionStartCycle(instSettleDateConfig, roundDSLEntity);
        }
    }

    /**
     * 结算周期条款 - 交易时间结束解析
     */
    private static void resolveDealEndDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String dealEndDateDSLExp = instSettleDateConfig.getTransactionEndDate();
        if (StringUtils.isNotBlank(dealEndDateDSLExp)) {
            DSLTypeEnum dealEndDSLType = DSLTypeEnum.mateDslType(dealEndDateDSLExp);
            AbstractDslResolver dealEndDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, dealEndDSLType);
            dealEndDSLResolver.fillDslTransactionEndCycle(instSettleDateConfig, roundDSLEntity);
        }
    }

    /**
     * 账单日解析
     */
    private static void resolveBillDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String billDSLExp = instSettleDateConfig.getBillDate();
        if (StringUtils.isNotBlank(billDSLExp)) {
            DSLTypeEnum billDSLType = DSLTypeEnum.mateDslType(billDSLExp);
            AbstractDslResolver billDSLTypeDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, billDSLType);
            billDSLTypeDSLResolver.fillDslSettleCycle(instSettleDateConfig, roundDSLEntity);
        }
    }

    /**
     * 账单日解析
     */
    private static void resolvePaymentDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String paymentDate = instSettleDateConfig.getPaymentDate();
        if (StringUtils.isNotBlank(paymentDate)) {
            DSLTypeEnum billDSLType = DSLTypeEnum.mateDslType(paymentDate);
            AbstractDslResolver billDSLTypeDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, billDSLType);
            billDSLTypeDSLResolver.fillDslPaymentCycle(instSettleDateConfig, roundDSLEntity);
        }
    }

    /**
     * 账单日解析
     */
    private static void resolveArrivedDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String arrivedDSLExp = instSettleDateConfig.getArriveDate();
        if (StringUtils.isNotBlank(arrivedDSLExp)) {
            DSLTypeEnum arrivedDSLType = DSLTypeEnum.mateDslType(arrivedDSLExp);
            AbstractDslResolver arrivedDSLTypeDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, arrivedDSLType);
            arrivedDSLTypeDSLResolver.fillDslArriveCycle(instSettleDateConfig, roundDSLEntity);
        }
    }


    /**
     * 换汇日解析
     */
    private static void resolveExchangeDate(SettleDate instSettleDateConfig, SettleRoundDSLEntity roundDSLEntity) {
        String exchangeDSLExp = instSettleDateConfig.getExchangeDate();
        if (StringUtils.isNotBlank(exchangeDSLExp)) {
            DSLTypeEnum exchangeDSLType = DSLTypeEnum.mateDslType(exchangeDSLExp);
            AbstractDslResolver exchangeDSLTypeDSLResolver = ApplicationUtils.getBean(AbstractDslResolver.class, exchangeDSLType);
            exchangeDSLTypeDSLResolver.fillDslExchangeCycle(instSettleDateConfig, roundDSLEntity);
        }
    }
}