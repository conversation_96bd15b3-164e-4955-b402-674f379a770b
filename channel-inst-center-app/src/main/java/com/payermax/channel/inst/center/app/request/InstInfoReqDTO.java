package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @ClassName InstInfoReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 20:31
 */
@Data
public class InstInfoReqDTO implements Serializable {

    private static final long serialVersionUID = -2708126723725033011L;

    /**
     * 机构简称
     */
    @ApiModelProperty(notes = "机构简称")
    @Size(max = 64, message = "[instName] maximum 64 length")
    private String instName;

    /**
     * 机构类型，可多个
     */
    @ApiModelProperty(notes = "机构类型")
    private String instTypes;

    /**
     * 主体所在地
     */
    @ApiModelProperty(notes = "主体所在地")
    private String entityCountry;

    /**
     * 简介
     */
    @ApiModelProperty(notes = "简介")
    @Size(max = 256, message = "[remark] maximum 256 length")
    private String remark;

    /**
     * 机构品牌信息
     */
    @ApiModelProperty(notes = "机构品牌信息")
    private InstBrandReqDTO brandDto;

    /**
     * 机构dd信息
     */
    @ApiModelProperty(notes = "机构dd信息")
    private InstDdReqDTO ddInfoDto;
}
