package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.nacos.common.utils.StringUtils;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 机构合同 Payin/技术服务 费用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstContractFeePayinDTO extends AbstractInstContractFeeDTO {


    /*-------------- 此处为 Excel 表格必填项 🔽---------------*/

    /**
     * 退款时是否退原交易手续费
     *      Y：yes
     *          N：no
     */
    @NotEmpty(message = "退款时是否退原交易手续费不能为空")
    @ExcelProperty("Refund Transaction Fee\n退款时是否退原交易手续费")
    private String refundTransactionFee;

    /**
     * 结算周期
     */
    @NotEmpty(message = "结算周期不能为空")
    @ExcelProperty("Settlement Term\n结算周期")
    private String settlementTerm;

    /**
     * 结算币种
     */
    @NotEmpty(message = "结算币种不能为空")
    @ExcelProperty("Settlement Currency\n结算币种")
    private String settlementCurrency;

    /**
     * 算费时间
     * 费用计算时间，用于财务核算
     *      枚举选项：
     *          交易日 Transaction Day
     *          结算日 Settlement Day
     */
    @NotEmpty(message = "算费时间不能为空")
    @ExcelProperty("Calculate Fees Time\n算费时间")
    private String calculateFeesTime;



    /*-------------- 此处为 Excel 表格非必填项 🔽---------------*/

    /**
     * 收单国家/区域
     */
    @ExcelProperty("Acquiring Country\n收单国家/区域")
    private String country;

    /**
     * 机构行业分类
     */
    @ExcelProperty("External Industry Category\n机构行业分类")
    private String externalIndustryCategory;

    /**
     * 资金来源
     */
    @ExcelProperty("FundingSource\n资金来源")
    private String fundingSource;


    /* ----------------- 退款相关信息 🔽 ----------------- */
    /**
     * 退款计费方式
     */
    @ExcelProperty("Refund Calculation Method\n退款计费方式")
    private String refundCalculationMethod;

    /**
     * 退款费用比例
     */
    @ExcelProperty("Refund Percentage Ratio\n退款费用比例")
    private String refundPercentageRatio;

    /**
     * 退款固定费用
     */
    @ExcelProperty("Refund Fixed Fee\n退款固定费用")
    private String refundFixedFee;

    /**
     * 退款费用币种
     */
    @ExcelProperty("Refund Fee Currency\n退款费用币种")
    private String refundFeeCurrency;

    /**
     * 退款最低收费
     */
    @ExcelProperty("Refund Min Fee\n退款最低收费")
    private String refundMinFee;

    /**
     * 退款封顶收费
     */
    @ExcelProperty("Refund Max Fee\n退款封顶收费")
    private String refundMaxFee;

    /* ----------------- 退款相关信息 🔼 ----------------- */


    /* ----------------- 争议相关信息 🔽 ----------------- */

    /**
     * 争议计费方式
     */
    @ExcelProperty("CB Calculation Method\n争议计费方式")
    private String cbCalculationMethod;

    /**
     * 争议费用比例
     */
    @ExcelProperty("CB Percentage Ratio\n争议费用比例")
    private String cbPercentageRatio;

    /**
     * 争议固定费用
     */
    @ExcelProperty("CB Fixed Fee\n争议固定费用")
    private String cbFixedFee;

    /**
     * 争议费用币种
     */
    @ExcelProperty("CB Fee Currency\n争议费用币种")
    private String cbFeeCurrency;

    /**
     * 争议最低收费
     */
    @ExcelProperty("CB Min Fee\n争议最低收费")
    private String cbMinFee;

    /**
     * 争议封顶收费
     */
    @ExcelProperty("CB Max Fee\n争议封顶收费")
    private String cbMaxFee;
    /* ----------------- 争议相关信息 🔼 ----------------- */



    /**
     * 机构+我方主体+合作模式+机构产品类型+机构产品名称+支付币种+国家+行业分类+资金来源+【发卡区域 +卡类型+持卡人类型+卡组织 +卡等级】+ 清算网络 + 费用承担方 +生效时间
     * 构成唯一机构合约
     */
    @Override
    public String getInstContractKey(){
        List<String> keyList = Stream.of(getInstCode(), getContractEntity(), getCooperationMode(), getProductType(), getInstProductName(),
                        getTransactionCurrency(), getCountry(),getExternalIndustryCategory(),getFundingSource(), getClearNetWork(), getFeeBearer(),
                        String.valueOf(getEffectiveTime().getTime()))
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, SymbolConstants.SYMBOL_STRIKE_LINE);
    }

    /**
     * 机构产品名称+支付币种+结算币种
     * 构成用于和结算信息对应的唯一标识
     */
    public String getSettleKey() {
        List<String> keyList = Stream.of(getInstProductName(), getTransactionCurrency(), getSettlementCurrency())
                .filter(Objects::nonNull)
                .map(Object::toString)
                .collect(Collectors.toList());
        return StringUtils.join(keyList, SymbolConstants.SYMBOL_STRIKE_LINE);
    }

}
