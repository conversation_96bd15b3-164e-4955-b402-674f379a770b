package com.payermax.channel.inst.center.app.model.contract.excelParser;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.dto.impl.AccumulatedInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeeVaDTO;
import com.payermax.channel.inst.center.app.dto.impl.StandardProductInfoDTO;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstContractExcelEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.VaFeeTypeEnum;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/21
 * @DESC
 */
@Slf4j
@Component
public class VAParser extends InstContractParser{



    /**
     * VA 默认支付类型
     */
    @NacosValue(value = "#{${inst.contract.fee.query.va.paymentMethodType:'BankTransfer'}}", autoRefreshed = true)
    private String PAYMENT_METHOD_TYPE;
    @NacosValue(value = "#{${inst.contract.fee.query.va.targetOrg:'*'}}", autoRefreshed = true)
    private String TARGET_ORG;
    @NacosValue(value = "#{${inst.contract.fee.query.va.cardOrg:'SETTLEORG'}}", autoRefreshed = true)
    private String CARD_ORG;

    @PostConstruct
    public void init() {
        registryParser(ContractBizTypeEnum.VA, this);
    }

    @Override
    public InstContractContext buildContext(InstContractParseRequestDTO request) {
        return instContractDraftAssembler.request2Context(request);
    }

    @Override
    public void resolve(byte[] fileContent, InstContractContext context) {
        ExcelUtil.ExcelParseInfo<InstContractFeeVaDTO> excelFeeInfo = ExcelUtil.readExcel(fileContent,
                InstContractFeeVaDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_VA.getExcelSheetName());
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = ExcelUtil.readExcel(fileContent,
                AccumulatedInfoDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_VA.getExcelSheetName());

        AssertUtil.isTrue(CollectionUtils.isNotEmpty(excelFeeInfo.getDataList()), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 费用信息为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(excelAccumulatedInfo.getDataList()), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 手续费信息为空");


        // 删除第一行数据（excel 中第二行，对列的解释）
        excelFeeInfo.getDataList().remove(0);
        excelAccumulatedInfo.getDataList().remove(0);

        // 填入 Excel 中不带的公共字段
        excelFeeInfo.getDataList().forEach(item -> instContractDraftAssembler.vaFeeDtoContextFill(item, context));

        // 费用信息、手续费信息、结算费用写入上下文
        context.setFeeInfo(excelFeeInfo);
        context.setAccumulatedInfo(excelAccumulatedInfo);
    }

    @Override
    public void valid(InstContractContext context) {
        // 对数据行判空，没有数据抛出异常
        if(context.getFeeInfo().getDataList().isEmpty()){
            throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(),"Excel 数据不允许为空");
        }

        // 校验 Excel 内容, 校验失败信息包装到返回值中
        Map<String, ExportErrorInfo> errorInfoMap = new HashMap<>(16);
        errorInfoMap.put(InstContractExcelEnum.INST_CONTRACT_FEE_VA.getExcelSheetName(), commonExcelValid.valid(context.getFeeInfo()).getErrorInfo());
        // 根据校验结果判断有无校验错误
        context.setIsHasError(errorInfoMap.values().stream().anyMatch(Objects::nonNull));
        context.setErrorInfoMap(errorInfoMap);

    }

    @Override
    public InstContractImportDTO dataHandle(InstContractContext context) {

        ExcelUtil.ExcelParseInfo<InstContractFeeVaDTO> excelFeeInfo = (ExcelUtil.ExcelParseInfo<InstContractFeeVaDTO>) context.getFeeInfo();
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = context.getAccumulatedInfo();

        // 机构合同解析结果去重（简单去重，后续可完善去重逻辑）
        List<InstContractFeeVaDTO> instContractFeeList = excelFeeInfo.getDataList().stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InstContractFeeVaDTO::getInstContractKey))), ArrayList::new));

        // 将解析到的手续费信息和业务唯一键进行对应，然后根据业务唯一键分组
        // 将业务唯一键和 Excel 所在行进行关联
        Map<Integer, String> excelRowBizKeyMap = excelFeeInfo.getDataList().stream()
                .collect(Collectors.toMap(InstContractFeeVaDTO::getExcelRowNo, InstContractFeeVaDTO::getInstContractKey));
        // 根据所在行对关联手续费和业务唯一键
        excelAccumulatedInfo.getDataList().forEach(item -> item.setInstContractKey(excelRowBizKeyMap.get(item.getExcelRowNo())));
        // 手续费分组
        Map<String, List<AccumulatedInfoDTO>> accumulatedInfoGroupByBizKey = excelAccumulatedInfo.getDataList().stream()
                .collect(Collectors.groupingBy(AccumulatedInfoDTO::getInstContractKey));

        // 将手续费写入机构合同列表，VA 合约无外汇、结算
        instContractFeeList.forEach( fee -> fee.setAccumulatedInfoList(accumulatedInfoGroupByBizKey.get(fee.getInstContractKey())));

        // 构造 VA 费用信息
        List<InstContractFeeVaDTO> vaFeeList = composeVaFee(instContractFeeList);

        // VA 费用 支付方式填充
        vaFeeList.forEach(fee -> {
            StandardProductInfoDTO standardProductInfoDTO = new StandardProductInfoDTO();
            StandardProductInfoDTO.PaymentItem paymentItem = new StandardProductInfoDTO.PaymentItem();
            paymentItem.setPaymentType(PAYMENT_METHOD_TYPE);
            paymentItem.setTarget(CARD_ORG);
            standardProductInfoDTO.setPaymentList(Collections.singletonList(paymentItem));
            fee.setStandardProductInfo(standardProductInfoDTO);
        });

        // 根据 Excel 行排序并返回结果
        InstContractImportDTO response = instContractDraftAssembler.context2ImportDTO(context);
        response.setDataList(vaFeeList.stream().sorted(Comparator.comparingInt(InstContractFeeVaDTO::getExcelRowNo)).collect(Collectors.toList()));

        return response;
    }

    @Override
    public InstContractImportDTO errorHandle(InstContractContext context) {
        return instContractDraftAssembler.context2ImportDTO(context);
    }


    /**
     * VA 交易费 | 账号费组装
     */
    private List<InstContractFeeVaDTO> composeVaFee(List<InstContractFeeVaDTO> feeInfoList){
        // 将业务唯一键进行分组，不区分账号费
        Map<String, List<InstContractFeeVaDTO>> bizKeyMap = feeInfoList.stream().collect(Collectors.groupingBy(InstContractFeeVaDTO::getInstContractKeyWithoutFeeType, Collectors.toList()));

        // 构造完整 VA 费用，包括交易费 | 账号费
        return bizKeyMap.values().stream().map(feeList -> {
            // 根据 FeeType 分组
            Map<VaFeeTypeEnum, InstContractFeeVaDTO> feeTypeMap = feeList.stream().collect(Collectors.toMap(item -> VaFeeTypeEnum.valueOf(item.getFeeType())
                    , item -> item, (v1, v2) -> {
                        throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 中费用项重复");
                    }));
            // 将账号费填入交易费
            InstContractFeeVaDTO vaFee;
            if(feeTypeMap.containsKey(VaFeeTypeEnum.TRANSACTION)){
                vaFee = feeTypeMap.get(VaFeeTypeEnum.TRANSACTION);
            }else{
                AssertUtil.isTrue(feeTypeMap.containsKey(VaFeeTypeEnum.ACCOUNT), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "交易费和账号费不可同时为空");
                // 无交易费时，将账号费的维度作为交易费维度填充
                vaFee = instContractDraftAssembler.accountInfo2FeeDto(feeTypeMap.get(VaFeeTypeEnum.ACCOUNT));
            }
            vaFee.setAccountFeeInfo(feeTypeMap.get(VaFeeTypeEnum.ACCOUNT));
            return vaFee;
        }).collect(Collectors.toList());

    }
}
