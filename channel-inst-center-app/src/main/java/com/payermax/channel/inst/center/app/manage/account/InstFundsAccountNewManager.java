package com.payermax.channel.inst.center.app.manage.account;

import com.payermax.omc.channel.exchange.facade.request.InstFundsAccountSaveRequest;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC 渠道账户管理-新
 */
public interface InstFundsAccountNewManager {

    /**
     * 新增/修改账户 流程发起
     * @param request 新增/修改账户请求
     * @param shareId 用户 ID
     * @return 流程发起结果
     */
    Boolean startSaveProcess(InstFundsAccountSaveRequest request, String shareId);
}
