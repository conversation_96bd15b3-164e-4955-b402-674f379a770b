package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountManage;
import com.payermax.channel.inst.center.app.manage.template.close.ApiCloseTemplate;
import com.payermax.channel.inst.center.app.manage.template.close.OfflineCloseTemplate;
import com.payermax.channel.inst.center.app.manage.template.close.SubAccountCloseTemplate;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.LockRegionEnum;
import com.payermax.channel.inst.center.common.enums.StatusEnumYN;
import com.payermax.channel.inst.center.common.enums.instcenter.CloseModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubAccountDetailByIdRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubFundsAccountRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.CloseSubAccountResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QuerySubAccountDetailByIdResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QuerySubFundsAccountResponseDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.util.LockUtils;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName InstFundsAccountManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/10/8 16:39
 * @Version 1.0
 */
@Service
@Slf4j
public class InstSubFundsAccountManageImpl implements InstSubFundsAccountManage {

    @Autowired
    LockUtils lockUtils;
    @Autowired
    ReqDoAssembler reqDoAssembler;
    @Autowired
    ResDoAssembler resDoAssembler;
    @Autowired
    InstFundsAccountService instFundsAccountService;
    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;

    @Override
    public List<QuerySubFundsAccountResponseDO> queryAccountDetailAndSubList(QuerySubFundsAccountRequestDO queryAccountDetailRequestDO) {
        // 默认仅查询支持子级资金账号的机构账号
        InstFundsAccountAndSubQueryEntity queryEntity = reqDoAssembler.toInstFundsAccountAndSubQueryEntity(queryAccountDetailRequestDO);
        List<InstFundsAccountEntity> accountEntityList = instFundsAccountService.queryAccountDetailAndSubList(queryEntity);
        return resDoAssembler.toQuerySubFundsAccountResponseDOs(accountEntityList);
    }

    @Override
    public QuerySubAccountDetailByIdResponseDO querySubAccountDetailById(QuerySubAccountDetailByIdRequestDO querySubAccountDetailByIdRequestDO) {
        // 默认仅查询支持子级资金账号的机构账号
        InstSubFundsAccountQueryEntity querySubEntity = reqDoAssembler.toInstSubFundsAccountQueryEntity(querySubAccountDetailByIdRequestDO);
        InstSubFundsAccountEntity subAccountEntity = instSubFundsAccountService.queryById(querySubEntity);
        if (Objects.isNull(subAccountEntity)) {
            return null;
        }
        
        InstFundsAccountQueryEntity queryEntity = new InstFundsAccountQueryEntity();
        queryEntity.setAccountId(subAccountEntity.getAccountId());
        InstFundsAccountEntity accountEntity = instFundsAccountService.queryById(queryEntity);
        if (Objects.isNull(accountEntity)) {
            return null;
        }
        
        return resDoAssembler.toQuerySubAccountDetailByIdResponseDO(accountEntity, subAccountEntity);
       
    }

    @Override
    public CloseSubAccountResponseDO closeSubAccountDetailById(String businessKey) {
        
        // 查询机构子级账号
        InstSubFundsAccountQueryEntity querySubEntity = new InstSubFundsAccountQueryEntity();
        querySubEntity.setBusinessKey(businessKey);
        InstSubFundsAccountEntity subAccountEntity = instSubFundsAccountService.queryByBusinessKey(querySubEntity);
        if (Objects.isNull(subAccountEntity)) {
            throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_NOT_EXIST.getCode(), ErrorCodeEnum.SUB_ACCOUNT_NOT_EXIST.getMsg());
        }

        // 已停用,或非已激活则不再进行后续操作
        if (Objects.equals(SubAccountStatusEnum.TERMINATED.getStatus(),subAccountEntity.getStatus())
                || !Objects.equals(SubAccountStatusEnum.ACTIVATED.getStatus(),subAccountEntity.getStatus())) {
            throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_STATUS_NOT_SUPPORT.getCode(), ErrorCodeEnum.SUB_ACCOUNT_STATUS_NOT_SUPPORT.getMsg());
        }

        // 查询机构账号
        InstFundsAccountQueryEntity queryEntity = new InstFundsAccountQueryEntity();
        queryEntity.setAccountId(subAccountEntity.getAccountId());
        InstFundsAccountEntity accountEntity = instFundsAccountService.queryById(queryEntity);
        if (Objects.isNull(accountEntity)) {
            throw new BusinessException(ErrorCodeEnum.ACCOUNT_NOT_EXIST.getCode(), ErrorCodeEnum.ACCOUNT_NOT_EXIST.getMsg());
        }

        if (!StringUtils.equals(accountEntity.getIsSupportCloseAccount(), StatusEnumYN.Y.getType())) {
            throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_NOT_SUPPORT_CLOSE.getCode(), ErrorCodeEnum.SUB_ACCOUNT_NOT_SUPPORT_CLOSE.getMsg());
        }

        // 调用关闭模版机构账号
        SubAccountCloseTemplate closeTemplate = routeCloseTemplate(accountEntity.getCloseMode());
        RequestAccountDO requestAccountDO = new RequestAccountDO();
        requestAccountDO.setInstFundsAccountEntity(accountEntity);
        requestAccountDO.setInstSubFundsAccountEntity(subAccountEntity);
        closeTemplate.close(requestAccountDO);

        // 组装响应信息
        InstSubFundsAccountEntity subAccountResult = instSubFundsAccountService.queryByBusinessKey(querySubEntity);
        CloseSubAccountResponseDO responseDO = resDoAssembler.toCloseSubAccountResponseDO(accountEntity);
        if (Objects.nonNull(subAccountResult)) {
            responseDO.setSubAccountId(subAccountResult.getSubAccountId());
            responseDO.setSubAccountName(subAccountResult.getSubAccountName());
            responseDO.setSubAccountNo(subAccountResult.getSubAccountNo());
            responseDO.setSubAccountStatus(subAccountResult.getStatus());
            responseDO.setBSubAccountNo(subAccountResult.getBSubAccountNo());
            responseDO.setMerchantNo(subAccountResult.getMerchantNo());
            responseDO.setSubMerchantNo(subAccountResult.getSubMerchantNo());
            responseDO.setSubScenes(subAccountResult.getScenes());
        }
        return responseDO;
    }

    @Override
    public boolean checkSubAccountNoDuplication(String subAccountNo, String subAccountId, String accountId) {
        Preconditions.checkArgument(StringUtils.isNotBlank(subAccountId), "param subAccountId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(accountId), "param accountId is mandatory");
        if (StringUtils.isEmpty(subAccountNo)) {
            return true;
        }
        String lockKey  = Joiner.on(StrPool.COLON).join(accountId, subAccountNo);
        if (lockUtils.lock(lockKey, LockRegionEnum.SUB_ACCOUNT_NO_LOCK)) {
            try {
                // 查询同一子账号的记录
                InstSubFundsAccountQueryEntity queryEntity = new InstSubFundsAccountQueryEntity();
                queryEntity.setAccountId(accountId);
                queryEntity.setSubAccountNo(subAccountNo);
                List<InstSubFundsAccountEntity> accountEntityList = instSubFundsAccountService.queryListByAccountId(queryEntity);
                if (CollUtil.isEmpty(accountEntityList)) {
                    return true;
                }
                // 筛掉当前账号记录的ID
                List<InstSubFundsAccountEntity> otherSubAccount = accountEntityList.stream().filter(subAccount -> !subAccount.getSubAccountId().equals(subAccountId)).collect(Collectors.toList());
                // 存在其他子级账号，则报异常
                return CollUtil.isEmpty(otherSubAccount);
            } finally {
                lockUtils.release(lockKey,LockRegionEnum.SUB_ACCOUNT_NO_LOCK);
            }
        }
        return false;
    }

    @Autowired
    ApiCloseTemplate apiCloseTemplate;
    @Autowired
    OfflineCloseTemplate offlineCloseTemplate;
    
    /**
     * 根据子级资金账户关闭模式获取一个处理模版
     **/
    public SubAccountCloseTemplate routeCloseTemplate(String closeMode) {
        if (StringUtils.isEmpty(closeMode)) {
            return null;
        }
        SubAccountCloseTemplate modeTemplate;
        switch (Objects.requireNonNull(CloseModeEnum.getByType(closeMode))) {
            case API:
                modeTemplate = apiCloseTemplate;
                break;
            case OFFLINE:
                modeTemplate = offlineCloseTemplate;
                break;
            default:
                throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_NOT_SUPPORT_CLOSE.getCode(), ErrorCodeEnum.SUB_ACCOUNT_NOT_SUPPORT_CLOSE.getMsg());
        }
        return modeTemplate;
    }
    
}
