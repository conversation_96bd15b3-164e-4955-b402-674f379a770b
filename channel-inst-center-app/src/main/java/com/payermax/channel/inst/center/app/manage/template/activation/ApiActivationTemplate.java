package com.payermax.channel.inst.center.app.manage.template.activation;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.manage.template.ApiTemplateUtil;
import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/9 20:30
 **/
@Service
@Slf4j
public class ApiActivationTemplate implements SubAccountActivationTemplate {
    public static final String ACTIVATION_SUB_ACCOUNT = "activationSubAccount";
    public static final String ALERT_TITLE = "机构子级账号【API激活】异常";
    @Autowired
    DingAlertClient dingAlertClient;
    @Autowired
    ApiTemplateUtil apiTemplateUtil;
    @Autowired
    public StateMachineExecutor stateMachineExecutor;
    @NacosValue(value = "${inst.sub.account.alert.message.for.api.activation.exception:}", autoRefreshed = true)
    private String subAccountApiActivationException;

    @Override
    public void activation(RequestAccountDO requestAccountDO) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 获取机构账号
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        
        Boolean checkResult = apiTemplateUtil.checkRequestParams(requestAccountDO, ACTIVATION_SUB_ACCOUNT);

        if (Boolean.FALSE.equals(checkResult)) {
            return;
        }
        // 构建调用渠道网关API查询接口请求参数
        JSONObject channelRequest = apiTemplateUtil.buildChannelRequest(instFundsAccountEntity, instSubFundsAccountEntity, requestAccountDO.getInstSubFundsAccountBucketsNotNullEntityList());

        try {
            // 发送渠道网关API请求
            apiTemplateUtil.sendRequest(requestAccountDO, ACTIVATION_SUB_ACCOUNT, channelRequest, ApiTemplateUtil.ACTIVATION_STATE);
        } catch (Exception e) {
            log.error("ApiActivationTemplate-sendRequest Exception!", e);
            // 发送钉钉通知
            dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountApiActivationException, channelRequest);
        }

        // 待激活状态 则需要更新为激活中（说明是超时等其他异常情况）
        if (Objects.equals(instSubFundsAccountEntity.getStatus(), SubAccountStatusEnum.TO_BE_ACTIVATED.getStatus())) {
            // 更新状态 -》激活中
            instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.ACTIVE.getStatus());
            StateRequest stateRequest = new StateRequest(SubAccountStatusEnum.TO_BE_ACTIVATED.getStatus(), ActivationModeEnum.getByType(instFundsAccountEntity.getActivationMode()), instSubFundsAccountEntity);
            stateMachineExecutor.transChangeSubAccountActivationState(stateRequest);
        }
    }
}
