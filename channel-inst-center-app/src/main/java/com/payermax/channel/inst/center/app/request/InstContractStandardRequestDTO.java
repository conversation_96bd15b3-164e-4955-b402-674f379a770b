package com.payermax.channel.inst.center.app.request;

import com.payermax.channel.inst.center.app.dto.impl.SettleInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.StandardProductInfoDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InstContractStandardRequestDTO {


    /**
     * 产品标准化信息
     */
    @Data
    public static class StandardProductInfo {

        /**
         * 草稿 ID
         */
        @NotBlank(message = "[draftId] is mandatory")
        private String draftId;

        /**
         * 产品标准化信息
         */
        @NotNull(message = "[standardProductInfo] is mandatory")
        private StandardProductInfoDTO productInfo;
    }

    /**
     * 结算标准化信息
     */
    @Data
    public static class StandardSettleInfo {

        /**
         * 草稿 ID
         */
        @NotBlank(message = "[draftId] is mandatory")
        private String draftId;

        /**
         * 结算信息
         */
        @NotNull(message = "[settleInfo] is mandatory")
        SettleInfoDTO settleInfo;

    }

    /**
     * 换汇标准化信息
     */
    @Data
    public static class StandardFxInfo{

        /**
         * 草稿 ID
         */
        @NotBlank(message = "[draftId] is mandatory")
        private String draftId;

        /**
         * 标准化换汇周期
         */
        private String standardFxCycle;

        /**
         * 目标基准
         */
        private String targetFxSource;

        /**
         * 合约 markUp
         */
        private String fxMarkup;

        /**
         * 换算后Markup
         */
        private String convertedFxMarkup;

    }

}
