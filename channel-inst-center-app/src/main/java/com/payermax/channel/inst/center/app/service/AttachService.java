package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.AttachEntity;

import java.util.List;

/**
 * 文件附件Service
 *
 * <AUTHOR>
 * @date 2022/5/15 20:50
 */
public interface AttachService {

    /**
     * 保存附件
     *
     * @param record
     * @return
     */
    int save(AttachEntity record);

    /**
     * 根据id查询附件信息
     *
     * @param idList
     * @return
     */
    List<AttachEntity> getByIdList(List<Long> idList);

}
