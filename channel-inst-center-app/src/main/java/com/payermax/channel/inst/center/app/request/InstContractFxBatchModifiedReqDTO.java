package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/17
 * @DESC
 */
@Data
public class InstContractFxBatchModifiedReqDTO {


    @ApiModelProperty(notes = "费用信息编号列表")
    @NotEmpty
    private List<String> feeItemNoList;

    @ApiModelProperty(notes = "外汇信息")
    private FxConfig fxConfig;

    private String instCode;

    private String entity;



    /**
     * 均为 String 类型，防止转换 null 值
     * {@link com.payermax.channel.inst.center.facade.request.contract.config.FxConfig}
     */
    @Data
    public static class FxConfig{

        /**
         * 合约外汇加点
         */
        private String contractFxSpread;

        /**
         * 外汇加点
         */
        private String fxSpread;

        /**
         * 换汇时机
         * <p>
         * {@link  com.payermax.channel.inst.center.facade.request.contract.config.content.CurrencyExchangeTiming}
         */
        private String currencyExchangeTime;
    }

}
