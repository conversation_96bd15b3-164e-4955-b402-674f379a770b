package com.payermax.channel.inst.center.app.manage.template.mode;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubAccountDetailByIdRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubFundsAccountRequestDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.FinFileExchangeClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> at 2022/10/9 16:08
 **/
@Service
@Slf4j
public class OfflineModeTemplate extends AbstractSubAccountModeTemplate {

    @Resource
    InstSubFundsAccountBucketManage instSubFundsAccountBucketManage;

    @Resource
    FinFileExchangeClient finFileExchangeClient;

    private Map<String, String> onlineFileApplyMap;

    @NacosValue(value = "${inst.sub.account.offline.apply.alert.message:}", autoRefreshed = true)
    private String subAccountOffLineApply;
    
    /**
     * 线上文件申请机构账号 Map<主账号ID，taskCode>
     */
    @NacosValue(value = "#{${inst.funds.account.online.file.apply.map:{'':''}}}", autoRefreshed = true)
    public void setOnlineFileApplyMap(Map<String, String> nacosOnlineFileApplyMap) {
        this.onlineFileApplyMap = nacosOnlineFileApplyMap;
    }

    @Override
    public void getSubAccountNo(RequestAccountDO requestAccountDO) {

        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();

        // 开通子级资金账户拓展参数
        instFundsAccountBucketManage.checkInstSubFundsAccountBucketId(requestAccountDO);

        // 用号段生成子级账号
        if (StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {

            // 用号段生成子级账号 或计算子级账号申请数量
            super.buildNumberSegmentAccountNoLock(requestAccountDO);

            // 检查子级账号是否生成
            if (StringUtils.isNotBlank(instFundsAccountEntity.getSubAccountRule()) && StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {
                throw new BusinessException(ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getCode(), ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getMsg());
            }
        }
        
        QuerySubAccountDetailByIdRequestDO requestDO = reqDoAssembler.toQuerySubAccountDetailByIdRequestDO(instSubFundsAccountEntity);
        // 是否线上文件申请
        if (onlineFileApplyMap.containsKey(instFundsAccountEntity.getAccountId())) {
            // 通过机构账号ID获取线上文件申请的TaskCode
            String taskCode = onlineFileApplyMap.get(instFundsAccountEntity.getAccountId());
            finFileExchangeClient.notifyFileExchange(taskCode, requestDO, instSubFundsAccountEntity, instSubFundsAccountEntity.getBusinessKey());
        }

    }

    @Override
    public InstSubFundsAccountEntity buildCreateSubAccountRecord(RequestAccountDO requestAccountDO) {
        return super.buildCreateSubAccountRecord(requestAccountDO);
    }

    @Override
    public void handleActivationOrOthersAction(RequestAccountDO requestAccountDO) {
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        
        // 更新状态 -》申请中
        StateRequest stateRequest = new StateRequest(instSubFundsAccountEntity.getStatus(), SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
        instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.APPLY.getStatus());
        stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);

        // 线上文件申请无需创建预申请记录以及钉钉通知
        if (onlineFileApplyMap.containsKey(instFundsAccountEntity.getAccountId())) {
            return;
        }

        // 检查是否支持预申请，支持则创建预申请账号
        // instSubFundsAccountBucketManage.buildAndCreatePreApplySubAccountBucket(instFundsAccountEntity, instSubFundsAccountEntity);

        try {
            QuerySubFundsAccountRequestDO accountRequestDO = reqDoAssembler.toQuerySubFundsAccountRequestDO(instFundsAccountEntity, instSubFundsAccountEntity);
            // 发送钉钉通知
            String title = "机构子级账号【线下申请】";
            dingAlertClient.sendMsgForGroupSubAccount(title, subAccountOffLineApply, accountRequestDO);
        } catch (Exception e) {
            log.info("OfflineModeTemplate-handleActivationOrOthersAction sendMsg Exception:{}", e);
        }
    }
}
