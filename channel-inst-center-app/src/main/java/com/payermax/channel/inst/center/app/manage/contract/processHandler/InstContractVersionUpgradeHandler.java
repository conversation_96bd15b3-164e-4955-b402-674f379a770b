package com.payermax.channel.inst.center.app.manage.contract.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractManagerAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.contract.InstContractVersionUpgradeDto;
import com.payermax.channel.inst.center.app.manage.contract.InstContractQueryService;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.model.contract.dataParser.FeeItemConvertUtils;
import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import com.payermax.channel.inst.center.app.request.contract.InstContractVersionUpgradeRequest;
import com.payermax.channel.inst.center.app.rocketmq.producer.InstContractRocketProducer;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractVersionInfoRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/13
 * @DESC 机构中心-合约升级处理器
 */
@Slf4j
@Setter
@Component
@RequiredArgsConstructor
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.INST_CENTER_CONTRACT_VERSION_MANAGER, actionType = OperateTypeEnum.UPDATE)
public class InstContractVersionUpgradeHandler extends AbstractWorkflowHandler {
    /**
     * 业务场景
     */
    private final BusinessTypeEnum BUSINESS_TYPE = BusinessTypeEnum.INST_CENTER;
    private final OperateTypeEnum OPERATE_TYPE = OperateTypeEnum.UPDATE;
    private final OperateModuleEnum MODULE_NAME = OperateModuleEnum.INST_CENTER_CONTRACT_VERSION_MANAGER;
    private final LogScenesTypeEnum LOG_SCENES_TYPE = LogScenesTypeEnum.CONTRACT_VERSION_MANAGE;


    /**
     * 合约升级最大条数限制
     */
    @NacosValue(value = "${inst.center.contractVersion.upgrade.contractFeeListLimit:30}", autoRefreshed = true)
    private Integer contractFeeListLimit;

    /**
     * 工作流配置
     */
    @NacosValue(value = "${omc.workflow.process.instContractVersion.upgrade.key:process_inst-center-inst-contract-version-upgrade}", autoRefreshed = true)
    private String processKey;
    @NacosValue(value = "${omc.workflow.process.instContractVersion.upgrade.desc:合约版本升级}", autoRefreshed = true)
    private String processDesc;
    @NacosValue(value = "${omc.workflow.process.instContractVersion.upgrade.formMsgTemplate:}", autoRefreshed = true)
    private String processFormMsgTemplate;
    @NacosValue(value = "${omc.workflow.process.approvalUrlHost:https://omc-dev-new.payermax.com}", autoRefreshed = true)
    private String approvalUrlHost;
    @NacosValue(value = "${omc.workflow.process.instContractVersion.upgrade.approvalUrlPath:/micro/omc-payment-channel/OMC_PAYMENT_CHANNEL_INSTER_CENTER#/instContractV2/instContractVersionUpgradeProcessForm?type=outer}", autoRefreshed = true)
    private String approvalUrlPath;

    private final IDGenerator idGenerator;
    private final InstContractBaseInfoRepository contractBaseInfoRepository;
    private final InstContractVersionInfoRepository contractVersionInfoRepository;
    private final InstContractQueryService instContractQueryService;

    private final InstContractManagerAssembler contractManagerAssembler;
    private final ContractVersionPOAssembler versionPOAssembler;

    private final VoucherAdapter voucherAdapter;
    private final TransactionTemplate transactionTemplate;
    private final InstContractRocketProducer instContractRocketProducer;



    /**
     * 合约升级流程发起
     *
     * @param request 请求参数
     * @return 初始化结果
     */
    public Boolean startUpgrade(InstContractVersionUpgradeRequest request,String operator) {

        // 校验数据并构建上下文
        InstContractVersionUpgradeDto upgradeInfo = validateAndBuildContext(request);

        // 费用列表合并
        contractFeeMerge(upgradeInfo);

        // 合约关联变更
        contractRelationChange(upgradeInfo);

        InstBusinessDraft draft = buildDraft(upgradeInfo, operator);

        return super.startProcess(draft);
    }

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        processConfig.setProcessDefKey(processKey);
        processConfig.setProcessDesc(processDesc);
        // 审批表单信息
        var upgradeInfo = JSON.parseObject(draft.getDraftData(), InstContractVersionUpgradeDto.class);
        var baseInfo = contractBaseInfoRepository.queryOneByNo(upgradeInfo.getCurrentContractVersion().getContractNo());
        var formMsg = new HashMap<String, Object>(4) {{
            put("instCode", baseInfo.getInstCode());
            put("contractEntity", baseInfo.getContractEntity());
            put("instProductType", baseInfo.getInstProductType());
            put("contractNo", baseInfo.getContractNo());
            put("effectiveStartTime", upgradeInfo.getComingContractVersion().getEffectStartTime());
            put("draftContext", JSON.toJSONString(upgradeInfo));
        }};
        processConfig.setFormInfoMap(formMsg);
        return processConfig;
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstContractVersionUpgradeHandler businessPassHandler");
        InstContractVersionUpgradeDto updateInfo = JSON.parseObject(draft.getDraftData(), InstContractVersionUpgradeDto.class);
        InstContractVersionInfo currentContractVersion = updateInfo.getCurrentContractVersion();
        InstContractVersionInfo comingContractVersion = updateInfo.getComingContractVersion();

        return transactionTemplate.execute(transactionStatus -> {
            // 保存新旧合约版本
            contractVersionInfoRepository.updateByPrimaryKey(versionPOAssembler.convertVersionInfoPO(currentContractVersion));
            log.info("{}", JSON.toJSONString(contractManagerAssembler.convertVersionInfoPO(comingContractVersion)));
            contractVersionInfoRepository.signNewContractVersion(contractManagerAssembler.convertVersionInfoPO(comingContractVersion));
            return Boolean.TRUE;
        });
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("InstContractVersionUpgradeHandler businessRejectHandler");
        return Boolean.TRUE;
    }

    @Override
    protected void callBackPostProcess(InstBusinessDraft draft) {
        log.info("InstContractVersionUpgradeHandler callBackPostProcess");
        // 发送 MQ 触发所有机器缓存刷新
        InstContractVersionUpgradeDto updateInfo = JSON.parseObject(draft.getDraftData(), InstContractVersionUpgradeDto.class);
        InstContractVersionInfo comingContractVersion = updateInfo.getComingContractVersion();
        instContractRocketProducer.sendContractVersionUpgradeNotify(contractManagerAssembler.version2Notify(comingContractVersion));
        super.callBackPostProcess(draft);
    }


    /**
     * 构造流程配置信息
     * @param draft 业务草稿
     * @return 流程配置信息
     */
    @Override
    protected ProcessRequest.ProcessStart processConfigBuilder(InstBusinessDraft draft) {
        // 填充默认信息
        log.info("approvalUrlHost: {}, approvalUrlPath: {}", approvalUrlHost, approvalUrlPath);
        ProcessRequest.ProcessStart processStartConfig = new ProcessRequest.ProcessStart();
        processStartConfig.setApplicantShareId(draft.getOwner());
        processStartConfig.setBusinessKey(draft.getDraftId());
        processStartConfig.setApprovalUrl(String.format("%s%s", approvalUrlHost, approvalUrlPath));
        // 不在飞书内打开
        processStartConfig.setPcOpen(true);
        // 填充自定义流程信息，包括 流程Key、流程描述、流程表单信息等
        fillCustomProcessConfig(draft,processStartConfig);
        return processStartConfig;
    }





    // -------------------------------------------------------------------------------------------

    /**
     * 校验并构建合约升级上下文
     */
    private InstContractVersionUpgradeDto validateAndBuildContext(InstContractVersionUpgradeRequest request) {
        // 获取现有合约版本
        InstContractVersionInfo currentVersion = instContractQueryService.queryLatestContract(request.getInstProductType(), request.getInstCode(), request.getContractEntity());
        InstContractVersionInfo comingVersion = contractManagerAssembler.versionInfoDeepCopy(currentVersion);

        AssertUtil.isTrue(Objects.nonNull(currentVersion), ErrorCodeEnum.INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR.getCode(), "获取不到生效的合约版本");
        AssertUtil.isTrue(currentVersion.getEffectStartTime().isBefore(request.getEffectiveStartTime()), ErrorCodeEnum.INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR.getCode(), "生效时间不能小于现有合约版本生效时间");

        int feeListCount = currentVersion.getOriginProducts().stream()
                .filter(product -> CollectionUtils.isNotEmpty(product.getContractFeeItems()))
                .mapToInt(product -> product.getContractFeeItems().size()).sum();
        AssertUtil.isTrue(feeListCount <= contractFeeListLimit, ErrorCodeEnum.INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR.getCode(), String.format("当前升级合约费用 条数 %s，超过限制 %s", feeListCount, contractFeeListLimit));

        return new InstContractVersionUpgradeDto()
                .setCurrentContractVersion(currentVersion)
                .setComingContractVersion(comingVersion)
                .setEffectiveStartTime(request.getEffectiveStartTime())
                .setFeeList(request.getFeeList());
    }

    /**
     * 合约费用合并
     */
    private void contractFeeMerge(InstContractVersionUpgradeDto upgradeInfo) {
        List<InstContractFeeItemRequestDTO> changeFeeList = upgradeInfo.getFeeList().stream()
                .filter( fee -> fee.getIsModify() || fee.getIsDelete())
                .collect(Collectors.toList());
        log.info("当前合约原始产品数量: {}, 标准产品数量: {}", upgradeInfo.getComingContractVersion().getOriginProducts().size(), upgradeInfo.getComingContractVersion().getStandardProducts().size());
        log.info(" 变更费用数量: {}", changeFeeList.size());

        // 构建原始产品 Map
        Map<String, InstContractOriginProduct> originProductMap = buildUniqueMap(upgradeInfo.getComingContractVersion().getOriginProducts()
                , contractManagerAssembler::originProductDeepCopy, InstContractOriginProduct::getInstOriginProductNo);

        // 费用合并
        changeFeeList.forEach(fee -> {
            AssertUtil.isTrue(originProductMap.containsKey(fee.getInstOriginProductNo()),
                    ErrorCodeEnum.INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR.getCode(), String.format("找不到费用对应的原始产品:%s", fee.getInstOriginProductNo()));
            InstContractOriginProduct originProduct = originProductMap.get(fee.getInstOriginProductNo());

            // 将费用变更合并到原始产品中
            List<InstContractFeeItem> feeItems = Optional.ofNullable(originProduct.getContractFeeItems()).orElseGet(ArrayList::new);
            Map<String, InstContractFeeItem> feeMap = buildUniqueMap(feeItems, contractManagerAssembler::feeItemDeepCopy, InstContractFeeItem::getInstContractFeeItemNo);

            // 根据标志位决定费用处理方式
            if (fee.getIsDelete()) {
                log.info("【合约版本变更】删除费用: {}", fee.getInstContractFeeItemNo());
                feeMap.remove(fee.getInstContractFeeItemNo());
            } else if (fee.getIsModify()) {
                log.info("【合约版本变更】修改费用: {}", fee.getInstContractFeeItemNo());
                feeMap.put(fee.getInstContractFeeItemNo(), versionPOAssembler.convertFeeItemDomain(fee));
            }
            log.info("费用条数: 当前版本: {}, 新版本: {}", feeItems.size(), feeMap.size());
            originProduct.setContractFeeItems(new ArrayList<>(feeMap.values()));
            // 覆盖 Map 中的原始产品
            originProductMap.put(originProduct.getInstOriginProductNo(), originProduct);
        });

        // 将原始产品合并到新合约中
        upgradeInfo.getComingContractVersion().setOriginProducts(new ArrayList<>(originProductMap.values()));
    }


    /**
     * 合约关联变更
     */
    private void contractRelationChange(InstContractVersionUpgradeDto upgradeInfo) {
        log.info("{}",upgradeInfo.getEffectiveStartTime());
        InstContractVersionInfo currentContractVersion = upgradeInfo.getCurrentContractVersion();
        InstContractVersionInfo comingContractVersion = upgradeInfo.getComingContractVersion();
        currentContractVersion.setEffectEndTime(upgradeInfo.getEffectiveStartTime());
        comingContractVersion.setEffectStartTime(upgradeInfo.getEffectiveStartTime());
        // 合约版本号变更
        contractVersionChange(upgradeInfo);
        // 原始产品变更
        originProductChange(upgradeInfo);

        upgradeInfo.setCurrentContractVersion(currentContractVersion);
        upgradeInfo.setComingContractVersion(comingContractVersion);

    }
    /**
     * 合约版本号变更
     */
    private void contractVersionChange(InstContractVersionUpgradeDto upgradeInfo) {
        InstContractVersionInfo contractVersion = upgradeInfo.getComingContractVersion();

        String newVersionNo = voucherAdapter.getContractVersionNo();
        // 版本信息
        contractVersion.setContractVersion(newVersionNo);
        // 原始产品
        contractVersion.getOriginProducts().forEach(originProduct -> {
            originProduct.setContractVersion(newVersionNo);
        });
        // 标准产品
        contractVersion.getStandardProducts().forEach(standardProduct -> {
            standardProduct.setContractVersion(newVersionNo);
        });
    }

    /**
     * 原始产品变更
     */
    private void originProductChange(InstContractVersionUpgradeDto upgradeInfo) {
        InstContractVersionInfo contractVersion = upgradeInfo.getComingContractVersion();
        List<InstContractOriginProduct> originProducts = contractVersion.getOriginProducts();
        List<InstContractStandardProduct> standardProducts = contractVersion.getStandardProducts();
        // 标准产品分组，用于查找及更新
        Map<String, List<InstContractStandardProduct>> standardProductMap = standardProducts.stream().collect(Collectors.groupingBy(InstContractStandardProduct::getInstOriginProductNo));

        // 原始产品处理
        originProductProcess(originProducts, standardProductMap);

        // 标准产品处理
        standardProductProcess(upgradeInfo, standardProductMap);

    }

    /**
     * 标准产品处理
     */
    private void standardProductProcess(InstContractVersionUpgradeDto upgradeInfo, Map<String, List<InstContractStandardProduct>> standardProductMap) {
        List<InstContractStandardProduct> standardProducts = standardProductMap.values().stream()
                .flatMap(list -> {
                    // 设置新标准产品码
                    updateDynamicStringField(list, InstContractStandardProduct::setInstStandardProductNo, voucherAdapter::getStandardProductCode);
                    return list.stream();
                })
                // 设置新增标志位
                .peek(item -> item.setNewlyCreated(Boolean.TRUE))
                .collect(Collectors.toList());
        upgradeInfo.getComingContractVersion().setStandardProducts(standardProducts);
    }

    /**
     * 原始产品处理
     */
    private void originProductProcess(List<InstContractOriginProduct> originProducts, Map<String, List<InstContractStandardProduct>> standardProductMap) {
        originProducts.stream()
                .filter(Objects::nonNull)
                .forEach(originProduct -> {
                    String oldProductNo = originProduct.getInstOriginProductNo();
                    String newProductNo = voucherAdapter.getOriginProductCode();
                    List<InstContractFeeItem> feeItems = originProduct.getContractFeeItems();
                    List<InstContractSettlementItem> settlementItems = originProduct.getSettlementItems();
                    // 设置新编码及标志位
                    originProduct.setInstOriginProductNo(newProductNo);
                    originProduct.setNewlyCreated(Boolean.TRUE);

                    // 标准产品列表存在时，修改原始产品编码
                    updateStringField(standardProductMap.get(oldProductNo), InstContractStandardProduct::setInstOriginProductNo, newProductNo);

                    // 费用列表存在时，修改原始产品编码及费用信息编码
                    updateStringField(feeItems, InstContractFeeItem::setInstOriginProductNo, newProductNo);

                    // 结算列表存在时，修改原始产品编码及结算信息编码
                    updateStringField(settlementItems, InstContractSettlementItem::setInstOriginProductNo, newProductNo);

                    // 更新 费用 及 结算信息编码
                    updateDynamicStringField(feeItems, InstContractFeeItem::setInstContractFeeItemNo, voucherAdapter::getInstProductFeeItemNo);
                    updateDynamicStringField(settlementItems, InstContractSettlementItem::setInstContractSettlementItemNo, voucherAdapter::getInstProductSettlementItemNo);

                    // feeItem 业务唯一性填充及校验
                    FeeItemConvertUtils.feeListUniqueFillAndCheck(feeItems);
                });
    }

    /**
     * 构造草稿
     */
    private InstBusinessDraft buildDraft(InstContractVersionUpgradeDto upgradeInfo, String shareId){
        // 构造草稿
        return InstBusinessDraftAssembler.INSTANCE.buildDefaultDraft(
                idGenerator.generateIdAccordingToSystem(BUSINESS_TYPE, LOG_SCENES_TYPE), MODULE_NAME,OPERATE_TYPE,
                String.valueOf(upgradeInfo.getCurrentContractVersion().getContractNo()),
                JSON.toJSONString(upgradeInfo, SerializerFeature.DisableCircularReferenceDetect), shareId
        );
    }


    /**
     * 通用构建映射表方法, 构建时使用拷贝函数
     * @param list         需要转换的列表
     * @param copyFunction 拷贝函数（如contractManagerAssembler::originProductDeepCopy）
     * @param keyExtractor 键提取函数 如InstContractStandardProduct::getXXX）
     * @param <T>          列表元素类型
     * @return 构建的映射表
     */
    private <T> Map<String, T> buildUniqueMap(List<T> list,Function<T, T> copyFunction, Function<T, String> keyExtractor) {
        return list.stream()
                .collect(Collectors.toMap(
                        keyExtractor,
                        copyFunction,
                        (existing, replacement) -> {
                            throw new BizException(ErrorCodeEnum.INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR,
                                    String.format("发现重复编码: %s", keyExtractor.apply(existing)));
                        }));
    }

    /**
     * 通用字段更新方法（带类型校验）
     * @param list 目标列表（自动过滤null）
     * @param setter 方法引用（如InstContractStandardProduct::setXXX）
     * @param value 要设置的值
     */
    private <T> void updateStringField(List<T> list, BiConsumer<T, String> setter, String value) {
        Optional.ofNullable(list)
                .ifPresent(items -> items.forEach(item -> setter.accept(item, value)));
    }

    /**
     * 通用字段更新方法（带类型校验）
     * @param list 目标列表（自动过滤null）
     * @param setter 方法引用（如InstContractStandardProduct::setXXX）
     * @param valueSupplier 动态值生成器（支持Lambda）
     */
    private <T> void updateDynamicStringField(List<T> list, BiConsumer<T, String> setter, Supplier<String> valueSupplier) {
        Optional.ofNullable(list)
                .ifPresent(items -> items.forEach(item -> setter.accept(item, valueSupplier.get())));
    }

}
