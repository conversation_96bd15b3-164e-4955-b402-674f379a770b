package com.payermax.channel.inst.center.app.manage.workflow;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOperateLogPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOperateLogRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.response.ProcessResponse;
import com.payermax.operating.omc.portal.workflow.facade.dubbo.ProcessDubboServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC 业务审批流程发起与回调处理
 */
@Slf4j
@Component
public abstract class AbstractWorkflowHandler {

    @DubboReference(version = "1.0", timeout = 10000)
    private ProcessDubboServiceI processDubboServiceI;
    @Resource
    private InstBusinessDraftRepository businessDraftRepository;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private OperateLogFactory operateLogFactory;
    @Resource
    private InstContractOperateLogRepository operateLogRepository;


    // ---------------------------------------------------- 流程发起 -------------------------------------------------------------


    /**
     * 填充自定义流程配置
     * @param draft draft 业务草稿
     * @param processConfig 流程配置信息
     * @return 流程发起配置
     */
    protected abstract ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig);

    /**
     * 业务草稿保存
     */
    protected InstBusinessDraft businessDraftSave(InstBusinessDraft draft){
        try{
            businessDraftRepository.save(InstBusinessDraftAssembler.INSTANCE.domain2Po(draft));
        }catch (Exception e){
            log.error("保存修改信息失败",e);
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "draft save fail");
        }
        return draft;
    }

    /**
     * 记录操作日志
     */
    protected Boolean recordOperateLog(InstBusinessDraft draft){
        log.info("record operate log");
        // 构造日志信息
        String operateContent = String.format("用户 %s 操作，模块: %s，操作类型: %s，业务主键：%s，草稿ID：%s，操作状态：%s",
                draft.getOwner(), draft.getModuleName(), draft.getOperateType(), draft.getBusinessKey(), draft.getDraftId(), draft.getStatus());
        Map<String,Object> logInfoMap = new HashMap<String, Object>(4){{
            put("draftData",draft.getDraftData());
        }};
        // 构造日志实体
        InstContractOperateLogPO logPo = operateLogFactory.composeDefaultOperateLog(draft.getBusinessType(), draft.getModuleName(), draft.getBusinessKey());
        logPo.setLogInfo(JSON.toJSONString(logInfoMap))
                .setOperator(draft.getOwner())
                .setOperateContent(operateContent)
                .setOperateRes(LogOperateResTypeEnum.SUCCESS)
                .setOperateType(draft.getOperateType());
        operateLogRepository.save(logPo);
        return Boolean.TRUE;
    }

    /**
     * 后置操作
     */
    protected void postProcess(InstBusinessDraft draft){
        log.info("业务流程发起后置操作，草稿ID:{}", draft.getDraftId());
    }

    /**
     * 业务流程审批发起
     * @param draft 业务草稿
     * @return Boolean 发起结果
     */
    protected Boolean workflowProcessStart(InstBusinessDraft draft){
        // 构造流程配置信息
        log.info("构造流程配置信息");
        ProcessRequest.ProcessStart processStartConfig = processConfigBuilder(draft);
        // 发起流程
        log.info("发起流程，业务 ID: {}, 草稿 ID: {}", draft.getBusinessKey(), draft.getDraftId());
        Result<ProcessResponse.ProcessStart> processStartResult = processDubboServiceI.start(processStartConfig);
        AssertUtil.isTrue(processStartResult.isSuccess(), ErrorCodeEnum.WORKFLOW_START_ERROR.getCode(),ErrorCodeEnum.WORKFLOW_START_ERROR.getMsg());
        log.info("发起流程成功：{}", processStartResult.getData().getProcessId());
        draft.setRelatedProcessId(processStartResult.getData().getProcessId());
        return Boolean.TRUE;
    }

    /**
     * 构造流程配置信息
     * @param draft 业务草稿
     * @return 流程配置信息
     */
    protected ProcessRequest.ProcessStart processConfigBuilder(InstBusinessDraft draft) {
        // 填充默认信息
        ProcessRequest.ProcessStart processStartConfig = new ProcessRequest.ProcessStart();
        processStartConfig.setApplicantShareId(draft.getOwner());
        processStartConfig.setBusinessKey(draft.getDraftId());
        // 填充自定义流程信息，包括 流程Key、流程描述、流程表单信息等
        fillCustomProcessConfig(draft,processStartConfig);
        return processStartConfig;
    }


    /**
     * 启动流程
     */
    public Boolean startProcess(InstBusinessDraft draft) {
        return transactionTemplate.execute(status -> {
            // 前置操作
            preProcess(draft);
            // 保存草稿
            businessDraftSave(draft);
            // 发起流程
            workflowProcessStart(draft);
            // 记录操作日志
            recordOperateLog(draft);
            // 后置操作
            postProcess(draft);
            // 返回
            return Boolean.TRUE;
        });
    }

    /**
     * 前置操作
     */
    protected void preProcess(InstBusinessDraft draft) {
        log.info("业务流程发起前置操作，草稿ID:{}", draft.getDraftId());
        // 校验流程唯一性
        processUniqueCheck(draft);
    }

    /**
     * 流程唯一性校验
     * @param draft 业务草稿
     */
    protected void processUniqueCheck(InstBusinessDraft draft){
        log.info("校验流程唯一性，业务主键:{}, 业务线:{}, 业务模块:{}, 操作类型:{}", draft.getBusinessKey(), draft.getModuleName(), draft.getOperateType(), draft.getOperateType());
        Boolean hasProcessing = businessDraftRepository.hasProcessing(InstBusinessDraftAssembler.INSTANCE.domain2Po(draft));
        String msg = String.format("存在正在处理中的流程，请联系 %s ", draft.getOwner());
        AssertUtil.isTrue(!hasProcessing, ErrorCodeEnum.WORKFLOW_START_ERROR.getCode(), msg);
    }


    // ---------------------------------------------------- 流程回调 -------------------------------------------------------------

    /**
     * 业务流程通过处理
     *
     * @param draft     业务草稿
     * @param eventInfo
     * @return Boolean 处理结果
     */
    protected abstract Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo);

    /**
     * 业务流程拒绝处理
     *
     * @param draft     业务草稿
     * @param eventInfo
     * @return Boolean 处理结果
     */
    protected abstract Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo);

    /**
     * 记录回调操作日志
     */
    protected Boolean recordCallbackOperateLog(InstBusinessDraft draft) {
        return this.recordOperateLog(draft);
    }


    /**
     * 回调后置操作
     */
    protected void callBackPostProcess(InstBusinessDraft draft) {
        log.info("业务流程发起后置操作，草稿ID:{}", draft.getDraftId());
        // 记录日志
        recordCallbackOperateLog(draft);
    }

    /**
     * 更新业务草稿状态
     */
    protected void updateDraftStatus(InstBusinessDraft draft, InstProcessStatusEnum status, WfProcessEventInfo eventInfo){
        log.info("更新业务草稿状态：{}", draft.getBusinessKey());
        AssertUtil.isTrue(InstProcessStatusEnum.PROCESSING.equals(draft.getStatus()), ErrorCodeEnum.INST_CENTER_DRAFT_STATUS_ERROR.getCode(), "业务草稿状态异常");
        draft.setRelatedProcessId(eventInfo.getProcessInfo().getProcessId());
        draft.setStatus(status);
        businessDraftRepository.updateDraftStatus(draft.getDraftId(), status.name());
    }


    /**
     * 回调处理
     * @param draft 业务草稿
     * @return Boolean 处理结果
     */
    public Boolean callbackHandler(InstBusinessDraft draft, InstProcessStatusEnum processStatus, WfProcessEventInfo eventInfo){
        log.warn("流程回调处理：{}", draft.getBusinessKey());
        return transactionTemplate.execute( status -> {
            // 执行业务逻辑，处理草稿状态，暂时只考虑通过，其他状态都为拒绝
            if(InstProcessStatusEnum.PASS.equals(processStatus)){
                processPassCallbackHandler(draft, eventInfo);
            }else {
                processRejectCallbackHandler(draft, eventInfo);
            }
            // 更新草稿
            updateDraftStatus(draft, processStatus, eventInfo);

            // 回调后置操作，失败不影响流程
            try {
                callBackPostProcess(draft);
            }catch (Exception e){
                log.error("回调后置操作失败：{}", draft.getBusinessKey(), e);
            }

            return Boolean.TRUE;
        });
    }

    /**
     * 重试处理
     * @param draft 业务草稿
     * @return Boolean 处理结果
     */
    public Boolean retryHandler(InstBusinessDraft draft){
        log.error("默认重试实现，无逻辑");
        return Boolean.TRUE;
    }

    protected void retryPreCheck(InstBusinessDraft draft){
        AssertUtil.isTrue(Objects.nonNull(draft), ErrorCodeEnum.INST_CENTER_PROCESS_RETRY_ERROR.getCode(), String.format("不存在该流程: %s", draft.getDraftId()));
        AssertUtil.isTrue(!draft.isFinalStatus(), ErrorCodeEnum.INST_CENTER_PROCESS_RETRY_ERROR.getCode(), String.format("流程已到达终态: %s, %s",  draft.getDraftId(), draft.getStatus()));
    }



}
