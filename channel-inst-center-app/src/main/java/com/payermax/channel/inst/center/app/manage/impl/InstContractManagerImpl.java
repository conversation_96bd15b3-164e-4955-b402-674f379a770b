package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity;
import com.payermax.channel.inst.center.app.manage.InstBaseInfoManager;
import com.payermax.channel.inst.center.app.manage.InstContractManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstBaseInfoReqDTO;
import com.payermax.channel.inst.center.app.request.InstContractReqDTO;
import com.payermax.channel.inst.center.app.response.InstBaseInfoVO;
import com.payermax.channel.inst.center.app.response.InstContractVO;
import com.payermax.channel.inst.center.app.service.InstApplyOrderService;
import com.payermax.channel.inst.center.app.service.InstContractService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName InstContractManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 0:19
 */
@Service
@Slf4j
public class InstContractManagerImpl implements InstContractManager {

    @Autowired
    private InstContractService instContractService;

    @Autowired
    private InstApplyOrderService instApplyOrderService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private InstBaseInfoManager instBaseInfoManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int save(InstContractReqDTO contractReqDTO) {
        InstContractEntity instContractEntity = reqDtoAssembler.toInstContractEntity(contractReqDTO);
        int result = instContractService.save(instContractEntity);
        // 更新阶段状态
        if(StringUtils.isNotBlank(contractReqDTO.getStatus())){
            instApplyOrderService.updateStageStatus(contractReqDTO.getApplyNo(), ApplyStageEnum.CONTRACT, contractReqDTO.getStatus());
        }
        return result;
    }

    @Override
    public InstContractVO query(InstContractReqDTO contractReqDTO) {
        InstContractEntity instContractEntity = reqDtoAssembler.toInstContractEntity(contractReqDTO);
        InstContractEntity contractEntity = instContractService.getByEntity(instContractEntity);
        InstContractVO instContractVO = respVoAssembler.toInstContractVo(contractEntity);
        return instContractVO;
    }

    @Override
    public List<InstContractVO> list(InstContractReqDTO contractReqDTO) {
        List<Long> instIds = contractReqDTO.getInstIds();
        List<InstContractEntity> contractEntityList = null;
        if(CollectionUtils.isEmpty(instIds)){
            InstContractEntity instContractEntity = reqDtoAssembler.toInstContractEntity(contractReqDTO);
            contractEntityList = instContractService.getByEntityList(instContractEntity);
        }else{
            contractEntityList = instContractService.getByInstIds(instIds);
        }
        List<InstContractVO> instContractVOs = respVoAssembler.toInstContractVos(contractEntityList);
        if(CollectionUtils.isNotEmpty(instContractVOs)){
            instContractVOs = instContractVOs.stream().map(obj -> {
                obj.setFundsSettleInst(getFundsSettleInst(obj));
                return obj;
            }).collect(Collectors.toList());
        }
        return instContractVOs;
    }

    /**
     * 获取资金处理机构
     * @return
     */
    private String getFundsSettleInst(InstContractVO instContractVO) {
        Long instId = instContractVO.getInstId();
        try {
            if(StringUtils.isNotBlank(instContractVO.getFundsSettleInst())){
                instId = Long.valueOf(instContractVO.getFundsSettleInst());
            }
            InstBaseInfoReqDTO instBaseInfoReqDTO = new InstBaseInfoReqDTO();
            instBaseInfoReqDTO.setInstId(instId);
            List<InstBaseInfoVO> instBaseInfoVOList = instBaseInfoManager.query(instBaseInfoReqDTO);
            if(CollectionUtils.isNotEmpty(instBaseInfoVOList)){
                String instCode = instBaseInfoVOList.get(0).getInstCode(); //NO_CHECK 方法未被调用
                String registerName = instBaseInfoVOList.get(0).getRegisterName(); //NO_CHECK 方法未被调用
                if(StringUtils.isNotBlank(instCode)){
                    return StringUtils.defaultIfBlank(registerName,StringUtils.EMPTY).concat("(").concat(instCode).concat(")");
                }
                return registerName;
            }
        }catch (Exception e){
            log.warn("InstContractManagerImpl#getFundsSettleInst error:",e);
        }
        return null;
    }
}
