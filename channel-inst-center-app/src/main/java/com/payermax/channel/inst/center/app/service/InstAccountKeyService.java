package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstAccountKeyQueryEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 21:39
 */
public interface InstAccountKeyService {
    /**
     * 批量保存商户密钥
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstAccountKeyEntity> records);

    /**
     * 更新商户密钥
     * @param record
     * @return
     */
    int update(InstAccountKeyEntity record);

    /**
     * 查询商户密钥
     *
     * @param queryEntity
     * @return
     */
    List<InstAccountKeyQueryEntity> queryList(InstAccountKeyQueryEntity queryEntity);

    /**
     * 删除商户密钥
     * @param id
     * @return
     */
    int delete(Long id);
}
