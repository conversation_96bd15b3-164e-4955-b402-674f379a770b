package com.payermax.channel.inst.center.app.manage.template;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.payermax.channel.inst.center.app.manage.InstFundsAccountBucketManage;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountManage;
import com.payermax.channel.inst.center.app.rocketmq.producer.InstCenterRocketProducer;
import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.CreateSubAccountRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.FileAccountStatusChangeMqInfo;
import com.payermax.channel.inst.center.domain.subaccount.response.ChannelApplyApiResponse;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.ChannelInternalClientProxy;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.ushareit.fintech.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/8 17:44
 **/
@Component
@Slf4j
public class ApiTemplateUtil {

    public static final String APPLY_STATE = "APPLY_STATE";

    public static final String ACTIVATION_STATE = "ACTIVATION_STATE";

    @Autowired
    public StateMachineExecutor stateMachineExecutor;
    @Autowired
    public InstCenterRocketProducer instCenterRocketProducer;
    @Autowired
    public ChannelInternalClientProxy channelInternalClientProxy;
    @Autowired
    public InstFundsAccountBucketManage instFundsAccountBucketManage;
    @Autowired
    public InstSubFundsAccountManage instSubFundsAccountManage;

    /**
     * 构建渠道网关API请求参数前检查必要API参数
     *
     * @param requestAccountDO
     * @param apiName
     */
    public Boolean checkRequestParams(RequestAccountDO requestAccountDO, String apiName) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 获取机构账号
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        // 获取机构账号拓展配置
        InstFundsAccountEntity.AccountJsonBo accountJsonBo = instFundsAccountEntity.getAccountJsonBo();
        // 检查必要对象是否为空
        if (Objects.isNull(instSubFundsAccountEntity) || Objects.isNull(instFundsAccountEntity) || Objects.isNull(accountJsonBo)) {
            return false;
        }
        // 检查是否存在相应配置
        HashMap<String, String> apiConfigMap =  accountJsonBo.getApiConfig();
        if (CollUtil.isEmpty(apiConfigMap) || !apiConfigMap.containsKey(apiName)) {
            log.warn("ApiTemplateUtil-checkRequestParams apiConfigMap is empty or apiConfigMap not exist {}", apiName);
            return false;
        }
        
        // 开通子级资金账户拓展参数
        instFundsAccountBucketManage.checkInstSubFundsAccountBucketId(requestAccountDO);
        return true;
    }

    /**
     * 发送渠道网关API请求
     *
     * @param requestAccountDO
     * @param apiName
     */
    public SubAccountStatusEnum sendRequest(RequestAccountDO requestAccountDO, String apiName, JSONObject channelRequest, String stateMachine) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 获取机构账号
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        // 获取机构账号拓展配置
        InstFundsAccountEntity.AccountJsonBo accountJsonBo = instFundsAccountEntity.getAccountJsonBo();
        // 检查是否存在相应配置
        HashMap<String, String> apiConfigMap =  accountJsonBo.getApiConfig();
        // 获取接口配置version版本
        String version = apiConfigMap.get(apiName);
        // 调用新渠道网关API接口
        Result result = null;
        try {
            result = channelInternalClientProxy.sendRequest(JSON.toJSONString(channelRequest), apiName, version, accountJsonBo.getInstBrand(), accountJsonBo.getInstMerchantCode(), ChannelApplyApiResponse.class);
        } catch (Exception e) {
            result = new Result();
            result.setMsg(e.getMessage());
            throw e;
        } finally {
            // 记录响应信息
            channelRequest.put("result", result);
        }
        // 处理响应结果
        if (Objects.isNull(result.getData())) {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        if (result.isSuccess()) {
            ChannelApplyApiResponse response = (ChannelApplyApiResponse) result.getData();
            // 更新子级账号
            Integer originalStatus = instSubFundsAccountEntity.getStatus();
            if (Objects.nonNull(response)) {
                FileAccountStatusChangeMqInfo.InstSubFundsAccountChange change = response.getSubAccountChange();
                // 检查渠道返回的子级账号是否重复
                boolean checkResult = instSubFundsAccountManage.checkSubAccountNoDuplication(change.getSubAccountNo(), instSubFundsAccountEntity.getSubAccountId(), instSubFundsAccountEntity.getAccountId());
                if (!checkResult) {
                    throw new BusinessException(ErrorCodeEnum.SUB_ACCOUNT_NO_DUPLICATE.getCode(), ErrorCodeEnum.SUB_ACCOUNT_NO_DUPLICATE.getMsg());
                }
                // 更新子账号信息
                instSubFundsAccountEntity.setSubAccountNo(change.getSubAccountNo());
                instSubFundsAccountEntity.setSubAccountName(change.getSubAccountName());
                instSubFundsAccountEntity.setBSubAccountNo(change.getBSubAccountNo());
                instSubFundsAccountEntity.setStatus(change.getStatus());
                InstSubFundsAccountEntity.AccountJsonBo subAccountJsonBo = instSubFundsAccountEntity.getAccountJsonBo();
                subAccountJsonBo.setChannelUniqueNumber(StringUtils.isNotEmpty(change.getChannelUniqueNumber()) ? change.getChannelUniqueNumber() : subAccountJsonBo.getChannelUniqueNumber());
                subAccountJsonBo.setRejCode(StringUtils.isNotEmpty(change.getRejCode()) ? change.getRejCode() : subAccountJsonBo.getRejCode());
                subAccountJsonBo.setRejMessage(StringUtils.isNotEmpty(change.getRejMessage()) ? change.getRejMessage() : subAccountJsonBo.getRejMessage());
            }
            if (StringUtils.equals(stateMachine, APPLY_STATE)) {
                // 更新结果状态
                StateRequest stateRequest = new StateRequest(originalStatus, SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
                stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
            } else if (StringUtils.equals(stateMachine, ACTIVATION_STATE)) {
                // 更新结果状态
                StateRequest stateRequest = new StateRequest(originalStatus, ActivationModeEnum.getByType(instFundsAccountEntity.getActivationMode()), instSubFundsAccountEntity);
                stateMachineExecutor.changeActivationSubAccountState(stateRequest);
            }
            instCenterRocketProducer.sendSubAccountStatusActivationNotify(instFundsAccountEntity, instSubFundsAccountEntity);
        } else {
            throw new BusinessException(result.getCode(), result.getMsg());
        }
        return SubAccountStatusEnum.getSubAccountStatusByStatus(instSubFundsAccountEntity.getStatus());
    }

    /**
     * 构建调用渠道网关API查询接口请求参数
     *
     * @param instFundsAccountEntity
     * @param instSubFundsAccountEntity
     */
    public JSONObject buildChannelRequest(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity, List<InstFundsAccountBucketEntity> bucketEntityList) {
        JSONObject jsonObject = new JSONObject();
        // 机构账户参数
        jsonObject.put("accountInfo",instFundsAccountEntity);
        // 机构子级账户参数
        jsonObject.put("subAccountInfo",instSubFundsAccountEntity);
        // 机构商户信息参数
        String requestBody = instSubFundsAccountEntity.getRequestBody();
        CreateSubAccountRequestDO createSubAccountRequest = JsonUtils.toBean(CreateSubAccountRequestDO.class, requestBody);
        jsonObject.put("merchantInfo",createSubAccountRequest.getMerchantInfo());
        // requestBody会影响日志参数格式化，无需向下游专递
        instSubFundsAccountEntity.setRequestBody(null);
        // 机构主账户拓展参数
        Map<String, String> bucketMap;
        if (CollUtil.isNotEmpty(bucketEntityList)) {
            bucketMap = bucketEntityList.stream().collect(Collectors.toMap(InstFundsAccountBucketEntity::getKeyName, InstFundsAccountBucketEntity::getKeyValue));
            jsonObject.put("buckets", bucketMap);
        }
        return jsonObject;
    }
}
