package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstAccountQueryReqDTO;
import com.payermax.channel.inst.center.app.request.InstAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstAccountQueryVO;
import com.payermax.channel.inst.center.app.response.InstAccountVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/7/8 11:06
 */
public interface InstAccountManager {

    Integer saveAccount(InstAccountQueryReqDTO instAccountQueryReqDTO);

    InstAccountQueryVO queryAccount(InstAccountQueryReqDTO instAccountQueryReqDTO);

    /**
     * 查询商户MID
     *
     * @return
     */
    List<InstAccountVO> queryMids(InstAccountReqDTO instAccountReqDTO);
}
