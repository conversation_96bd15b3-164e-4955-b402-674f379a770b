package com.payermax.channel.inst.center.app.valid.excel.impl;

import com.payermax.channel.inst.center.app.valid.excel.ExcelValid;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import org.springframework.stereotype.Component;

/**
 * CommonExcelValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class CommonExcelValid<T> implements ExcelValid<ExcelUtil.BaseExcelRow, T> {

    @Override
    public Result<ExcelUtil.BaseExcelRow> valid(ExcelUtil.ExcelParseInfo<ExcelUtil.BaseExcelRow> excelParseInfo, T formData) {
        return valid(excelParseInfo);
    }
}
