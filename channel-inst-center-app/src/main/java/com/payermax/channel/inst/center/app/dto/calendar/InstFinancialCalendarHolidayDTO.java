package com.payermax.channel.inst.center.app.dto.calendar;

import com.payermax.channel.inst.center.domain.enums.calendar.HolidayOperateEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.Month;

/**
 * <AUTHOR>
 * @date 2024/8/29
 * @DESC
 */
@Data
@NoArgsConstructor
public class InstFinancialCalendarHolidayDTO {


    /**
     * 日历ID
     */
    private String calendarId;

    /**
     * 节假日日期
     */
    private LocalDate holidayDate;

    /**
     * 节假日月份
     */
    private Month holidayMonth;

    /**
     * 节假日操作类型
     */
    private HolidayOperateEnum holidayOperate;

    /**
     * 是否工作日， 1 表示工作日， 0 表示非工作日
     */
    private Boolean isWorkday;

    /**
     * 描述
     */
    private String description;
}
