package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstFundsAccountManage;
import com.payermax.channel.inst.center.app.manage.template.CreateSubAccount;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.StatusEnumYN;
import com.payermax.channel.inst.center.common.enums.instcenter.IsSupportYNEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.*;
import com.payermax.channel.inst.center.domain.subaccount.response.CreateSubAccountResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryAccountDetailByIdResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryAccountDetailResponseDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryAccountsResponseDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.ushareit.fintech.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName InstFundsAccountManageImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/10/8 16:39
 * @Version 1.0
 */
@Service
@Slf4j
public class InstFundsAccountManageImpl implements InstFundsAccountManage {

    @Autowired
    ReqDoAssembler reqDoAssembler;
    @Autowired
    ResDoAssembler resDoAssembler;
    @Autowired
    CreateSubAccount createSubAccount;
    @Autowired
    InstFundsAccountService instFundsAccountService;
    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;

    @Override
    public List<QueryAccountsResponseDO> queryAccount(QueryAccountsRequestDO accountQueryDO) {
        // 查询可用机构账号
        InstFundsAccountQueryEntity accountQueryEntity = reqDoAssembler.toInstFundsAccountQueryEntity(accountQueryDO);
        accountQueryEntity.setStatus(StatusEnumYN.Y.getType());
        List<InstFundsAccountEntity> list = instFundsAccountService.queryListByQueryEntity(accountQueryEntity);
        if (Objects.equals(accountQueryEntity.getIsSupportSubAccount(), IsSupportYNEnum.Y.getType())) {
             // 预筛选
             InstSubFundsAccountRequestDO subFundsAccountRequestDO = reqDoAssembler.toInstSubFundsAccountRequestDO(accountQueryDO);
             list = createSubAccount.filterInstAccount(list, subFundsAccountRequestDO);
        }

        List<QueryAccountsResponseDO> queryAccountsResponseDOList = resDoAssembler.toQueryAccountsResponseDOList(list);

        return queryAccountsResponseDOList;
    }

    @Override
    public CreateSubAccountResponseDO createSubAccount(CreateSubAccountRequestDO createSubAccountRequestDO) {

        InstFundsAccountQueryEntity accountQueryEntity = reqDoAssembler.toInstFundsAccountQueryEntity(createSubAccountRequestDO);
        InstSubFundsAccountRequestDO subFundsAccountDO = reqDoAssembler.toInstSubFundsAccountRequestDO(createSubAccountRequestDO);
        subFundsAccountDO.setRequestBody(JsonUtils.toJson(createSubAccountRequestDO));
        // 查询可用机构账号
        accountQueryEntity.setStatus(StatusEnumYN.Y.getType());
        // 创建子级账号机构必须支持
        accountQueryEntity.setIsSupportSubAccount(IsSupportYNEnum.Y.getType());
        List<InstFundsAccountEntity> list = instFundsAccountService.queryListByQueryEntity(accountQueryEntity);
        // 预筛选
        list = createSubAccount.filterInstAccount(list, subFundsAccountDO);
        if (CollectionUtil.isEmpty(list)) {
            throw new BusinessException(ErrorCodeEnum.NO_INST_ACCOUNT_SUPPORT.getCode(), ErrorCodeEnum.NO_INST_ACCOUNT_SUPPORT.getMsg());
        }

        RequestAccountDO requestAccountDO = new RequestAccountDO();
        requestAccountDO.setInstFundsAccountEntityList(list);
        requestAccountDO.setInstSubFundsAccountRequestDO(subFundsAccountDO);
        ResponseAccountDO responseAccountDO = createSubAccount.createSubAccount(requestAccountDO);

        // 响应转换
        CreateSubAccountResponseDO createSubAccountResponseDO = resDoAssembler.toCreateSubAccountResponseDO(responseAccountDO);

        // 构建响应数据
        return createSubAccountResponseDO;
    }

    @Override
    public QueryAccountDetailResponseDO queryAccountDetail(QueryAccountDetailRequestDO queryAccountDetailRequestDO) {

        InstFundsAccountQueryEntity accountQueryEntity = reqDoAssembler.toInstFundsAccountQueryEntity(queryAccountDetailRequestDO);
        InstSubFundsAccountQueryEntity subFundsAccountQueryEntity = reqDoAssembler.toInstSubFundsAccountQueryEntity(queryAccountDetailRequestDO);

        InstFundsAccountEntity accountEntity = null;
        InstSubFundsAccountEntity instSubFundsAccountEntity = null;
        // 如果子级账号传递，查询机构账号子级账号
        if (StringUtils.isNotBlank(subFundsAccountQueryEntity.getSubAccountNo()) || StringUtils.isNotBlank(subFundsAccountQueryEntity.getBSubAccountNo())) {
            List<InstSubFundsAccountEntity> subFundsAccountList = instSubFundsAccountService.queryListByQueryEntity(subFundsAccountQueryEntity);
            // 检查查询到的子级资金账号数量
            if (CollectionUtil.isEmpty(subFundsAccountList)) {
                return null;
            } else if (subFundsAccountList.size() > 1) {
                // 子级账号传递，查询到多条子级账号数据异常
                throw new BusinessException(ErrorCodeEnum.MORE_THAN_ONE.getCode(), ErrorCodeEnum.MORE_THAN_ONE.getMsg());
            }
            // 子级账号传递，查询到1条子级账号数据
            instSubFundsAccountEntity = subFundsAccountList.get(0); // CHECKED
            accountQueryEntity.setAccountId(instSubFundsAccountEntity.getAccountId());
            accountEntity = instFundsAccountService.queryById(accountQueryEntity);
        } else {
            // 查询机构账号
            List<InstFundsAccountEntity> accountEntityList = instFundsAccountService.queryByAccountNoAndInstCode(accountQueryEntity);
            if (CollectionUtil.isEmpty(accountEntityList)) {
                return null;
            } else if (accountEntityList.size() > 1) {
                // 非子级账号传递，查询到多条数据异常
                throw new BusinessException(ErrorCodeEnum.MORE_THAN_ONE.getCode(), ErrorCodeEnum.MORE_THAN_ONE.getMsg());
            } else {
                // 非子级账号传递，查询到1条数据
                accountEntity = accountEntityList.get(0); // CHECKED
            }
        }

        QueryAccountDetailResponseDO queryAccountDetailResponseDO = resDoAssembler.toQueryAccountDetailResponseDO(accountEntity);
        if (Objects.nonNull(instSubFundsAccountEntity)) {
            queryAccountDetailResponseDO.setSubAccountId(instSubFundsAccountEntity.getSubAccountId());
            queryAccountDetailResponseDO.setSubAccountName(instSubFundsAccountEntity.getSubAccountName());
            queryAccountDetailResponseDO.setSubAccountNo(instSubFundsAccountEntity.getSubAccountNo());
            queryAccountDetailResponseDO.setSubAccountStatus(instSubFundsAccountEntity.getStatus());
            queryAccountDetailResponseDO.setBSubAccountNo(instSubFundsAccountEntity.getBSubAccountNo());
            queryAccountDetailResponseDO.setMerchantNo(instSubFundsAccountEntity.getMerchantNo());
            queryAccountDetailResponseDO.setSubMerchantNo(instSubFundsAccountEntity.getSubMerchantNo());
            queryAccountDetailResponseDO.setSubScenes(instSubFundsAccountEntity.getScenes());
        }

        return queryAccountDetailResponseDO;
    }

    @Override
    public QueryAccountDetailByIdResponseDO queryAccountDetailById(QueryAccountDetailByIdRequestDO queryAccountDetailByIdRequestDO) {

        InstFundsAccountQueryEntity accountQueryEntity = reqDoAssembler.toInstFundsAccountQueryEntity(queryAccountDetailByIdRequestDO);
        InstSubFundsAccountQueryEntity subAccountQueryEntity = reqDoAssembler.toInstSubFundsAccountQueryEntity(queryAccountDetailByIdRequestDO);

        InstSubFundsAccountEntity instSubFundsAccountEntity = instSubFundsAccountService.queryById(subAccountQueryEntity);

        InstFundsAccountEntity instFundsAccountEntity;
        if (Objects.nonNull(instSubFundsAccountEntity)) {
            accountQueryEntity.setAccountId(instSubFundsAccountEntity.getAccountId());
            instFundsAccountEntity = instFundsAccountService.queryById(accountQueryEntity);
        } else {
            instFundsAccountEntity = instFundsAccountService.queryById(accountQueryEntity);
        }

        QueryAccountDetailByIdResponseDO queryAccountDetailByIdResponseDO = resDoAssembler.toQueryAccountDetailByIdResponseDO(instFundsAccountEntity);
        // 若不是机构账号，则是机构子级账号
        if (Objects.nonNull(instSubFundsAccountEntity)) {
            queryAccountDetailByIdResponseDO.setAccountId(instSubFundsAccountEntity.getSubAccountId());
            queryAccountDetailByIdResponseDO.setAccountNo(instSubFundsAccountEntity.getSubAccountNo());
            queryAccountDetailByIdResponseDO.setAccountName(instSubFundsAccountEntity.getSubAccountName());
            queryAccountDetailByIdResponseDO.setSubAccountStatus(instSubFundsAccountEntity.getStatus());
            queryAccountDetailByIdResponseDO.setBSubAccountNo(instSubFundsAccountEntity.getBSubAccountNo());
            queryAccountDetailByIdResponseDO.setMerchantNo(instSubFundsAccountEntity.getMerchantNo());
            queryAccountDetailByIdResponseDO.setSubMerchantNo(instSubFundsAccountEntity.getSubMerchantNo());
            queryAccountDetailByIdResponseDO.setSubScenes(instSubFundsAccountEntity.getScenes());
        }
        return queryAccountDetailByIdResponseDO;
    }
}
