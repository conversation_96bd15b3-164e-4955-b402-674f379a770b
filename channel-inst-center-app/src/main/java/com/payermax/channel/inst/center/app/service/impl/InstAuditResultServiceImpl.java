package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstAuditResultEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAuditResultDao;
import com.payermax.channel.inst.center.app.service.InstAuditResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstAuditResultServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 9:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstAuditResultServiceImpl implements InstAuditResultService {

    @Autowired
    private InstAuditResultDao instAuditResultDao;

    @Override
    public InstAuditResultEntity query(InstAuditResultEntity instAuditResultEntity) {
        Preconditions.checkArgument(instAuditResultEntity != null, "param instAuditResultEntity is mandatory");
        List<InstAuditResultEntity> instAuditResultEntityList = instAuditResultDao.selectAll(instAuditResultEntity);
        if (CollectionUtils.isNotEmpty(instAuditResultEntityList)) {
            return instAuditResultEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public int save(InstAuditResultEntity instAuditResultEntity) {
        Preconditions.checkArgument(instAuditResultEntity != null, "param instAuditResultEntity is mandatory");
        int result = 0;
        //主键不为空则根据主键更新信息
        if (null != instAuditResultEntity.getId()) {
            result = instAuditResultDao.updateByPrimaryKey(instAuditResultEntity);
        } else {
            result = instAuditResultDao.insert(instAuditResultEntity);
        }
        return result;
    }
}
