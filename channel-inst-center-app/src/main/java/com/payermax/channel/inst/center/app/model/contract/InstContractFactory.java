package com.payermax.channel.inst.center.app.model.contract;

import cn.hutool.core.lang.Pair;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractFeeItemAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractSettleItemAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractVersionStatusEnum;
import com.payermax.channel.inst.center.domain.vo.contract.ContractProductBusinessKey;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractStandardProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOriginProductRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractStandardProductRepository;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.*;

/**
 * <AUTHOR> at 2023/6/12 4:00 PM
 **/
@Component
public class InstContractFactory {

    @Resource
    private InstContractFeeItemAssembler instContractFeeItemAssembler;

    @Resource
    private InstContractSettleItemAssembler instContractSettleItemAssembler;

    @Resource
    private VoucherAdapter voucherAdapter;

    @Resource
    private InstFundsAccountService fundsAccountService;

    @Resource
    private InstContractOriginProductRepository instContractOriginProductRepository;

    @Resource
    private InstContractStandardProductRepository instContractStandardProductRepository;

    @Resource
    private ContractVersionPOAssembler contractVersionPOAssembler;

    /**
     * 构造 机构主合同
     */
    public InstContractBaseInfo composeInstContractBase(String instCode, String contractEntity, ContractBusinessTypeEnum businessTypeEnum) {
        return InstContractBaseInfo.builder()
                .contractNo(voucherAdapter.getBaseInfoContractNo())
                .instCode(instCode)
                .contractEntity(contractEntity)
                .instProductType(businessTypeEnum)
                // 此时状态默认是未激活，还未生效的
                .status(ContractStatusEnum.ACTIVATED)
                .newlyCreated(true)
                .build();
    }


    /**
     * 构造 因本次合同变更 生成的新合同版本
     */
    public InstContractVersionInfo composeInstContractNewVersion(ContractProcessByExcelContext context,
                                                                 InstContractBaseInfo baseInfo) {

        return InstContractVersionInfo.builder()
                .contractNo(baseInfo.getContractNo())
                .contractVersion(voucherAdapter.getContractVersionNo())
                .effectStartTime(context.getEffectStartTime() != null ? context.getEffectStartTime()
                        : LocalDateTime.of(2023, Month.JANUARY, 1, 0, 0))
                .effectEndTime(null)
                .status(ContractVersionStatusEnum.EFFECTIVE)
                .newlyCreated(true)
                .build();
    }

    /**
     * 根据 机构成本/结算条款 构造机构原始产品 + 标准化产品列表
     */
    public Pair<List<InstContractOriginProduct>, List<InstContractStandardProduct>> composeInstProducts(ContractProcessByExcelContext context,
                                                                                                        String contractNo, String contractVersion) {

        List<InstContractOriginProduct> originProductList = new ArrayList<>();
        List<InstContractStandardProduct> standardProductList = new ArrayList<>();

        // 1. 获取所有机构产品名称（Key为合同中的机构产品名称）
        Map<ContractProductBusinessKey, List<InstContractFeeItem>> instContractFeeMap = context.getInstContractFeeMap();
        Map<ContractProductBusinessKey, List<InstContractSettlementItem>> instContractSettlementMap = context.getInstContractSettlementMap();


        Set<ContractProductBusinessKey> businessKeys = new HashSet<>();
        businessKeys.addAll(instContractFeeMap.keySet());
        businessKeys.addAll(instContractSettlementMap.keySet());

        // 2. 根据合约条款构造原始产品
        businessKeys.forEach(businessKey -> {
            // -------------------------------//
            // -------------------------------//
            // ---------原始产品新增幂等--------//
            InstContractOriginProductPO instContractOriginProductPO = instContractOriginProductRepository.queryExistOriginProduct(contractNo, contractVersion, businessKey.getBusinessKey());
            String originProductCode;
            if (instContractOriginProductPO != null) {
                originProductCode = instContractOriginProductPO.getInstOriginProductNo();
            } else {
                originProductCode = voucherAdapter.getOriginProductCode();
            }
            // ---------原始产品新增幂等--------//
            // -------------------------------//
            // -------------------------------//

            // 2.0. 找到「原始机构产品」对应条款（原始机构产品名称相同，则认为合约条款是相同的，虽然在excel中是多行）
            List<InstContractFeeItem> feeItemList = (List<InstContractFeeItem>) MapUtils.getObject(instContractFeeMap, businessKey, Collections.emptyList());
            List<InstContractSettlementItem> settlementItemList = (List<InstContractSettlementItem>) MapUtils.getObject(instContractSettlementMap, businessKey, Collections.emptyList());

            // 2.1. 补充原始产品 合约条款项内容
            feeItemList.forEach(feeItem -> {
                feeItem.setInstContractFeeItemNo(voucherAdapter.getInstProductFeeItemNo());
                feeItem.setInstOriginProductNo(originProductCode);
            });
            settlementItemList.forEach(settlementItem -> {
                settlementItem.setInstContractSettlementItemNo(voucherAdapter.getInstProductSettlementItemNo());
                settlementItem.setInstOriginProductNo(originProductCode);
            });

            // 2.2. 构造机构原始产品--原来不存在就新增
            InstContractOriginProduct originProduct;
            if (instContractOriginProductPO == null) {
                originProduct = InstContractOriginProduct.builder()
                        .contractNo(contractNo)
                        .contractVersion(contractVersion)
                        .instOriginProductNo(originProductCode)
                        .instProductName(businessKey.getBusinessKey())
                        .contractFeeItems(feeItemList)
                        .settlementItems(settlementItemList)
                        .newlyCreated(true)
                        .build();
            } else {
                //否则就用之前的
                originProduct = contractVersionPOAssembler.convertOriginProductPO2Domain(instContractOriginProductPO);
                originProduct.setSettlementItems(settlementItemList);
                originProduct.setContractFeeItems(feeItemList);
            }
            originProductList.add(originProduct);

            // 2.3. 如果发现已经标准化，则需要填充标准化产品对象
            if (businessKey.isStandardized()) {
                // -------------------------------//
                // ---------标准产品新增幂等--------//
                InstContractStandardProductPO standardProductPO = instContractStandardProductRepository
                        .queryExistOriginProduct(originProductCode, contractNo, contractVersion
                                , businessKey.getPaymentMethodType(), businessKey.getTargetOrg(), businessKey.getCardOrg());

                if (standardProductPO == null) {
                    InstContractStandardProduct standardProduct = InstContractStandardProduct.builder()
                            .contractNo(contractNo)
                            .contractVersion(contractVersion)
                            .instStandardProductNo(voucherAdapter.getStandardProductCode())
                            .paymentMethodType(businessKey.getPaymentMethodType())
                            .targetOrg(businessKey.getTargetOrg())
                            .cardOrg(businessKey.getCardOrg())
                            .originProduct(originProduct)
                            .newlyCreated(true)
                            .build();
                    standardProductList.add(standardProduct);
                }
                // ---------标准产品新增幂等--------//
                // -------------------------------//
            }
        });

        return new Pair<>(originProductList, standardProductList);
    }


    /**
     * 构造 非阶梯(单条)合同费用条款
     */
    public InstContractFeeItem composePayInContractSingleFeeItem(InstContractFeeRowItemDTO dtoLine) {
        Map<FeeTypeEnum, InstFeeConfig> feeConfigMap = new HashMap<>();

        // 1. 构造单笔交易费用
        InstFeeConfig tradeFee = instContractFeeItemAssembler.convertSingleInstTradeFeeFromExcel(dtoLine);
        feeConfigMap.put(FeeTypeEnum.TRADE, tradeFee);

        // 2. 构造单笔退款费用
        if (StringUtils.isNotBlank(dtoLine.getRefundCalculateType())) {
            InstFeeConfig refundFee = instContractFeeItemAssembler.convertInstRefundFeeFromExcel(dtoLine);
            feeConfigMap.put(FeeTypeEnum.REFUND, refundFee);
        }

        // 3. 构造税费
        List<InstTaxConfig> instTaxConfigList
                = instContractFeeItemAssembler.convertInstTaxFromExcel(dtoLine.getTaxInfoList());

        InstContractFeeItem feeItem = instContractFeeItemAssembler.convertContractFeeItemFromExcel(dtoLine);
        feeItem.setTaxConfigs(instTaxConfigList);
        feeItem.setFeeConfigs(feeConfigMap);

        return feeItem;
    }


    /**
     * 构造 阶梯合同费用条款
     */
    public InstContractFeeItem composePayInContractStepFeeItem(List<InstContractFeeRowItemDTO> dtoLineList) {
        InstContractFeeRowItemDTO dtoLine = dtoLineList.get(0); //CHECKED
        Map<FeeTypeEnum, InstFeeConfig> feeConfigMap = new HashMap<>();

        // 1. 构造阶梯交易费用
        InstFeeConfig tradeFee = instContractFeeItemAssembler.convertStepInstTradeFeeFromExcel(dtoLine);
        tradeFee.setStepPercentAmount(instContractFeeItemAssembler.convertStepCombineFeeListFromExcel(dtoLineList));
        feeConfigMap.put(FeeTypeEnum.TRADE, tradeFee);

        // 2. 构造退款费用
        if (StringUtils.isNotBlank(dtoLine.getRefundCalculateType())) {
            InstFeeConfig refundFee = instContractFeeItemAssembler.convertInstRefundFeeFromExcel(dtoLine);
            feeConfigMap.put(FeeTypeEnum.REFUND, refundFee);
        }

        // 3. 构造税费
        List<InstTaxConfig> instTaxConfigList
                = instContractFeeItemAssembler.convertInstTaxFromExcel(dtoLine.getTaxInfoList());

        InstContractFeeItem feeItem = instContractFeeItemAssembler.convertContractFeeItemFromExcel(dtoLine);
        feeItem.setTaxConfigs(instTaxConfigList);
        feeItem.setFeeConfigs(feeConfigMap);

        return feeItem;
    }

    /**
     * 构造结算条款
     */
    public InstContractSettlementItem composePayInContractSettleItem(InstContractSettleRowItemDTO dtoLine) {
        // 1. 构造结算条款 - 结算基本信息
        InstSettlePaymentConfig instSettlePaymentConfig = instContractSettleItemAssembler.convertInstSettlePaymentInfoFromExcel(dtoLine);

        // 根据accountId查询accountType
        if (StringUtils.isNotEmpty(dtoLine.getAccountId())) {
            InstFundsAccountQueryEntity queryCondition = new InstFundsAccountQueryEntity();
            queryCondition.setAccountId(dtoLine.getAccountId());
            InstFundsAccountEntity instFundsAccount = fundsAccountService.queryById(queryCondition);
            instSettlePaymentConfig.setAccountType(instFundsAccount.getAccountType());
        }

        // 2. 构造结算周期
        List<InstSettleDateConfig> instSettleDateConfig = instContractSettleItemAssembler.convertInstSettleRoundFromExcel(dtoLine.getSettleDateInfoList());

        // 3. 构造结算费用
        InstFeeConfig instFeeConfig = instContractSettleItemAssembler.convertInstSettleFeeFromExcel(dtoLine);

        InstContractSettlementItem instContractSettlementItem = instContractSettleItemAssembler.convertContractSettleItemFromExcel(dtoLine);
        instContractSettlementItem.setSettleFeeConfig(instFeeConfig);
        instContractSettlementItem.setSettleDateConfigs(instSettleDateConfig);
        instContractSettlementItem.setSettlePaymentConfig(instSettlePaymentConfig);

        return instContractSettlementItem;
    }

}