package com.payermax.channel.inst.center.app.valid.excel.impl;

import com.payermax.channel.inst.center.app.config.InstContractFeeParseConfig;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.app.dto.common.ValidatorUtils;
import com.payermax.channel.inst.center.app.request.InstContractImportRequestDTO;
import com.payermax.channel.inst.center.app.valid.excel.ExcelValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * InstContractFeeExcelValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class InstContractFeeExcelValid implements ExcelValid<InstContractFeeRowItemDTO, InstContractImportRequestDTO> {

    @Resource
    private InstContractFeeParseConfig instContractFeeParseConfig;;

    @Override
    public Result<InstContractFeeRowItemDTO> valid(ExcelUtil.ExcelParseInfo<InstContractFeeRowItemDTO> excelParseInfo) {
        Result<InstContractFeeRowItemDTO> result = new Result<InstContractFeeRowItemDTO>().setSuccess(Boolean.TRUE).setData(excelParseInfo);
        if(Objects.isNull(excelParseInfo) || CollectionUtils.isEmpty(excelParseInfo.getDataList())) {
            return result;
        }
        ExportErrorInfo exportErrorInfo = ExportErrorInfo.builder().exportErrorInfoItems(new ArrayList<>()).build();
        excelParseInfo.getDataList().forEach(item -> {
            List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItemList = ValidatorUtils.validateItemAll(item, excelParseInfo.getHeaderMap());
            if(!CollectionUtils.isEmpty(errorInfoItemList)) {
                exportErrorInfo.setHasErrors(true);
                exportErrorInfo.getExportErrorInfoItems().addAll(errorInfoItemList);
            }
            if(CollectionUtils.isEmpty(item.getTaxInfoList())) {
                return;
            }
            item.getTaxInfoList().forEach(taxInfo -> {
                if(Objects.isNull(taxInfo)) {
                    return;
                }
                Set<ConstraintViolation<InstContractFeeRowItemDTO.TaxInfo>> constraintViolationSet = ValidatorUtils.valid(taxInfo);
                if(CollectionUtils.isEmpty(constraintViolationSet)) {
                    return;
                }
                constraintViolationSet.forEach(constraintViolation -> {
                    exportErrorInfo.setHasErrors(true);
                    exportErrorInfo.getExportErrorInfoItems().add(buildTaxErrorInfo(constraintViolation, item.getExcelRowNo(), excelParseInfo.getLang()));
                });
            });
            item.setTaxInfoList(item.getTaxInfoList().stream().filter(taxInfo -> Objects.nonNull(taxInfo)).collect(Collectors.toList()));
        });
        if(exportErrorInfo.isHasErrors()) {
            result.setSuccess(Boolean.FALSE).setErrorInfo(exportErrorInfo);
        }
        return result;
    }

    private ExportErrorInfo.ExportErrorInfoItem buildTaxErrorInfo(ConstraintViolation<InstContractFeeRowItemDTO.TaxInfo> constraintViolation, int rowNo, LangEnum langEnum) {
        ExportErrorInfo.ExportErrorInfoItem error = new ExportErrorInfo.ExportErrorInfoItem();
        String fieldName = constraintViolation.getPropertyPath().toString();
        error.setColumnName(instContractFeeParseConfig.getFieldMapping().get(LangEnum.code(langEnum)).get(fieldName));
        error.setRowNo(rowNo);
        error.setCellValue(constraintViolation.getInvalidValue());
        error.setErrorMsg(constraintViolation.getMessage());
        return error;
    }
}
