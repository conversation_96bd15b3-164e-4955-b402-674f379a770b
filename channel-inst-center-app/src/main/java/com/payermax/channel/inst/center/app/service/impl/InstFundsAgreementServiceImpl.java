package com.payermax.channel.inst.center.app.service.impl;

import com.payermax.channel.inst.center.app.assembler.domain.InstFundsAgreementAssembler;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.factory.InstFundsAgreementFactory;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstFundsAgreementQueryVO;
import com.payermax.channel.inst.center.app.service.InstFundsAgreementService;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOperateLogPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBizAgreementRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOperateLogRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFundsAgreementRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFundsSettleRuleRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 资金协议 Service
 */
@Service
@AllArgsConstructor
@Slf4j
public class InstFundsAgreementServiceImpl implements InstFundsAgreementService {

    private final InstFundsAgreementRepository fundsAgreementRepository;
    private final InstBizAgreementRepository bizAgreementRepository;
    private final InstFundsSettleRuleRepository fundsSettleRuleRepository;
    private final InstFundsAgreementFactory fundsAgreementFactory;
    private final OperateLogFactory operateLogFactory;
    private final InstContractOperateLogRepository operatorRepository;
    private final InstFundsAgreementAssembler fundsAgreementAssembler;


    /**
     * 保存资金协议
     * @param context 资金协议上下文
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InstFundsAgreementContextDTO fundsAgreementSave(InstFundsAgreementContextDTO context) {

            // 1. 初始化业务协议
            InstBizAgreementPO bizAgreement = fundsAgreementFactory.composeBizAgreement(context.getBizAgreement());
            // 2. 初始化资金协议
            context.getFundsAgreement().setBizAgreementNo(bizAgreement.getBizAgreementNo());
            InstFundsAgreementPO fundsAgreement = fundsAgreementFactory.composeFundsAgreement(context.getFundsAgreement());
            AssertUtil.isTrue(fundsAgreement.isNewlyCreated(), "error", "资金协议已存在，如需修改/新增清算规则，请到资金修改界面");
            // 3. 初始化结算规则
            List<InstFundsSettleRulePO> settleRules = context.getSettleRules().stream()
                    .map(rule -> fundsAgreementFactory.composeFundsSettleRule(fundsAgreement.getFundsAgreementNo(), rule)).collect(Collectors.toList());

            // 4. 保存
            if(bizAgreement.isNewlyCreated()) {
                bizAgreementRepository.save(bizAgreement);
            }
            if(fundsAgreement.isNewlyCreated()) {
                fundsAgreementRepository.saveWithCheck(fundsAgreement);
            }
            settleRules.forEach(fundsSettleRuleRepository::saveIgnoreDuplicate);
            return context;
    }



    /**
     * 资金协议查询
     *
     * @param request 查询参数
     * @return 资金协议VO（资金协议、协议发起方、协议对手方）
     */
    @Override
    public List<InstFundsAgreementQueryVO> fundsAgreementQuery(int pageNum, int pageSize, InstFundsAgreementQueryRequestDTO request) {
        // 入参转换
        InstFundsAgreementPO fundsAgreementParams = fundsAgreementAssembler.request2FundsPo(request);
        // 查询资金协议、业务协议
        List<InstFundsAgreementPO> fundsAgreementList = fundsAgreementRepository.queryByConditions(fundsAgreementParams, request.getInitiator(), request.getCounter(), pageNum, pageSize);
        return fundsAgreementList.stream().map(fundsAgreementAssembler::fundsPo2QueryVo).collect(Collectors.toList());
    }

    /**
     * 根据 ID 查询资金协议
     *
     * @param fundsAgreementNo 资金协议编号
     * @return 清算规则
     */
    @Override
    public InstFundsAgreementPO getFundsAgreementById(String fundsAgreementNo) {
        return fundsAgreementRepository.selectById(fundsAgreementNo);
    }

    /**
     * 修改业务协议
     * @param request 业务协议
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bizAgreementModify(String shareId, InstFundsAgreementContextDTO.BizAgreement request) {
        // 1. 唯一性校验
        InstBizAgreementPO modifiedData = fundsAgreementAssembler.bizAgreementContext2Po(request);
        AssertUtil.isTrue(bizAgreementRepository.modifyUniqueCheck(modifiedData),"ERROR","业务协议唯一性校验不通过");
        // 2. 查询原有数据
        InstBizAgreementPO originData = bizAgreementRepository.getById(modifiedData.getBizAgreementNo());
        // 3. 更新数据
        InstContractOperateLogPO operateLog = operateLogFactory.composeOperateLog(shareId, modifiedData.getBizAgreementNo(), originData, modifiedData,
                OperateModuleEnum.INST_CENTER_BIZ_AGREEMENT_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        operatorRepository.save(operateLog);
        return bizAgreementRepository.updateById(modifiedData);
    }

    /**
     * 修改资金协议
     *
     * @param shareId 用户 ID
     * @param request 资金协议
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fundsAgreementModify(String shareId, InstFundsAgreementContextDTO.FundsAgreement request) {
        // 1. 唯一性校验
        InstFundsAgreementPO modifiedData = fundsAgreementAssembler.fundsAgreementContext2Po(request);
        AssertUtil.isTrue(fundsAgreementRepository.uniqueCheckIgnoreSelf(modifiedData),"ERROR","资金协议唯一性校验不通过");
        // 2. 查询原始数据
        InstFundsAgreementPO originData = fundsAgreementRepository.getById(modifiedData.getFundsAgreementNo()).setSettleRules(null).setBizAgreement(null);
        // 3. 更新数据
        InstContractOperateLogPO operateLog = operateLogFactory.composeOperateLog(shareId, modifiedData.getFundsAgreementNo(), originData, modifiedData,
                OperateModuleEnum.INST_CENTER_FUND_AGREEMENT_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        operatorRepository.save(operateLog);
        return fundsAgreementRepository.saveWithCheck(modifiedData);
    }

    /**
     * 资金协议状态修改
     *
     * @param shareId 用户 ID
     * @param request 资金协议状态
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fundsAgreementStatusChange(String shareId, InstFundsAgreementContextDTO.FundsAgreement request) {
        // 1. 查询原数据
        InstFundsAgreementPO originData = fundsAgreementRepository.getById(request.getFundsAgreementNo()).setSettleRules(null).setBizAgreement(null);
        InstFundsAgreementPO modifiedData = fundsAgreementAssembler.fundsAgreementCopy(originData).setStatus(request.getStatus().name());
        // 2. 当状态切换为生效时，需要进行唯一性校验，确保维度相等的资金协议同时只能生效一条
        AssertUtil.isTrue(fundsAgreementRepository.uniqueCheckIgnoreSelf(modifiedData),"ERROR","存在状态相同的资金协议");
        // 3. 更新数据
        InstContractOperateLogPO operateLog = operateLogFactory.composeOperateLog(shareId, modifiedData.getFundsAgreementNo(), originData, modifiedData,
                OperateModuleEnum.INST_CENTER_FUND_AGREEMENT_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        operatorRepository.save(operateLog);
        return fundsAgreementRepository.updateById(modifiedData);
    }

    /**
     * 新增清算规则
     *
     * @param shareId 用户 ID
     * @param request 清算规则
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean settleRuleAdd(String shareId, InstFundsAgreementContextDTO.FundsSettleRule request) {
        // 清算规则构造
        InstFundsSettleRulePO modifiedData = fundsAgreementFactory.composeFundsSettleRule(request.getFundsAgreementNo(), request);
        AssertUtil.isTrue(fundsSettleRuleRepository.uniqueCheck(modifiedData),"ERROR","清算规则唯一性校验不通过");
        // 保存数据
        InstContractOperateLogPO operateLog = operateLogFactory.composeOperateLog(shareId, modifiedData.getFundsAgreementNo(), null, modifiedData,
                OperateModuleEnum.INST_CENTER_FUNDS_SETTLE_RULE_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.ADD);
        operatorRepository.save(operateLog);
        return fundsSettleRuleRepository.save(modifiedData);
    }


    /**
     * 清算规则修改
     * @param shareId 用户 ID
     * @param request 清算规则
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean settleRuleModify(String shareId, InstFundsAgreementContextDTO.FundsSettleRule request) {
        InstFundsSettleRulePO modifiedData = fundsAgreementAssembler.fundsSettleRuleContext2Po(request);
        // 1. 查询原有数据
        InstFundsSettleRulePO originData = fundsSettleRuleRepository.getById(modifiedData.getSettleRuleNo());
        // 2. 更新数据
        InstContractOperateLogPO operateLog = operateLogFactory.composeOperateLog(shareId, modifiedData.getSettleRuleNo(), originData, modifiedData,
                OperateModuleEnum.INST_CENTER_FUNDS_SETTLE_RULE_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        operatorRepository.save(operateLog);
        return fundsSettleRuleRepository.updateById(modifiedData);
    }


}
