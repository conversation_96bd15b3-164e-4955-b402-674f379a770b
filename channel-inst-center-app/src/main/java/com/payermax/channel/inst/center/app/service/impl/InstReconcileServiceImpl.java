package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstReconcileDao;
import com.payermax.channel.inst.center.app.service.InstReconcileService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机构对账Service实现
 *
 * <AUTHOR>
 * @date 2022/6/4 17:37
 */
@Service
public class InstReconcileServiceImpl implements InstReconcileService {

    @Autowired
    private InstReconcileDao instReconcileDao;

    @Override
    public int save(InstReconcileEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instReconcileDao.insert(record);
        } else {
            // 更新
            result = instReconcileDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstReconcileEntity query(InstReconcileEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");

        List<InstReconcileEntity> entityList = instReconcileDao.selectAll(queryEntity);
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }
}
