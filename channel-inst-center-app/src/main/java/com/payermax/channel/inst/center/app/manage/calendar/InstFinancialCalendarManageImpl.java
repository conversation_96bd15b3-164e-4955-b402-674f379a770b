package com.payermax.channel.inst.center.app.manage.calendar;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarHolidayDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarInitTemplateDTO;
import com.payermax.channel.inst.center.app.manage.calendar.processHandler.InstFinancialCalendarActivateHandler;
import com.payermax.channel.inst.center.app.manage.calendar.processHandler.InstFinancialCalendarSaveHandler;
import com.payermax.channel.inst.center.app.manage.calendar.processHandler.InstFinancialCalendarUpdateHandler;
import com.payermax.channel.inst.center.app.request.calendar.*;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.utils.CalendarUtil;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.channel.inst.center.domain.enums.calendar.HolidayOperateEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.infra.ionia.fs.dto.UploadResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Slf4j
@Service
@AllArgsConstructor
public class InstFinancialCalendarManageImpl implements InstFinancialCalendarManage {

    private final InstFinancialCalendarRepository calendarRepository;
    private final InstFinancialCalendarHolidayRepository holidayRepository;
    private final InstFinancialCalendarSaveHandler calendarSaveHandler;
    private final InstFinancialCalendarUpdateHandler calendarUpdateHandler;
    private final InstFinancialCalendarActivateHandler calendarActivateHandler;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final InstFinancialCalendarContextBuilder calendarContextBuilder;
    private final CalendarExportHandler calendarExportHandler;


    @Override
    public Page<InstFinancialCalendarDTO> queryCalendarList(InstFinancialCalendarRequest request) {
        // 请求转换
        InstFinancialCalendarPO calendar = InstFinancialCalendarAssembler.INSTANCE.request2Po(request);

        // 查询日历列表
        Page<InstFinancialCalendarPO> calendarPage = calendarRepository.queryByConditionsPaging(calendar,request.getPageNum(),request.getPageSize());

        // 查询审核中的流程
        InstBusinessDraft draft = InstBusinessDraft.builder().businessType(BusinessTypeEnum.INST_CENTER).moduleName(OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER).status(InstProcessStatusEnum.PROCESSING).build();
        Map<String, InstBusinessDraft> draftMap = businessDraftRepository.queryByConditions(InstBusinessDraftAssembler.INSTANCE.domain2Po(draft))
                .stream().map(InstBusinessDraftAssembler.INSTANCE::po2Domain).collect(Collectors.toMap(InstBusinessDraft::getBusinessKey, Function.identity()));

        // 处理已存在的日历，填充关联流程信息
        List<InstFinancialCalendarDTO> dtoList = calendarPage.getRecords().stream().map(InstFinancialCalendarAssembler.INSTANCE::po2dto)
                // 填充关联流程信息
                .peek(calendarDTO -> Optional.ofNullable(draftMap.get(calendarDTO.getCalendarId()))
                        .ifPresent(processDock -> InstFinancialCalendarAssembler.INSTANCE.fillCalendarProcessMsg(calendarDTO, processDock)))
                .collect(Collectors.toList());

        // 处理新增流程审批中的日历
        Set<String> calendarIdSet = dtoList.stream().map(InstFinancialCalendarDTO::getCalendarId).collect(Collectors.toSet());
        List<InstFinancialCalendarDTO> draftCalendarList = draftMap.entrySet().stream().filter(item -> !calendarIdSet.contains(item.getKey()))
                .map(item -> {
                    InstFinancialCalendarContext context = JSON.parseObject(item.getValue().getDraftData(), InstFinancialCalendarContext.class);
                    InstFinancialCalendarDTO calendarDto = InstFinancialCalendarAssembler.INSTANCE.domain2Dto(context.getCalendar());
                    calendarDto.setDisplayStatus(item.getValue().getStatus().name());
                    return calendarDto;
                }).collect(Collectors.toList());
        dtoList.addAll(draftCalendarList);

        // 根据显示状态过滤，生效状态/关联流程状态
        dtoList = dtoList.stream()
                .filter(calendarDTO -> StringUtils.isBlank(request.getStatus()) || calendarDTO.getDisplayStatus().equals(request.getStatus()))
                .collect(Collectors.toList());

        // 排序
        dtoList.sort(Comparator.comparing(InstFinancialCalendarDTO::getCalendarYear).reversed()
                .thenComparing(InstFinancialCalendarDTO::getCountry)
                .thenComparing(InstFinancialCalendarDTO::getCurrency));

        return InstFinancialCalendarAssembler.INSTANCE.poPage2DtoPage(calendarPage, dtoList);
    }

    @Override
    public InstFinancialCalendarDTO queryCalendarById(InstFinancialCalendarRequest request) {

        // 查询日历
        InstFinancialCalendarPO calendarPo = calendarRepository.getById(request.getCalendarId());
        InstFinancialCalendarDTO calendar = InstFinancialCalendarAssembler.INSTANCE.po2dto(calendarPo);
        String calendarId = calendarPo.getCalendarId();

        // 当存在引用的日历时，使用引用日历 ID 查询节假日
        if (StringUtils.isNotBlank(calendar.getSourceCalendar())){
            InstFinancialCalendarPO sourceCalendar = calendarRepository.getById(calendar.getSourceCalendar());
            calendarId = sourceCalendar.getCalendarId(); //CHECKED
        }

        // 查询日历节假日，获取新增/取消节假日列表
        List<InstFinancialCalendarHolidayDTO> holidays = holidayRepository.queryByConditions(InstFinancialCalendarHolidayPO.builder().calendarId(calendarId).build(), null, null)
                .stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).map(InstFinancialCalendarAssembler.INSTANCE::domain2Dto).collect(Collectors.toList());
        calendar.setHolidayList(holidays);
        List<InstFinancialCalendarHolidayDTO> addHolidayList = holidays.stream().filter(item -> HolidayOperateEnum.ADD.equals(item.getHolidayOperate())).collect(Collectors.toList());
        List<InstFinancialCalendarHolidayDTO> cancelHolidayList = holidays.stream().filter(item -> HolidayOperateEnum.CANCEL.equals(item.getHolidayOperate())).collect(Collectors.toList());
        calendar.setAddHolidayList(addHolidayList);
        calendar.setCancelHolidayList(cancelHolidayList);

        return calendar;
    }

    @Override
    public List<InstFinancialCalendarHoliday> queryHolidayList(InstFinancialCalendarHolidayRequest request) {
        InstFinancialCalendarHolidayPO po = InstFinancialCalendarAssembler.INSTANCE.request2Po(request);
        List<InstFinancialCalendarHolidayPO> poList = holidayRepository.queryByConditions(po, request.getStartDate(), request.getEndDate());
        return poList.stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());
    }


    @Override
    public InstFinancialCalendarInitTemplateDTO initCalendarTemplate(InstFinancialCalendarInitTemplateRequest request) {
        List<LocalDate> dateList = CalendarUtil.getDatesOfYear(request.getCalendarYear());
        String calendarId = calendarContextBuilder.generateCalendarId(String.valueOf(request.getCalendarYear()), request.getCountry(), request.getCurrency());
        // 日历类型校验
        InstFinancialCalendar calendar = InstFinancialCalendar.builder()
                .country(StringUtils.isNotBlank(request.getCountry()) ? request.getCountry() : CommonConstants.STAR)
                .currency(StringUtils.isNotBlank(request.getCurrency()) ? request.getCurrency() : CommonConstants.STAR)
                .calendarType(CalendarTypeEnum.valueOf(request.getCalendarType())).build();
        calendarContextBuilder.calendarTypeValid(calendar, true);
        // 日历审核校验
        InstBusinessDraftPO draftPo = InstBusinessDraftAssembler.INSTANCE.domain2Po(InstBusinessDraft.builder().businessKey(calendarId).status(InstProcessStatusEnum.PROCESSING).build());
        List<InstBusinessDraftPO> draftList = businessDraftRepository.queryByConditions(draftPo);
        AssertUtil.isTrue(draftList.isEmpty(), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_PROCESSING_ERROR.getCode(), "已存在审核中的日历");
        // 生成周末信息
        List<InstFinancialCalendarHolidayDTO> holidayList = weekendInit(dateList, request);
        return InstFinancialCalendarAssembler.INSTANCE.initCalendarTemplate(request, calendarId, holidayList);
    }

    @Override
    public Boolean startCalendarSaveProcess(InstFinancialCalendarSaveRequest request) {
        return calendarSaveHandler.startCalendarSave(request);
    }

    @Override
    public Boolean startCalendarUpdateProcess(InstFinancialCalendarSaveRequest request) {
        return calendarUpdateHandler.startCalendarUpdate(request);
    }

    @Override
    public Boolean startCalendarActivateProcess(InstFinancialCalendarActivateRequest request) {
        return calendarActivateHandler.startCalendarActivate(request);
    }

    @Override
    public UploadResponse calendarExport(List<String> calendarList) {
        try{
            return calendarExportHandler.calendarExport(calendarList);
        }catch (Exception e){
            throw new BusinessException(ErrorCodeEnum.INST_EXCEL_EXPORT_ERROR.getCode(), ErrorCodeEnum.INST_EXCEL_EXPORT_ERROR.getMsg());
        }
    }

    /**
     * 日历初始化-周末
     */
    private List<InstFinancialCalendarHolidayDTO> weekendInit(List<LocalDate> dateList, InstFinancialCalendarInitTemplateRequest request) {
        Set<DayOfWeek> weekendSet = request.getWeekendList().stream().map(DayOfWeek::valueOf).collect(Collectors.toSet());
        return dateList.stream()
                .filter(date -> CalendarUtil.inWeekendList(date, weekendSet))
                .map(InstFinancialCalendarAssembler.INSTANCE::buildWeekendHoliday)
                .collect(Collectors.toList());
    }




}
