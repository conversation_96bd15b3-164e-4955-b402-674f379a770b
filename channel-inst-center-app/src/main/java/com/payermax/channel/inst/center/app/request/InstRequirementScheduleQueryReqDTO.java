package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/28 10:51
 */
@Data
public class InstRequirementScheduleQueryReqDTO extends InstRequirementScheduleReqDTO {

    private long pageNum;
    private long pageSize = 10;

    private String applyNo;
    private String shareitEntity;
    private String instCode;
    private String instName;
    private String bdId;
    private String amId;
    private String applyPriority;
    private String channelType;
    private String paymentMethodType;
    private String country;
    private List<String> bdList;
    private List<String> amList;
    private List<String> pdList;
    private Date expectReleaseTimeFrom;
    private Date expectReleaseTimeTo;
    private Date planReleaseDateFrom;
    private Date planReleaseDateTo;
    private Date realReleaseDateFrom;
    private Date realReleaseDateTo;

}
