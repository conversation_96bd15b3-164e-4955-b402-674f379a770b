package com.payermax.channel.inst.center.app.valid.annon.impl;

import com.payermax.channel.inst.center.app.service.InstBaseInfoService;
import com.payermax.channel.inst.center.common.constrains.InterfaceValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.common.lang.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * InstCodeValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class InstCodeValid implements InterfaceValid<String, InstCodeValid.SimpleInstInfo> {

    @Resource
    private InstBaseInfoService instBaseInfoService;

    @Override
    public boolean valid(String value, LangEnum lang) {
        if(StringUtil.isEmpty(value)) {
            return Boolean.TRUE;
        }
        return dataList().stream().anyMatch(item -> value.equals(item.getInstCode()));
    }

    @Override
    public List<SimpleInstInfo> queryData() {
        List<InstBaseInfoEntity> list = instBaseInfoService.query(new InstBaseInfoEntity());
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(item -> new SimpleInstInfo().setInstCode(item.getInstCode())).collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public class SimpleInstInfo {

        private String instCode;
    }
}
