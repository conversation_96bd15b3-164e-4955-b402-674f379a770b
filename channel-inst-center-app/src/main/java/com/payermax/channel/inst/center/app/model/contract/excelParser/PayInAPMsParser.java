package com.payermax.channel.inst.center.app.model.contract.excelParser;

import com.payermax.channel.inst.center.app.dto.impl.AccumulatedInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayinDTO;
import com.payermax.channel.inst.center.app.dto.impl.SettleInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.StandardProductInfoDTO;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstContractExcelEnum;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
public class PayInAPMsParser extends InstContractParser {

    @PostConstruct
    public void init() {
        registryParser(ContractBizTypeEnum.I, this);
    }

    /**
     * 构建上下文
     */
    @Override
    public InstContractContext buildContext(InstContractParseRequestDTO request) {
        InstContractContext context = new InstContractContext();
        BeanUtils.copyProperties(request,context);
        return instContractDraftAssembler.request2Context(request);
    }

    /**
     * 解析 Excel，构造原始数据
     */
    @Override
    public void resolve(byte[] fileContent, InstContractContext context) {
        // 1. 解析 excel文件sheet内容
        ExcelUtil.ExcelParseInfo<InstContractFeePayinDTO> excelFeeInfo = ExcelUtil.readExcel(fileContent,
                InstContractFeePayinDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_PAYIN.getExcelSheetName());
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = ExcelUtil.readExcel(fileContent,
                AccumulatedInfoDTO.class, InstContractExcelEnum.INST_CONTRACT_FEE_PAYIN.getExcelSheetName());
        ExcelUtil.ExcelParseInfo<SettleInfoDTO> excelSettlementInfo = ExcelUtil.readExcel(fileContent,
                SettleInfoDTO.class, InstContractExcelEnum.INST_CONTRACT_SETTLEMENT_PAYIN.getExcelSheetName());

        AssertUtil.isTrue(CollectionUtils.isNotEmpty(excelFeeInfo.getDataList()), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 费用信息为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(excelAccumulatedInfo.getDataList()), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 手续费信息为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(excelSettlementInfo.getDataList()), ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(), "Excel 结算信息为空");

        // 删除第一行数据（excel 中第二行，对列的解释）
        excelFeeInfo.getDataList().remove(0);
        excelAccumulatedInfo.getDataList().remove(0);
        excelSettlementInfo.getDataList().remove(0);

        // 填入 Excel 中不带的公共字段
        excelFeeInfo.getDataList().forEach(item -> BeanUtils.copyProperties(context,item));

        // 4. 费用信息、手续费信息、结算费用写入上下文
        context.setFeeInfo(excelFeeInfo);
        context.setAccumulatedInfo(excelAccumulatedInfo);
        context.setSettlementInfo(excelSettlementInfo);
    }

    /**
     * Excel 数据校验
     */
    @Override
    public void valid(InstContractContext context) {
        // 对数据行判空，没有数据抛出异常
        if(context.getFeeInfo().getDataList().isEmpty()){
            throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(),"Excel 数据不允许为空");
        }

        // 校验 Excel 内容, 校验失败信息包装到返回值中
        Map<String, ExportErrorInfo> errorInfoMap = new HashMap<>(16);
        errorInfoMap.put(InstContractExcelEnum.INST_CONTRACT_FEE_PAYIN.getExcelSheetName(), commonExcelValid.valid(context.getFeeInfo()).getErrorInfo());
        errorInfoMap.put(InstContractExcelEnum.INST_CONTRACT_SETTLEMENT_PAYIN.getExcelSheetName(), commonExcelValid.valid(context.getSettlementInfo()).getErrorInfo());
        // 根据校验结果判断有无校验错误
        context.setIsHasError(errorInfoMap.values().stream().anyMatch(Objects::nonNull));
        context.setErrorInfoMap(errorInfoMap);
        log.info(context.getIsHasError());
    }

    /**
     * 数据处理
     */
    @Override
    public InstContractImportDTO dataHandle(InstContractContext context) {

        ExcelUtil.ExcelParseInfo<InstContractFeePayinDTO> excelFeeInfo = (ExcelUtil.ExcelParseInfo<InstContractFeePayinDTO>) context.getFeeInfo();
        ExcelUtil.ExcelParseInfo<AccumulatedInfoDTO> excelAccumulatedInfo = context.getAccumulatedInfo();
        ExcelUtil.ExcelParseInfo<SettleInfoDTO> excelSettlementInfo = context.getSettlementInfo();

        // 1. 机构合同解析结果去重（简单去重，后续可完善去重逻辑）
        List<InstContractFeePayinDTO> instContractFeeList = excelFeeInfo.getDataList().stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InstContractFeePayinDTO::getInstContractKey))), ArrayList::new));

        // 2. 将解析到的手续费信息和业务唯一键进行对应，然后根据业务唯一键分组
        // 2.1 将业务唯一键和 Excel 所在行进行关联
        Map<Integer, String> excelRowBizKeyMap = excelFeeInfo.getDataList().stream()
                .collect(Collectors.toMap(InstContractFeePayinDTO::getExcelRowNo, InstContractFeePayinDTO::getInstContractKey));
        // 2.2 根据所在行对关联手续费和业务唯一键
        excelAccumulatedInfo.getDataList().forEach(item -> item.setInstContractKey(excelRowBizKeyMap.get(item.getExcelRowNo())));
        // 2.3 手续费分组
        Map<String, List<AccumulatedInfoDTO>> accumulatedInfoGroupByBizKey = excelAccumulatedInfo.getDataList().stream()
                .collect(Collectors.groupingBy(AccumulatedInfoDTO::getInstContractKey));

        // 3. 根据 结算信息唯一标识 对结算信息进行分组
        Map<String, SettleInfoDTO> settleInfoMapByProduct = excelSettlementInfo.getDataList().stream().collect(Collectors.toMap(SettleInfoDTO::getSettleKey,item->item,
                (s1,s2)->{throw new BusinessException(ErrorCodeEnum.EXCEL_CONTEXT_FORMAT_EXCEPTIONS.getCode(),String.format("结算信息唯一标识重复:%s",s1.getSettleKey()));}
        ));

        // 3.4 将手续费、结算信息填充到机构合同列表，可在此处对产品的结算、手续费信息进行特殊处理
        instContractFeeList.forEach( fee -> {
            fee.setAccumulatedInfoList(accumulatedInfoGroupByBizKey.get(fee.getInstContractKey()));
            fee.setSettleInfo(settleInfoMapByProduct.get(fee.getSettleKey()));
            // 行业分类写入产品标准化信息
            fee.setStandardProductInfo(industryCategoryParse(fee.getExternalIndustryCategory()));

            // 将结算周期填入结算信息中
            fee.setSettleInfo(Optional.ofNullable(fee.getSettleInfo()).orElseGet(SettleInfoDTO::new));
            Optional.ofNullable(fee.getSettlementTerm()).ifPresent(fee.getSettleInfo()::setSettlementTerm);

            // 固定值 路透
            fee.setTargetFxSource("Reuters");
        });

        // 根据 Excel 行排序并返回结果
        InstContractImportDTO response = new InstContractImportDTO();
        BeanUtils.copyProperties(context,response);
        response.setDataList(instContractFeeList.stream().sorted(Comparator.comparingInt(InstContractFeePayinDTO::getExcelRowNo)).collect(Collectors.toList()));

        return response;
    }

    /**
     * 对行业机构进行解析填充到产品标准化信息中并返回
     */
    private StandardProductInfoDTO industryCategoryParse(String industryCategory){
        if(StringUtils.isNotBlank(industryCategory)){
            StandardProductInfoDTO standardProductInfoDTO = new StandardProductInfoDTO();
            List<StandardProductInfoDTO.InstTradeItem> tradeItemList = Arrays.stream(industryCategory.split(","))
                    .map(item -> new StandardProductInfoDTO.InstTradeItem() {{ setTradeType(item); }})
                    .collect(Collectors.toList());
            standardProductInfoDTO.setInstTradeList(tradeItemList);
            return standardProductInfoDTO;
        }
        return null;
    }

    /**
     * @param context
     * @return
     */
    @Override
    public InstContractImportDTO errorHandle(InstContractContext context) {
        // 返回解析异常信息
        InstContractImportDTO response = new InstContractImportDTO();
        BeanUtils.copyProperties(context, response);
        return response;
    }
}
