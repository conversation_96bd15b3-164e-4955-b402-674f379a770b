package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName InstTransFeeDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 23:36
 */
@Data
public class InstTransFeeDTO implements Serializable {
    private static final long serialVersionUID = 5142471648682373987L;
    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "费用分组id")
    private String feeGroupId;

    @ApiModelProperty(notes = "费用种类")
    private String feeType;

    @ApiModelProperty(notes = "行业分类")
    private String mcc;

    @ApiModelProperty(notes = "计费方式类型")
    private String calculateType;

    @ApiModelProperty(notes = "保底费用")
    private BigDecimal minFee;

    @ApiModelProperty(notes = "封顶费用")
    private BigDecimal maxFee;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "费用详情")
    private String feeDetail;
}
