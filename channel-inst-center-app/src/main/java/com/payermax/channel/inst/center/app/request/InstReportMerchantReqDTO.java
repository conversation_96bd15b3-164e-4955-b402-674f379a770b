package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 机构商户报备DTO
 *
 * <AUTHOR>
 * @date 2022/6/4 17:29
 */
@Data
public class InstReportMerchantReqDTO implements Serializable {

    private static final long serialVersionUID = -6424806449259779388L;

    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String isNeedReportMerchant;

    private String reportRequire;

    private String reportType;

    private String reportProcessTime;

    private String reportTemplate;
}
