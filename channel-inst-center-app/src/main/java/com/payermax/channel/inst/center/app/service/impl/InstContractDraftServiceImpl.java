package com.payermax.channel.inst.center.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractDraftAssembler;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayinDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeePayoutDTO;
import com.payermax.channel.inst.center.app.dto.impl.InstContractFeeVaDTO;
import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.request.InstContractInitRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractInitResponseVo;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.app.service.InstContractDraftService;
import com.payermax.channel.inst.center.app.service.SysUserPermissionService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ActionType;
import com.payermax.channel.inst.center.common.enums.instcenter.ContractStatusEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstProductTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.InstContractRoleEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractStandardProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.*;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-11-16 15:15:39
 */
@Slf4j
@Service
public class InstContractDraftServiceImpl implements InstContractDraftService {

    private final InstContractDraftRepository instContractDraftRepository;
    private final SysUserPermissionService sysUserPermissionService;
    private final InstContractDraftAssembler instContractDraftAssembler;
    private final InstBaseInfoRepository instBaseInfoRepository;
    private final InstContractBaseInfoRepository instContractBaseInfoRepository;
    private final InstContractVersionInfoRepository instContractVersionInfoRepository;
    private final InstContractOriginProductRepository instContractOriginProductRepository;
    private final InstContractStandardProductRepository instContractStandardProductRepository;
    private final InstContractFeeItemRepository instContractFeeItemRepository;
    private final InstContractSettlementItemRepository instContractSettlementItemRepository;

    @Autowired
    public InstContractDraftServiceImpl(InstContractDraftRepository instContractDraftRepository,
                                        SysUserPermissionService sysUserPermissionService,
                                        InstContractDraftAssembler instContractDraftAssembler,
                                        InstBaseInfoRepository instBaseInfoRepository, InstContractBaseInfoRepository instContractBaseInfoRepository, InstContractVersionInfoRepository instContractVersionInfoRepository, InstContractOriginProductRepository instContractOriginProductRepository, InstContractStandardProductRepository instContractStandardProductRepository, InstContractFeeItemRepository instContractFeeItemRepository, InstContractSettlementItemRepository instContractSettlementItemRepository){
        this.instContractDraftRepository = instContractDraftRepository;
        this.sysUserPermissionService = sysUserPermissionService;
        this.instContractDraftAssembler = instContractDraftAssembler;
        this.instBaseInfoRepository = instBaseInfoRepository;
        this.instContractBaseInfoRepository = instContractBaseInfoRepository;
        this.instContractVersionInfoRepository = instContractVersionInfoRepository;
        this.instContractOriginProductRepository = instContractOriginProductRepository;
        this.instContractStandardProductRepository = instContractStandardProductRepository;
        this.instContractFeeItemRepository = instContractFeeItemRepository;
        this.instContractSettlementItemRepository = instContractSettlementItemRepository;
    }

    /**
     * @param instContract
     */
    @Override
    public InstContractInitResponseVo initInstContract(InstContractInitRequestDTO instContract) {

        // 初始化时间
        Timestamp initTime = new Timestamp(System.currentTimeMillis());

        // 合同共同信息
        InstContractInitRequestDTO contractCommonData = new InstContractInitRequestDTO();
        BeanUtils.copyProperties(instContract,contractCommonData,"dataList");
        String extendFieldsJson = JSON.toJSONString(contractCommonData);

        // 判断产品类型，转换成对应产品类型 DTO
        List<? extends AbstractInstContractFeeDTO> dataList = data2FeeDtoList(instContract);
        List<InstContractDraft> draftList = new ArrayList<>();
        InstContractInitResponseVo response = new InstContractInitResponseVo();

        // 产品列表转换成草稿列表
        List<InstContractInitResponseVo.FailRecord> failRecordList = new ArrayList<>();
        dataList.forEach( item -> {
            // 判断是否存在并存入
            if(instContractDraftRepository.queryById(item.getInstContractKey()) == null){
                InstContractDraft draft = new InstContractDraft();
                draft.setDraftId(item.getInstContractKey());
                draft.setBusinessKey(item.getInstContractKey());
                draft.setInstCode(item.getInstCode());
                draft.setInstProductName(item.getInstProductName());
                draft.setContractEntity(item.getContractEntity());
                draft.setEffectiveTime(item.getEffectiveTime());
                draft.setDraftData(JSON.toJSONString(item));
                draft.setExtendFields(extendFieldsJson);
                draft.setContractNo(contractCommonData.getContractNo());
                draft.setOwner(contractCommonData.getOwner());
                draft.setBusinessType(contractCommonData.getProductType());
                draft.setActionType(ContractStatusEnum.INIT.name());
                draft.setStatus(ActionType.UN_STANDARDIZED.name());
                draft.setUtcModified(initTime);
                draft.setUtcCreate(initTime);
                draftList.add(draft);
            }else{
                InstContractInitResponseVo.FailRecord record = new InstContractInitResponseVo.FailRecord();
                record.setBizKey(item.getInstContractKey());
                record.setReason("业务唯一键重复");
                record.setMessage("业务唯一键重复");
                failRecordList.add(record);
            }
        });
        // 批量保存
        boolean saveBatchRes = instContractDraftRepository.saveBatch(draftList);
        if(saveBatchRes){
            response.setSavedList(draftList.stream().map(InstContractDraft::getDraftId).collect(Collectors.toList()));
        }
        response.setSaveFailList(failRecordList);
        return response;
    }


    /**
     * 将传入的产品列表转换成产品对应 DTO
     */
    private List<? extends AbstractInstContractFeeDTO> data2FeeDtoList(InstContractInitRequestDTO instContract){
        // Payout
        if(instContract.getProductType().equals(InstProductTypeEnum.PAYOUT.getValue())){
            return instContract.getDataList().stream()
                    .map(item -> JSON.parseObject(JSON.toJSONString(item), InstContractFeePayoutDTO.class)).collect(Collectors.toList());
        }
        // PayIn
        if(instContract.getProductType().equals(InstProductTypeEnum.PAYIN_WITH_APMS.getValue()) || instContract.getProductType().equals(InstProductTypeEnum.TECH_SERVICE.getValue())){
            return instContract.getDataList().stream()
                    .map(item -> JSON.parseObject(JSON.toJSONString(item), InstContractFeePayinDTO.class)).collect(Collectors.toList());
        }
        // VA
        if(instContract.getProductType().equals(InstProductTypeEnum.VA.getValue())){
            return instContract.getDataList().stream()
                    .map(item -> JSON.parseObject(JSON.toJSONString(item), InstContractFeeVaDTO.class)).collect(Collectors.toList());
        }
        throw new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(),"暂不支持的产品类型");
    }

    /**
     * 根据 shareId 查询草稿列表
     * 根据查询人过滤,财务BP/PD 可以看到所有草稿,GP只能看到负责机构的草稿)
     * @param shareId
     */
    public List<InstProductItemVO> queryList(String shareId) {
        // 2. 根据 ShareId 判断用户角色查询草稿, 并包装成 VO 返回，避免敏感信息泄露
        //     财务 BP、PD 可以查看所有草稿，BD 只能查看负责机构的草稿
        // 2.1 管理员、财务 BP、清结算人员、PD、外汇人员 可以查询所有草稿
        if(sysUserPermissionService.hasPermissions(shareId, InstContractRoleEnum.INST_CONTRACT_ADMIN)
                || sysUserPermissionService.hasPermissions(shareId,InstContractRoleEnum.FINANCIAL_BP)
                || sysUserPermissionService.hasPermissions(shareId,InstContractRoleEnum.SETTLEMENT)
                || sysUserPermissionService.hasPermissions(shareId,InstContractRoleEnum.PD)
                || sysUserPermissionService.hasPermissions(shareId, InstContractRoleEnum.FX)){
            return instContractDraftRepository.queryAll().stream()
                    .map(instContractDraftAssembler::draft2ProductItem).collect(Collectors.toList());
        }else if(sysUserPermissionService.hasPermissions(shareId,InstContractRoleEnum.BD)){
            //2.2 查询 BD 负责的机构列表
            List<String> instCodeList = instBaseInfoRepository.queryInstByBd(shareId);
            return instContractDraftRepository.queryByInstCode(instCodeList).stream()
                    .map(instContractDraftAssembler::draft2ProductItem).collect(Collectors.toList());
        }
        // 没有对应角色则抛出异常
        throw new BusinessException(ErrorCodeEnum.BUSINESS_SCENE_NOT_SUPPORTED.getCode(),"当前用户无权限");
    }

    /**
     * 根据条件查询草稿列表
     * @param draft 草稿条件
     * @param start 开始时间
     * @param end 结束时间
     */
    @Override
    public List<InstProductItemVO> queryByConditions(InstContractDraft draft, Date start, Date end) {
        // 查询后转换成 VO 返回，避免敏感信息泄露
        return instContractDraftRepository.queryByConditionsAndCreateTime(draft,start,end).stream()
                .map(instContractDraftAssembler::draft2ProductItem)
                .sorted(Comparator.comparing(InstProductItemVO::getUtcCreate).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 将机构产品解析结果入库
     *
     * @param context 解析上下文
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean draftParseResultSaving(InstProductParseContext context) {
        // 保存前判断 isNewlyCreated，防止重复保存
        Optional.of(context.getInstContractBaseInfo()).filter(InstContractBaseInfoPO::isNewlyCreated).ifPresent(instContractBaseInfoRepository::save);
        Optional.of(context.getInstContractVersionInfo()).filter(InstContractVersionInfoPO::isNewlyCreated).ifPresent(instContractVersionInfoRepository::save);
        Optional.of(context.getOriginProduct()).filter(InstContractOriginProductPO::isNewlyCreated).ifPresent(instContractOriginProductRepository::save);
        context.getStandardProductList().parallelStream().filter(InstContractStandardProductPO::isNewlyCreated).forEach(instContractStandardProductRepository::save);
        instContractFeeItemRepository.saveBatch(context.getFeeItemList());
        Optional.ofNullable(context.getSettlementItem()).ifPresent(instContractSettlementItemRepository::saveIgnoreDuplicate);
        return true;
    }

}
