package com.payermax.channel.inst.center.app.status;

import lombok.Data;

/**
 * 状态机请求
 *
 * <AUTHOR>
 * @date 2021/8/16 15:02
 */
@Data
public class StateRequest<E, D> {

    public StateRequest() {
    }

    public StateRequest(Integer originalStatus, E event, D data) {
        this.originalStatus = originalStatus;
        this.event = event;
        this.data = data;
    }

    /**
     * original status
     **/
    private Integer originalStatus;

    /**
     * event
     */
    private E event;

    /**
     * the data present by user-defined
     */
    private D data;

}
