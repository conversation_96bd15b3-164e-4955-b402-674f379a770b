package com.payermax.channel.inst.center.app.manage.impl;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountKeyEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstAccountKeyQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAccountDao;
import com.payermax.channel.inst.center.infrastructure.client.FinSecurityClientProxy;
import com.payermax.channel.inst.center.app.manage.InstAccountKeyManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstAccountKeyReqDTO;
import com.payermax.channel.inst.center.app.request.InstAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstAccountKeyQueryVO;
import com.payermax.channel.inst.center.app.service.InstAccountKeyService;
import com.ushareit.fintech.security.entity.req.EncryptRequest;
import com.ushareit.fintech.security.entity.resp.EncryptResponse;
import com.ushareit.fintech.security.entity.resp.SecurityResponse;
import com.ushareit.fintech.security.enums.EncryptTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName InstAccountKeyManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 21:17
 */
@Service
@Slf4j
public class InstAccountKeyManagerImpl implements InstAccountKeyManager {

    @Autowired
    private InstAccountDao instAccountDao;

    @Autowired
    private InstAccountKeyService instAccountKeyService;

    @Autowired
    private FinSecurityClientProxy finSecurityClientProxy;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;


    @Override
    public List<InstAccountKeyQueryVO> queryMidKeys(InstAccountReqDTO instAccountReqDTO) {
        InstAccountKeyQueryEntity entity = new InstAccountKeyQueryEntity();
        entity.setRequirementOrderId(instAccountReqDTO.getRequirementOrderId());
        entity.setInstId(instAccountReqDTO.getInstId());
        List<InstAccountKeyQueryEntity> instAccountKeyQueryEntities = instAccountKeyService.queryList(entity);
        List<InstAccountKeyQueryVO> instAccountKeyQueryVOS = respVoAssembler.toInstAccountKeyQueryVOList(instAccountKeyQueryEntities);
        if(CollectionUtils.isNotEmpty(instAccountKeyQueryVOS)){
            List<String> keyValues = instAccountKeyQueryVOS.stream().filter(obj-> StringUtils.isNotBlank(obj.getKeyValue())).map(InstAccountKeyQueryVO::getKeyValue).distinct().collect(Collectors.toList());
            List<SecurityResponse> securityResponses = finSecurityClientProxy.decryptBatch(keyValues);
            if(CollectionUtils.isNotEmpty(securityResponses)){
                Map<String, SecurityResponse> responseMap = securityResponses.stream().collect(Collectors.toMap(SecurityResponse::getToken, Function.identity()));
                instAccountKeyQueryVOS = instAccountKeyQueryVOS.stream().map(obj->{
                    if(StringUtils.isNotBlank(obj.getKeyValue())){
                        SecurityResponse securityResponse = responseMap.get(obj.getKeyValue());
                        if(securityResponse != null){
                            obj.setKeyValue(StringUtils.defaultIfBlank(securityResponse.getValue(),obj.getKeyValue()));
                        }
                    }
                    return obj;
                }).collect(Collectors.toList());
            }
        }
        return instAccountKeyQueryVOS;
    }

    @Override
    public int saveBatch(InstAccountReqDTO instAccountReqDTO) {
        InstAccountEntity entity = new InstAccountEntity();
        entity.setId(instAccountReqDTO.getId());
        List<InstAccountEntity> instAccountEntities = instAccountDao.selectAll(entity);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instAccountEntities), "inst account is not exists");
        List<InstAccountKeyEntity> instAccountKeyEntities = reqDtoAssembler.toInstAccountKeyEntities(instAccountReqDTO.getInstAccountKeyReqDTOList());
        List<EncryptRequest> encryptRequestList = instAccountKeyEntities.stream().map(obj -> {
            EncryptRequest encryptRequest = new EncryptRequest();
            encryptRequest.setName(SecureUtil.md5(obj.getKeyType()+obj.getKeyValue()+obj.getEncryptType()));
            encryptRequest.setValue(obj.getKeyValue());
            encryptRequest.setEncryptType(EncryptTypeEnum.PASSWORD);
            return encryptRequest;
        }).collect(Collectors.toList());
        List<EncryptResponse> encryptResponseList = finSecurityClientProxy.encryptBatch(encryptRequestList);
        int result = 0;
        if(CollectionUtils.isNotEmpty(encryptResponseList)){
            Map<String, EncryptResponse> encryptResponseMap = encryptResponseList.stream().collect(Collectors.toMap(EncryptResponse::getName, Function.identity()));
            instAccountKeyEntities = instAccountKeyEntities.stream().map(obj -> {
                EncryptResponse encryptResponse = encryptResponseMap.get(SecureUtil.md5(obj.getKeyType() + obj.getKeyValue() + obj.getEncryptType()));
                obj.setKeyValue(encryptResponse.getToken());
                return obj;
            }).collect(Collectors.toList());
            result = instAccountKeyService.saveBatch(instAccountKeyEntities);
        }
        return result;
    }

    @Override
    public int modifyMidKey(InstAccountKeyReqDTO instAccountKeyReqDTO) {
        InstAccountKeyEntity record = reqDtoAssembler.toInstAccountKeyEntity(instAccountKeyReqDTO);
        EncryptRequest encryptRequest = new EncryptRequest();
        encryptRequest.setName(SecureUtil.md5(instAccountKeyReqDTO.getKeyType()+instAccountKeyReqDTO.getKeyValue()+instAccountKeyReqDTO.getEncryptType()));
        encryptRequest.setValue(instAccountKeyReqDTO.getKeyValue());
        encryptRequest.setEncryptType(EncryptTypeEnum.PASSWORD);
        EncryptResponse encryptResponse = finSecurityClientProxy.encrypt(encryptRequest);
        int result = 0;
        if(encryptResponse != null){
            record.setKeyValue(encryptResponse.getToken());
            result = instAccountKeyService.update(record);
        }
        return result;
    }

    @Override
    public int deleteMidKey(Long id) {
        int result = instAccountKeyService.delete(id);
        return result;
    }
}
