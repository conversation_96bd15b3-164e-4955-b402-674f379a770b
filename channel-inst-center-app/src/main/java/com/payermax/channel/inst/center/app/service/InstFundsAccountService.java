package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAccountPo;

import java.util.List;


/**
 * 机构资金账号Service
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstFundsAccountService {

    /**
     * 查询资金账号
     *
     * @param instFundsAccountQueryEntity
     * @return
     */
    InstFundsAccountEntity queryById(InstFundsAccountQueryEntity instFundsAccountQueryEntity);

    /**
     * 查询资金账号列表
     *
     * @param accountQueryEntity
     * @return
     */
    List<InstFundsAccountEntity> queryListByQueryEntity(InstFundsAccountQueryEntity accountQueryEntity);

    /**
     * 查询资金账号 通过唯一索引（instCode和accountNo）
     *
     * @param accountQueryEntity
     * @return
     */
    List<InstFundsAccountEntity> queryByAccountNoAndInstCode(InstFundsAccountQueryEntity accountQueryEntity);


    /**
     * 根据账户别名查询账户信息
     * @param accountAlias 账户别名
     * @return
     */
    List<InstFundsAccountPo> queryDetailByAccountAlias(String accountAlias);


    /**
     * 根据账户 ID 查询账户信息
     *
     * @param accountId 账户ID
     */
    InstFundsAccountPo queryDetailByAccountId(String accountId);

    /**
     * 查询资金账号及其符合条件的子级账号
     *
     * @param queryEntity
     * @return
     */
    List<InstFundsAccountEntity> queryAccountDetailAndSubList(InstFundsAccountAndSubQueryEntity queryEntity);
}
