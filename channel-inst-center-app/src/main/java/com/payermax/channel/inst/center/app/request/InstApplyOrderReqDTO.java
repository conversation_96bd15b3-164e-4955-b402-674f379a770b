package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName InstApplyOrderReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 23:22
 */
@Data
public class InstApplyOrderReqDTO implements Serializable{

    private static final long serialVersionUID = -2136821838907982689L;

    private long pageNum;

    private long pageSize;

    /**
     * 机构信息
     */
    @Valid
    @NotNull(message = "inst info cannot be null")
    private InstBaseInfoReqDTO instBaseInfoReqDTO;

    /**
     * 机构品牌信息
     */
    @Valid
    @NotNull(message = "brand info cannot be null")
    private InstBrandReqDTO instBrandReqDTO;

    /**
     * 机构DD信息
     */
    //@Valid
    @NotNull(message = "dd info cannot be null")
    private InstDdReqDTO instDdReqDTO;

    /**
     * 申请类型
     */
    @NotBlank(message = "applyType cannot be null")
    private String applyType;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 机构品牌
     */
    @NotBlank(message = "brandName cannot be null")
    private String brandName;

    /**
     * 机构简称
     */
    @NotBlank(message = "instName cannot be null")
    private String instName;

    /**
     * 我司主体
     */
    private String shareitEntity;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 支付方式类型
     */
    private String paymentMethodTypes;

    /**
     * 接入产品
     */
    private String products;

    /**
     * 接入国家/地区
     */
    private String countrys;

    /**
     * 接入价值
     */
    private String reason;

    /**
     * 渠道BD
     */
    private String bdId;

    /**
     * 渠道AM
     */
    private String amId;

    /**
     * 渠道BD集合
     */
    private List<String> bdList;

    /**
     * 渠道AM集合
     */
    private List<String> amList;

    /**
     * BD和AM
     */
    private String dbAndAmId;

    /**
     * 合作模式
     */
    private String cooperationMode;

    /**
     * 接入阶段
     */
    private String phase;

    /**
     * 接入优先级
     */
    private String priority;

    /**
     * 申请时间
     */
    private Date utcCreate;

    /**
     * 期望投产日期
     */
    private Date expectReleaseTime;

    /**
     * 申请日期范围
     */
    private Date utcCreateFrom;
    private Date utcCreateTo;

    /**
     * 期望投产日期范围
     */
    private Date expectReleaseTimeFrom;
    private Date expectReleaseTimeTo;

    //*************************************
    /**
     * 接入国家
     */
    private List<String> accessCountrys;

    /**
     * 接入产品
     */
    private List<AccessProduct> accessProducts;

    /**
     * 包含的阶段以及状态 {"NDA":["COMPLETE"]}
     */
    private Map<String, List<String>> includeStageStatusMap;
    /**
     * 不包含的阶段以及状态 {"NDA":["COMPLETE"]}
     */
    private Map<String, List<String>> excludeStageStatusMap;

    @Data
    public static class AccessProduct implements Serializable{
        private String channelType;
        private List<String> paymentMethodTypes;
    }

    /**
     * 机构类型
     */
    private List<String> instTypes;

    /**
     * 申请单状态
     */
    private String status;

    private List<String> applyNos;
}
