package com.payermax.channel.inst.center.app.event;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 机构号段事件
 *
 * <AUTHOR> at 2022/10/26 13:50
 **/
@Getter
public class InstSubNumberSegmentAlertEvent extends ApplicationEvent {

    /**
     * 查询到的号段记录(不超过500)
     **/
    private List<InstSubNumberSegmentEntity> subNumberSegmentEntityList;

    /**
     * 号段机构信息
     **/
    private InstFundsAccountEntity instFundsAccountEntity;

    public InstSubNumberSegmentAlertEvent(Object source, InstFundsAccountEntity instFundsAccountEntity, List<InstSubNumberSegmentEntity> subNumberSegmentEntityList) {
        super(source);
        this.instFundsAccountEntity = instFundsAccountEntity;
        this.subNumberSegmentEntityList = subNumberSegmentEntityList;
    }
}
