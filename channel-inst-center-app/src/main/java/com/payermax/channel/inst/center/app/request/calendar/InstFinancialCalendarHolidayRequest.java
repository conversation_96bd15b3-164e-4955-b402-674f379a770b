package com.payermax.channel.inst.center.app.request.calendar;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Data
public class InstFinancialCalendarHolidayRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 节假日ID
     */
    private Long holidayId;

    /**
     * 日历ID
     */
    private String calendarId;

    /**
     * 节假日日期
     */
    private LocalDate holidayDate;

    /**
     * 当天为星期几
     */
    private String dayOfWeek;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;
}
