package com.payermax.channel.inst.center.app.manage.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.app.manage.InstContractProductManager;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.response.InstContractProductVO;
import com.payermax.channel.inst.center.app.service.InstContractProductService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstContractProductManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/14 15:28
 */
@Service
public class InstContractProductManagerImpl implements InstContractProductManager {

    @Autowired
    private InstContractProductService instContractProductService;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public List<InstContractProductVO> getInstContractProduct(String productCode, String version) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "productCode is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(version), "version is mandatory");
        InstContractProductEntity entity = new InstContractProductEntity();
        entity.setInstProductCode(productCode);
        entity.setVersion(version);
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(entity);
        if(CollectionUtils.isNotEmpty(instContractProductEntities)){
            List<InstContractProductVO> instContractProductVOs = respVoAssembler.toInstContractProductVos(instContractProductEntities);
            return instContractProductVOs;
        }
        return null;
    }
}
