package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.model.contract.desensitizer.SensitiveType;
import com.payermax.channel.inst.center.common.enums.instcontract.InstContractRoleEnum;

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @DESC
 */
public interface SysUserPermissionService {


    /**
     * 根据 shareId 和 角色判断用户是否有权限
     * @param shareId
     * @param roleEnum
     */
    Boolean hasPermissions(String shareId, InstContractRoleEnum roleEnum);

    /**
     * 根据 shardID 判断是否拥有费用信息权限
     * @param shareId
     * @param sensitiveType
     */
    Boolean hasValuePermission(String shareId, SensitiveType sensitiveType);

}
