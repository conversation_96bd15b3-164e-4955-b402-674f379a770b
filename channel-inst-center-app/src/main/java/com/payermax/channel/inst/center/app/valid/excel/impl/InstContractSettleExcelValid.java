package com.payermax.channel.inst.center.app.valid.excel.impl;

import com.payermax.channel.inst.center.app.config.InstContractSettleDateParseConfig;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.dto.common.ValidatorUtils;
import com.payermax.channel.inst.center.app.request.InstContractImportRequestDTO;
import com.payermax.channel.inst.center.app.valid.excel.ExcelValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * InstContractSettleExcelValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class InstContractSettleExcelValid implements ExcelValid<InstContractSettleRowItemDTO, InstContractImportRequestDTO> {

    @Resource
    private InstContractSettleDateParseConfig instContractSettleDateParseConfig;

    @Override
    public Result<InstContractSettleRowItemDTO> valid(ExcelUtil.ExcelParseInfo<InstContractSettleRowItemDTO> excelParseInfo) {
        Result<InstContractSettleRowItemDTO> result = new Result<InstContractSettleRowItemDTO>().setSuccess(Boolean.TRUE).setData(excelParseInfo);
        if(Objects.isNull(excelParseInfo) || CollectionUtils.isEmpty(excelParseInfo.getDataList())) {
            return result;
        }
        ExportErrorInfo exportErrorInfo = ExportErrorInfo.builder().exportErrorInfoItems(new ArrayList<>()).build();
        excelParseInfo.getDataList().forEach(item -> {
            List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItemList = ValidatorUtils.validateItemAll(item, excelParseInfo.getHeaderMap());
            if(!CollectionUtils.isEmpty(errorInfoItemList)) {
                exportErrorInfo.setHasErrors(true);
                exportErrorInfo.getExportErrorInfoItems().addAll(errorInfoItemList);
            }
            if(CollectionUtils.isEmpty(item.getSettleDateInfoList())) {
                return;
            }
            item.getSettleDateInfoList().forEach(settleDateInfo -> {
                if(Objects.isNull(settleDateInfo)) {
                    return;
                }
                Set<ConstraintViolation<InstContractSettleRowItemDTO.SettleDateInfo>> constraintViolationSet = ValidatorUtils.valid(settleDateInfo);
                if(CollectionUtils.isEmpty(constraintViolationSet)) {
                    return;
                }
                constraintViolationSet.forEach(constraintViolation -> {
                    exportErrorInfo.setHasErrors(true);
                    exportErrorInfo.getExportErrorInfoItems().add(buildSettleErrorInfo(constraintViolation, item.getExcelRowNo(), excelParseInfo.getLang()));
                });
            });
            item.setSettleDateInfoList(item.getSettleDateInfoList().stream().filter(settleDateInfo -> Objects.nonNull(settleDateInfo)).collect(Collectors.toList()));
        });
        if(exportErrorInfo.isHasErrors()) {
            result.setSuccess(Boolean.FALSE).setErrorInfo(exportErrorInfo);
        }
        return result;
    }

    private ExportErrorInfo.ExportErrorInfoItem buildSettleErrorInfo(ConstraintViolation<InstContractSettleRowItemDTO.SettleDateInfo> constraintViolation, Integer rowNo, LangEnum langEnum) {
        ExportErrorInfo.ExportErrorInfoItem error = new ExportErrorInfo.ExportErrorInfoItem();
        String fieldName = constraintViolation.getPropertyPath().toString();
        error.setColumnName(instContractSettleDateParseConfig.getFieldMapping().get(LangEnum.code(langEnum)).get(fieldName));
        error.setRowNo(rowNo);
        error.setCellValue(constraintViolation.getInvalidValue());
        error.setErrorMsg(constraintViolation.getMessage());
        return error;
    }
}
