package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * InstContractImportRequestDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class InstContractImportRequestDTO {

    /**
     * 国际化标识
     */
    private String lang;

    /**
     * 合同导入文件地址
     */
    @NotBlank(message = "[contractFileUrl] is mandatory")
    private String contractFileUrl;
}
