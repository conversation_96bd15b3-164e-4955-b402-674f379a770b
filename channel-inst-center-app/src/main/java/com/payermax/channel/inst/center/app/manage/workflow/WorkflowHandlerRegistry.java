package com.payermax.channel.inst.center.app.manage.workflow;

import com.payermax.channel.inst.center.facade.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WorkflowHandlerRegistry implements ApplicationContextAware {

    private static final ConcurrentHashMap<String, AbstractWorkflowHandler> HANDLER_MAP = new ConcurrentHashMap<>();

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void registerHandlers() {
        Map<String, AbstractWorkflowHandler> beans = applicationContext.getBeansOfType(AbstractWorkflowHandler.class);
        log.info("Registering callback handlers...");
        log.info("Found {} workflow callback handlers.", beans.size());
        for (AbstractWorkflowHandler handler : beans.values()) {
            WorkflowCallbackHandler annotation = handler.getClass().getAnnotation(WorkflowCallbackHandler.class);
            String handlerName = getHandlerName(annotation.businessType().name(), annotation.moduleName().name(), annotation.actionType().name());
            // 注册回调处理器
            log.info("Registering callback handler: {}", handlerName);
            registerHandler(handlerName, handler);
            log.info("Callback handler registered successfully.");
        }
        log.info("All callback handlers registered successfully.");
        getHandlerCount();
    }


    /**
     * 扫描指定包下的所有类，并获取回调处理器的数量
     */
    public void getHandlerCount() {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AnnotationTypeFilter(WorkflowCallbackHandler.class));
        // 扫描指定包下的所有类
        log.info("Scanning for workflow callback handlers...");
        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents("com.payermax.channel.inst.center.app.manage");
        log.info("Found {} callback handlers. Registered {} handlers.", beanDefinitions.size(), HANDLER_MAP.size());
        log.info("Registered handlers: {}", HANDLER_MAP.keySet());
    }

    /**
     * 注册回调处理器
     *
     * @param handleName 回调处理器名称
     * @param handler 回调处理器
     */
    public void registerHandler(String handleName, AbstractWorkflowHandler handler) {
        if(StringUtils.isBlank(handleName)){
            throw new RuntimeException("workflow callback module name cannot be empty");
        }else if(Objects.isNull(handler)){
            throw new RuntimeException("workflow callback handler cannot be empty");
        }else if(HANDLER_MAP.containsKey(handleName)){
            throw new RuntimeException("workflow callback handler already exists");
        }else {
            HANDLER_MAP.put(handleName, handler);
        }
    }

    /**
     * 获取指定模块的回调处理器
     * @param handleName 回调处理器名称
     * @return 回调处理器，如果不存在则返回 null
     */
    public AbstractWorkflowHandler getHandler(String handleName) {
        AbstractWorkflowHandler processor = HANDLER_MAP.get(handleName);
        AssertUtil.isTrue(Objects.nonNull(processor), "workflow callback handler not found");
        return processor;
    }

    /**
     * 获取处理器名称
     */
    public String getHandlerName(String businessType, String moduleName, String actionType){
        return String.join("-", businessType, moduleName, actionType);
    }
}
