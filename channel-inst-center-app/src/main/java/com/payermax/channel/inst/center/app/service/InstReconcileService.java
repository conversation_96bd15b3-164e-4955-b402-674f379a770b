package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstReconcileEntity;

/**
 * 机构对账Service
 *
 * <AUTHOR>
 * @date 2022/6/4 17:36
 */
public interface InstReconcileService {

    /**
     * 保存
     *
     * @param record
     * @return
     */
    int save(InstReconcileEntity record);

    /**
     * 根据申请单号查询
     *
     * @param queryEntity
     * @return
     */
    InstReconcileEntity query(InstReconcileEntity queryEntity);

}
