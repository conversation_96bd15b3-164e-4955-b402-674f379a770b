package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstContractProductDao;
import com.payermax.channel.inst.center.app.service.InstContractProductService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName InstContractProductServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/28 0:50
 */
@Service
public class InstContractProductServiceImpl implements InstContractProductService {

    @Autowired
    private InstContractProductDao instContractProductDao;

    @Override
    public int saveBatch(List<InstContractProductEntity> records) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(records), "param records is mandatory");
        int result = instContractProductDao.insertBatch(records);
        return result;
    }

    @Override
    public List<InstContractProductEntity> getByContractNo(String contractNo) {
        Preconditions.checkArgument(StringUtils.isNotBlank(contractNo), "param contractNo is mandatory");
        InstContractProductEntity instContractProductEntity = new InstContractProductEntity();
        instContractProductEntity.setContractNo(contractNo);
        List<InstContractProductEntity> instContractProductEntities = instContractProductDao.selectAll(instContractProductEntity);
        return instContractProductEntities;
    }

    @Override
    public InstContractProductEntity selectByProductCodeAndCapabilityCode(String productCode, String capabilityCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(capabilityCode), "param capabilityCode is mandatory");
        InstContractProductEntity instContractProductEntity = instContractProductDao.selectByProductCodeAndCapabilityCode(productCode,capabilityCode);
        return instContractProductEntity;
    }

    @Override
    public List<InstContractProductEntity> selectByProductCodeAndCapabilityCodes(String productCode, List<String> capabilityCodes) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        List<InstContractProductEntity> instContractProductEntities = instContractProductDao.selectByProductCodeAndCapabilityCodes(productCode,capabilityCodes);
        return instContractProductEntities;
    }

    @Override
    public List<InstContractProductEntity> selectByEntity(InstContractProductEntity entity) {
        Preconditions.checkArgument(entity != null, "param entity is mandatory");
        List<InstContractProductEntity> instContractProductEntities = instContractProductDao.selectAll(entity);
        return instContractProductEntities;
    }

    @Override
    public int deleteByProductCodeAndCapabilityCode(String productCode, List<String> capabilityCodes) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        int result = instContractProductDao.deleteByProductCodeAndCapabilityCode(productCode,capabilityCodes);
        return result;
    }

    @Override
    public int deleteByProductCodeAndVersion(String productCode, String version) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(version), "param version is mandatory");
        int result = instContractProductDao.deleteByProductCodeAndVersion(productCode,version);
        return result;
    }

    @Override
    public int updateFeeGroupId(String contractNo, String productCode, List<String> capabilityCodes,String feeGroupId) {
        Preconditions.checkArgument(StringUtils.isNotBlank(contractNo), "param contractNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(capabilityCodes), "param capabilityCodes is mandatory");
        int result = instContractProductDao.updateFeeGroupId(contractNo, productCode, capabilityCodes, feeGroupId);
        return result;
    }

    @Override
    public int deleteByProductCode(String productCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(productCode), "param productCode is mandatory");
        int result = instContractProductDao.deleteByProductCode(productCode);
        return result;
    }
}
