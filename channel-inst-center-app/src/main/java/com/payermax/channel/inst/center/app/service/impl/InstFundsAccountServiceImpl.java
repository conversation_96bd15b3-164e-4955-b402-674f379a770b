package com.payermax.channel.inst.center.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.facade.enums.AccountExtKeyEnum;
import com.payermax.channel.inst.center.infrastructure.client.RedisClientProxy;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountExtEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountDao;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsAccountCustomMapper;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstFundsAccountMidMappingMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAccountMidMappingPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAccountPo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2022/10/8 16:50
 **/
@Service
@Log4j2
public class InstFundsAccountServiceImpl implements InstFundsAccountService {

    public static final String INST_FUNDS_ACCOUNT_PREFIX = "instFundsAccount:instFundsAccount";
    @Autowired
    InstFundsAccountDao instFundsAccountDao;
    @Resource
    RedisClientProxy redisClientProxy;

    @Resource
    private InstFundsAccountMidMappingMapper instFundsAccountMidMappingMapper;

    @Resource
    InstFundsAccountCustomMapper mapper;


    @Override
    public InstFundsAccountEntity queryById(InstFundsAccountQueryEntity instFundsAccountQueryEntity) {
        Preconditions.checkArgument(instFundsAccountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instFundsAccountQueryEntity.getAccountId()), "param accountId is mandatory");

        InstFundsAccountQueryEntity queryEntity = new InstFundsAccountQueryEntity();
        queryEntity.setAccountId(instFundsAccountQueryEntity.getAccountId());
        List<InstFundsAccountEntity> list = redisClientProxy.getAndTryAndLock(
                StringUtils.joinWith(StrPool.COLON, INST_FUNDS_ACCOUNT_PREFIX, queryEntity.getAccountId()),
                () -> instFundsAccountDao.selectByQueryEntity(queryEntity),
                10,
                TimeUnit.MINUTES,
                1,
                TimeUnit.MINUTES,
                InstFundsAccountEntity.class);

        // swiftCode取值逻辑变更
        swiftCodeValueLogicChange(list);
        // 过滤绑定商户号的账号
        filterBindMerchantNo(list, instFundsAccountQueryEntity.getMerchantNo());

        // 补充账号关联MID
        enhanceMid(list);

        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);// NO_CHECK
        }
        return null;
    }

    @Override
    public List<InstFundsAccountEntity> queryListByQueryEntity(InstFundsAccountQueryEntity accountQueryEntity) {
        // 资金账号列表
        List<InstFundsAccountEntity> list = instFundsAccountDao.selectByQueryEntity(accountQueryEntity);
        // swiftCode取值逻辑变更
        swiftCodeValueLogicChange(list);
        // 过滤绑定商户号的账号
        filterBindMerchantNo(list, accountQueryEntity.getMerchantNo());
        // 补充账号关联MID
        enhanceMid(list);
        return list;
    }

    @Override
    public List<InstFundsAccountEntity> queryByAccountNoAndInstCode(InstFundsAccountQueryEntity accountQueryEntity) {
        Preconditions.checkArgument(accountQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(accountQueryEntity.getAccountNo()), "param accountNo is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(accountQueryEntity.getInstCode()), "param instCode is mandatory");

        InstFundsAccountQueryEntity queryEntity = new InstFundsAccountQueryEntity();
        queryEntity.setAccountNo(accountQueryEntity.getAccountNo());
        queryEntity.setInstCode(accountQueryEntity.getInstCode());
        queryEntity.setCountry(accountQueryEntity.getCountry());
        queryEntity.setCurrency(accountQueryEntity.getCurrency());
        List<InstFundsAccountEntity> list = redisClientProxy.getAndTryAndLock(
                StringUtils.joinWith(StrPool.COLON, INST_FUNDS_ACCOUNT_PREFIX, queryEntity.getInstCode(), queryEntity.getAccountNo(), queryEntity.getCountry(), queryEntity.getCurrency()),
                () -> instFundsAccountDao.selectByQueryEntity(queryEntity),
                10,
                TimeUnit.MINUTES,
                1,
                TimeUnit.MINUTES,
                InstFundsAccountEntity.class);

        // swiftCode取值逻辑变更
        swiftCodeValueLogicChange(list);
        return list;
    }

    @Override
    public List<InstFundsAccountEntity> queryAccountDetailAndSubList(InstFundsAccountAndSubQueryEntity queryEntity) {
        // 资金账号列表
        List<InstFundsAccountEntity> list = instFundsAccountDao.selectAccountAndSubByRequest(queryEntity);
        // swiftCode取值逻辑变更
        swiftCodeValueLogicChange(list);
        return list;
    }

    /**
     * 根据账户别名查询账户信息
     *
     * @param accountAlias 账户别名
     * @return
     */
    @Override
    public List<InstFundsAccountPo> queryDetailByAccountAlias(String accountAlias) {
        LambdaQueryWrapper<InstFundsAccountPo> queryWrapper = Wrappers.<InstFundsAccountPo>lambdaQuery()
                .like(InstFundsAccountPo::getAccountAlias, accountAlias);
        return mapper.selectList(queryWrapper);
    }


    /**
     * 根据账户 ID 查询账户信息
     *
     * @param accountId 账户ID
     */
    @Override
    public InstFundsAccountPo queryDetailByAccountId(String accountId) {
        LambdaQueryWrapper<InstFundsAccountPo> queryWrapper = Wrappers.<InstFundsAccountPo>lambdaQuery()
                .eq(InstFundsAccountPo::getAccountId, accountId);
        return mapper.selectOne(queryWrapper);
    }

    /**
     * swiftCode取值逻辑变更
     *
     * @param accountEntity 机构主账号
     */
    public void swiftCodeValueLogicChange(List<InstFundsAccountEntity> accountEntity) {
        if (CollUtil.isEmpty(accountEntity)) {
            return;
        }

        accountEntity.forEach(entity -> {
            if (StringUtils.isNotBlank(entity.getSwiftCode())) {
                InstFundsAccountExtEntity instFundsAccountExt = new InstFundsAccountExtEntity();
                instFundsAccountExt.setClearSystem(CommonConstants.SWIFT_CLEAR_SYSTEM);
                instFundsAccountExt.setExtKey(AccountExtKeyEnum.SWIFT_CODE.getCode());
                instFundsAccountExt.setExtValue(entity.getSwiftCode());
                if (CollUtil.isEmpty(entity.getAccountExtList())) {
                    entity.setAccountExtList(ListUtil.toList(instFundsAccountExt));
                } else {
                    List<InstFundsAccountExtEntity> extEntitys = entity.getAccountExtList().stream().filter(t -> StringUtils.equals(t.getExtKey(), AccountExtKeyEnum.SWIFT_CODE.getCode())).collect(Collectors.toList());
                    if (CollUtil.isEmpty(extEntitys)) {
                        entity.getAccountExtList().add(instFundsAccountExt);
                    } else {
                        entity.getAccountExtList().forEach(t -> {
                            if (StringUtils.equals(t.getExtKey(), AccountExtKeyEnum.SWIFT_CODE.getCode())) {
                                t.setExtValue(entity.getSwiftCode());
                            }
                        });
                    }
                }
            }
        });
    }

    /**
     * 过滤绑定商户号的账号
     *
     * @param accountEntity 机构主账号
     * @param merchantNo    商户号
     */
    public void filterBindMerchantNo(List<InstFundsAccountEntity> accountEntity, String merchantNo) {
        if (CollUtil.isEmpty(accountEntity) || StringUtils.isEmpty(merchantNo)) {
            return;
        }

        accountEntity.removeIf(entity -> {
            if (Objects.nonNull(entity.getAccountJsonBo()) && CollUtil.isNotEmpty(entity.getAccountJsonBo().getBindMerchantNo())) {
                List<String> bindMerchant = entity.getAccountJsonBo().getBindMerchantNo();
                if (!bindMerchant.contains(merchantNo)) {
                    return true;
                }
            }
            return false;
        });
    }

    private void enhanceMid(List<InstFundsAccountEntity> list) {
        try {
            if (CollectionUtil.isEmpty(list)) {
                return;
            }
            List<String> ids = new ArrayList<>(list.size());
            Map<String, InstFundsAccountEntity> id2EntityMap = new HashMap<>();
            list.forEach(item -> {
                ids.add(item.getAccountId());
                id2EntityMap.put(item.getAccountId(), item);
            });
            QueryWrapper<InstFundsAccountMidMappingPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("account_id", ids);
            List<InstFundsAccountMidMappingPO> midMappingPOS = instFundsAccountMidMappingMapper.selectList(queryWrapper);
            if (!CollectionUtil.isEmpty(midMappingPOS)) {
                Map<String, Set<String>> id2MidMap = new HashMap<>();
                midMappingPOS.forEach(item -> {
                    Set<String> midList = id2MidMap.computeIfAbsent(item.getAccountId(), a -> new HashSet<>());
                    midList.add(item.getMid());
                });
                list.forEach(item -> {
                    Set<String> midList = id2MidMap.getOrDefault(item.getAccountId(), new HashSet<>());
                    item.setRelatedMidList(new ArrayList<>(midList));
                });
            }
        } catch (Exception e) {
            log.error("enhanceMid error!", e);
        }
    }
}
