package com.payermax.channel.inst.center.app.manage.contract;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.app.assembler.contract.InstContractInfoAssembler;
import com.payermax.channel.inst.center.app.dto.contract.InstContractBatchVerifyContext;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.api.InstNewInfoQueryFacade;
import com.payermax.channel.inst.center.facade.request.contract.InstContractBatchVerifyRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.response.InstContractBatchVerifyResponse;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractMidMappingCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstContractIntegrityBatchValidator {

    private final InstNewContractMidMappingCacheManager midMappingCache;
    private final InstContractInfoAssembler contractInfoAssembler;
    private final InstNewInfoQueryFacade newInfoQueryFacade;
    private final InstContractIntegrityValidator singleValidator;


    /**
     * 批量校验仅校验到币种维度
     */
    public InstContractBatchVerifyResponse validate(InstContractBatchVerifyRequest request){
        log.info("合约完整性校验-批量校验开始，校验列表数量: {}", request.getVerifyList().size());

        InstContractBatchVerifyContext context = new InstContractBatchVerifyContext()
                .setRequest(request)
                .setVerifyDataList(contractInfoAssembler.verifyItem2Dto(request.getVerifyList()))
                .setTransactionTime(System.currentTimeMillis())
                .setAllVerifyPass(Boolean.FALSE);

        // 参数校验
        paramCheck(context);

        // 合约校验
        context.getVerifyDataList().forEach(item -> paymentMethodContractValidate(item, context));

        // 组装返回结果
        composeResponse(context);

        log.info("合约完整性校验-批量校验结束");
        return context.getResponse();
    }

    /**
     * 参数校验
     */
    private void paramCheck(InstContractBatchVerifyContext context) {
        InstContractBatchVerifyRequest request = context.getRequest();
        AssertUtil.notEmpty(request.getVerifyList(), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验-批量校验列表为空");
        request.getVerifyList().forEach(item -> {
            String prefixMsg = String.format("业务类型【%s】, 机构编码【%s】，签约主体【%s】", item.getBizType(), item.getInstCode(), item.getEntity());
            log.info("合约完整性校验: {}", prefixMsg);
            AssertUtil.isTrue(StringUtils.isNotBlank(item.getInstCode()), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: instCode 为空, " + prefixMsg);
            AssertUtil.isTrue(StringUtils.isNotBlank(item.getEntity()), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: entity 为空, " + prefixMsg);
            AssertUtil.isTrue(EnumUtils.isValidEnum(ContractBizTypeEnum.class, item.getBizType()), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: bizType 必须为枚举值, " + prefixMsg);
            AssertUtil.isTrue(StringUtils.isNotBlank(item.getPaymentMethodType()), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: paymentMethodType 为空, " + prefixMsg);
            AssertUtil.isTrue(StringUtils.isNotBlank(item.getTargetOrg()) || StringUtils.isNotBlank(item.getCardOrg())
                    , ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: targetOrg 和 cardOrg 均为空, " + prefixMsg);
            AssertUtil.notEmpty(item.getMidList(), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: mid 列表为空, " + prefixMsg);
            AssertUtil.isTrue(item.getMidList().stream().allMatch(StringUtils::isNotBlank), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: mid 列表中存在空值, " + prefixMsg);
            AssertUtil.notEmpty(item.getCcyList(), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: ccy 列表为空, " + prefixMsg);
            AssertUtil.isTrue(item.getCcyList().stream().allMatch(StringUtils::isNotBlank), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "合约完整性校验: ccy 列表中存在空值, " + prefixMsg);
        });
    }

    /**
     * 支付方式维度合约校验
     */
    private void paymentMethodContractValidate(InstContractBatchVerifyContext.VerifyItemDto item, InstContractBatchVerifyContext context) {
        // MID 校验
        midValidate(item);

        // 合约校验
        contractInfoValidate(item, context);

        // 填充校验结果
        item.setAllVerifyPass(CollectionUtils.isEmpty(item.getVerifyFailMsgList()));
    }

    /**
     * 组装返回结果
     */
    private void composeResponse(InstContractBatchVerifyContext context) {
        InstContractBatchVerifyResponse response = new InstContractBatchVerifyResponse();
        // 是否全部支付方式校验通过
        boolean allVerifyPass = context.getVerifyDataList().stream().allMatch(InstContractBatchVerifyContext.VerifyItemDto::getAllVerifyPass);
        long verifyTotalCount = context.getVerifyDataList().size();
        long verifyPassCount = context.getVerifyDataList().stream().filter(InstContractBatchVerifyContext.VerifyItemDto::getAllVerifyPass).count();
        long verifyFailCount = verifyTotalCount - verifyPassCount;

        response.setAllVerifyPass(allVerifyPass)
                .setVerifyTotalCount(verifyTotalCount)
                .setVerifyPassCount(verifyPassCount)
                .setVerifyFailCount(verifyFailCount)
                .setVerifyFailMsgList(contractInfoAssembler.verifyItem2FailMsgs(context.getVerifyDataList()));
        context.setAllVerifyPass(allVerifyPass).setResponse(response);
    }

    /**
     * MID 校验
     */
    private void midValidate(InstContractBatchVerifyContext.VerifyItemDto verifyData) {
        Set<String> midSet = new HashSet<>(verifyData.getMidList());
        String bizType = verifyData.getBizType();

        // 校验 MID
        log.info("开始 MID 校验,数量: {}", midSet.size());

        // 进行有效和无效 MID 的划分
        Map<Boolean, List<String>> partitionedMid = midSet.stream()
                .collect(Collectors.partitioningBy(mid -> midMappingCache.isMidValid(mid, bizType)));
        List<String> validMidList = partitionedMid.getOrDefault(Boolean.TRUE, new ArrayList<>());
        Set<String> invalidMidSet = new HashSet<>(partitionedMid.getOrDefault(Boolean.FALSE, new ArrayList<>()));
        log.info("无效 MID 列表:{}, 有效 MID 列表:{}", invalidMidSet, validMidList);

        // 校验有效 MID 对应合同是否同一份
        Set<String> contractSet = validMidList.stream()
                .map(mid -> midMappingCache.queryMappingByMid(mid, bizType))
                .map(InstContractMidMappingPO::getContractNo)
                .collect(Collectors.toSet());

        AssertUtil.isTrue(contractSet.size() <= 1, ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode()
                , String.format("合约完整性校验-批量校验列表中 MID 对应合同不一致, bizType: %s, midList: %s", bizType, midSet));

        // 存在无效 MID 时，填充错误信息
        if(CollectionUtils.isNotEmpty(invalidMidSet)){
            verifyData.getVerifyFailMsgList().add(String.format("存在无效MID, MID 列表:%s", invalidMidSet));
        }
        verifyData.setInvalidMidList(new ArrayList<>(invalidMidSet))
                .setValidMidList(validMidList);
    }

    /**
     * 合约批量校验
     */
    private void contractInfoValidate(InstContractBatchVerifyContext.VerifyItemDto verifyData, InstContractBatchVerifyContext context) {
        String verifyMsg = String.format("业务类型:【%s】,机构:【%s】,签约主体:【%s】, 支付方式:【%s】,目标机构:【%s】,目标卡组:【%s】",
                verifyData.getBizType(),verifyData.getInstCode(), verifyData.getEntity(), verifyData.getPaymentMethodType(),
                verifyData.getTargetOrg(), verifyData.getCardOrg());

        log.info("开始合约批量校验: {}", verifyMsg);
        // 数据拆分
        List<InstInfoQueryRequest> contractQueryList = buildInstInfoQueryRequests(verifyData, context);
        // 合约校验
        List<String> verifyFailMsgList = contractQueryList.parallelStream()
                .map(item -> singleContractValidate(item, verifyMsg))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        verifyData.getVerifyFailMsgList().addAll(verifyFailMsgList);
    }

    /**
     * 单份合约校验
     */
    private String singleContractValidate(InstInfoQueryRequest item, String verifyMsg) {
        String queryMsg = String.format("%s,币种:【%s】", verifyMsg, item.getPayCurrency());
        try {
            log.info("单份合约校验: {}", queryMsg);
            Result<InstInfoQueryResponse> queryRes = newInfoQueryFacade.queryFeeInfo(item);
            if (!queryRes.isSuccess()) {
                throw new BusinessException(queryRes.getCode(), queryRes.getMsg());
            }
            singleValidator.validate(queryRes.getData());
        } catch (Exception e) {
            log.warn("单份合约校验失败: {}, 失败原因:【{}】", queryMsg, e.getMessage());
            return String.format("%s, 失败原因:【%s】", queryMsg, e.getMessage());
        }
        return null;
    }

    /**
     * 构建查询合约请求列表
     */
    private List<InstInfoQueryRequest> buildInstInfoQueryRequests(InstContractBatchVerifyContext.VerifyItemDto verifyData, InstContractBatchVerifyContext context) {

        // 合约不存在生效的 MID 时，填充错误信息并返回空列表
        if (CollectionUtils.isEmpty(verifyData.getValidMidList())) {
            verifyData.getVerifyFailMsgList().add("不存在生效的 MID");
            return Collections.emptyList();
        }

        long transactionTime = context.getTransactionTime();
        String channelMerchantCode = verifyData.getValidMidList().get(0); //CHECKED 查询合约时已校验
        List<InstInfoQueryRequest> contractQueryList = verifyData.getCcyList().stream().distinct()
                .map(ccy -> contractInfoAssembler.verifyItem2QueryRequest(verifyData, channelMerchantCode, ccy, transactionTime))
                .collect(Collectors.toList());
        log.info("支付方式批量校验，币种数量: {}, 校验合约数量: {}, 校验时间: {}", verifyData.getCcyList(), contractQueryList.size(), transactionTime);

        return contractQueryList;

    }
}
