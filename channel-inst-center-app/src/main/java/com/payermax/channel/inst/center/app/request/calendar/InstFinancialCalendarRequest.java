package com.payermax.channel.inst.center.app.request.calendar;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Data
public class InstFinancialCalendarRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日历ID
     */
    private String calendarId;

    /**
     * 日历年份
     */
    private String calendarYear;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 状态
     */
    private String status;

    /**
     * 页数
     */
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    private Integer pageSize = 10;
}
