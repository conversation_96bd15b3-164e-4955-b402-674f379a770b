package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.event.InstFunsAccountBucketsAlertEvent;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;

/**
 * 机构资金账号参数桶Manage
 *
 * <AUTHOR>
 * @date 2022/11/24 17:35
 */
public interface InstFundsAccountBucketManage {

    /**
     * 检查并填充生成子级资金账号需要使用的参数桶信息
     *
     * @param requestAccountDO
     */
    void checkInstSubFundsAccountBucketId(RequestAccountDO requestAccountDO);
    

    /**
     * 机构资金账号参数桶事件
     *
     * @param event
     */
    void onApplicationEvent(InstFunsAccountBucketsAlertEvent event);
}
