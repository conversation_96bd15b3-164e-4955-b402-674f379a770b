package com.payermax.channel.inst.center.app.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * DD调研问卷请求
 *
 * <AUTHOR>
 * @date 2022/5/18 18:10
 */
@Data
public class InstDdSurveyReqDTO implements Serializable {

    private static final long serialVersionUID = 8942905050630877268L;

    private Long id;

    private Long ddId;

    private String hasPaymentLicense;

    private String hasTransMonitor;

    private String hasDueDiligence;

    private String hasSanctionsScan;

    private String hasPciCertification;

    private Date pciCertificationDate;

    private Long surveyAttachId;

    private String remark;

}
