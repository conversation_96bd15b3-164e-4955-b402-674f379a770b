package com.payermax.channel.inst.center.app.model.contract.dataParser;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.impl.*;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import com.payermax.channel.inst.center.app.model.contract.InstContractFactory;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.InstProductTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CurrencyExchangeTimingEnumV2;
import com.payermax.channel.inst.center.common.enums.instcontract.TieredFeeTypeEnum;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractOriginProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractStandardProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOriginProductRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractStandardProductRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractVersionInfoRepository;
import com.payermax.common.lang.util.AssertUtil;
import javafx.util.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class InstProductDataParser {

    @NacosValue(value = "${excel.import.inst-contract.parse.cardPaymentMethods:CardPay}", autoRefreshed = true)
    private String cardPaymentMethods;

    private final InstContractDraftAssembler instContractDraftAssembler;
    private final InstContractBaseInfoRepository instContractBaseInfoRepository;
    private final InstContractFactory instContractFactory;
    private final ContractVersionPOAssembler contractVersionPOAssembler;
    private final InstContractVersionInfoRepository instContractVersionInfoRepository;
    private final VoucherAdapter voucherAdapter;
    private final InstContractOriginProductRepository instContractOriginProductRepository;
    private final InstContractStandardProductRepository instContractStandardProductRepository;
    private final InstFundsAccountService instFundsAccountService;
    private final FeeItemConvertUtils feeItemConvertUtils;


    /**
     * 换汇信息解析
     */
    public void fxInfoParse(InstProductParseContext context){
        InstContractFeeItemPO originFeeItem = context.getOriginFeeItem();
        // 换汇时间
        originFeeItem.setCurrencyExchangeTime(FeeItemConvertUtils.getEnumByDescOnExcel(CurrencyExchangeTimingEnumV2.class, context.getDraftDataDto().getFxTiming()));
        // 合约加点
        Optional.ofNullable(context.getDraftDataDto().getFxMarkup())
                .ifPresent(item -> originFeeItem.setContractFxSpread(ConvertUtils.convertPercentBigDecimal(item)));
        // 换算后外汇加点
        Optional.ofNullable(context.getDraftDataDto().getConvertedFxMarkup())
                .ifPresent( item -> originFeeItem.setFxSpread(ConvertUtils.convertPercentBigDecimal(item)));

    }


    /**
     * 手续费信息解析
     */
    public FeeConfig accumulatedInfoParse(InstProductParseContext context){
        // 手续费信息解析
        FeeConfig tradeConfig = instContractDraftAssembler.draft2TradeConfig((InstContractFeePayinDTO) context.getDraftDataDto());

        // 阶梯费用解析
        tradeConfig = accumulatedInfoParse(tradeConfig, context.getDraftDataDto());

        // 判断大小阶梯(单笔阶梯\累积阶梯)，填写大小阶梯标志
        String tieredFeeType = FeeItemConvertUtils.getEnumByDescOnExcel(TieredFeeTypeEnum.class, context.getDraftDataDto().getTieredFeeType());
        if (tieredFeeType != null) {
            tradeConfig.setExtendFields(ImmutableMap.of(InstFeeConfig.ExtendFieldsKey.TIERED_FEE_TYPE, tieredFeeType));
        }

        return tradeConfig;
    }

    /**
     * VA 账号费解析, 账号费无大阶梯
     */
    private FeeConfig vaAccountFeeParse(InstProductParseContext context) {
        InstContractFeeVaDTO accountFee = JSON.parseObject(context.getDraft().getDraftData(), InstContractFeeVaDTO.class).getAccountFeeInfo();
        FeeConfig feeConfig = null;
        if (Objects.nonNull(accountFee)){
            feeConfig = instContractDraftAssembler.draft2VaAccountFeeConfig(accountFee);
            // 阶梯费用解析
            feeConfig = accumulatedInfoParse(feeConfig, accountFee);
        }
        return feeConfig;
    }

    /**
     * 阶梯信息解析
     */
    private FeeConfig accumulatedInfoParse(FeeConfig feeConfig, AbstractInstContractFeeDTO dataDto){
        List<AccumulatedInfoDTO> accumulatedInfoList = Optional.ofNullable(dataDto.getAccumulatedInfoList()).orElse(Collections.emptyList());

        // 判断是否为阶梯计费，并设置算费方式，填入阶梯信息，> 1 时表示存在多个阶梯，算费方式为阶梯计费，
        if(accumulatedInfoList.size() == 1){
            feeConfig = instContractDraftAssembler.singleAccumulationFeeParse(feeConfig,accumulatedInfoList.get(0),dataDto); //CHECKED
        }
        if(accumulatedInfoList.size() > 1){
            List<FeeConfig.FeeStepCombineConfig> accumulationConfigList = accumulatedInfoList.stream()
                    .map(accumulatedInfo -> instContractDraftAssembler.accumulationInfo2StepConfig(accumulatedInfo,dataDto))
                    .collect(Collectors.toList());
            feeConfig.setStepPercentAmount(accumulationConfigList);
            feeConfig.setCalculateType(FeeCalculateTypeEnum.STEP_COMBINE.name());
        }
        return feeConfig;
    }


    /**
     * 结算信息解析
     */
    public void settleInfoParse(InstProductParseContext context){
        SettleInfoDTO settleInfo = context.getDraftDataDto().getSettleInfo();
        // 结算信息不根据 MCC 做区分
        InstContractFeeItemPO originFeeItem = context.getOriginFeeItem();
        //  结算信息列表 -> 结算费用基础信息
        InstContractSettlementItemPO settlementItem = instContractDraftAssembler.convertBaseSettleItem(originFeeItem, settleInfo, context.getOriginProduct());

        // 根据 fee_item 构造结算信息
        // Payout | VA 不存在结算费用
        // PayIn | Tech_Service: ID -> 结算周期 -> fee_config -> 账户配置
        List<String> needSettleTypes = Stream.of(InstProductTypeEnum.PAYIN_WITH_APMS, InstProductTypeEnum.PAYIN_WITH_CARD, InstProductTypeEnum.TECH_SERVICE)
                                            .map(InstProductTypeEnum::getValue).collect(Collectors.toList());
        if(needSettleTypes.contains(context.getDraftDataDto().getProductType())){
            // 根据 账户ID 查询账户信息
            InstFundsAccountPo instFundsAccountPo = Optional.ofNullable(settleInfo.getSettlementAccount()).map(instFundsAccountService::queryDetailByAccountId).orElse(new InstFundsAccountPo());
            settlementItem.setInstContractSettlementItemNo(voucherAdapter.getInstProductSettlementItemNo());
            settlementItem.setSettleDateConfig(JSON.toJSONString(Collections.singletonList(instContractDraftAssembler.settleItem2SettleDateConfig(context.getDraftDataDto().getSettleInfo()))));
            settlementItem.setSettleFeeConfig(JSON.toJSONString(instContractDraftAssembler.settleItem2SettleFeeConfig(context.getDraftDataDto().getSettleInfo())));
            settlementItem.setSettlePaymentConfig(JSON.toJSONString(instContractDraftAssembler.settleItem2SettlePaymentConfig(context.getDraftDataDto().getSettleInfo(),instFundsAccountPo.getAccountType())));

            // 将费用信息中的结算币种写入结算信息
            Optional.ofNullable(((InstContractFeePayinDTO)context.getDraftDataDto()).getSettlementCurrency()).ifPresent(settlementItem::setSettleCurrency);
            context.setSettlementItem(settlementItem);
        }
    }


    /**
     * 根据行业分类判断是否需要分成多条费用信息
     */
    public List<InstContractFeeItemPO> tradeInfoParse(InstProductParseContext context){
        List<InstContractFeeItemPO> feeItemList = new ArrayList<>();
        AbstractInstContractFeeDTO draftDataDto = context.getDraftDataDto();
        InstContractFeeItemPO originFeeItem = context.getOriginFeeItem();
        // 行业分类解析，存在多个行业分类时根据行业分类复制多条 fee_item，不存在时则使用原始费用信息
        if(CollectionUtils.isNotEmpty(draftDataDto.getStandardProductInfo().getInstTradeList())){
            feeItemList = draftDataDto.getStandardProductInfo().getInstTradeList().stream().map(trade -> {
                InstContractFeeItemPO feeItem = JSON.parseObject(JSON.toJSONString(originFeeItem), InstContractFeeItemPO.class);
                feeItem.setOriginMcc(trade.getTradeType());
                feeItem.setMccLogic(trade.getRelationType());
                feeItem.setStandardMcc(trade.getTradeCode());
                return feeItem;
            }).collect(Collectors.toList());
        }else{
            feeItemList.add(originFeeItem);
        }
        return feeItemList;
    }

    /**
     * 根据业务数据校验费用信息唯一性，防止录入多条 fee_item
     */
    public void feeItemUniqueCheck(InstProductParseContext context){
        List<InstContractFeeItemPO> feeItemList = context.getFeeItemList();

        InstContractDraft draft = context.getDraft();
        String contractEntity = draft.getContractEntity();
        String instCode = draft.getInstCode();
        String businessType = FeeItemConvertUtils.getBusinessType(draft.getBusinessType()).name();
        String originProductNo = context.getOriginProduct().getInstOriginProductNo();

        // fee_item 业务唯一键集合
        Set<String> businessKeySet = feeItemConvertUtils.getFeeItemKeySet(contractEntity,instCode, businessType, originProductNo,true);
        List<InstContractFeeItemPO> newlyFeeList = feeItemList.stream().filter(feeItem -> !businessKeySet.contains(feeItem.getFeeBusinessKey())).collect(Collectors.toList());
        List<InstContractFeeItemPO> existedFeeList = feeItemList.stream().filter(feeItem -> businessKeySet.contains(feeItem.getFeeBusinessKey())).collect(Collectors.toList());
        context.setFeeItemList(newlyFeeList);
        context.getErrorInfoMap().put("existedFeeItem",JSON.toJSONString(existedFeeList));
    }

    /**
     * 构造原始产品信息
     */
    public void originProductParse(InstProductParseContext context){
        Set<String> originProductKeys = context.getStandardProductList().stream().map(InstContractStandardProductPO::getInstOriginProductNo).collect(Collectors.toSet());
        AssertUtil.isTrue(originProductKeys.size() == 1, "error", "原始产品 NO 存在多条");
        String originProductNo = new ArrayList<>(originProductKeys).get(0); //CHECKED
        // 查询原始产品
        InstContractOriginProductPO instContractOriginProduct = instContractOriginProductRepository.queryByOriginProductNo(originProductNo);
        InstContractOriginProduct originProduct;
        // 原始产品不存在时新建原始产品
        if(Objects.nonNull(instContractOriginProduct)){
            originProduct = contractVersionPOAssembler.convertOriginProductPO2Domain(instContractOriginProduct);
        }else{
            String contractNo = context.getInstContractBaseInfo().getContractNo();
            String contractVersion = context.getInstContractVersionInfo().getContractVersion();
            String productName = context.getDraftDataDto().getInstProductName();
            originProduct = InstContractOriginProduct.builder()
                    .contractNo(contractNo)
                    .contractVersion(contractVersion)
                    .instOriginProductNo(originProductNo)
                    .instProductName(productName)
                    .newlyCreated(true)
                    .build();
        }
        context.setOriginProduct(instContractDraftAssembler.originProduct2Po(originProduct));
    }

    /**
     * 构造标准化产品信息
     */
    public void standardProductParse(InstProductParseContext context){
        String contractNo = context.getInstContractBaseInfo().getContractNo();
        String contractVersion = context.getInstContractVersionInfo().getContractVersion();
        List<StandardProductInfoDTO.PaymentItem> paymentList = context.getDraftDataDto().getStandardProductInfo().getPaymentList();

        // 技术服务类合约，需要拼接 支付方式类型， 支付方式类型 = 支付业务类型 + 支付方式类型
        if(InstProductTypeEnum.TECH_SERVICE.getValue().equals(context.getDraftDataDto().getProductType())){
            paymentList.forEach(item -> item.setPaymentType(String.join("_", item.getPaymentBusinessType(), item.getPaymentType())));
        }

        // 对支付方式及目标机构求笛卡尔积
        List<Pair<String, String>> paymentMap = paymentList.stream()
                .flatMap(payment -> Arrays.stream(payment.getTarget().split(","))
                        .map(target -> new Pair<>(payment.getPaymentType(), target)))
                .collect(Collectors.toList());

        // 生成标准化产品列表
        List<InstContractStandardProduct> standardProductList = paymentMap.stream().map(payment -> {
            String paymentMethodType = payment.getKey();
            String targetOrg = payment.getValue();
            String cardOrg = CommonConstants.CARD_PAY_CODE;
            List<String> cardPaymentMethodList = Arrays.stream(cardPaymentMethods.split(",")).collect(Collectors.toList());
            // 特定支付方式为 CardPay | 合约类型为 VA 时，需要设置目标卡组
            if(cardPaymentMethodList.contains(payment.getKey()) || InstProductTypeEnum.VA.getValue().equals(context.getDraftDataDto().getProductType())){
                targetOrg = "*";
                cardOrg = payment.getValue();
            }

            InstContractStandardProductPO standardProductPo = instContractStandardProductRepository.queryExistStandardProduct(contractNo, contractVersion, paymentMethodType, targetOrg, cardOrg);
            // 标准产品不存在时
            if (Objects.isNull(standardProductPo)) {
                return InstContractStandardProduct.builder()
                        .contractNo(contractNo)
                        .contractVersion(contractVersion)
                        .instStandardProductNo(voucherAdapter.getStandardProductCode())
                        .paymentMethodType(payment.getKey())
                        .targetOrg(targetOrg)
                        .cardOrg(cardOrg)
                        .newlyCreated(true)
                        .build();
            }
            return instContractDraftAssembler.productPo2Standard( standardProductPo);
        }).collect(Collectors.toList());

        // 向标准产品填充原始产品编号
        // 去重后的原始产品
        List<InstContractOriginProductPO> originProductList = standardProductList.stream().filter(product -> !product.isNewlyCreated())
                .map(product-> instContractOriginProductRepository.queryByOriginProductNo(product.getInstOriginProductNo()))
                .collect(Collectors.toList());
        List<String> originProductNoList = originProductList.stream().map(InstContractOriginProductPO::getInstOriginProductNo).distinct().collect(Collectors.toList());
        AssertUtil.isTrue(originProductNoList.size() <=1,"error","标准产品对应多个原始产品");
        String originProductNo = CollectionUtils.isNotEmpty(originProductNoList) ?  originProductNoList.get(0) : voucherAdapter.getOriginProductCode(); //CHECKED

        context.setStandardProductList(standardProductList.stream().map(instContractDraftAssembler::standardProduct2Po).peek(product -> product.setInstOriginProductNo(originProductNo)).collect(Collectors.toList()));
    }

    /**
     * 合同信息处理流程
     */
    public void contractProcess(InstProductParseContext context){
        InstContractDraft draft = context.getDraft();
        String businessType = draft.getBusinessType();
        String contractEntity = draft.getContractEntity();
        String instCode = draft.getInstCode();

        // 1. 获取已入库合同
        InstContractBaseInfoPO baseInfoPo = instContractBaseInfoRepository.queryActiveBaseInfo(instCode, contractEntity, FeeItemConvertUtils.getBusinessType(businessType).name(), ContractStatusEnum.ACTIVATED.name());
        InstContractBaseInfo contractBaseInfo;
        InstContractVersionInfo contractVersionInfo;
        log.info(instCode, contractEntity, businessType, ContractStatusEnum.ACTIVATED.name());

        // 2. 判断合同是否存在
        if (Objects.isNull(baseInfoPo)) {
            // 还未存在，则构造新的合同基本信息
            contractBaseInfo = instContractFactory.composeInstContractBase(instCode, contractEntity, FeeItemConvertUtils.getBusinessType(businessType));
            // v1 版本合同上下文，填入必要信息及合同生效时间
            ContractProcessByExcelContext v1Context = new ContractProcessByExcelContext(instCode, contractEntity,
                    FeeItemConvertUtils.getBusinessType(businessType), new ArrayList<>(), null, null);
            v1Context.setEffectStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(draft.getEffectiveTime().getTime()), ZoneId.systemDefault()));;
            contractVersionInfo = instContractFactory.composeInstContractNewVersion(v1Context, contractBaseInfo);
        } else {
            // 已存在，则直接获取 合同基准信息和当前版本，TODO 目前没有版本迭代逻辑，仅使用已存在版本，版本迭代逻辑后续考虑
            contractBaseInfo = contractVersionPOAssembler.convertBaseInfoDomain(baseInfoPo);
            InstContractVersionInfoPO versionInfoPo = instContractVersionInfoRepository.queryActiveVersion(contractBaseInfo.getContractNo());
            AssertUtil.notNull(versionInfoPo, "error", "contract version not exist!");
            contractVersionInfo = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoPo);
        }
        context.setInstContractBaseInfo(instContractDraftAssembler.contract2Po(contractBaseInfo));
        context.setInstContractVersionInfo(instContractDraftAssembler.contractVersion2Po(contractVersionInfo));
    }

    /**
     * 费用信息处理校验流程
     */
    public void feeItemProcess(InstProductParseContext context){
        AbstractInstContractFeeDTO draftData = context.getDraftDataDto();
        Map<String, FeeConfig> feeConfigMap = new HashMap<>(4);

        // 费用基础信息
        InstContractFeeItemPO originFeeItemPo = instContractDraftAssembler.draft2FeeItem((InstContractFeePayinDTO) draftData);
        originFeeItemPo.setInstOriginProductNo(context.getOriginProduct().getInstOriginProductNo());
        // 大阶梯时填充 accumulationKey，后期补充
        if(context.getDraftDataDto().getTieredFeeType().equals(TieredFeeTypeEnum.ACCUMULATED_VOLUME.getDescOnExcel())){
            originFeeItemPo.setAccumulationKey("TODO");
        }
        context.setOriginFeeItem(originFeeItemPo);

        // 换汇信息解析
        fxInfoParse(context);

        // 税费信息解析
        List<InstTaxConfig> taxInfoList = Optional.ofNullable(draftData.getTaxInfoList()).orElse(Collections.emptyList()).stream().map(instContractDraftAssembler::draft2TaxInfoList).collect(Collectors.toList());
        originFeeItemPo.setTaxConfig(JSON.toJSONString(taxInfoList));

        // 手续费信息解析
        feeConfigMap.put(FeeTypeEnum.TRADE.name(), accumulatedInfoParse(context));

        // 退款信息解析
        feeConfigMap.put(FeeTypeEnum.REFUND.name(), instContractDraftAssembler.draft2RefundConfig((InstContractFeePayinDTO) context.getDraftDataDto()));

        // 争议费用解析
        feeConfigMap.put(FeeTypeEnum.CHARGEBACK.name(), instContractDraftAssembler.draft2ChargebackConfig((InstContractFeePayinDTO) context.getDraftDataDto()));

        // VA 账号费解析
        accountFeeParse(feeConfigMap, context);

        // 将 fee_config 写入 原始费用信息
        originFeeItemPo.setFeeConfig(JSON.toJSONString(feeConfigMap));

        // 行业分类解析
        context.setFeeItemList(tradeInfoParse(context));

        // 对将要写入 fee_item 表的数据进行补充
        context.getFeeItemList().forEach(item -> {
            item.setInstContractFeeItemNo(voucherAdapter.getInstProductFeeItemNo());
            item.setFeeBusinessKey(FeeItemConvertUtils.buildFeeItemBusinessKey(item, context.getOriginProduct().getInstOriginProductNo()));
        });

        // 费用信息唯一性校验，此处将重复的 fee_item 剔除，防止重复录入
        feeItemUniqueCheck(context);
    }


    /**
     * 账号费解析
     */
    private void accountFeeParse(Map<String, FeeConfig> feeConfigMap, InstProductParseContext context) {
        if(InstProductTypeEnum.VA.name().equals(context.getDraftDataDto().getProductType())){
            feeConfigMap.put(FeeTypeEnum.VA_ACCOUNT.name(), vaAccountFeeParse(context));
        }
    }


    /**
     * 产品信息处理流程
     */
    public void productProcess(InstProductParseContext context){
        // 先查询标准产品，根据标准产品获取原始产品，绕过产品名称差异
        // 标准产品
        standardProductParse(context);
        // 原始产品
        originProductParse(context);
    }


    /**
     * 主处理流程
     */
    public InstProductParseContext process(InstContractDraft draft) {
        // 判断产品标准化信息是否已完成
        AssertUtil.isTrue(FeeItemConvertUtils.isStandardized(draft), "error", "产品标准化未完成");
        // 1. 初始化上下文对象
        InstProductParseContext context = instContractDraftAssembler.draft2InstProductContext(draft);
        context.setErrorInfoMap(new HashMap<>(16));

        // 2. 合同信息处理
        contractProcess(context);

        // 3. 产品信息处理
        productProcess(context);

        // 4. 费用信息处理
        feeItemProcess(context);

        // 5. 结算信息处理
        settleInfoParse(context);
        return context;
    }


}
