package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.model.dto.Result;

import java.util.List;

/**
 * <AUTHOR> at 2022/10/16 16:14
 **/
public interface SubAccountCompensateManage {

    /**
     * 机构账号子级资金账号补偿处理
     *
     * @param subFundsAccountEntity
     * @return
     */
   void subAccountCompensate(InstSubFundsAccountEntity subFundsAccountEntity, Boolean compensateRetry);

    /**
     * 补偿MQ
     *
     * @param subAccountIds
     * @return
     */
    Result<Integer> compensateSubAccountNoMq(List<String> subAccountIds);


}
