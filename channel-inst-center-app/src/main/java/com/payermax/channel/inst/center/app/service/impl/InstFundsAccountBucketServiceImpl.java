package com.payermax.channel.inst.center.app.service.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstFundsAccountBucketService;
import com.payermax.channel.inst.center.infrastructure.client.RedisClientProxy;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountBucketDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> at 2022/10/8 16:50
 **/
@Service
public class InstFundsAccountBucketServiceImpl implements InstFundsAccountBucketService {

    public static final String INST_FUNDS_ACCOUNT_BUCKET_PREFIX = "instFundsAccount:instFundsAccountBucket";
    @Autowired
    InstFundsAccountBucketDao instFundsAccountBucketDao;
    @Resource
    RedisClientProxy redisClientProxy;

    @Override
    public List<InstFundsAccountBucketEntity> queryBucketsIdIsNullByAccountId(InstFundsAccountBucketEntity bucketEntity) {
        Preconditions.checkArgument(bucketEntity != null, "param bucketEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(bucketEntity.getAccountId()), "param accountId is mandatory");
        
        List<InstFundsAccountBucketEntity> list = redisClientProxy.getAndTryAndLock(
                StringUtils.joinWith(StrPool.COLON, INST_FUNDS_ACCOUNT_BUCKET_PREFIX, bucketEntity.getAccountId(), bucketEntity.getStatus()),
                () -> instFundsAccountBucketDao.selectBucketsIdIsNullByAccountId(bucketEntity),
                10,
                TimeUnit.MINUTES,
                1,
                TimeUnit.MINUTES,
                InstFundsAccountBucketEntity.class);
        return list;
    }

    @Override
    public List<InstFundsAccountBucketEntity> queryBucketsIdNotNullByBucketsId(InstFundsAccountBucketEntity bucketEntity) {
        Preconditions.checkArgument(bucketEntity != null, "param bucketEntity is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(bucketEntity.getAccountId()), "param accountId is mandatory");
        Preconditions.checkArgument(ObjectUtil.isNotNull(bucketEntity.getBucketsId()), "param bucketsId is mandatory");
        
        List<InstFundsAccountBucketEntity> list = redisClientProxy.getAndTryAndLock(
                StringUtils.joinWith(StrPool.COLON, INST_FUNDS_ACCOUNT_BUCKET_PREFIX, bucketEntity.getAccountId(), bucketEntity.getBucketsId(), bucketEntity.getStatus()),
                () ->  instFundsAccountBucketDao.selectBucketsIdNotNullByBucketsId(bucketEntity),
                10,
                TimeUnit.MINUTES,
                1,
                TimeUnit.MINUTES,
                InstFundsAccountBucketEntity.class);
        return list;
    }
}
