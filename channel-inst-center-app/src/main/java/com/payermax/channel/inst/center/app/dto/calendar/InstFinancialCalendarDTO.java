package com.payermax.channel.inst.center.app.dto.calendar;

import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarStatusEnum;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Data
public class InstFinancialCalendarDTO{

    /**
     * 日历ID
     */
    private String calendarId;

    /**
     * 日历年份
     */
    private String calendarYear;


    /**
     * 日历类型，国家/币种/银行（国家&币种）
     */
    private CalendarTypeEnum calendarType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 周末列表
     */
    private List<String> weekendList;

    /**
     * 源日历（被引用日历）
     */
    private String sourceCalendar;

    /**
     * 目标日历（引用此日历的日历）
     */
    private String targetCalendar;

    /**
     * 生效状态
     */
    private CalendarStatusEnum status;

    /**
     * 负责人
     */
    private String owner;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联流程状态
     */
    private InstProcessStatusEnum  relationProcessStatus;

    /**
     * 关联流程操作人
     */
    private String relationProcessOperator;

    /**
     * 显示状态
     */
    private String  displayStatus;

    /**
     * 添加节假日列表
     */
    private List<InstFinancialCalendarHolidayDTO> addHolidayList;

    /**
     * 取消节假日列表
     */
    private List<InstFinancialCalendarHolidayDTO> cancelHolidayList;

    /**
     * 节假日列表
     */
    private List<InstFinancialCalendarHolidayDTO> holidayList;

    /**
     * 是否生成关联币种日历
     */
    private Boolean generateRelatedCurrency = false;

    /**
     * 是否生成关联国家日历
     */
    private Boolean generateRelatedCountry = false;


}
