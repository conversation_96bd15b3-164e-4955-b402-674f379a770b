package com.payermax.channel.inst.center.app.request.account;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @DESC
 */
@Data
public class InstBankAccountSaveOrUpdateRequest {


    /**
     * 账户标识
     */
    private Long id;

    /**
     * 机构标识
     */
    @NotNull(message = "机构标识不能为空")
    private Long instId;

    /**
     * 账户开户所在地
     */
    @NotBlank(message = "账户开户所在地不能为空")
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开户银行
     */
    @NotBlank(message = "开户银行不能为空")
    private String bankCode;

    /**
     * 开户银行名称
     */
    @NotBlank(message = "开户银行名称不能为空")
    private String bankName;

    /**
     * 开户名称
     */
    private String accountName;

    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空")
    private String accountNo;

    /**
     * 分支行
     */
    private String branch;

    /**
     * 分支行地址
     */
    private String branchAddress;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * iban
     */
    private String iban;

    /**
     * 账户用途
     */
    @NotEmpty(message = "账户用途列表不能为空")
    private List<String> accountUseList;

    /**
     * 我方渠道充值场景下，可充值的币种列表
     */
    @NotEmpty(message = "可充值币种列表不能为空")
    private List<String> rechargeCanUseCcyList;

    /**
     * 我方渠道充值场景下，当前账户的优先级
     */
    @NotBlank(message = "充值优先级不能为空")
    private String rechargeUseOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 Y:启用 N:停用
     */
    private String status;
}
