package com.payermax.channel.inst.center.app.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/3
 * @DESC
 */
@Data
@Accessors(chain = true)
public class InstContractProductQueryRequest implements Serializable {

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 我方主体
     */
    private String contractEntity;


    /**
     * 机构产品类型
     */
    private String instProductType;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 卡组
     */
    private String cardOrg;

    /**
     * 支付币种
     */
    private String payCurrency;
}
