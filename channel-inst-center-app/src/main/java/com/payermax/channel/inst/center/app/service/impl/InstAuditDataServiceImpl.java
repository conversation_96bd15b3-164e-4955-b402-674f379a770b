package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAuditDataDao;
import com.payermax.channel.inst.center.app.service.InstAuditDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @ClassName InstAuditDataServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 20:28
 * @Version 1.0
 */
@Service
@Slf4j
public class InstAuditDataServiceImpl implements InstAuditDataService {

    @Autowired
    private InstAuditDataDao instAuditDataDao;

    @Override
    public InstAuditDataEntity query(InstAuditDataEntity instAuditDataEntity) {
        Preconditions.checkArgument(instAuditDataEntity != null, "param instAuditDataEntity is mandatory");
        List<InstAuditDataEntity> instAuditDataEntityList = instAuditDataDao.selectAll(instAuditDataEntity);
        if (CollectionUtils.isNotEmpty(instAuditDataEntityList)) {
            return instAuditDataEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public List<InstAuditDataEntity> queryList(InstAuditDataEntity instAuditDataEntity) {
        Preconditions.checkArgument(instAuditDataEntity != null, "param instAuditDataEntity is mandatory");

        List<InstAuditDataEntity> instAuditDataEntityList = instAuditDataDao.selectAll(instAuditDataEntity);
        return instAuditDataEntityList;
    }

    @Override
    public int save(InstAuditDataEntity instAuditDataEntity) {
        Preconditions.checkArgument(instAuditDataEntity != null, "param instAuditDataEntity is mandatory");
        int result = 0;
        //主键不为空则根据主键更新信息
        if (null != instAuditDataEntity.getId()) {
            result = instAuditDataDao.updateByPrimaryKey(instAuditDataEntity);
        } else {
            result = instAuditDataDao.insert(instAuditDataEntity);
        }
        return result;
    }
}
