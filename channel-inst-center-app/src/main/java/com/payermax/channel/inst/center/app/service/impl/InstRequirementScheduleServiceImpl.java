package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstRequirementScheduleDao;
import com.payermax.channel.inst.center.app.service.InstRequirementScheduleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 集成需求单排期Service实现
 *
 * <AUTHOR>
 * @date 2022/6/4 14:01
 */
@Service
public class InstRequirementScheduleServiceImpl implements InstRequirementScheduleService {

    @Autowired
    private InstRequirementScheduleDao instRequirementScheduleDao;

    @Override
    public int save(InstRequirementScheduleEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instRequirementScheduleDao.insert(record);
        } else {
            // 更新
            result = instRequirementScheduleDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstRequirementScheduleEntity getById(Long id) {
        Preconditions.checkArgument(id != null, "param id is mandatory");

        InstRequirementScheduleQueryEntity record = new InstRequirementScheduleQueryEntity();
        record.setId(id);
        List<InstRequirementScheduleEntity> instRequirementScheduleEntityList = instRequirementScheduleDao.selectAll(record);
        if (CollectionUtils.isNotEmpty(instRequirementScheduleEntityList)) {
            return instRequirementScheduleEntityList.get(0); //NO_CHECK 根据 ID 查询，只有一个
        }
        return null;
    }

    @Override
    public IPage<InstRequirementScheduleQueryEntity> queryPageList(InstRequirementScheduleQueryEntity queryEntity, Long pageNum, Long pageSize) {
        Page<InstRequirementScheduleQueryEntity> page = new Page<>(pageNum, pageSize);
        return instRequirementScheduleDao.selectScheduleList(page, queryEntity);
    }
}
