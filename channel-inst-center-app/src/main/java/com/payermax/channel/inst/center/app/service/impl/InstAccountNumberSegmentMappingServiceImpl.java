package com.payermax.channel.inst.center.app.service.impl;

import com.payermax.channel.inst.center.app.service.InstAccuntNumberSegmentMappingService;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountNumberSegmentMappingEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAccountNumberSegmentMappingDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> at 2022/10/8 16:50
 **/
@Service
public class InstAccountNumberSegmentMappingServiceImpl implements InstAccuntNumberSegmentMappingService {

    @Autowired
    InstAccountNumberSegmentMappingDao instAccountNumberSegmentMappingDao;

    @Override
    public int updateByEntity(InstAccountNumberSegmentMappingEntity instAccountNumberSegmentMappingEntity) {
        // 资金账号列表
        return instAccountNumberSegmentMappingDao.updateByEntity(instAccountNumberSegmentMappingEntity);
    }

}
