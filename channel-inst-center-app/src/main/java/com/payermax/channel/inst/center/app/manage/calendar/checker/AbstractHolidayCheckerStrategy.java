package com.payermax.channel.inst.center.app.manage.calendar.checker;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.channel.inst.center.facade.request.calendar.*;
import com.payermax.channel.inst.center.facade.response.calendar.*;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.common.lang.util.AssertUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Setter
@Component
public abstract class AbstractHolidayCheckerStrategy {

    @Resource
    private InstFinancialCalendarHolidayRepository holidayRepository;

    @Resource
    private InstFinancialCalendarRepository calendarRepository;

    private static final String DATA_FORMAT = "yyyy-MM-dd";

    private static final Map<CalendarTypeEnum, AbstractHolidayCheckerStrategy> EXECUTOR_MAP = new ConcurrentHashMap<>();

    protected final LoadingCache<String, List<InstFinancialCalendar>> CALENDAR_CACHE = CacheBuilder.newBuilder().concurrencyLevel(Runtime.getRuntime().availableProcessors()).initialCapacity(1024).maximumSize(1024).expireAfterWrite(5, TimeUnit.MINUTES).build(new CacheLoader<String, List<InstFinancialCalendar>>() {
        @Override
        public List<InstFinancialCalendar> load(@Nonnull String s) {
            log.info("query InstFinancialCalendar from db start:{}", s);
            // 查询年份对应日历
            InstFinancialCalendar calendarQuery = InstFinancialCalendar.builder().calendarYear(s).build();
            List<InstFinancialCalendar> calendars = calendarRepository.queryByConditions(InstFinancialCalendarAssembler.INSTANCE.domain2Po(calendarQuery)).stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());
            AssertUtil.isTrue(!CollectionUtils.isEmpty(calendars), ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), "当前年份无对应日历");
            log.info("query InstFinancialCalendar from db done:{}", s);
            return calendars;
        }
    });


    protected final LoadingCache<String, List<InstFinancialCalendarHoliday>> HOLIDAY_CACHE = CacheBuilder.newBuilder().concurrencyLevel(Runtime.getRuntime().availableProcessors()).initialCapacity(1024).maximumSize(1024).expireAfterWrite(5, TimeUnit.MINUTES).build(new CacheLoader<String, List<InstFinancialCalendarHoliday>>() {
        @Override
        public List<InstFinancialCalendarHoliday> load(@Nonnull String s) {
            log.info("query CalendarHoliday from db start:{}", s);
            // 查询年份对应日历
            InstFinancialCalendarHolidayPO holidayQuery = InstFinancialCalendarAssembler.INSTANCE.domain2Po(InstFinancialCalendarHoliday.builder().calendarId(s).isWorkday(false).build());
            List<InstFinancialCalendarHoliday> result = holidayRepository.queryByConditions(holidayQuery, null, null).stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());
            log.info("query CalendarHoliday from db done:{}", s);
            return result;
        }
    });


    public static void registry(CalendarTypeEnum calendarType, AbstractHolidayCheckerStrategy strategy) {
        log.info("registry calendar type: {}, strategy: {}", calendarType, strategy);
        AssertUtil.isTrue(!EXECUTOR_MAP.containsKey(calendarType), ErrorCodeEnum.INNER_ERROR.getCode(), "重复注册策略");
        EXECUTOR_MAP.put(calendarType, strategy);
    }

    public static AbstractHolidayCheckerStrategy get(CalendarTypeEnum calendarType) {
        log.info("get calendar type: {}", calendarType);
        AssertUtil.isTrue(EXECUTOR_MAP.containsKey(calendarType), ErrorCodeEnum.INNER_ERROR.getCode(), "未注册的策略");
        return EXECUTOR_MAP.get(calendarType);
    }


    /**
     * 日历过滤器
     *
     * @param calendars 日历列表
     * @param country   国家
     * @param currency  币种
     * @return 匹配的日历列表
     */
    abstract List<InstFinancialCalendar> calendarFilter(List<InstFinancialCalendar> calendars, String country, String currency);

    /**
     * 获取节假日集合
     */
    Set<LocalDate> getHolidayCollection(List<InstFinancialCalendar> calendars) {
        // 对日历列表中的节假日取并集
        return calendars.stream().flatMap(calendar -> calendar.getHolidays().stream()).map(InstFinancialCalendarHoliday::getHolidayDate).collect(Collectors.toSet());
    }

    /**
     * 节假日检查
     */
    public HolidayCheckResponse holidayCheck(HolidayCheckRequest request) {

        // 根据年份获取所有日历
        List<InstFinancialCalendar> yearCalendars = getYearCalendars(request.getDate());

        // 获取匹配的日历列表
        List<InstFinancialCalendar> calendars = calendarFilter(yearCalendars, request.getCountry(), request.getCurrency());

        // 获取节假日列表
        Set<LocalDate> holidays = getHolidayCollection(calendars);

        // 组装结果返回
        return responseAssembler(holidays, request);
    }

    public NextWorkdayCalculationResponse calculateNextWorkdays(NextWorkdayCalculationRequest request) {

        int numOfWorkdays = 0;

        Map<String, List<InstFinancialCalendar>> yearMap = new HashMap<>();

        AssertUtil.isTrue(StringUtils.isNotBlank(request.getDate()), "ERROR", "date can not be empty!" + request);
        AssertUtil.isTrue(request.getNumOfWorkdays() != null, "ERROR", "numOfWorkdays can not be empty!" + request);
        AssertUtil.isTrue(request.getNumOfWorkdays() > 0, "ERROR", "numOfWorkdays must >= 0 !" + request);

        LocalDate date = LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));

        while (true) {
            String nowString = date.toString();
            // 获取年份列表
            List<InstFinancialCalendar> yearCal = yearMap.computeIfAbsent(date.toString(), a -> getYearCalendars(nowString));

            AssertUtil.notEmpty(yearMap, "Error", "FinancialCalendar is not enough! " + date);

            // 根据类型过滤出日历
            List<InstFinancialCalendar> calendars = calendarFilter(yearCal, request.getCountry(), request.getCurrency());

            // 获取节假日列表
            Set<LocalDate> holidays = getHolidayCollection(calendars);
            AssertUtil.notEmpty(holidays, "Error", "FinancialCalendar holidays is not enough! " + date);

            if (!holidays.contains(date)) {
                numOfWorkdays++;
            }

            date = date.plusDays(1);

            if (numOfWorkdays >= request.getNumOfWorkdays() && !holidays.contains(date)) {
                //需要找到最后一个工作日
                break;
            }

        }
        return nextWorkdayCalculationAssembler(request, date);
    }

    public HolidayMultipleCheckResponse calculateNextWorkdaysOfMultiple(HolidayMultipleCheckRequest request) {

        int numOfWorkdays = 0;

        Map<String, List<InstFinancialCalendar>> yearMap = new HashMap<>();
        List<HolidayMultipleItem> multipleItems = request.getMultipleItems();

        AssertUtil.isTrue(!CollectionUtils.isEmpty(multipleItems), "ERROR", "multipleItems can not be empty!");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getDate()), "ERROR", "date can not be empty!" + request);
        AssertUtil.isTrue(request.getNumOfWorkdays() != null, "ERROR", "numOfWorkdays can not be empty!" + request);
        AssertUtil.isTrue(request.getNumOfWorkdays() >= 0, "ERROR", "numOfWorkdays must >= 0 !" + request);

        LocalDate date = LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));

        while (true) {
            String nowString = date.toString();
            // 获取年份列表
            List<InstFinancialCalendar> yearCal = yearMap.computeIfAbsent(date.toString(), a -> getYearCalendars(nowString));

            AssertUtil.notEmpty(yearMap, "Error", "FinancialCalendar is not enough! " + date);

            // 根据类型过滤出日历
            List<InstFinancialCalendar> calendars = new ArrayList<>();

            for (HolidayMultipleItem multipleItem : multipleItems) {
                List<InstFinancialCalendar> calendarItem = calendarFilter(yearCal, multipleItem.getCountry(), multipleItem.getCurrency());
                if (!CollectionUtils.isEmpty(calendarItem)) {
                    calendars.addAll(calendarItem);
                }
            }

            // 获取节假日列表
            Set<LocalDate> holidays = getHolidayCollection(calendars);
            AssertUtil.notEmpty(holidays, "Error", "FinancialCalendar holidays is not enough! " + date);

            // 当查询第 0 天时，实际上是判断当天为工作日，为工作日则直接返回
            if(request.getNumOfWorkdays() == 0 && !holidays.contains(date)){
                break;
            }

            if (!holidays.contains(date)) {
                numOfWorkdays++;
            }

            date = date.plusDays(1);

            if (numOfWorkdays >= request.getNumOfWorkdays() && !holidays.contains(date)) {
                //需要找到最后一个工作日
                break;
            }

        }
        return nextWorkdayCalculationMultipleAssembler(request, date);
    }

    public NumOfWorkdaysCalculationResponse calculateNumOfWorkdays(NumOfWorkdaysCalculationRequest request) {

        int numOfWorkdays = 0;

        Map<String, List<InstFinancialCalendar>> yearMap = new HashMap<>();

        AssertUtil.isTrue(StringUtils.isNotBlank(request.getStartDate()), "ERROR", "StartDate can not be empty!" + request);
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getEndDate()), "ERROR", "EndDate can not be empty!" + request);

        LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));
        LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        AssertUtil.isTrue(daysBetween <= 365, "ERROR", "时间间隔不能超过365天!" + startDate + "->" + endDate);

        LocalDate now = startDate;
        while (now.isBefore(endDate) || now.isEqual(endDate)) {
            String nowString = now.toString();
            // 获取国家列表
            List<InstFinancialCalendar> yearCal = yearMap.computeIfAbsent(nowString, a -> getYearCalendars(nowString));
            AssertUtil.notEmpty(yearMap, "Error", "FinancialCalendar is not enough! " + now);

            // 根据类型过滤出日历
            List<InstFinancialCalendar> calendars = calendarFilter(yearCal, request.getCountry(), request.getCurrency());

            // 获取节假日列表
            Set<LocalDate> holidays = getHolidayCollection(calendars);
            AssertUtil.notEmpty(holidays, "Error", "FinancialCalendar holidays is not enough! " + now);

            if (!holidays.contains(now)) {
                numOfWorkdays++;
            }

            now = now.plusDays(1);
        }
        return numOfWorkdayCalculationAssembler(request, numOfWorkdays);
    }

    public HolidayQueryResponse queryAllHolidaysByDate(HolidayQueryRequest request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getStartDate()), "ERROR", "StartDate can not be empty!" + request);
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getEndDate()), "ERROR", "EndDate can not be empty!" + request);

        LocalDate startDate = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));
        LocalDate endDate = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));
        AssertUtil.isTrue(endDate.isAfter(startDate), "ERROR", "endDate必须大于startDate");

        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        AssertUtil.isTrue(daysBetween <= 365, "ERROR", "时间间隔不能超过365天!" + startDate + "->" + endDate);

        int startYear = startDate.getYear();
        int endYear = endDate.getYear();

        List<InstFinancialCalendar> yearCal = getYearCalendars(request.getStartDate());
        AssertUtil.notEmpty(yearCal, "Error", "FinancialCalendar is not enough! " + request.getStartDate());

        List<InstFinancialCalendar> calendars = calendarFilter(yearCal, request.getCountry(), request.getCurrency());

        String finalEndYear;
        boolean needCross = false;
        if (endYear != startYear) {
            finalEndYear = request.getEndDate();
            needCross = true;
        } else {
            finalEndYear = startDate.plusYears(1).toString();
        }

        //开始日期和结束日期跨年了,就要查询2遍
        try {
            List<InstFinancialCalendar> yearCal2 = getYearCalendars(finalEndYear);
            AssertUtil.notEmpty(yearCal2, "Error", "FinancialCalendar is not enough! " + finalEndYear);

            List<InstFinancialCalendar> calendars2 = calendarFilter(yearCal2, request.getCountry(), request.getCurrency());
            AssertUtil.notEmpty(calendars2, "Error", "FinancialCalendar is not enough! " + request.getEndDate());

            calendars.addAll(calendars2);

        } catch (Exception e) {
            // 查询第二个年份可能不存在
            if (needCross) {
                throw e;
            }
            log.warn("查询异常", e);
        }

        Map<String, Set<String>> groupResult = new HashMap<>();

        // 找在startDate之前的连续节假日用的map
        Map<String, Set<String>> groupResultBefore = new HashMap<>();

        // 最后一个节假日的名字
        String latestName = null;
        LocalDate latestDate = null;

        //所查范围第一个节假日的日期和名字
        LocalDate firstHolidayDate = null;
        String firstHolidayName = null;

        // 获取节假日列表
        for (InstFinancialCalendar cal : calendars) {
            List<String> weekends = cal.getWeekendList();

            List<InstFinancialCalendarHoliday> holidays = cal.getHolidays();

            boolean needWeekends = request.getContainsWeekends() == null || request.getContainsWeekends();

            // 1. 遍历所有节假日,找到符合时间范围的
            for (InstFinancialCalendarHoliday holidayItem : holidays) {
                LocalDate date = holidayItem.getHolidayDate();

                boolean isEqual = date.isEqual(endDate) || date.isEqual(startDate);

                boolean isBetween = date.isBefore(endDate) && date.isAfter(startDate);

                if (!isEqual && !isBetween) {
                    // 时间区间不符合
                    continue;
                }

                boolean isWeekends = weekends.contains(holidayItem.getDescription().toUpperCase());

                if (isWeekends && !needWeekends) {
                    // 本身是周末,但是不需要周末
                    continue;
                }

                if (holidayItem.getIsWorkday()) {
                    // 如果是工作日直接就跳过
                    continue;
                }
                if (firstHolidayDate == null) {
                    firstHolidayDate = holidayItem.getHolidayDate();
                    firstHolidayName = holidayItem.getDescription();
                }
                latestName = holidayItem.getDescription();
                latestDate = holidayItem.getHolidayDate();
                Set<String> groupHolidays = groupResult.computeIfAbsent(holidayItem.getDescription(), a -> new LinkedHashSet<>());
                groupHolidays.add(holidayItem.getHolidayDate().toString());
            }
            // 2. 只要有节假日,就需要再看下最后一个节假日是不是超过了endDate
            if (StringUtils.isNotBlank(latestName)) {
                for (InstFinancialCalendarHoliday holidayItem : holidays) {
                    // 防止一个节假日跳过一个日期endDate,所以需要再遍历一遍
                    // 一个日历的节假日就几十个,时间复杂度上区别不大
                    String name = holidayItem.getDescription();
                    LocalDate localDate = holidayItem.getHolidayDate();
                    if (latestName.equalsIgnoreCase(name) && localDate.equals(latestDate.plusDays(1))) {
                        Set<String> groupHolidays = groupResult.computeIfAbsent(holidayItem.getDescription(), a -> new LinkedHashSet<>());
                        groupHolidays.add(holidayItem.getHolidayDate().toString());
                        latestDate = localDate;
                    }
                    if (firstHolidayName.equalsIgnoreCase(name) && localDate.isBefore(startDate)) {
                        // 向前
                        Set<String> groupHolidays = groupResultBefore.computeIfAbsent(holidayItem.getDescription(), a -> new LinkedHashSet<>());
                        groupHolidays.add(holidayItem.getHolidayDate().toString());
                    }
                }
                // merge 在startDate之前的连续日历
                if (!CollectionUtils.isEmpty(groupResult)) {
                    firstHolidayDate = mergeBeforeHolidays(groupResult, groupResultBefore, firstHolidayDate);
                }
            }
        }
        //3. 如果节假日的第一个日期是1.1号,那么就需要看看上一年所有的节假日了
        LocalDate firstDay = LocalDate.parse(startDate.getYear() + "-01-01", DateTimeFormatter.ofPattern(DATA_FORMAT));

        if (firstHolidayDate != null && firstHolidayDate.isEqual(firstDay)) {
            // 只有第一个假期是1.1号才需要处理
            dealBeforeYearsHoliday(request, startDate, firstHolidayName, groupResult);
        }

        return buildQueryResults(request, groupResult);
    }

    private static LocalDate mergeBeforeHolidays(Map<String, Set<String>> groupResult, Map<String, Set<String>> groupResultBefore, LocalDate firstHolidayDate) {
        Set<String> keys = groupResult.keySet();
        for (String item : keys) {
            Set<String> groupHolidayBefore = groupResultBefore.get(item);
            if (!CollectionUtils.isEmpty(groupHolidayBefore)) {
                Set<String> groupHoliday = groupResult.get(item);
                List<String> groupHolidayList = new ArrayList<>(groupHoliday);
                LocalDate holidayFirstDay = LocalDate.parse(groupHolidayList.get(0), DateTimeFormatter.ofPattern(DATA_FORMAT));//CHECKED 就是要取第一个

                // 有可能存在一年内有多个同名的节假日,所以还是要看是不是连续的
                List<String> groupBeforeHolidayList = new ArrayList<>(groupHolidayBefore);
                List<String> beforeDayHolidaysTmp = new ArrayList<>();
                for (int i = groupBeforeHolidayList.size() - 1; i >= 0; i--) {
                    LocalDate beforeLatestDay = LocalDate.parse(groupBeforeHolidayList.get(i), DateTimeFormatter.ofPattern(DATA_FORMAT));
                    if (beforeLatestDay.equals(holidayFirstDay.minusDays(1))) {
                        holidayFirstDay = beforeLatestDay;
                        beforeDayHolidaysTmp.add(beforeLatestDay.toString());
                        if (firstHolidayDate.isAfter(beforeLatestDay)) {
                            firstHolidayDate = beforeLatestDay;
                        }
                    } else {
                        break;
                    }
                }
                //这个时候顺序是倒序,需要翻转一下
                Collections.reverse(beforeDayHolidaysTmp);
                Set<String> finalBeforeDays = new LinkedHashSet<>(beforeDayHolidaysTmp);

                //开始拼接之前的
                Set<String> finalMergeHolidays = new LinkedHashSet<>();
                finalMergeHolidays.addAll(finalBeforeDays);
                finalMergeHolidays.addAll(groupHoliday);
                groupResult.put(item, finalMergeHolidays);
            }
        }
        return firstHolidayDate;
    }


    private void dealBeforeYearsHoliday(HolidayQueryRequest request, LocalDate startDate, String firstHolidayName, Map<String, Set<String>> groupResult) {
        List<InstFinancialCalendar> calendarsBefore = null;
        LocalDate beforeYear = startDate.minusYears(1);
        try {
            List<InstFinancialCalendar> yearCalBefore = getYearCalendars(beforeYear.toString());
            AssertUtil.notEmpty(yearCalBefore, "Error", "FinancialCalendar is not enough! " + beforeYear);

            calendarsBefore = calendarFilter(yearCalBefore, request.getCountry(), request.getCurrency());
            AssertUtil.notEmpty(calendarsBefore, "Error", "FinancialCalendar is not enough! " + beforeYear);
        } catch (Exception e) {
            // 查询第1个年份可能不存在
            log.warn("查询异常", e);
        }
        if (!CollectionUtils.isEmpty(calendarsBefore)) {
            LocalDate lastDay = LocalDate.parse(beforeYear.getYear() + "-12-31", DateTimeFormatter.ofPattern(DATA_FORMAT));
            List<String> beforeHoliday = new ArrayList<>();
            for (InstFinancialCalendar instFinancialCalendar : calendarsBefore) {
                List<InstFinancialCalendarHoliday> holidays = new ArrayList<>(instFinancialCalendar.getHolidays());
                Collections.reverse(holidays);
                for (InstFinancialCalendarHoliday item : holidays) {
                    if (item.getHolidayDate().equals(lastDay) && item.getDescription().equalsIgnoreCase(firstHolidayName)) {
                        beforeHoliday.add(item.getHolidayDate().toString());
                        lastDay = lastDay.minusDays(1);
                    } else {
                        break;
                    }
                }

            }
            Collections.reverse(beforeHoliday);
            Set<String> beforeHolidaySet = new LinkedHashSet<>(beforeHoliday);
            Set<String> originHolidays = groupResult.get(firstHolidayName);
            beforeHolidaySet.addAll(originHolidays);
            groupResult.put(firstHolidayName, beforeHolidaySet);
        }
    }


    /**
     * 查询年份对应日历
     */
    protected List<InstFinancialCalendar> getYearCalendars(String date) {
        String year = String.valueOf(LocalDate.parse(date, DateTimeFormatter.ofPattern(DATA_FORMAT)).getYear());
        return CALENDAR_CACHE.getUnchecked(year);
    }


    /**
     * 日历精准匹配
     */
    public abstract List<InstFinancialCalendar> calendarPreciseFilter(List<InstFinancialCalendar> calendars, String country, String currency);

    /**
     * 获取日历节假日
     */
    protected List<InstFinancialCalendarHoliday> getHolidays(InstFinancialCalendar calendar) {
        // 当存在日历引用时，使用源日历作为 ID 查询
        String calendarId = StringUtils.isBlank(calendar.getSourceCalendar()) ? calendar.getCalendarId() : calendar.getSourceCalendar();
        return HOLIDAY_CACHE.getUnchecked(calendarId);
    }


    /**
     * 获取最近的下一个工作日
     *
     * @param holidays 节假日集合
     * @param date     日期
     * @return LocalDate 下一个工作日
     */
    protected LocalDate getNearlyNextWorkDay(Set<LocalDate> holidays, LocalDate date) {
        LocalDate nextDay = date.plusDays(1); // 从给定日期下一天开始
        while (holidays.contains(nextDay)) {
            nextDay = nextDay.plusDays(1);
        }
        return nextDay;
    }


    /**
     * 组装结果返回
     */
    private HolidayCheckResponse responseAssembler(Set<LocalDate> holidays, HolidayCheckRequest request) {
        LocalDate date = LocalDate.parse(request.getDate(), DateTimeFormatter.ofPattern(DATA_FORMAT));
        // 组装结果返回
        boolean isHoliday = holidays.contains(date);
        LocalDate nearlyNextWorkDay = getNearlyNextWorkDay(holidays, date);
        return InstFinancialCalendarAssembler.INSTANCE.holidayCheckResponseBuild(request, isHoliday, date, nearlyNextWorkDay);
    }


    /**
     * 组装结果返回
     */
    private HolidayMultipleCheckResponse nextWorkdayCalculationMultipleAssembler(HolidayMultipleCheckRequest request, LocalDate date) {
        HolidayMultipleCheckResponse response = InstFinancialCalendarAssembler.INSTANCE.nextWorkdayCalculationMultipleMapper(request);
        response.setTargetDate(date.toString());
        return response;
    }


    /**
     * 组装结果返回
     */
    private NextWorkdayCalculationResponse nextWorkdayCalculationAssembler(NextWorkdayCalculationRequest request, LocalDate date) {
        NextWorkdayCalculationResponse response = InstFinancialCalendarAssembler.INSTANCE.nextWorkdayCalculationMapper(request);
        response.setTargetDate(date.toString());
        return response;
    }

    private NumOfWorkdaysCalculationResponse numOfWorkdayCalculationAssembler(NumOfWorkdaysCalculationRequest request, int numberOfWorkdays) {
        NumOfWorkdaysCalculationResponse response = InstFinancialCalendarAssembler.INSTANCE.numOfWorkdaysCalculationMapper(request);
        response.setNumOfWorkdays(numberOfWorkdays);
        return response;
    }

    private HolidayQueryResponse buildQueryResults(HolidayQueryRequest request, Map<String, Set<String>> groupResult) {
        HolidayQueryResponse response = InstFinancialCalendarAssembler.INSTANCE.holidayQueryMapper(request);
        List<HolidayItem> holidays = new ArrayList<>();

        groupResult.forEach((key, value) -> {
            HolidayItem item = new HolidayItem();
            item.setHolidayName(key);
            item.setHolidayDates(new ArrayList<>(value));
            holidays.add(item);
        });

        response.setHolidays(holidays);
        return response;
    }

}
