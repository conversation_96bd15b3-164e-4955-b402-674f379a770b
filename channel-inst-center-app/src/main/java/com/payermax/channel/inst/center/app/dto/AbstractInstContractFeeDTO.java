package com.payermax.channel.inst.center.app.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.payermax.channel.inst.center.app.dto.impl.AccumulatedInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.SettleInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.StandardProductInfoDTO;
import com.payermax.channel.inst.center.app.dto.impl.TaxInfoDTO;
import com.payermax.channel.inst.center.app.service.InstContractExcelDataTransfer;
import com.payermax.channel.inst.center.common.enums.instcontract.CardTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.ClearNetworkEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CustomerTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.FeeBearerEnum;
import com.payermax.channel.inst.center.common.utils.ApplicationUtils;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 机构合同费用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public abstract class AbstractInstContractFeeDTO extends ExcelUtil.BaseExcelRow implements ExcelUtil.DefineExcelParser{

    /*--------------此处为录入人员输入信息---------------*/
    /**
     * 机构
     */
    @NotEmpty(message = "机构不能为空")
    @ExcelIgnore
    private String instCode;

    /**
     * 我方主体
     */
    @NotEmpty(message = "我方主体不能为空")
    @ExcelIgnore
    private String contractEntity;

    /**
     * 合作方式
     */
    @NotEmpty(message = "合作方式不能为空")
    @ExcelIgnore
    private String cooperationMode;

    /**
     * 机构产品类型
     */
    @NotEmpty(message = "机构产品类型不能为空")
    @ExcelIgnore
    private String productType;

    /**
     * 生效时间
     */
    @NotNull(message = "生效时间不能为空")
    @ExcelIgnore
    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    @JSONField(format="yyyy-MM-dd hh:mm")
    private Date effectiveTime;

    /**
     * 合同编号
     */
    @ExcelIgnore
    private String contractNo;


    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;




    /*--------------此处为 Excel 表格必填项---------------*/

    /**
     * 机构产品名称
     */
    @NotEmpty(message = "机构产品名称不能为空")
    @ExcelProperty("PaymentMethod Name\n机构产品名称")
    private String instProductName;


    /**
     * 支付币种
     */
    @NotEmpty(message = "支付币种不能为空")
    @ExcelProperty("Transaction Currency\n支付币种")
    private String transactionCurrency;

    /**
     * 阶梯分类
     * 枚举值：
     * None：非阶梯
     * Per Transaction：单笔阶梯
     * Accumulated Volume：累积阶梯
     */
    @NotEmpty(message = "阶梯分类不能为空")
    @ExcelProperty("Tiered Fee Type\n阶梯分类")
    private String tieredFeeType;

    /**
     * 计费方式
     */
    @NotEmpty(message = "计费方式不能为空")
    @ExcelProperty("Calculation Method\n计费方式")
    private String calculateMethod;


    /**
     * 算费币种
     */
    @NotEmpty(message = "算费币种不能为空")
    @ExcelProperty("Currency for Calculate Fees\n算费币种")
    private String feeCalculateCurrency;

    /**
     * 扣费币种
     */
    @NotEmpty(message = "扣费币种不能为空")
    @ExcelProperty("Deduction currency\n扣费币种")
    private String deductionCurrency;

    /**
     * 保留小数位数, 需要数字校验
     */
    @NotEmpty(message = "保留小数位数不能为空")
    @ExcelProperty("Retain Decimal Places\n保留小数位数")
    private String roundingScale;


    /**
     * 保留小数算法
     */
    @NotEmpty(message = "保留小数算法不能为空")
    @ExcelProperty("Decimal retention method\n保留小数算法")
    private String roundingMode;


    /*--------------此处为 Excel 表格非必填项---------------*/


    /**
     * 渠道商户号
     * */
    @ExcelProperty("External MID\n渠道商户号")
    private String externalMid;


    /**
     * 渠道二级商户号
     */
    @ExcelProperty("SubMID\n渠道二级商户号")
    private String subMid;


    /**
     * 累计周期
     */
    @ExcelProperty("Accumulated Cycle\n累计周期")
    private String accumulateCycle;

    /**
     * 累计类型
     */
    @ExcelProperty("Accumulated Method\n累计类型")
    private String accumulateType;

    /**
     * 累计生效方法
     */
    @ExcelProperty("Accumulated Calculate Method\n累计生效方法")
    private String accumulateEffectMethod;

    /**
     * 累计生效范围
     * Total ：到达指定金额/笔数后，全量的都按照优惠价格计算
     * Excess Parts：到达指定金额/笔数后，超出部分按照优惠价格计算
     */
    @ExcelProperty("Accumulated Calculate Scope\n累计生效范围")
    private String accumulateEffectScope;

    /**
     * 是否参与累计
     * Y：参与累积
     * N：不参与累积
     */
    @ExcelProperty("Whether to participate in accumulation\n是否参与累计")
    private String isParticipateAccumulation;

    /**
     * 累计收费时机
     * Real Time Deduction：实时
     * Periodic Unified Deduction：后收费
     */
    @ExcelProperty("Deduct Fee  Timing\n累计收费时机")
    private String deductFeeTiming;

    /**
     * 计费公式
     * 枚举值：
     * TransactionAmount*Percentage Ratio  交易金额
     * TransactionAmount/(1+TaxRatio)*Percentage Ratio 先算税再算费
     * (TransactionAmount+TaxFee)*Percentage Ratio  加税后算费
     * (TransactionAmount-TaxFee)*Percentage Ratio 减税后算费
     */
    @ExcelProperty("Calculation Formula\n计费公式")
    private String calculationFormula;


    /**
     * 阶梯单位
     * 按金额：填写具体币种
     * 按笔：填写【笔】
     */
    @ExcelProperty("Range Unit\n阶梯单位")
    private String rangeUnit;

    /**
     * 换汇时机
     * 枚举值
     * Deposit Day：充值换汇
     * Payment Day：交易换汇
     * No Exchange：自由换汇
     */
    @ExcelProperty("FX Timing\n换汇时机")
    private String fxTiming;

    /**
     * 外汇基准
     */
    @ExcelProperty("FX Source\n外汇基准")
    private String fxSource;

    /**
     * 外汇加点
     */
    @ExcelProperty("FX Markup\n外汇加点")
    private String fxMarkup;

    /**
     * 清算网络
     * {@link ClearNetworkEnum}
     */
    @ExcelProperty("Clear Network\n清算网络")
    private String clearNetWork;

    /**
     * 费用承担方
     * {@link FeeBearerEnum}
     */
    @ExcelProperty("Fee Bearer\n费用承担方")
    private String feeBearer;

    /**
     * 客户类型
     * {@link CustomerTypeEnum}
     */
    @ExcelProperty("Customer Type\n客户类型")
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    @ExcelProperty("Card Type\n卡类型")
    private String cardType;


    /*-------------- 其他方式解析到的数据 ---------------*/



    /**
     * 目标基准
     */
    @ExcelIgnore
    private String targetFxSource;

    /**
     * 换算后外汇加点
     */
    @ExcelIgnore
    private String convertedFxMarkup;


    /**
     * 税费列表
     * */
    @ExcelIgnore
    private List<TaxInfoDTO> taxInfoList;

    /**
     * 手续费列表
     */
    @ExcelIgnore
    private List<AccumulatedInfoDTO> accumulatedInfoList;

    /**
     * 产品标准化信息
     */
    @ExcelIgnore
    private StandardProductInfoDTO standardProductInfo;

    /**
     * 结算/充值信息
     */
    @ExcelIgnore
    private SettleInfoDTO settleInfo;



    /**
     * 机构+我方主体+合作模式+机构产品类型+机构产品名称+支付币种+国家+行业分类+资金来源+【发卡区域 +卡类型+持卡人类型+卡组织 +卡等级】+ 清算网络 + 费用承担方 +生效时间
     * 构成唯一机构合约
     */
    public abstract String getInstContractKey();


    @Override
    public void rowDataParse(List<ExcelCellData> excelCellDataList) {
        InstContractExcelDataTransfer instContractExcelDataTransfer = ApplicationUtils.getBean(InstContractExcelDataTransfer.class);
        setTaxInfoList(instContractExcelDataTransfer.taxInfoListTransfer(getLang(), excelCellDataList));
    }
}
