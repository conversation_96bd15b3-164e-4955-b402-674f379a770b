package com.payermax.channel.inst.center.app.manage.contract;

import com.google.common.collect.Sets;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.model.contract.ContractContentEnum;
import com.payermax.channel.inst.center.app.model.contract.ContractProcessByExcelContext;
import com.payermax.channel.inst.center.app.model.contract.InstContractFactory;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum;
import com.payermax.channel.inst.center.domain.vo.contract.ContractProductBusinessKey;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/9 6:14 PM
 **/
@Slf4j
@Component
public class ContractSettlementProcessor extends AbstractContractTermsProcessor {

    @Resource
    private InstContractFactory instContractFactory;

    @Resource
    private InstFundsAccountDao instFundsAccountDao;

    @Override
    public void assembleContractItems(ContractProcessByExcelContext context) {
        if (MapUtils.isEmpty(context.getStandardizedProductGroupSettleMap())
                && MapUtils.isEmpty(context.getUnStandardizedProductGroupSettleMap())) {
            log.warn("instCode:{} contractEntity:{} businessType:{} 结算信息为空，请关注！",
                    context.getInstCode(), context.getContractEntity(), context.getBusinessTypeEnum());
            return;
        }

        /**
         * 遍历所有机构原始产品，处理对应产品的结算条款
         */
        context.getStandardizedProductGroupSettleMap().entrySet().forEach(entry ->
                processSingleBusinessUnit(context, entry)
        );
        context.getUnStandardizedProductGroupSettleMap().entrySet().forEach(entry ->
                processSingleBusinessUnit(context, entry)
        );
    }

    private void processSingleBusinessUnit(ContractProcessByExcelContext context,
                                           Map.Entry<ContractProductBusinessKey, List<InstContractSettleRowItemDTO>> entry) {
        /**
         * a. 构造处理后的结算条款领域实体列表，并加入上下文中
         */
        List<InstContractSettlementItem> feeItemList = new ArrayList<>();
        context.addContractSettlementItemsForInstProduct(entry.getKey(), feeItemList);

        /**
         * b. 遍历原始条款信息，构造结算条款领域实体
         *    businessKeyItemMap：
         *      -- key   见getContractItemBusinessKey()，
         *      -- value 由于多段周期描述一个完整的计算周期场景：例如一个月分前十天、中间十天、后十天的情况，使用动态列的模式，因此value的size必须是1，否则应报错
         */
        Map<String, List<InstContractSettleRowItemDTO>> businessKeyItemMap =
                entry.getValue().stream().collect(Collectors.groupingBy(InstContractSettleRowItemDTO::getContractItemBusinessKey));

        businessKeyItemMap.forEach((key, feeItemRowList) -> {

            fillSettleErrorInfoEntityInContext(context);

            ExportErrorInfo settleErrorInfo = context.getSettleErrorInfo();
            List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems = settleErrorInfo.getExportErrorInfoItems();

            if (CollectionUtils.isEmpty(feeItemRowList) || feeItemRowList.size() != 1) {
                buildErrorMsg(settleErrorInfo, key, "结算-业务唯一键-校验不合法", exportErrorInfoItems);
            } else {
                /**
                 * c.1.（对应excel一行）DTO -> domain
                 */
                InstContractSettleRowItemDTO settleRowItemDTO = feeItemRowList.get(0); // CHECKED
                InstContractSettlementItem settlementItem = instContractFactory.composePayInContractSettleItem(settleRowItemDTO);

                settlementItem.recordExcelRowList(feeItemRowList.stream().map(InstContractSettleRowItemDTO::getExcelRowNo).collect(Collectors.toList()));

                feeItemList.add(settlementItem);
            }
        });
    }


    @Override
    public void businessValidateOnContractItems(ContractProcessByExcelContext context) {
        Map<ContractProductBusinessKey, List<InstContractSettlementItem>> instContractSettlementMap = context.getInstContractSettlementMap();

        instContractSettlementMap.forEach((productBusinessKey, contractSettlementItemList) -> {
            // 错误信息实体填充
            fillSettleErrorInfoEntityInContext(context);

            contractSettlementItemList.forEach((contractSettlementItem) -> {

                // 1、结算账户信息校验
//                validateSettlePaymentInfo(context, contractSettlementItem);

                // 2、结算费用信息校验
                validateSettleFeeInfo(context, contractSettlementItem);

                // 3、结算周期信息校验
                validateSettleRoundInfo(context, contractSettlementItem);
            });

        });
    }

    /**
     * 上下文错误信息填充
     */
    private void fillSettleErrorInfoEntityInContext(ContractProcessByExcelContext context) {
        ExportErrorInfo settleErrorInfo = Objects.nonNull(context.getSettleErrorInfo()) ? context.getSettleErrorInfo() : new ExportErrorInfo();
        context.setSettleErrorInfo(settleErrorInfo);

        List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems = CollectionUtils.isNotEmpty(settleErrorInfo.getExportErrorInfoItems()) ? settleErrorInfo.getExportErrorInfoItems() : new ArrayList<>();
        settleErrorInfo.setExportErrorInfoItems(exportErrorInfoItems);
    }

    /**
     * 校验结算周期信息
     */
    private void validateSettleRoundInfo(ContractProcessByExcelContext context, InstContractSettlementItem contractSettlementItem) {
        List<InstSettleDateConfig> settleDateConfigs = contractSettlementItem.getSettleDateConfigs();

        ExportErrorInfo settleErrorInfo = context.getSettleErrorInfo();
        List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems = settleErrorInfo.getExportErrorInfoItems();
        WithdrawMethodEnum withdrawMethod = contractSettlementItem.getSettlePaymentConfig().getWithdrawMethod();

        // 1、结算框定的交易开始和结束时间 - 校验
        for (int i = 0; i < settleDateConfigs.size(); i++) {
            String transactionStartDate = settleDateConfigs.get(i).getTransactionStartDate();
            String transactionEndDate = settleDateConfigs.get(i).getTransactionEndDate();
            String billDate = settleDateConfigs.get(i).getBillDate();
            String paymentDate = settleDateConfigs.get(i).getPaymentDate();
            String arriveDate = settleDateConfigs.get(i).getArriveDate();

            //开始时间
            try {
                DSLTypeEnum.mateDslType(transactionStartDate);
            } catch (Exception e) {
                buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-开始时间-解析错误", exportErrorInfoItems);
            }

            //结束时间
            try {
                DSLTypeEnum.mateDslType(transactionEndDate);
            } catch (Exception e) {
                buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-结束时间-解析错误", exportErrorInfoItems);
            }


            // 账单日
            try {
                DSLTypeEnum.mateDslType(billDate);
            } catch (Exception e) {
                buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-账单日-解析错误", exportErrorInfoItems);
            }

            // 打款日
            try {
                DSLTypeEnum.mateDslType(paymentDate);
            } catch (Exception e) {
                buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-打款日-解析错误", exportErrorInfoItems);
            }

            //到账日
            try {
                if (WithdrawMethodEnum.WITHDRAW_MANUALLY.equals(withdrawMethod)) {
                    settleDateConfigs.get(i).setArriveDate(null);
                } else {
                    DSLTypeEnum.mateDslType(arriveDate);
                }
            } catch (Exception e) {
                buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-到账日-解析错误", exportErrorInfoItems);
            }

            // 注：选取交易开始时间进行D、M、W的关键词识别
            if ((DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_WEEK, transactionStartDate)
                    && DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_WEEK, transactionEndDate)
                    ||
                    (DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_MONTH, transactionStartDate)
                            && DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_MONTH, transactionEndDate)))) {
                // W
                if (DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_WEEK, transactionStartDate)
                        && DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_WEEK, transactionEndDate)) {
                    // 交易 开始 & 结束 时间
                    if (i == 0 && !"W.1".equals(transactionStartDate)) {
                        buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-交易开始日期-首端不合法", exportErrorInfoItems);
                    }

                    if (i == settleDateConfigs.size() - 1 && !"W.7".equals(transactionEndDate)) {
                        buildErrorMsg(settleErrorInfo, billDate, "结算-结算周期-交易结束日期-末端不合法", exportErrorInfoItems);
                    }
                }
                // M
                if (DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_MONTH, transactionStartDate)
                        && DSLTypeEnum.whetherMateDsl(DSLTypeEnum.SIMPLE_MONTH, transactionEndDate)) {
                    // 交易 开始 & 结束 时间
                    if (i == 0 && !"M.1".equals(transactionStartDate)) {
                        buildErrorMsg(settleErrorInfo, transactionStartDate, "结算-结算周期-交易开始日期-首端不合法", exportErrorInfoItems);
                    }

                    if (i == settleDateConfigs.size() - 1 && !"M.-1".equals(transactionEndDate)) {
                        buildErrorMsg(settleErrorInfo, transactionEndDate, "结算-结算周期-交易结束日期-末端不合法", exportErrorInfoItems);
                    }
                }
            }

        }

    }

    private static void buildErrorMsg(ExportErrorInfo settleErrorInfo, String billDate, String msg, List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems) {
        settleErrorInfo.setHasErrors(true);
        ExportErrorInfo.ExportErrorInfoItem exportErrorInfoItem = new ExportErrorInfo.ExportErrorInfoItem();
        exportErrorInfoItem.setCellValue(billDate);
        exportErrorInfoItem.setErrorMsg(msg);
        exportErrorInfoItems.add(exportErrorInfoItem);
    }

    /**
     * 校验结算费用信息
     */
    private void validateSettleFeeInfo(ContractProcessByExcelContext context, InstContractSettlementItem
            contractSettlementItem) {
        ExportErrorInfo settleErrorInfo = context.getSettleErrorInfo();
        List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems = settleErrorInfo.getExportErrorInfoItems();

        InstFeeConfig settleFeeConfig = contractSettlementItem.getSettleFeeConfig();
        FeeCalculateTypeEnum calculateType = settleFeeConfig.getCalculateType();

        // 结算费用允许为空，实际会存在确实没有结算费用的情况
        if (Objects.nonNull(calculateType)) {
            // 1、结算费用如果有，限制单笔比例 / 单笔固定这两种类型
            if (!FeeCalculateTypeEnum.SINGLE_RATE.equals(calculateType) && !FeeCalculateTypeEnum.SINGLE_MONEY.equals(calculateType)) {
                List<Integer> excelRowNoList = contractSettlementItem.excelRowNoList;

                settleErrorInfo.setHasErrors(true);
                ExportErrorInfo.ExportErrorInfoItem exportErrorInfoItem = new ExportErrorInfo.ExportErrorInfoItem();
                exportErrorInfoItem.setRowNo(excelRowNoList.get(0)); // NO_CHECK 获取 Excel 行号，只存在一个
                exportErrorInfoItem.setCellValue(calculateType);
                exportErrorInfoItem.setErrorMsg(String.format("结算费用不支持的计费方式:[%s]", calculateType));
                exportErrorInfoItems.add(exportErrorInfoItem);
            }

            // 2、结算费用如果有，限制单笔比例 / 单笔固定这两种类型
            // 2.1 单笔比例 - 则费率百分比不能为空
            if (FeeCalculateTypeEnum.SINGLE_RATE.equals(calculateType) && Objects.isNull(settleFeeConfig.getFeeRateValue())) {
                settleErrorInfo.setHasErrors(true);
                ExportErrorInfo.ExportErrorInfoItem exportErrorInfoItem = new ExportErrorInfo.ExportErrorInfoItem();
                exportErrorInfoItem.setCellValue(settleFeeConfig.getFeeRateValue());
                exportErrorInfoItem.setErrorMsg("结算-计费方式[Percentage]下，列[单笔比例]不能为空");
                exportErrorInfoItems.add(exportErrorInfoItem);
            } else if (FeeCalculateTypeEnum.SINGLE_MONEY.equals(calculateType)) {
                // 2.2 单笔固定 - 则费用币种 && 费用金额 不能为空
                if (StringUtils.isBlank(settleFeeConfig.getFeeCurrency())) {
                    buildErrorMsg(settleErrorInfo, settleFeeConfig.getFeeCurrency(), "结算-计费方式[Fix]下，列[单笔固定币种]不能为空", exportErrorInfoItems);
                } else if (Objects.isNull(settleFeeConfig.getFeeValue())) {
                    settleErrorInfo.setHasErrors(true);
                    ExportErrorInfo.ExportErrorInfoItem exportErrorInfoItem = new ExportErrorInfo.ExportErrorInfoItem();
                    exportErrorInfoItem.setCellValue(settleFeeConfig.getFeeValue());
                    exportErrorInfoItem.setErrorMsg("结算-计费方式[Fix]下，列[单笔固定金额]不能为空");
                    exportErrorInfoItems.add(exportErrorInfoItem);
                }
            }
        }
    }


    /**
     * 校验结算付款信息（主要是accountID）
     */
    private void validateSettlePaymentInfo(ContractProcessByExcelContext context, InstContractSettlementItem contractSettlementItem) {
        ExportErrorInfo settleErrorInfo = context.getSettleErrorInfo();
        List<ExportErrorInfo.ExportErrorInfoItem> exportErrorInfoItems = settleErrorInfo.getExportErrorInfoItems();

        InstSettlePaymentConfig settlePaymentConfig = contractSettlementItem.getSettlePaymentConfig();

        // 1、查询
        InstFundsAccountQueryEntity queryEntity = new InstFundsAccountQueryEntity();
        queryEntity.setAccountId(settlePaymentConfig.getAccountId());
        List<InstFundsAccountEntity> instFundsAccountEntities = instFundsAccountDao.selectByQueryEntity(queryEntity);

        // 2、校验
        if (CollectionUtils.isEmpty(instFundsAccountEntities) || instFundsAccountEntities.size() != 1) {
            List<Integer> excelRowNoList = contractSettlementItem.excelRowNoList;

            settleErrorInfo.setHasErrors(true);
            ExportErrorInfo.ExportErrorInfoItem exportErrorInfoItem = new ExportErrorInfo.ExportErrorInfoItem();
            exportErrorInfoItem.setRowNo(excelRowNoList.get(0)); // NO_CHECK 获取 Excel 行号，只存在一个
            exportErrorInfoItem.setCellValue(settlePaymentConfig.getAccountId());
            exportErrorInfoItem.setErrorMsg(String.format("我方收款账户:[%s]在机构中心不存在", settlePaymentConfig.getAccountId()));
            exportErrorInfoItems.add(exportErrorInfoItem);
        }
    }

    @Override
    public Set<ContractContentEnum> supportedScenarios() {
        return Sets.newHashSet(ContractContentEnum.INST_SETTLEMENT_PAYIN, ContractContentEnum.INST_SETTLEMENT_PAYOUT);
    }
}
