package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstDdSurveyEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstDdSurveyDao;
import com.payermax.channel.inst.center.app.service.InstDdSurveyService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DD调研报告Service实现
 *
 * <AUTHOR>
 * @date 2022/5/18 16:04
 */
@Service
public class InstDdSurveyServiceImpl implements InstDdSurveyService {

    @Autowired
    private InstDdSurveyDao instDdSurveyDao;

    @Override
    public int save(InstDdSurveyEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instDdSurveyDao.insert(record);
        } else {
            // 更新
            result = instDdSurveyDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstDdSurveyEntity getByDdId(Long ddId) {
        Preconditions.checkArgument(ddId != null, "param ddId is mandatory");

        InstDdSurveyEntity queryEntity = new InstDdSurveyEntity();
        queryEntity.setDdId(ddId);
        List<InstDdSurveyEntity> resultList = instDdSurveyDao.selectAll(queryEntity);
        if (CollectionUtils.isNotEmpty(resultList)) {
            return resultList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }
}
