package com.payermax.channel.inst.center.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.payermax.channel.inst.center.common.enums.instcenter.ApplyStageEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstApplyOrderEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity;

import java.util.List;

/**
 * @Description 申请单 Service
 * <AUTHOR>
 * @Date 2022/5/17 23:05
 **/
public interface InstApplyOrderService extends IService<InstApplyOrderEntity> {
    /**
     * 保存申请单
     *
     * @param record
     * @return
     */
    int saveEntity(InstApplyOrderEntity record);

    /**
     * 更新申请阶段状态
     *
     * @param applyNo
     * @param applyStageEnum
     * @param status
     * @return
     */
    int updateStageStatus(String applyNo, ApplyStageEnum applyStageEnum, String status);

    /**
     * 分页查询申请单
     *
     * @param applyOrderQueryEntity
     * @return
     */
    IPage<ApplyOrderQueryEntity> queryPageList(ApplyOrderQueryEntity applyOrderQueryEntity, Long pageNum, Long pageSize);

    /**
     * 查询申请单列表
     *
     * @param applyOrderQueryEntity
     * @return
     */
    List<ApplyOrderQueryEntity> queryList(ApplyOrderQueryEntity applyOrderQueryEntity);

    /**
     * 查询申请单列表
     *
     * @param instIds
     * @return
     */
    List<InstApplyOrderEntity> queryList(List<Long> instIds);

    /**
     * 根据申请单号查申请单信息
     *
     * @param applyNo
     * @return
     */
    ApplyOrderQueryEntity queryByApplyNo(String applyNo);
}
