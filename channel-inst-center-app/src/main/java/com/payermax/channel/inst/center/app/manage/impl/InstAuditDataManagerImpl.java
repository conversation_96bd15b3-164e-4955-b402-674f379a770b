package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.infrastructure.entity.InstAuditDataEntity;
import com.payermax.channel.inst.center.app.manage.InstAuditDataManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstAuditDataReqDTO;
import com.payermax.channel.inst.center.app.response.InstAuditDataVO;
import com.payermax.channel.inst.center.app.service.InstAuditDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @ClassName InstAuditDataManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 00:28
 * @Version 1.0
 */
@Service
public class InstAuditDataManagerImpl implements InstAuditDataManager {

    @Autowired
    private InstAuditDataService instAuditDataService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public InstAuditDataVO query(InstAuditDataReqDTO instAuditDataReqDTO) {
        //请求转换
        InstAuditDataEntity instAuditDataReqEntity = reqDtoAssembler.toInstAuditDataEntity(instAuditDataReqDTO);
        //响应转换
        InstAuditDataEntity instAuditDataRespEntity = instAuditDataService.query(instAuditDataReqEntity);
        return respVoAssembler.toInstAuditDataVo(instAuditDataRespEntity);
    }

    @Override
    public int save(InstAuditDataReqDTO instAuditDataReqDTO) {
        //请求转换
        InstAuditDataEntity instAuditDataEntity = reqDtoAssembler.toInstAuditDataEntity(instAuditDataReqDTO);
        return instAuditDataService.save(instAuditDataEntity);

    }
}
