package com.payermax.channel.inst.center.app.event;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Map;

/**
 * 机构账号参数桶事件EVENT
 *
 * <AUTHOR> at 2022/11/26 13:50
 **/
@Getter
public class InstFunsAccountBucketsAlertEvent extends ApplicationEvent {

    /**
     * 参数计数桶
     **/
    private List<InstFundsAccountBucketEntity> accountBucketEntityList;

    /**
     * 机构账号信息
     **/
    private InstFundsAccountEntity instFundsAccountEntity;

    /**
     * 机构下子级账号 参数计数桶已使用次数 （参数桶ID，次数）
     **/
    private Map<Long, Integer> subAccountCountMap;

    public InstFunsAccountBucketsAlertEvent(Object source, InstFundsAccountEntity instFundsAccountEntity, List<InstFundsAccountBucketEntity> accountBucketEntityList, Map<Long, Integer> subAccountCountMap) {
        super(source);
        this.instFundsAccountEntity = instFundsAccountEntity;
        this.accountBucketEntityList = accountBucketEntityList;
        this.subAccountCountMap = subAccountCountMap;
    }
}
