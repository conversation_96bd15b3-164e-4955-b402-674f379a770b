package com.payermax.channel.inst.center.app.manage.template.mode;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.manage.template.ApiTemplateUtil;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/9 16:08
 **/
@Service
@Slf4j
public class ApiModeTemplate extends AbstractSubAccountModeTemplate {
    
    public static final String CREATE_SUB_ACCOUNT = "createSubAccount";

    public static final String ALERT_TITLE = "机构子级账号【API申请】异常通知";
    
    @NacosValue(value = "${inst.sub.account.alert.message.for.api.apply.exception:}", autoRefreshed = true)
    private String subAccountApiApplyException;

    @Override
    public void getSubAccountNo(RequestAccountDO requestAccountDO) {
        // 获取机构子级账号信息
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 获取机构账号
        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        
        Boolean checkResult = apiTemplateUtil.checkRequestParams(requestAccountDO, CREATE_SUB_ACCOUNT);

        if (Boolean.FALSE.equals(checkResult)) {
            return;
        }
        // 用号段生成子级账号
        if (StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {
            // 用号段生成子级账号
            super.buildNumberSegmentAccountNoLock(requestAccountDO);

            // 若是号段生成子级资金账号规则不为空，则子级账号不可为空
            if (StringUtils.isNotBlank(instFundsAccountEntity.getSubAccountRule()) && StringUtils.isBlank(instSubFundsAccountEntity.getNumberSegmentNo())) {
                throw new BusinessException(ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getCode(), ErrorCodeEnum.NO_NUMBER_SEGMENT_AVAILABLE.getMsg());
            }
        }

        // 构建调用渠道网关API查询接口请求参数
        JSONObject channelRequest = apiTemplateUtil.buildChannelRequest(instFundsAccountEntity, instSubFundsAccountEntity, requestAccountDO.getInstSubFundsAccountBucketsNotNullEntityList());
        
        try {
            // 发送渠道网关API请求
            apiTemplateUtil.sendRequest(requestAccountDO, CREATE_SUB_ACCOUNT, channelRequest, ApiTemplateUtil.APPLY_STATE);
        } catch (Exception e) {
            log.error("ApiModeTemplate-sendRequest Exception!", e);
            // 发送钉钉通知
            dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountApiApplyException, channelRequest);
        }
        
        // 初始化状态 则需要更新为申请中（说明是超时等其他异常情况）
        if (Objects.equals(instSubFundsAccountEntity.getStatus(), SubAccountStatusEnum.INITIATE.getStatus())) {
            // 更新状态 -》申请中
            instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.APPLY.getStatus());
            StateRequest stateRequest = new StateRequest(SubAccountStatusEnum.INITIATE.getStatus(), SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
            stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
        }
    }

    @Override
    public InstSubFundsAccountEntity buildCreateSubAccountRecord(RequestAccountDO requestAccountDO) {
        return super.buildCreateSubAccountRecord(requestAccountDO);
    }

    @Override
    public void handleActivationOrOthersAction( RequestAccountDO requestAccountDO) {
        // 激活检查, 并激活
        super.checkIsNeedActivation(requestAccountDO);
    }

}
