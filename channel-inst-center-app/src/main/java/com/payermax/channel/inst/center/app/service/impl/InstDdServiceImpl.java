package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstDdEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstDdQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstDdDao;
import com.payermax.channel.inst.center.app.service.InstDdService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DD service实现
 *
 * <AUTHOR>
 * @date 2022/5/15 22:55
 */
@Service
public class InstDdServiceImpl implements InstDdService {

    @Autowired
    private InstDdDao instDdDao;

    @Override
    public int save(InstDdEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instDdDao.insert(record);
        } else {
            // 更新
            result = instDdDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public InstDdEntity getByInstId(Long instId) {
        Preconditions.checkArgument(instId != null, "param instId is mandatory");

        InstDdEntity record = new InstDdEntity();
        record.setInstId(instId);
        List<InstDdEntity> instDdEntityList = instDdDao.selectAll(record);
        if (CollectionUtils.isNotEmpty(instDdEntityList)) {
            return instDdEntityList.get(0); //NO_CHECK 方法未被调用
        }
        return null;
    }

    @Override
    public List<InstDdQueryEntity> getByInstIds(List<Long> instIds) {
        return instDdDao.selectDdList(instIds);
    }

}
