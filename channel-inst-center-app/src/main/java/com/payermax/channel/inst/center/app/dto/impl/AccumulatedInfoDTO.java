package com.payermax.channel.inst.center.app.dto.impl;

import com.alibaba.excel.annotation.ExcelProperty;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class AccumulatedInfoDTO extends ExcelUtil.BaseExcelRow{


    /**
     * 机构唯一标识
     * */
    private String instContractKey;


    /**
     * 最低收费
     */
    @ExcelProperty("Min Fee\n最低收费")
    private String minFee;

    /**
     * 最低收费币种
     */
    @ExcelProperty("Min Fee Currency\n最低收费币种")
    private String minFeeCurrency;

    /**
     * 封顶收费
     */
    @ExcelProperty("Max Fee\n封顶收费")
    private String maxFee;

    /**
     * 封顶收费币种
     */
    @ExcelProperty("Max Fee Currency\n封顶收费币种")
    private String maxFeeCurrency;

    /**
     * 固定收费
     */
    @ExcelProperty("Fixed Fee\n固定费用")
    private String fixedFee;

    /**
     * 固定费用币种
     */
    @ExcelProperty("Fixed Fee Currency\n固定费用币种")
    private String fixedFeeCurrency;

    /**
     * 比例费用
     */
    @ExcelProperty("Percentage Ratio\n比例费用")
    private String percentageRatio;
    /**
     * 累计阶梯上限（不含）, 需要数字校验
     */
    @ExcelProperty("Upper Limit of Tiered \n阶梯上限")
    private String accumulateStepUpper;

    /**
     * 累计阶梯下限（含）, 需要数字校验
     */
    @ExcelProperty("Lower Limit of Tiered \n阶梯下限")
    private String accumulateStepLower;


    /**
     * 计费公式
     * 枚举值：
     * TransactionAmount*Percentage Ratio  交易金额
     * TransactionAmount/(1+TaxRatio)*Percentage Ratio 先算税再算费
     * (TransactionAmount+TaxFee)*Percentage Ratio  加税后算费
     * (TransactionAmount-TaxFee)*Percentage Ratio 减税后算费
     */
    @ExcelProperty("Calculation Formula\n计费公式")
    private String calculationFormula;
}
