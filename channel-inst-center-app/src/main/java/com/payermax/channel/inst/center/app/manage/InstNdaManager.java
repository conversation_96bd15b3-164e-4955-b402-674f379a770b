package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstNdaReqDTO;
import com.payermax.channel.inst.center.app.response.InstNdaVO;

/**
 * NDA相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/13 22:32
 */
public interface InstNdaManager {

    /**
     * 查询机构NDA信息
     *
     * @return
     */
    InstNdaVO query(InstNdaReqDTO instNdaReqDTO);

    /**
     * 保存机构NDA信息
     *
     * @return
     */
    int save(InstNdaReqDTO instNdaReqDTO);
}
