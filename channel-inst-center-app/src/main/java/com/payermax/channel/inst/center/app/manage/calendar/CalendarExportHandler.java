package com.payermax.channel.inst.center.app.manage.calendar;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.CalendarUtil;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.adapter.FileUploadService;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarHolidayRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstFinancialCalendarRepository;
import com.payermax.infra.ionia.fs.dto.UploadResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/19
 * @DESC
 */
@Slf4j
@Component
@AllArgsConstructor
public class CalendarExportHandler {

    private final InstFinancialCalendarRepository calendarRepository;
    private final InstFinancialCalendarHolidayRepository holidayRepository;
    private final FileUploadService fileUploadAdapter;

    private final String isWorkdaySymbol = "√";
    private final String isNotWorkdaySymbol = "×";

    public UploadResponse calendarExport(List<String> calendarList){

        log.info("开始导出日历信息");
        List<List<String>> headerData = buildExcelHeader(calendarList);
        List<List<String>> excelData = buildExcelData(calendarList);

        String exportFileName = "日历信息";

        MultipartFile multipartFile = buildMultipartFile(exportFileName, exportFileName, headerData, excelData);
        // 上传文件
        return fileUploadAdapter.upLoadToS3(multipartFile, exportFileName + ".xlsx");
    }

    private MultipartFile buildMultipartFile(String fileName, String sheetName, List<List<String>> headerData, List<List<String>> excelData) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 生成 Excel 文件
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .head(headerData)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            excelWriter.write(excelData, writeSheet);
            excelWriter.finish();

            // 将生成的 Excel 文件保存到字节数组中
            byte[] bytes = outputStream.toByteArray();

            // 创建 CommonsMultipartFile 对象
            DiskFileItem fileItem = (DiskFileItem) new DiskFileItemFactory().createItem("file",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", true, fileName);

            try (OutputStream outStream = fileItem.getOutputStream()) {
                outStream.write(bytes);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            return new CommonsMultipartFile(fileItem);
        } catch (Exception e) {
            throw new RuntimeException("构造excel文件失败", e);
        }
    }

    /**
     * 构建 excel 表头数据
     */
    private List<List<String>> buildExcelHeader(List<String> calendarList) {
        List<List<String>> excelHeader = new ArrayList<>();
        // 默认表头
        List<String> excelHeaderMonth = new ArrayList<>(Arrays.asList("Month","Month"));
        List<String> excelHeaderDate = new ArrayList<>(Arrays.asList("Date","Date"));
        List<String> excelHeaderWeekday = new ArrayList<>(Arrays.asList("Weekday","Weekday"));
        excelHeader.add(excelHeaderMonth);
        excelHeader.add(excelHeaderDate);
        excelHeader.add(excelHeaderWeekday);

        // 日历表头
        calendarList.forEach(item -> {
            List<String> isWorkday = new ArrayList<>(Arrays.asList(item, "是否工作日"));
            List<String> description = new ArrayList<>(Arrays.asList(item, "备注"));
            excelHeader.add(isWorkday);
            excelHeader.add(description);
        });
        return excelHeader;
    }

    /**
     * 保存文件到本地
     */
    private void saveToLocalPath(MultipartFile multipartFile){

        // 保存路径
        String localPath = "";
        String filePath = localPath + File.separator + multipartFile.getOriginalFilename() + ".xlsx";
        File file = new File(filePath);
        try {
            multipartFile.transferTo(file);
            log.info("文件保存成功:{}", filePath);
        }catch (IOException e){
            log.error("文件保存失败", e);
        }
    }

    /**
     * 构造excel行数据
     */
    private List<List<String>> buildExcelData(List<String> calendarIdList) {

        // 查询日历列表
        List<InstFinancialCalendar> calendarList = calendarRepository.queryByCalendarIds(calendarIdList)
                .stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());

        // 年份校验
        List<String> collect = calendarList.stream().map(InstFinancialCalendar::getCalendarYear).distinct().collect(Collectors.toList());
        AssertUtil.isTrue(collect.size() == 1, ErrorCodeEnum.INNER_ERROR.getCode(), "只允许导出同一年份的日历");

        // 获取年份的所有日期
        List<LocalDate> dateOfYear = CalendarUtil.getDatesOfYear(Integer.parseInt(collect.get(0))); //CHECKED
        // 填充节假日
        calendarList = calendarList.stream().map(this::fillHoliday).collect(Collectors.toList());

        // 获取所有日历的节假日，按照日期分组
        Map<LocalDate, List<InstFinancialCalendarHoliday>> calendarDateMap = calendarList.stream()
                .flatMap(item -> item.getHolidays().stream())
                .collect(Collectors.groupingBy(InstFinancialCalendarHoliday::getHolidayDate));


        // 构造 excel 数据
        List<List<String>> excelDataList = new ArrayList<>();
        // 构造每一行数据
        for( LocalDate date : dateOfYear){
            // 默认信息
            List<String> rowData = new ArrayList<>(Arrays.asList(date.getMonth().name(), String.valueOf(date.getDayOfMonth()), date.getDayOfWeek().name()));
            if(calendarDateMap.containsKey(date)){
                // 日期对应的日历Map
                Map<String, InstFinancialCalendarHoliday> calendarHolidayMap = calendarDateMap.get(date).stream()
                        .collect(Collectors.toMap(InstFinancialCalendarHoliday::getCalendarId, Function.identity(),
                                (o1, o2) -> o1));
                // 构造每行数据
                calendarList.forEach(calendar -> {
                    String calendarId = calendar.getCalendarId();
                    // 存在节假日时填入，否则填充默认值
                    if(calendarHolidayMap.containsKey(calendarId)){
                        InstFinancialCalendarHoliday holiday = calendarHolidayMap.get(calendarId);
                        rowData.add(holiday.getIsWorkday() ? isWorkdaySymbol : isNotWorkdaySymbol);
                        rowData.add(holiday.getDescription());
                    }else{
                        setWorkdayCell(rowData);
                    }
                });
            }else{
                // 填充默认值
                calendarList.forEach(calendarId -> setWorkdayCell(rowData));
            }
            excelDataList.add(rowData);
        }
        return excelDataList;
    }


    /**
     * 填充日历节假日信息
     */
    private InstFinancialCalendar fillHoliday(InstFinancialCalendar calendar){
        List<InstFinancialCalendarHolidayPO> holidayPoList;
        // 根据是否存在源日历进行查询
        if(StringUtils.isNotBlank(calendar.getSourceCalendar())){
            holidayPoList = holidayRepository.queryByCalendarIds(Collections.singletonList(calendar.getSourceCalendar()));
            // 变更关联日历
            holidayPoList.forEach(item -> item.setCalendarId(calendar.getCalendarId()));
        }else{
            holidayPoList = holidayRepository.queryByCalendarIds(Collections.singletonList(calendar.getCalendarId()));
        }
        List<InstFinancialCalendarHoliday> holidays = holidayPoList.stream().map(InstFinancialCalendarAssembler.INSTANCE::po2Domain).collect(Collectors.toList());
        calendar.setHolidays(holidays);
        return calendar;

    }

    /**
     * 设置非工作日单元格
     */
    private void setWorkdayCell(List<String> rowData){
        rowData.add(isWorkdaySymbol);
        rowData.add("");
    }

}
