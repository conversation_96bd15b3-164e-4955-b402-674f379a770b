package com.payermax.channel.inst.center.app.manage.impl;

import com.payermax.channel.inst.center.infrastructure.client.FintechBaseClientProxy;
import com.payermax.channel.inst.center.app.manage.DictManage;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.response.DictItemVO;
import com.ushareit.fintech.base.dto.DictItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 字典Manage实现类
 *
 * <AUTHOR>
 * @date 2022/5/15 17:32
 */
@Service
public class DictManageImpl implements DictManage {

    @Autowired
    private FintechBaseClientProxy baseClientProxy;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public List<DictItemVO> queryDictItem(String dictCode) {
        List<DictItemVO> itemVOList = new ArrayList<>();
        // 调用基础服务查询字典项
        List<DictItemDTO> itemDTOList = baseClientProxy.queryDict(dictCode);
        itemDTOList.forEach(item -> {
            DictItemVO itemVO = respVoAssembler.toDictItemVo(item);
            itemVOList.add(itemVO);
        });
        return itemVOList;
    }
}
