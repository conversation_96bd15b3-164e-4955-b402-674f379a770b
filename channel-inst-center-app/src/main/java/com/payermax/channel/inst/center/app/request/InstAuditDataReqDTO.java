package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstAuditDataReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 23:17
 * @Version 1.0
 */
@Data
public class InstAuditDataReqDTO implements Serializable {

    private static final long serialVersionUID = -7075909428462830335L;
    /**
     * 主键
     */
    @ApiModelProperty(notes = "主键")
    private Long id;

    /**
     * 关联业务类型 KYC：KYC审核，DD：DD审核
     */
    @ApiModelProperty(notes = "关联业务类型")
    private String businessType;

    /**
     * 关联业务单号
     */
    @ApiModelProperty(notes = "关联业务单号")
    private String businessNo;

    /**
     * 资料类型
     */
    @ApiModelProperty(notes = "资料类型")
    private String dataType;

    /**
     * 资料附件
     */
    @ApiModelProperty(notes = "资料附件")
    private String dataAttachId;

    /**
     * 证件号
     */
    @ApiModelProperty(notes = "证件号")
    private String idNumber;

    /**
     * 证件有效期
     */
    @ApiModelProperty(notes = "证件有效期")
    private Date idValidityDate;

    /**
     * 是否已有水印
     */
    @ApiModelProperty(notes = "是否已有水印")
    private String hasWatermarkWord;

    /**
     * 备注
     */
    @ApiModelProperty(notes = "备注")
    private String remark;
}
