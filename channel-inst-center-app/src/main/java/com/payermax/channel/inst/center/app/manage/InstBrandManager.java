package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.app.request.InstBrandReqDTO;
import com.payermax.channel.inst.center.app.response.InstBrandVO;

import java.util.List;

/**
 * 机构品牌信息相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
@Deprecated
public interface InstBrandManager {

    /**
     * 查询机构品牌信息
     *
     * @return
     */
    List<InstBrandVO> query(InstBrandReqDTO instBrandReqDTO);

    /**
     * 保存机构品牌信息
     *
     * @return
     */
    int save(InstBrandReqDTO instBrandReqDTO);
}
