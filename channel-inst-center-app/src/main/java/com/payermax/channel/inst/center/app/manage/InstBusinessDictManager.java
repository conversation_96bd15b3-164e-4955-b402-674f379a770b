package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.app.request.InstBusinessDictReqDTO;
import com.payermax.channel.inst.center.app.response.InstBusinessDictVO;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:32
 */
public interface InstBusinessDictManager {
    int save(InstBusinessDictReqDTO record);

    int saveAll(List<InstBusinessDictReqDTO> instBusinessDictReqDTOS);

    List<InstBusinessDictVO> queryByBusinessType(String businessType);
}
