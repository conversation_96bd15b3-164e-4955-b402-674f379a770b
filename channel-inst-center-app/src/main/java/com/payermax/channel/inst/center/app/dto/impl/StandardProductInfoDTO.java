package com.payermax.channel.inst.center.app.dto.impl;

import lombok.Data;

import java.util.List;

@Data
public class StandardProductInfoDTO {

    List<PaymentItem> paymentList;

    List<InstTradeItem> instTradeList;

    @Data
    public static class PaymentItem{

        /**
         * 业务类型,I/O/VA
         */
        private String paymentBusinessType;

        /**
         * 支付类型
         */
        private String paymentType;

        /**
         * 目标机构/卡组
         */
        private String target;
    }

    @Data
    public static class InstTradeItem{

        /**
         * 行业分类
         */
        private String tradeType;

        /**
         * 关系类型
         */
        private String relationType;

        /**
         * 行业分类代码
         */
        private String tradeCode;
    }


}
