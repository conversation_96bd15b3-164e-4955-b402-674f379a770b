package com.payermax.channel.inst.center.app.dto.workflow;

import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/28
 * @DESC
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InstContractFeeFormMsgDTO extends InstContractBaseFormMsgDTO {


    /**
     * FeeItem 编号
     */
    private String feeItemNo;

    /**
     * 原始信息
     */
    private InstContractFeeItemPO originData;


    /**
     * 修改后信息
     */
    private InstContractFeeItemPO modifiedData;
}
