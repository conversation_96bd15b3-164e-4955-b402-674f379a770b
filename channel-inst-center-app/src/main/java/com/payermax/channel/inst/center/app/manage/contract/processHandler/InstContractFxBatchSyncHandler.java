package com.payermax.channel.inst.center.app.manage.contract.processHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractProductAssembler;
import com.payermax.channel.inst.center.app.dto.contract.InstContractFxBatchSyncDto;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.manage.contract.InstContractBusinessValidator;
import com.payermax.channel.inst.center.app.manage.contract.InstContractQueryService;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.request.InstContractFxBatchModifiedReqDTO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.*;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.infrastructure.adapter.CommCenterNoticeAdapter;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOperateLogPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractFeeItemRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOperateLogRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/16
 * @DESC
 */
@Slf4j
@Setter
@Component
@RequiredArgsConstructor
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.INST_CENTER_FX_MANAGER, actionType = OperateTypeEnum.BATCH_SYNC)
public class InstContractFxBatchSyncHandler extends AbstractWorkflowHandler {

    private final IDGenerator idGenerator;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final InstContractProductAssembler instContractProductAssembler;
    private final TransactionTemplate transactionTemplate;
    private final InstContractBusinessValidator businessValidator;
    private final OperateLogFactory operateLogFactory;
    private final InstContractOperateLogRepository operateLogRepository;
    private final InstContractFeeItemRepository feeItemRepository;
    private final InstContractQueryService contractQueryService;
    private final CommCenterNoticeAdapter commCenterNoticeAdapter;

    private final static String FX_BATCH_SYNC_DRAFT_KEY = "FX_BATCH_SYNC";

    @NacosValue(value = "${inst.contract.fx.batch.sync.shareId:lihongji}", autoRefreshed = true)
    private String fxBatchSyncShareId;
    @NacosValue(value = "${inst.contract.fx.batch.sync.webhook:https://open.feishu.cn/open-apis/bot/v2/hook/5a189bf5-16a4-4566-a02f-6dc7e1a0693e}", autoRefreshed = true)
    private String fxSyncNoticeWebhook;

    @NacosValue(value = "${inst.contract.fx.batch.sync.needRetry:true}", autoRefreshed = true)
    private Boolean needRetry;
    @NacosValue(value = "${inst.contract.fx.batch.sync.maxRetryCount:5}", autoRefreshed = true)
    private int maxRetryCount;

    private static final String SCENE_CODE = "FX_CHANGE_NOTICE";


    /**
     * FX 加点批量同步受理
     */
    public String acceptFxBatchSync(InstContractFxBatchSyncRequest request, long transactionTime){
        // 记录草稿
        List<InstBusinessDraftPO> unfinishedList = businessDraftRepository.getUnfinishedList(FX_BATCH_SYNC_DRAFT_KEY, BusinessTypeEnum.INST_CENTER, OperateModuleEnum.INST_CENTER_FX_MANAGER, OperateTypeEnum.BATCH_SYNC);
        AssertUtil.isTrue(CollectionUtils.isEmpty(unfinishedList), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), "存在已受理处理中批量同步流程");
        InstContractFxBatchSyncDto syncMsg = instContractProductAssembler.channelFxBatchSyncReq2Dto(request, transactionTime);
        InstBusinessDraftPO draft = instContractProductAssembler.channelFxBatchSync2Draft(syncMsg, idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.FX_MANAGE), FX_BATCH_SYNC_DRAFT_KEY, fxBatchSyncShareId);
        businessDraftRepository.save(draft);
        return draft.getDraftId();
    }

    /**
     * FX 加点批量同步执行
     */
    public Boolean fxBatchSyncExecute(String draftId){
        final String logPrefix = "FX 加点批量同步-";
        log.info("{}开始执行", logPrefix);

        InstBusinessDraft draft = InstBusinessDraftAssembler.INSTANCE.po2Domain(businessDraftRepository.getById(draftId));
        AssertUtil.isTrue(Objects.nonNull(draft), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), String.format("不存在该流程: %s", draftId));
        AssertUtil.isTrue(!draft.isFinalStatus(), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), String.format("流程已到达终态: %s, %s", draftId, draft.getStatus()));

        InstContractFxBatchSyncDto fxBatchSyncMsg = JSON.parseObject(draft.getDraftData(), InstContractFxBatchSyncDto.class);
        long transactionTime = fxBatchSyncMsg.getTransactionTime();
        // 执行
        try  {
            transactionTemplate.execute(status -> {
                // 开始批量同步
                List<InstContractFxBatchSyncDto.SyncMsg> syncMsgList = fxBatchSyncMsg.getFxConfigSyncList().stream()
                        .map(item -> batchFxBatchModifiedWithoutWorkflow(item, transactionTime))
                        .filter(Objects::nonNull).collect(Collectors.toList());
                // 同步完成推进草稿状态
                businessDraftRepository.updateDraftStatus(draftId, InstProcessStatusEnum.PASS.name());
                // 发送通知
                sendNotice(syncMsgList);
                log.info("{}执行完成", logPrefix);
                return Boolean.TRUE;
            });
        }catch (Exception e){
            // 草稿进入待重试状态
            businessDraftRepository.handleRetryOrAbort(InstBusinessDraftAssembler.INSTANCE.domain2Po(draft), needRetry, maxRetryCount);
            log.warn("{}执行失败，回滚，等待重试: {}", logPrefix, draft.getDraftId());
        }
        return Boolean.TRUE;
    }





    private InstContractFxBatchSyncDto.SyncMsg batchFxBatchModifiedWithoutWorkflow(InstContractFxBatchSyncDto.FxConfigItem fxConfigItem, long transactionTime){
        String commonMsg = String.format("%s-%s-%s-%s-%s", fxConfigItem.getBizType(), fxConfigItem.getInstCode(), fxConfigItem.getEntity(), fxConfigItem.getPayCurrency(), fxConfigItem.getFxSpread());
        log.info("FX 批量变更开始: {}", commonMsg);
        try{
            // 根据 合约获取费用列表
            InstContractVersionInfo versionInfo = contractQueryService.queryActiveContract(fxConfigItem.getBizType().name(), fxConfigItem.getInstCode(), fxConfigItem.getEntity(), transactionTime);
            List<InstContractFeeItemPO> originItemList = versionInfo.getOriginProducts().stream()
                    .filter(product -> CollectionUtils.isNotEmpty(product.getContractFeeItems()))
                    // 展开费用列表 -> 根据币种过滤 —> 对象转换
                    .flatMap(product -> product.getContractFeeItems().stream())
                    .filter(feeItem -> feeItem.getPayCurrency().equalsIgnoreCase(fxConfigItem.getPayCurrency()))
                    .map(instContractProductAssembler::feeItem2Po)
                    .collect(Collectors.toList());
            AssertUtil.isTrue(CollectionUtils.isNotEmpty(originItemList), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), "feeItemList is empty");

            // 批量修改预检查
            String businessKey = businessValidator.fxBatchModifyPreCheck(originItemList);

            // 组装费用数据
            InstContractFxBatchModifiedReqDTO.FxConfig fxConfig = instContractProductAssembler.channelFxBatchSyncItem2Req(fxConfigItem);
            List<InstContractFeeItemPO> modifiedItemList = originItemList.stream()
                    .map(instContractProductAssembler::feePoDeepCopy)
                    .peek(item -> instContractProductAssembler.fx2FeeItemPo(item, fxConfig))
                    .collect(Collectors.toList());

            // 构造变更日志
            InstContractOperateLogPO logPo = operateLogFactory.composeOperateLog(fxBatchSyncShareId, businessKey,
                    JSON.toJSONString(originItemList), JSON.toJSONString(modifiedItemList),
                    OperateModuleEnum.INST_CENTER_FX_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
            logPo.setOperateContent("FX 加点系统自动变更");

            // 批量变更
            feeItemRepository.updateBatchById(modifiedItemList);

            // 保存日志
            operateLogRepository.save(logPo);

            return getChannelFxUpdateMsgList(originItemList, fxConfigItem);
        }catch (Exception e){
            String errorMsg = String.format("渠道 FX 批量同步失败, %s, %s", commonMsg, e);
            log.error(errorMsg);
            throw new BusinessException(ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), errorMsg);
        }
    }


    private InstContractFxBatchSyncDto.SyncMsg getChannelFxUpdateMsgList(List<InstContractFeeItemPO> originItemList, InstContractFxBatchSyncDto.FxConfigItem fxConfigItem){
        // 计算原始加点及修改加点偏差
        InstContractFeeItemPO sampleOriginItem = originItemList.get(0);// CHECK 获取第一条作为模板
        BigDecimal modifyFxSpread = new BigDecimal(fxConfigItem.getFxSpread());
        BigDecimal fxSpreadDeviation = modifyFxSpread.subtract(sampleOriginItem.getFxSpread()).abs();

        return instContractProductAssembler.channelFxBatchSyncItem2SyncMsg(fxConfigItem, sampleOriginItem, fxSpreadDeviation);
    }

    private void sendNotice(List<InstContractFxBatchSyncDto.SyncMsg> fxSyncMsgList){
        try {
            log.info("FX 加点批量同步开始发送通知");
            Map<String, Object> noticeContent = new HashMap<String, Object>(4){{
                put("content", JSON.parseObject(JSON.toJSONString(fxSyncMsgList), new TypeReference<List<Map<String, Object>>>(){}));
            }};
            String businessKey = idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.FX_MANAGE);
            log.info("通知内容: {}, 消息唯一键: {}, 通知地址: {}, 场景CODE: {}", noticeContent, businessKey, fxSyncNoticeWebhook, SCENE_CODE);
            commCenterNoticeAdapter.sendWebhookNotice(businessKey, fxSyncNoticeWebhook, SCENE_CODE, noticeContent);
            log.info("FX 加点批量同步发送通知完成");
        }catch (Exception e){
            log.error("FX 加点批量同步发送通知失败, {}", e.getMessage());
        }
    }


    /**
     * 重试处理
     * @param draft 业务草稿
     * @return Boolean 处理结果
     */
    public Boolean retryHandler(InstBusinessDraft draft){
        if(needRetry){
            log.info("开始重试: {}", draft.getDraftId());
            retryPreCheck(draft);
            fxBatchSyncExecute(draft.getDraftId());
        }else{
            log.warn("已关闭重试");
        }
        return Boolean.TRUE;
    }

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        log.info("空实现");
        return null;
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("空实现");
        return null;
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("空实现");
        return null;
    }
}
