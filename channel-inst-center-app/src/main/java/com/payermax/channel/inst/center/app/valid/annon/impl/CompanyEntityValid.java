package com.payermax.channel.inst.center.app.valid.annon.impl;

import com.payermax.channel.inst.center.common.constrains.InterfaceValid;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.infrastructure.client.OmcCrmFacadeClientProxy;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.crm.facade.req.CompanyEntityListReq;
import com.payermax.crm.facade.resp.CompanyEntityListResp;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CompanyEntityValid
 *
 * <AUTHOR>
 * @desc
 */
@Component
public class CompanyEntityValid implements InterfaceValid<String, CompanyEntityValid.SimpleCompanyEntity> {

    @Resource
    private OmcCrmFacadeClientProxy crmFacadeClientProxy;

    @Override
    public boolean valid(String value, LangEnum lang) {
        if(StringUtil.isEmpty(value)) {
            return Boolean.TRUE;
        }
        return dataList().stream().anyMatch(item -> value.equals(item.getGroupEntityCode()));
    }

    @Override
    public List<SimpleCompanyEntity> queryData() {
        List<CompanyEntityListResp> list = crmFacadeClientProxy.companyEntityListQuery(new CompanyEntityListReq());
        return list.stream().map(item -> new SimpleCompanyEntity().setGroupEntityCode(item.getGroupEntityCode())
                .setEntityBizCode(item.getEntityBizCode())).collect(Collectors.toList());
    }

    @Data
    @Accessors(chain = true)
    public class SimpleCompanyEntity {
        /**
         * 集团主体编码，见表 omc_crm.tb_company_entity_config
         * @mock HK03
         */
        private String groupEntityCode;

        /**
         * 交易编码
         * @mock P01
         */
        private String entityBizCode;
    }
}
