package com.payermax.channel.inst.center.app.manage.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.enums.instcenter.StatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity;
import com.payermax.channel.inst.center.app.manage.InstTargetOrgManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstTargetOrgReqDTO;
import com.payermax.channel.inst.center.app.response.InstTargetOrgVO;
import com.payermax.channel.inst.center.app.service.InstTargetOrgService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName InstTargetOrgManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 13:03
 */
@Service
public class InstTargetOrgManagerImpl implements InstTargetOrgManager {

    @Autowired
    private InstTargetOrgService instTargetOrgService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;

    @Autowired
    private RespVoAssembler respVoAssembler;

    @Override
    public int save(InstTargetOrgReqDTO record) {
        InstTargetOrgEntity instTargetOrgEntity = reqDtoAssembler.toInstTargetOrgEntity(record);
        int result = instTargetOrgService.save(instTargetOrgEntity);
        return result;
    }

    @Override
    public int saveAll(List<String> targetOrgs) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(targetOrgs), "param targetOrgs is mandatory");
        List<InstTargetOrgEntity> targetOrgEntityList = targetOrgs.stream().filter(obj -> StringUtils.isNotBlank(obj)).distinct().map(targetOrg -> {
            InstTargetOrgEntity entity = new InstTargetOrgEntity();
            entity.setStatus(StatusEnum.Y.getValue());
            entity.setTargetOrgCode(targetOrg);
            return entity;
        }).collect(Collectors.toList());
        int result = instTargetOrgService.saveBatch(targetOrgEntityList);
        return result;
    }

    @Override
    public List<InstTargetOrgVO> query(InstTargetOrgReqDTO record) {
        InstTargetOrgEntity instTargetOrgEntity = reqDtoAssembler.toInstTargetOrgEntity(record);
        instTargetOrgEntity.setStatus(StatusEnum.Y.getValue());
        List<InstTargetOrgEntity> instTargetOrgEntityList = instTargetOrgService.getByEntity(instTargetOrgEntity);
        if(CollectionUtils.isNotEmpty(instTargetOrgEntityList)){
            List<InstTargetOrgVO> instTargetOrgVOS = respVoAssembler.toInstTargetOrgVos(instTargetOrgEntityList);
            return instTargetOrgVOS;
        }
        return null;
    }
}
