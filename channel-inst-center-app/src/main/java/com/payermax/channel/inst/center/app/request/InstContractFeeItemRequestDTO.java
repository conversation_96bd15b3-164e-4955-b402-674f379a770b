package com.payermax.channel.inst.center.app.request;

import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**

* <AUTHOR>
* @date 2024/3/17
* @DESC 
*/
@Data
@EqualsAndHashCode(callSuper = true)
public class InstContractFeeItemRequestDTO extends InstContractFeeItemPO implements Serializable {


    private Boolean isModify = Boolean.FALSE;

    private Boolean isDelete = Boolean.FALSE;

}
