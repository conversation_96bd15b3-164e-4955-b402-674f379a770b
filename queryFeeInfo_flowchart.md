# queryFeeInfo方法详细流程图

```mermaid
flowchart TD
    A[开始: queryFeeInfo] --> B[ValidationUtils.validate校验请求参数]
    B --> C[instContractRequestAssembler.convertRequest2Response<br/>初始化响应对象]
    C --> D[paramsCheck参数校验]
    
    D --> D1{检查targetOrg和cardOrg<br/>是否全部为空}
    D1 -->|是| D2[抛出异常: 目标机构和卡组不能全部为空]
    D1 -->|否| D3{检查bizType是否为I或O}
    D3 -->|否| D4[抛出异常: BizType只能为I/O]
    D3 -->|是| D5[处理兜底数据替换]
    D5 --> D6[targetOrg替换*为空字符串]
    D6 --> D7[cardOrg替换CARDPAY为空字符串]
    D7 --> D8{mcc是否为空}
    D8 -->|是| D9[设置mcc为UNKNOWN]
    D8 -->|否| E
    D9 --> E
    
    <PERSON>[1. 查询合同: instContractQueryService.queryActiveContract] --> E1[根据mid+bizType查询映射关系]
    E1 --> E2[根据合同号和交易时间查询生效合同版本]
    E2 --> E3[转换为领域对象InstContractVersionInfo]
    E3 --> F[设置合同号和版本到响应对象]
    
    F --> G[2. 查询标准产品: queryStandardProduct] --> G1[从合同的标准产品列表中匹配]
    G1 --> G2{精准匹配标准产品}
    G2 -->|匹配成功| H
    G2 -->|匹配失败| G3[模糊匹配标准产品]
    G3 --> G4{模糊匹配成功}
    G4 -->|成功| H
    G4 -->|失败| G5[抛出异常: 标准产品没找到]
    
    H[3. 查询原始产品: queryOriginProductByNo] --> H1[根据标准产品的原始产品编号查询]
    H1 --> H2[从合同版本的原始产品列表中过滤匹配]
    H2 --> I
    
    I[4. 查询费用信息: queryFeeByOriProduct] --> I1[获取原始产品的费用项列表]
    I1 --> I2[构建漏斗匹配项FunnelMatchItem]
    I2 --> I3[使用漏斗模型匹配服务匹配费用]
    I3 --> J
    
    J[5. 查询结算信息] --> J1{业务类型是否为I入款}
    J1 -->|否| K
    J1 -->|是| J2[querySettleByOriProduct查询结算信息]
    J2 --> J3[精准匹配结算项]
    J3 --> J4{精准匹配成功}
    J4 -->|成功| K
    J4 -->|失败| J5[模糊匹配结算项]
    J5 --> J6{模糊匹配成功}
    J6 -->|成功| K
    J6 -->|失败| J7[记录警告日志]
    J7 --> J8[channelErrInfoNotify异步通知错误信息]
    J8 --> K
    
    K[6. 组装返回: assemblerResponse] --> K1[处理费用配置映射]
    K1 --> K2[补全争议费用配置]
    K2 --> K3[设置费用配置到响应对象]
    K3 --> K4[设置计算精度和舍入模式]
    K4 --> K5[处理税费配置]
    K5 --> K6[处理结算配置]
    K6 --> L[返回成功结果]
    
    %% 异常处理
    B --> EX1[BizException]
    D --> EX1
    E --> EX1
    G --> EX1
    H --> EX1
    I --> EX1
    J2 --> EX2[Exception - 结算信息查询异常]
    EX2 --> J7
    
    EX1 --> EX3[BusinessException/IllegalArgumentException]
    EX1 --> EX4[其他Exception]
    EX3 --> EX5[返回业务异常结果]
    EX4 --> EX6[返回未知异常结果]
    
    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style D2 fill:#ffcdd2
    style D4 fill:#ffcdd2
    style G5 fill:#ffcdd2
    style EX5 fill:#ffcdd2
    style EX6 fill:#ffcdd2
```

## 主要步骤说明

### 1. 参数校验阶段
- **ValidationUtils.validate**: 使用Hibernate Validator进行基础参数校验
- **paramsCheck**: 业务逻辑参数校验
  - 检查targetOrg和cardOrg不能全部为空
  - 检查bizType只能为I(入款)或O(出款)
  - 处理兜底数据替换和默认值设置

### 2. 合同查询阶段
- 根据渠道商户号(mid)和业务类型(bizType)查询合同映射关系
- 根据合同号和交易时间查询生效的合同版本
- 包含原始产品和标准产品信息

### 3. 产品匹配阶段
- **标准产品匹配**: 先精准匹配，失败后模糊匹配
- **原始产品查询**: 根据标准产品编号查询对应的原始产品

### 4. 费用信息查询
- 使用漏斗模型匹配服务进行费用匹配
- 支持多维度匹配(币种、MCC、商户号等)

### 5. 结算信息查询
- 仅对入款业务(bizType=I)查询结算信息
- 支持精准匹配和模糊匹配
- 异常情况下记录日志并异步通知

### 6. 响应组装
- 组装费用配置、税费配置、结算配置等信息
- 补全必要的默认配置(如争议费用)
