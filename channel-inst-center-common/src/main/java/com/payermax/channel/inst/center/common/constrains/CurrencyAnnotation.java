package com.payermax.channel.inst.center.common.constrains;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <AUTHOR> at 2023/6/11 9:16 AM
 **/
@Constraint(validatedBy = CurrencyAnnotationValidator.class)
@Target({java.lang.annotation.ElementType.FIELD})
@Retention(java.lang.annotation.RetentionPolicy.RUNTIME)
@Documented
public @interface CurrencyAnnotation {

    String message() default "currency not valid";

    String values() default "";

    //下面这两个属性必须添加
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
