package com.payermax.channel.inst.center.common.constrains;

import com.payermax.channel.inst.center.common.enums.LangEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * InterfaceAnnotation
 *
 * <AUTHOR>
 * @desc
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = { InterfaceAnnotationValidator.class })
public @interface InterfaceAnnotation {

    String message() default "data not valid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    Class<? extends InterfaceValid> clazz() default InterfaceValid.class;

    LangEnum lang() default LangEnum.ZH;
}
