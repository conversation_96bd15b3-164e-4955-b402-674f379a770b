package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/27
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum AccumulationMethodEnumV2 implements IDescOnExcelEnum{

    NEXT("next", "Next Cycle", "Next Cycle"),

    CURRENT("current", "Current Cycle", "Current Cycle"),
    ;


    private final String code;

    private final String desc;

    private final String descOnExcel;
}
