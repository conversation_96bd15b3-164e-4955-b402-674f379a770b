package com.payermax.channel.inst.center.common.exception;

import com.zaxxer.hikari.SQLExceptionOverride;
import java.sql.SQLException;

public class HikariCPSQLException implements SQLExceptionOverride {
  @java.lang.Override
  public Override adjudicate(final SQLException sqlException) {
    String sqlState = sqlException.getSQLState();
    if ("08S02".equalsIgnoreCase(sqlState) || "08007".equalsIgnoreCase(sqlState)) {
      return Override.DO_NOT_EVICT;
    } else {
      return Override.CONTINUE_EVICT;
    }
  }
}
