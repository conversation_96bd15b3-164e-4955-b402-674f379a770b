package com.payermax.channel.inst.center.common.utils;

import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.constrains.PercentAnnotationValidator;
import com.payermax.channel.inst.center.common.enums.IDescEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;

/**
 * <AUTHOR> at 2023/6/14 8:35 PM
 *
 * 在bean转换过程中 辅助工具类
 **/
public class ConvertUtils {

    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100.00");

    /**
     * @param percentStr 1.5%
     * @return new BigDecimal("0.015")，方便理解 + 计算
     */
    public static BigDecimal convertPercentBigDecimal(String percentStr) {
        if (PercentAnnotationValidator.checkValid(percentStr, false)) {

            return new BigDecimal(percentStr.substring(0, percentStr.length() - 1)).divide(ONE_HUNDRED, 5, RoundingMode.HALF_UP);
        }
        return null;
    }

    /**
     * @param rawDecimal 100000.00
     * @return new BigDecimal("100000.00")
     */
    public static BigDecimal convertRawBigDecimal(String rawDecimal) {
        if (StringUtils.isBlank(rawDecimal)) {
            return null;
        }

        return new BigDecimal(rawDecimal);
    }

    /**
     * 在机构合约录入中，会有不同MCC，成本不同的场景，例如 零售行业，电商行业5%，其他行业4%
     * 在录入时候会是  零售,电商 5%，!零售,电商 4%
     *
     * @param rawConfig  !零售,电商
     * @return processExceptLogicKeyEnum 零售,电商
     *         processOnLogicKeyEnum  LogicKeyEnum.EXCLUDE
     */
    public static String processExceptLogicKeyEnum(String rawConfig) {
        if (StringUtils.isBlank(rawConfig)) {
            return rawConfig;
        }

        if (StringUtils.startsWith(rawConfig, SymbolConstants.SYMBOL_EXCLAMATION_POINT_ENG)
                || StringUtils.startsWith(rawConfig, SymbolConstants.SYMBOL_EXCLAMATION_POINT_CH)) {
            return rawConfig.substring(1);
        }
        return rawConfig;
    }
    public static LogicKeyEnum processOnLogicKeyEnum(String rawConfig) {
        if (StringUtils.isBlank(rawConfig)) {
            return null;
        }

        if (StringUtils.startsWith(rawConfig, SymbolConstants.SYMBOL_EXCLAMATION_POINT_ENG)
                || StringUtils.startsWith(rawConfig, SymbolConstants.SYMBOL_EXCLAMATION_POINT_CH)) {
            return LogicKeyEnum.EXCLUDE;
        }
        return LogicKeyEnum.INCLUDE;
    }

    public static <E extends IDescEnum> E getEnumByDesc(Class<E> classInstance, String value) {
        E[] enums = classInstance.getEnumConstants();
        LangEnum langEnum = LangEnum.getLangEnumThreadLocal();

        return Arrays.stream(enums).filter(item -> StringUtils.equalsIgnoreCase(item.getDesc(langEnum), value))
                .findFirst().orElse(null); //NO_CHECK 匹配枚举类中的值，无风险
    }
}
