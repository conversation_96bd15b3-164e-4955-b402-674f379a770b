package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

public enum CountryEnum {

    IN("IN", -330, "印度", "INR"),
    CN("CN", -480, "中国", "CNY"),
    ID("ID", -420, "印尼", "IDR"),
    PH("PH", -480, "菲律宾", "PHP"),
    AE("AE", -240, "阿联酋", "AED"),
    SA("SA", -180, "沙特阿拉伯", "SR"),
    EG("EG", -120, "埃及", "EGP"),
    HK("HK", -480, "香港", "HKD"),
    SG("SG", -480, "新加坡", "SGD"),
    MY("MY", -480, "马来西亚", "MYR"),
    PL("PL", -60, "波兰", "PLN"),
    RU("RU", -180, "俄罗斯", "RUB"),
    ZA("ZA", -120, "南非", "ZAR"),
    BR("BR", 180, "巴西", "BRL"),
    TR("TR", -180, "土耳其", "TRL"),
    BH("BH", -180, "巴林", "BHD"),
    KW("KW", -180, "科威特", "KWD"),
    OM("OM", -240, "阿曼", "OMR"),
    QA("QA", -180, "卡塔尔", "QAR"),
    JO("JO", -120, "约旦", "JOD"),
    LB("LB", -120, "黎巴嫩", "LBP"),
    TH("TH", -420, "泰国", "THB"),
    CL("CL", 240, "智利", "CLP"),
    CO("CO", 300, "哥伦比亚", "COP"),
    MX("MX", 360, "墨西哥", "MXN"),
    PE("PE", 300, "秘鲁", "PEN"),
    PY("PY", 240, "巴拉圭", "PYG"),
    US("US", 480, "美国", "USD"),
    AU("AU", -480, "澳大利亚", "AUD"),
    UY("UY", 180, "乌拉圭", "UYU"),
    AR("AR", 180, "阿根廷", "ARS"),
    TW("TW", -480, "中国台湾", "TWD"),
    KR("KR", -540, "韩国", "KRW"),
    VN("VN", -420, "越南", "VND");

    @Getter
    private String countryCode;

    @Getter
    private Integer jetLag;

    @Getter
    private String countryName;

    @Getter
    private String currency;

    CountryEnum(String countryCode, Integer jetLag, String timeZone, String currency) {
        this.countryCode = countryCode;
        this.jetLag = jetLag;
        this.countryName = timeZone;
        this.currency = currency;
    }

    public static Integer getJetLag(String countryCode) {
        for (CountryEnum countryEnum : values()) {
            if (countryEnum.countryCode.equals(countryCode)) {
                return countryEnum.getJetLag();
            }
        }
        return 0;
    }

    public static String getCountryName(String countryCode) {
        for (CountryEnum countryEnum : values()) {
            if (countryEnum.countryCode.equals(countryCode)) {
                return countryEnum.getCountryName();
            }
        }
        return null;
    }

    public static String getCurrency(String countryCode) {
        for (CountryEnum countryEnum : values()) {
            if (countryEnum.countryCode.equals(countryCode)) {
                return countryEnum.getCurrency();
            }
        }
        return null;
    }
}
