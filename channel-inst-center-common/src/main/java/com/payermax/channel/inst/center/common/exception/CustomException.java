package com.payermax.channel.inst.center.common.exception;


import com.payermax.channel.inst.center.common.result.BaseResult;
import lombok.Data;

@Data
public class CustomException extends RuntimeException {

    private String code;

    private String msg;

    public CustomException(String msg){
        this.code = BaseResult.ResponseEnum.BAD_REQUEST.getCode();
        this.msg = msg;
    }

    public CustomException(String code, String msg){
        this.code = code;
        this.msg = msg;
    }
}
