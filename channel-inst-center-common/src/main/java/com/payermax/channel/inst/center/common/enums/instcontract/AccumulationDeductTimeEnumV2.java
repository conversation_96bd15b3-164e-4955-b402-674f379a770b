package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/27
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum AccumulationDeductTimeEnumV2 implements IDescOnExcelEnum{

    REALTIME("realtime", "Real Time Deduction", "Real Time Deduction"),

    PERIODIC("periodic", "Periodic Unified Deduction", "Periodic Unified Deduction"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;

}
