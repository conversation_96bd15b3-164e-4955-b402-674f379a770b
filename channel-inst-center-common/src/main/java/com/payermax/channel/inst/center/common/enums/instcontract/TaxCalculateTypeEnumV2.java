package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 税种计算方式枚举类
 */
@AllArgsConstructor
@Getter
public enum TaxCalculateTypeEnumV2 implements IDescOnExcelEnum {

    VAT_INCLUDED_IN_TPV("vat-included-in-tpv", "交易金额 * 税率 / (1 + 税率)", "TransactionAmount*TaxRatio/(1+TaxRatio)"),

    TAX_ON_TPV("tax-on-tpv", "交易金额 * 税率", "TransactionAmount*TaxRatio"),

    TAX_ON_FEE("tax-on-fee", "手续费金额 * 税率", "Fee*TaxRatio"),

    TAX_ON_TPV_EXCLUDE_FEE("tax-on-tpv-exclude-fee", "(交易金额 - 手续费) * 税率", "(TransactionAmount-Fee)*TaxRatio"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;
}
