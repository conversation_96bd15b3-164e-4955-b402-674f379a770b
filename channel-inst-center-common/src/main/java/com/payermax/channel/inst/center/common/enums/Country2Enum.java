package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

@Getter
public enum Country2Enum {

    IN("IN", "印度", "UTC + 5:30 Kolkata(India)", "INR", "India"),
    ID("ID", "印尼", "UTC + 7:00 Jakarta(Indonesia)", "IDR", "Indonesia"),
    PH("PH", "菲律宾", "UTC + 8:00 Manila(Philippines)", "PHP", "Philippines"),
    AE("AE", "阿联酋", "UTC + 4:00 Dubai(United Arab Emirates)", "AED", "United Arab Emirates"),
    SA("SA", "沙特阿拉伯", "UTC + 3:00 Riyadh(Saudi Arabia)", "SR", "Saudi Arabia"),
    EG("EG", "埃及", "UTC + 2:00 Cairo(Egypt)", "EGP", "Egypt"),
    HK("HK", "香港", "UTC + 8:00 ", "HKD", "HongKong"),
    SG("SG", "新加坡", "UTC + 8:00 Singapore(Singapore)", "SGD", "Singapore"),
    MY("MY", "马来西亚", "UTC + 8:00 Kuching(Malaysia)", "MYR", "Malaysia"),
    PL("PL", "波兰", "UTC + 1:00 Warsaw(Poland)", "USD", "Poland"),
    CN("CN", "中国", "UTC + 8:00 Beijing(China)", "CNY", "China"),
    RU("RU", "俄罗斯", "UTC + 3:00 Moscow(Russia)", "RUB", "Russia"),
    ZA("ZA", "南非", "UTC + 2:00 Pretoria(South Africa)", "ZAR", "South Africa"),
    BR("BR", "巴西", "UTC -3:00 São Paulo(Brazil)", "BRL", "Brazil"),
    TR("TR", "土耳其", "UTC + 2:00 Ankara(Turkey)", "TRY", "Turkey"),
    BH("BH", "巴林", "UTC + 3:00 Manama(Bahrain)", "BHD", "Bahrain"),
    KW("KW", "科威特", "UTC + 3:00 Kuwait City(Kuwait)", "KWD", "Kuwait"),
    OM("OM", "阿曼", "UTC + 4:00 Muscat(Oman)", "OMR", "Oman"),
    QA("QA", "卡塔尔", "UTC + 3:00 Doha(Qatar)", "QAR", "Qatar"),
    JO("JO", "约旦", "UTC + 2:00 Amman(Jordan)", "USD", "Jordan"),
    LB("LB", "黎巴嫩", "UTC + 2:00 Beirut(Lebanon)", "USD", "Lebanon"),
    TH("TH", "泰国", "UTC +7:00 Bangkok(Thailand)", "THB", "Thailand"),
    CL("CL", "智利", "UTC -4:00 Santiago(Chile)", "CLP", "Chile"),
    CO("CO", "哥伦比亚", "UTC -5:00 Bogota(Colombia)", "COP", "Colombia"),
    MX("MX", "墨西哥", "UTC -6:00 Mexico City(Mexico)", "MXN", "Mexico"),
    PE("PE", "秘鲁", "UTC -5:00 Lima(Peru)", "PEN", "Peru"),
    PY("PY", "巴拉圭", "UTC -4:00 Asuncion(Paraguay)", "PYG", "Paraguay"),
    US("US", "美国", "UTC -8:00 Asuncion(Paraguay)", "USD", "America"),
    AU("AU", "澳大利亚", "UTC +8:00 Asuncion(Paraguay)", "AUD", "Australia"),
    UY("UY", "乌拉圭", "UTC -3:00 Montevideo(Uruguay)", "UYU", "Uruguay"),
    AR("AR", "阿根廷", "UTC -3:00 Argentina Time", "ARS", "Argentina"),
    TW("TW", "中国台湾", "UTC +8:00  Beijing(China)", "TWD", "Taiwan(China)"),
    KR("KR", "韩国", "UTC +9:00 Seoul(South Korea)", "KRW", "South Korea"),
    VN("VN", "越南", "UTC +7:00 Ho Chi Minh(Vietnam)", "VND", "Vietnam");

    private String code;
    private String name;
    private String jetLag;
    private String currency;
    private String englishName;

    Country2Enum(String code, String name, String jetLag, String currency, String englishName) {
        this.code = code;
        this.name = name;
        this.jetLag = jetLag;
        this.currency = currency;
        this.englishName = englishName;
    }

    public static String getNameByCode(String code) {
        for (Country2Enum country : values()) {
            if (country.getCode().equals(code)) {
                return country.getName();
            }
        }
        return "";
    }

    public static String getEnglishNameByCode(String code) {
        for (Country2Enum country : values()) {
            if (country.getCode().equals(code)) {
                return country.getEnglishName();
            }
        }
        return null;
    }

    public static String getCurrencyByCode(String code) {
        for (Country2Enum country2Enum : values()) {
            if (country2Enum.getCode().equals(code)) {
                return country2Enum.getCurrency();
            }
        }
        return null;
    }
}
