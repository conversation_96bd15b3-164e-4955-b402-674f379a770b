package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

@Getter
public enum BizTypeEnum {

    PAYOUT((byte) 1, "Payout"),
    RECEIPT((byte) 2, "Receipt"),
    REFUND((byte) 3, "Refund"),
    SETTLE((byte) 4, "Settle"),
    RECHARGE((byte) 5, "Recharge"),
    CB((byte) 6, "Cb");

    private byte code;
    private String name;

    BizTypeEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(byte code) {
        for (BizTypeEnum bizType : values()) {
            if (bizType.getCode() == code) {
                return bizType.getName();
            }
        }
        return RECEIPT.getName();
    }
}
