package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

/**
 * 缓存域枚举
 *
 * <AUTHOR>
 * @date 2021/7/28 11:06
 */
public enum RegionEnum {

    AMOUNT_LIMIT("amountLimit", "金额限制", false),
    PAYMENT_METHOD_INST("paymentMethodInst", "支付方式实例空间", true),
    TABLE_EVENT("tableEvent", "临时事件表空间", true),
    TABLE_CODE("tableCode", "错误码映射表空间", true),
    TABLE_ROUTE("tableRoute", "路由表空间", true),
    TABLE_SPECIAL_ROUTE("tableSpecialRoute", "特殊路由表空间", true),
    TABLE_CHANNEL("tableChannel", "渠道配置表空间", true),
    TABLE_AUTO_ROUTE("tableAutoRoute", "渠道自动路由表空间", true),
    TABLE_PAYOUT("tablePayout", "出款表空间", false),
    TABLE_COUNTRY("tableCountry", "国家表空间", true),
    TABLE_TARGET_MERCHANT("tableTargetMerchant", "目标商户表空间", true),
    TABLE_CHANNEL_MERCHANT("tableChannelMerchant", "渠道和商户关系表空间", true),
    TABLE("table", "公共表空间", true),
    DEFAULT("default", "默认空间", true),
    LIMIT("limit", "交易限制", false);

    RegionEnum(String region, String name, boolean isClear) {
        this.region = region;
        this.name = name;
        this.isClear = isClear;
    }

    @Getter
    private String region;

    @Getter
    private String name;

    @Getter
    private boolean isClear;

    public static RegionEnum getValueByKey(String region) {
        for (RegionEnum current : values()) {
            if (current.getRegion().equals(region)) {
                return current;
            }
        }
        return null;
    }
}
