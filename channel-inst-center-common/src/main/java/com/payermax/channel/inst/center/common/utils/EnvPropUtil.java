package com.payermax.channel.inst.center.common.utils;

import com.payermax.common.lang.util.StringUtil;
import org.springframework.core.env.Environment;

import java.util.Objects;

/**
 * EnvPropUtil
 *
 * <AUTHOR>
 * @desc
 */
public class EnvPropUtil {

    private static final char PROP_SEPARATOR = '.';

    private static Environment ENVIRONMENT = null;

    public static String getProp(String... propNameItems) {
        if(Objects.isNull(propNameItems) || propNameItems.length == 0) {
            return null;
        }
        return getEnv().getProperty(StringUtil.join(propNameItems, PROP_SEPARATOR));
    }

    public static String getPropWithDefault(String defaultValue, String... propNameItems) {
        String value = getProp(propNameItems);
        return getValueWithDefault(value, defaultValue);
    }

    public static <T> T getProp(Class<T> clazz, String... propNameItems) {
        if(Objects.isNull(propNameItems) || propNameItems.length == 0) {
            return null;
        }
        return getEnv().getProperty(StringUtil.join(propNameItems, PROP_SEPARATOR), clazz);
    }

    public static <T> T getPropWithDefault(Class<T> clazz, T defaultValue, String... propNameItems) {
        T value = getProp(clazz, propNameItems);
        return getValueWithDefault(value, defaultValue);
    }

    private static Environment getEnv() {
        if(Objects.isNull(ENVIRONMENT)) {
            ENVIRONMENT = ApplicationUtils.getBean(Environment.class);
        }
        return ENVIRONMENT;
    }

    private static <T> T getValueWithDefault(T value, T defaultValue) {
        if(Objects.isNull(value)) {
            return defaultValue;
        }
        return value;
    }
}
