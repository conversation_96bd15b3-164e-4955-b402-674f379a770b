package com.payermax.channel.inst.center.common.utils;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URL;

/**
 * 暂无使用，使用前需充分测试
 */
@Slf4j
@Component
public class AwsS3Util {

    /**
     * 域名
     */
    @NacosValue(value = "${s3.upload.domain.name:https://img-cdn-sg.payermax.com/}", autoRefreshed = true)
    private String domainName;

    /**
     * region名称
     */
    @NacosValue(value = "${s3.upload.region.enum.name:ap-southeast-1}", autoRefreshed = true)
    private String regionName;

    private final static Logger logger = LoggerFactory.getLogger(AwsS3Util.class);

    private static AmazonS3 s3 = new AmazonS3Client(new InstanceProfileCredentialsProvider(false));


    /**
     * @param fileName
     * @param uploadFile
     * @param bucketName
     * @return
     * @dest 上传文件到s3
     */
    public String uploadToS3(String fileName, File uploadFile, String bucketName) {
        try {
            Region south1 = Region.getRegion(Regions.fromName(regionName));
            s3.setRegion(south1);

            s3.putObject(new PutObjectRequest(bucketName, fileName, uploadFile).withCannedAcl(CannedAccessControlList.Private));
            /**
             * 策略需要使用CannedAccessControlList.BucketOwnerFullControl
             */
            URL url = s3.getUrl(bucketName, fileName);

            if (url != null) {
                String urlString = url.toString();
                String[] split = urlString.split("src/");
                return domainName + split[1];
            }

            log.info(url.toString());
        } catch (Exception ase) {
            ase.printStackTrace();
            log.error("file upload exception :" + ase.getMessage());
        }
        log.error("file had uploaded done");
        return null;
    }

}
