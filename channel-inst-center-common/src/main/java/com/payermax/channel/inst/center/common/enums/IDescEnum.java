package com.payermax.channel.inst.center.common.enums;

/**
 * <AUTHOR> at 2023/6/11 11:06 AM
 **/
public interface IDescEnum {

    String getChineseDesc();

    String getEnglishDesc();


    default String getDesc(LangEnum langEnum) {
        return langEnum == LangEnum.EN ? getEnglishDesc() : getChineseDesc();
    }

    default String getDesc(String langCode) {
        System.out.println("DescEnum");
        return getDesc(LangEnum.parse(langCode));
    }
}
