package com.payermax.channel.inst.center.common.enums;

/**
 * 钉钉告警配置key
 *
 * <AUTHOR>
 * @date 2022/5/12 14:44
 */
public enum AlertEnum {

    /**
     * 【机构中心】机构信息推送集团合同系统异常通知
     */
    PUSH_INST_INFO_GROUP("push-inst-info-exceptionNotify", "【机构中心】机构信息推送集团合同系统异常通知"),


    /**
     * 【机构中心】申请VA账号异常
     */
    PUSH_SUB_ACCOUNT_EXCEPTION_GROUP("push-sub-account-exceptionNotify", "【机构中心】申请VA账号异常"),
    
    /**
     * 【机构中心】机构信息推送集团合同系统异常通知
     */
    PUSH_SUB_ACCOUNT_HANDLE_GROUP("push-sub-account-handleNotify", "【机构中心】申请VA账号通知"),

    OTHER("other", "其他")
    ;

    private String groupName;

    private String title;

    AlertEnum(String groupName, String title) {
        this.groupName = groupName;
        this.title = title;
    }

    public String getGroupName() {
        return groupName;
    }

    public String getTitle() {
        return title;
    }

}
