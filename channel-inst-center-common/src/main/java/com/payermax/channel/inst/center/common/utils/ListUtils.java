package com.payermax.channel.inst.center.common.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.common.lang.model.constants.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @DESC
 */
public class ListUtils {


    /**
     * 字符串转list
     * @param str 字符串
     * @param symbol 分隔符
     * @return list
     */
    public static List<String> string2List(String str, String symbol){
        if(StringUtils.isBlank(str)){
            return Collections.emptyList();
        }
        return new ArrayList<>(Arrays.asList(str.split(symbol)));
    }

    /**
     * list转字符串
     * @param list 列表
     * @param symbol 分隔符
     * @return 字符串
     */
    public static String list2String(List<String> list, String symbol){
        if(CollectionUtils.isEmpty(list)){
            return "";
        }
        return String.join(symbol, list);
    }

    /**
     * 字符串转枚举list
     * @param str 字符串
     * @param enumClass 枚举类
     * @param delimiter 分隔符
     * @return 列表
     */
    public static <T extends Enum<T>> List<T> string2EnumList(String str, String delimiter, Class<T> enumClass) {
        return Arrays.stream(str.split(delimiter))
                .map(String::trim)
                .map(enumString -> Enum.valueOf(enumClass, enumString))
                .collect(Collectors.toList());
    }

    /**
     * 枚举list转字符串
     * @param list 列表
     * @param delimiter 分隔符
     * @return 字符串
     */
    public static String enumList2String(List<? extends Enum<?>> list, String delimiter){
        if(CollectionUtils.isEmpty(list)){
            return "";
        }
        return list.stream().map(Enum::name).collect(Collectors.joining(delimiter));
    }

    /**
     * 快速构建 Key
     */
    public static String quickBuildUnderlineKey(Object... args){
        return Stream.of(args).filter(Objects::nonNull).map(String::valueOf)
                .collect(Collectors.joining(CommonConstants.SYMBOL_UNDERLINE));
    }
}
