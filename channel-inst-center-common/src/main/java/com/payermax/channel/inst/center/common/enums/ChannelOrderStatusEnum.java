package com.payermax.channel.inst.center.common.enums;

/**
 * 订单状态
 */
public enum ChannelOrderStatusEnum {

    INITIATE(9, "初始化"),
    PENDING(0, "进行中"),
    SUCCESS(1, "成功"),
    FAILED(2, "失败"),
    BOUNCEBACK(4, "退票");

    private Integer status;

    private String desc;

    ChannelOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据订单状态获取枚举
     *
     * @param status
     * @return
     */
    public static ChannelOrderStatusEnum getOrderStatusByStatus(Integer status) {
        for (ChannelOrderStatusEnum orderStatus : values()) {
            if (orderStatus.getStatus().equals(status)) {
                return orderStatus;
            }
        }
        return null;
    }

    /**
     * 是否是终态
     *
     * @param status
     * @return
     */
    public static boolean isFinalStatus(Integer status) {
        if (SUCCESS.getStatus().equals(status) || FAILED.getStatus().equals(status)) {
            return true;
        }
        return false;
    }
}

