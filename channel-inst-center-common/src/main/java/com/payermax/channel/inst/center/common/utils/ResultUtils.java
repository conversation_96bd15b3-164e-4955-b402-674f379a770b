package com.payermax.channel.inst.center.common.utils;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.constants.CommonConstants;
import com.payermax.common.lang.model.dto.Result;

public class ResultUtils {

    public static <T> Result<T> success(T data) {
        return new Result<T>(data, CommonConstants.RESULT_SUCCESS_MESSAGE, CommonConstants.RESULT_SUCCESS_CODE);
    }

    public static <T> Result<T> unknownFail() {
        return new Result<T>(ErrorCodeEnum.FAIL.getMsg(), ErrorCodeEnum.FAIL.getCode());
    }


    public static <T> Result<T> bizExceptionFail(BizException bizException) {
        return new Result<T>(bizException.getErrorCode().getMsg(), bizException.getErrorCode().getCode());
    }

    public static <T> Result<T> bizException(Exception e) {
        return new Result<T>(e.getMessage(), ErrorCodeEnum.PARAMETER_INVALID.getCode());
    }
    public static <T> Result<T> businessException(BusinessException e) {
        return new Result<T>(e.getMessage(), e.getErrCode());
    }
    public static <T> Result<T> parameterInvalidFail(Exception e) {
        return new Result<T>(e.getMessage(), ErrorCodeEnum.PARAMETER_INVALID.getCode());
    }

}
