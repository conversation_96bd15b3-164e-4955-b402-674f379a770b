package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

@Getter
public enum AccountTypeEnum {

    A_10001("10001", "商户待清算-入款业务"),
    A_10002("10002", "商户基本户-出款业务"),
    A_10003("10003", "商户待结算-入款业务"),
    A_10004("10004", "商户待打款-入款业务"),
    A_10005("10005", "冻结账户-CB冻结"),
    A_11002("11002", "商户基本户-出款业务");

    private String code;
    private String name;

    AccountTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getAccountTypeName(String code) {
        for (AccountTypeEnum bizType : values()) {
            if (bizType.getCode().equals(code) ) {
                return bizType.getName();
            }
        }
        return null;
    }
}
