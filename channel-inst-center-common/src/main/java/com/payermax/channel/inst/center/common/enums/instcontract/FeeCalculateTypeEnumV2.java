package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum FeeCalculateTypeEnumV2 implements IDescOnExcelEnum{

    /**
     * 单笔比例
     */
    SINGLE_RATE("single-rate", "单笔比例","Percentage"),

    /**
     * 单笔固定
     */
    SINGLE_MONEY("single-money", "单笔固定","Fix"),

    /**
     * 单笔组合
     */
    SINGLE_COMBINE("single-combine", "单笔组合","Percentage/Fix Combined"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;
}
