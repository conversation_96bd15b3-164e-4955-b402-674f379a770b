package com.payermax.channel.inst.center.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "分页结果")
public class PageResult<T> implements Serializable {

    @ApiModelProperty(value = "总条数", position = 1)
    private Long total;

    @ApiModelProperty(value = "数据", position = 2)
    private List<T> rows;
}
