package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

@Getter
public enum CountryRegionEnum {

    MIDDLE_EAST("中东", "AE,SA,BH,EG,JO,KW,LB,OM,QA"),

    LATIN_AMERICA("拉美", "BR,PE,PY"),

    OTHERS("其他", "HK,SG,PL,RU,ZA,TR,TH,CL,CO,MX,US,AU,UY,AR,TW");

    String name;
    String desc;

    CountryRegionEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
