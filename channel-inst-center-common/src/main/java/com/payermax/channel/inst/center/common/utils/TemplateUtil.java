/*
 * Copyright (C) [2023] [PayerMax]
 *
 * This file is part of [channel-gateway].
 *
 * [channel-gateway] is a product of [PayerMax]. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
 * following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following
 * disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the
 * following disclaimer in the documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of [PayerMax] nor the names of its contributors may be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY [PayerMax] "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL
 * [PayerMax] BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENT<PERSON><PERSON> DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * You should have received a copy of the GNU General Public License along with this program. If not, see
 * <http://www.gnu.org/licenses/>.
 */

package com.payermax.channel.inst.center.common.utils;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.engine.freemarker.FreemarkerEngine;
import cn.hutool.extra.template.engine.velocity.VelocityEngine;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> at 8/11/23 12:43 AM
 **/
public class TemplateUtil {
    
    private static final Map<TemplateType, TemplateEngine> TEMPLATE_ENGINE = new HashMap<>(2);

    static {
        TEMPLATE_ENGINE.put(TemplateType.FREEMARKER, createTemplateEngine(FreemarkerEngine.class));
        TEMPLATE_ENGINE.put(TemplateType.VELOCITY, createTemplateEngine(VelocityEngine.class));

    }

    private static TemplateEngine createTemplateEngine(Class<? extends TemplateEngine> customEngine) {
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setCustomEngine(customEngine);
        return cn.hutool.extra.template.TemplateUtil.createEngine(templateConfig);
    }

    public static String render(TemplateType templateType, String template, Map<String, Object> context) {
        template = template.replace("\\n","\n");
        return TEMPLATE_ENGINE.get(templateType).getTemplate(template).render(context);
    }

    public enum TemplateType {

        /**
         * VELOCITY
         */
        VELOCITY,

        /**
         * freemarker
         */
        FREEMARKER,;

        public static TemplateType getByCode(String type) {
            return EnumUtil.fromString(TemplateType.class, type);
        }

    }
}
