package com.payermax.channel.inst.center.common.enums;

import com.payermax.common.lang.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Locale;
import java.util.Objects;

/**
 * LangEnum
 *
 * <AUTHOR>
 * @desc
 */
@Getter
@AllArgsConstructor
public enum LangEnum {

    ZH("zh", "中文"),
    EN("en", "英文");

    private String code;

    private String desc;

    private static ThreadLocal<LangEnum> CONTRACT_LANG_ENUM_THREAD_LOCAL = new ThreadLocal<>();

    public static void setLangEnumThreadLocal(LangEnum langEnum) {
        CONTRACT_LANG_ENUM_THREAD_LOCAL.set(langEnum);
    }

    public static LangEnum getLangEnumThreadLocal() {
        LangEnum threadLocalLang = CONTRACT_LANG_ENUM_THREAD_LOCAL.get();
        return threadLocalLang == null ? LangEnum.ZH : threadLocalLang;
    }

    public static void invalidLangEnumThreadLocal() {
        CONTRACT_LANG_ENUM_THREAD_LOCAL.remove();
    }

    public static LangEnum parse(String code) {
        if(StringUtil.isEmpty(code)) {
            return ZH;
        }
        for(LangEnum langEnum : values()) {
            if(code.toUpperCase(Locale.ROOT).equals(langEnum.getCode().toUpperCase(Locale.ROOT))) {
                return langEnum;
            }
        }
        return ZH;
    }

    public static String code(LangEnum langEnum) {
        return langEnum(langEnum).getCode();
    }

    public static LangEnum langEnum(LangEnum langEnum) {
        if(Objects.isNull(langEnum)) {
            return ZH;
        }
        return langEnum;
    }
}
