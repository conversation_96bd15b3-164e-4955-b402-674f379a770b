package com.payermax.channel.inst.center.common.constrains;

import com.payermax.channel.inst.center.common.utils.ApplicationUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * InterfaceAnnotationValidator
 *
 * <AUTHOR>
 * @desc
 */
public class InterfaceAnnotationValidator implements ConstraintValidator<InterfaceAnnotation, String> {


    private InterfaceAnnotation constraintAnnotation;

    @Override
    public void initialize(InterfaceAnnotation constraintAnnotation) {
        this.constraintAnnotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        Class<? extends InterfaceValid> clazz = this.constraintAnnotation.clazz();
        InterfaceValid interfaceValid = ApplicationUtils.getBean(clazz);
        return interfaceValid.valid(s, this.constraintAnnotation.lang());
    }
}
