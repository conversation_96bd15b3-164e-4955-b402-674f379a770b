package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

public enum AwsUploadLocationEnum {

    /**
     * 文件类型
     */
    ADD_THIRD_ORDER_NO(1, "orderinfo");

    @Getter
    private Integer type;

    @Getter
    private String location;

    AwsUploadLocationEnum(Integer type, String location) {
        this.type = type;
        this.location = location;
    }

    public static AwsUploadLocationEnum getByValue(Integer value) {
        for (AwsUploadLocationEnum awsUploadLocationEnum : AwsUploadLocationEnum.values()) {
            if (awsUploadLocationEnum.getType().equals(value)) {
                return awsUploadLocationEnum;
            }
        }
        return null;
    }
}
