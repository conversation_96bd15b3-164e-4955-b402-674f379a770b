package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/26
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum CurrencyExchangeTimingEnumV2 implements IDescOnExcelEnum{

    /**
     * 交易日换汇
     */
    EXCHANGE_IN_TRADE_DAY("exchange-in-trade-day", "交易日换汇", "Payment Day"),

    /**
     * 结算日换汇
     */
    EXCHANGE_IN_SETTLEMENT_DAY("exchange-in-settlement-day", "结算日换汇", "Deposit Day"),

    /**
     * 提现日换汇
     */
    EXCHANGE_IN_WITHDRAW_DAY("exchange-in-withdraw-day", "提现日换汇", "Withdraw Day"),

    /**
     * 不换汇
     */
    NO_EXCHANGE("on-exchange", "不换汇","No Exchange"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;
}
