package com.payermax.channel.inst.center.common.constrains;

import com.payermax.common.lang.util.StringUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * NumberValidator
 *
 * <AUTHOR>
 * @desc
 */
public class NumberAnnotationValidator implements ConstraintValidator<NumberAnnotation, String> {

    private static final Pattern INTEGER_PATTERN = Pattern.compile("^0|([-]?[1-9][0-9]*)$");

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^0|([-]?[1-9][0-9]*)|([-]?[1-9][0-9]*\\.[0-9]*)|([-]?0\\.[0-9]*)$");

    private NumberAnnotation constraintAnnotation;

    @Override
    public void initialize(NumberAnnotation constraintAnnotation) {
        this.constraintAnnotation = constraintAnnotation;
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        boolean empty = StringUtil.isEmpty(s);
        if(constraintAnnotation.allowNull() && empty) {
            return true;
        }
        if(empty) {
            return false;
        }
        if(constraintAnnotation.isInteger()) {
            return INTEGER_PATTERN.matcher(s).matches();
        }
        return NUMBER_PATTERN.matcher(s).matches();
    }
}
