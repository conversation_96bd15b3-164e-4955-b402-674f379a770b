package com.payermax.channel.inst.center.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum ErrorCodeEnum {

  /**
   * 统一错误码
   */
  SUCCESS("0000", "success"),

  ILLEGAL_ARGUMENT_ERROR("ILLEGAL_ARGUMENTS_ERROR","ILLEGAL_ARGUMENTS_ERROR"),

  BUSINESS_SCENE_NOT_SUPPORTED("500", "business scene not supported"),

  RATE_DIRECTION_TYPE_UNKNOWN("501", "cannot know use bid rate or offer for this business scene"),

  CALCULATE_BID_OR_OFFER_RATE_NOT_SUPPORT("502", "cannot calculate bid or offer rate"),

  BID_OFFER_MID_ALL_MISSING("1000", "bid/offer/mid rate are all missing"),

  BID_OFFER_ONLY_EXIST_BID("1001", "only bid rate, no offer rate"),

  BID_OFFER_ONLY_EXIST_OFFER("1002", "only offer rate, no bid rate"),

  MID_RATE_LESS_EQUAL_ZERO("1003", "mid rate less than or equal to zero"),

  BID_RATE_LESS_EQUAL_ZERO("1004", "bid rate less than or equal to zero"),

  OFFER_RATE_LESS_EQUAL_ZERO("1005", "offer rate less than or equal to zero"),

  BID_GREATER_THAN_OFFER("1006", "bid rate great than offer rate"),

  CONFIG_BENCHMARK_PLAN_ITEM_EMPTY("2000", "have no currency config in benchmark plan"),

  CONFIG_BENCHMARK_PLAN_ITEM_REFERENCE_EMPTY("2001", "currency pair in benchmark plan reference null institution config"),

  CONTRACT_SPREAD_QUERY_ERROR("5001", "result error when query contract spread"),

  CONTRACT_SPREAD_QUERY_EXCEPTION("5002", "exception when query contract spread"),

  CONTRACT_RESULT_CLIENT_PLAN_CODE_MISSING("5003", "client plan code missing in contract query"),

  CONTRACT_SPREAD_QUERY_CURRENCY_MISSING("5004", "part currency missing when query contract spread"),

  CONTRACT_SPREAD_QUERY_BENCHMARK_EMPTY("5005", "benchmark paln code missing when query contract spread"),

  FIND_CLIENT_ERROR("9991", "find client error"),

  FILE_UPLOAD_TO_S3_ERROR("FILE_UPLOAD_TO_S3_ERROR", "file upload to S3 fail"),

  FILE_DOWNLOAD_FROM_S3_ERROR("FILE_DOWNLOAD_FROM_S3_ERROR", "file download from S3 fail"),

  RPC_INVOKE_ERROR("RPC_INVOKE_ERROR", "RPC远程调用异常"),

  JSON_PARSE_ERROR("JSON_PARSE_ERROR", "json parse error"),


  FAIL("9999", "system inner error!"),

  //--------------------------子级账户业务异常---------------------

  PARAMETER_INVALID("REQUEST_PARAM_INVALID", "request parameter invalid"),

  // 内部异常
  INNER_ERROR("SYSTEM_BUSY", "inner error"),

  // 不支持的状态转换
  UNSUPPORTED_STATE_TRANS("STATE_TRANSITION_NO_SUPPORT", "unsupported state transitions"),

  // JSON转换失败
  JSON_FORMAT_ERROR("DATE_FORMAT_EXCEPTION", "data is not json"),

  // 渠道网关请求失败
  CHANNEL_NO_RESPONSE("CHANNEL_FRONT_RPC_EXCEPTION", "channel no response"),

  // 子级账户更新失败
  UPDATE_SUB_ACCOUNT_FAIL("UPDATE_SUB_ACCOUNT_FAIL", "sub account update fail"),

  // 子级账户预申请表使用失败
  UPDATE_SUB_ACCOUNT_PRE_APPLY_FAIL("UPDATE_SUB_ACCOUNT_PRE_APPLY_FAIL", "sub account pre apply update fail"),

  // 幂等校验未通过
  REQUEST_IDEMPOTENCY_CHECK_FAIL("REQUEST_IDEMPOTENCY_CHECK_FAIL", "Idempotency check failed"),

  // 无机构账号支持
  NO_INST_ACCOUNT_SUPPORT("NO_INST_ACCOUNT_SUPPORT", "no available inst account support"),

  // 查询到多条数据
  MORE_THAN_ONE("MORE_THAN_ONE", "more than one piece of data"),

  /**
   * 机构合约错误码 开始
   */
  // 机构产品大类错误
  INST_CONTRACT_PRODUCT_TYPE_ERROR("INST_CONTRACT_PRODUCT_TYPE_ERROR", "inst contract product type error"),

  NOTHING_FOUND_BUT_EXPECT_ONE("NOTHING_FOUND_BUT_EXPECT_ONE", "one piece of data expected, but nothing found"),

  CONTRACT_CONTENT_NOT_EXIST("CONTRACT_CONTENT_NOT_EXIST", "no contract content exist"),

  INST_CONTRACT_DRAFT_IS_EXIST("INST_CONTRACT_DRAFT_IS_EXIST", "draft institutional contract exists"),
  /**
   * 机构合约错误码 结束
   */


  // 机构子级账号已存在
  RECORD_SUB_ACCOUNT_FAIL("RECORD_SUB_ACCOUNT_FAIL", "sub account or business_key already exists"),

  // 机构账号子级模式不支持
  SUB_ACCOUNT_MODE_NOT_SUPPORT("SUB_ACCOUNT_MODE_NOT_SUPPORT", "sub account create mode not support"),

  // 机构账号不支持激活
  SUB_ACCOUNT_ACTIVATION_NOT_SUPPORT("SUB_ACCOUNT_ACTIVATION_NOT_SUPPORT", "sub account activation mode not support"),

  // 机构账号不支持关闭
  SUB_ACCOUNT_NOT_SUPPORT_CLOSE("SUB_ACCOUNT_NOT_SUPPORT_CLOSE", "sub account not support close"),

  // 机构账号不存在
  ACCOUNT_NOT_EXIST("ACCOUNT_NOT_EXIST", "inst account not exist"),

  // 机构子级账号不存在
  SUB_ACCOUNT_NOT_EXIST("SUB_ACCOUNT_NOT_EXIST", "inst sub account not exist"),

  // 机构子级账号状态不支持
  SUB_ACCOUNT_STATUS_NOT_SUPPORT("SUB_ACCOUNT_STATUS_NOT_SUPPORT", "sub account status does not support this operation"),

  // 机构子级账号重复
  SUB_ACCOUNT_NO_DUPLICATE("SUB_ACCOUNT_NO_DUPLICATE", "sub account no duplicate"),

  // 调用渠道网关异常
  CHANNEL_EXCEPTION("CHANNEL_EXCEPTION", "channel exception"),

  // 机构中心路由规则异常
  ROUTE_EXCEPTION("ROUTE_EXCEPTION", "route exception"),

  // 无可用号段生成子级账号
  NO_NUMBER_SEGMENT_AVAILABLE("NO_NUMBER_SEGMENT_AVAILABLE", "no available number segment support"),

  // 模版生成子账号异常
  TEMPLATE_CREATE_SUB_ACCOUNT("TEMPLATE_CREATE_SUB_ACCOUNT", "template create sub account exception"),

  // 机构账号参数桶异常
  INST_ACCOUNT_PARAMS_BUCKET_EXCEPTIONS("INST_ACCOUNT_PARAMS_BUCKET_EXCEPTIONS", "get inst account params bucket exception"),

  // excel内容格式异常
  EXCEL_CONTEXT_FORMAT_EXCEPTIONS("EXCEL_CONTEXT_FORMAT_EXCEPTIONS", "excel content format exception"),

  // 工作流发起异常
  WORKFLOW_START_ERROR("WORKFLOW_START_ERROR", "workflow start error"),

  // 草稿状态异常
  INST_CENTER_DRAFT_STATUS_ERROR("INST_CENTER_DRAFT_STATUS_ERROR", "draft status error"),

  // Excel 导出错误
  INST_EXCEL_EXPORT_ERROR("INST_EXCEL_EXPORT_ERROR", "excel export error"),

  // ----------------- 机构查询 -----------------
  // 机构信息查询异常
  INST_INFO_QUERY_ERROR("INST_INFO_QUERY_ERROR", "inst info query error"),

  // ----------------- 金融日历 -----------------

  // 日历校验异常
  INST_FINANCIAL_CALENDAR_CHECK_ERROR("INST_FINANCIAL_CALENDAR_CHECK_ERROR", "financial calendar check error"),

  // 存在审核中的日历
  INST_FINANCIAL_CALENDAR_PROCESSING_ERROR("INST_FINANCIAL_CALENDAR_PROCESSING_ERROR", "financial calendar processing error"),

  // 查找不到对应日历
  INST_FINANCIAL_CALENDAR_FIND_ERROR("INST_FINANCIAL_CALENDAR_FIND_ERROR", "financial calendar find error"),

  // ----------------- 银行账户 -----------------

  // 银行账户校验异常
  INST_BANK_ACCOUNT_VALIDATE_ERROR("INST_BANK_ACCOUNT_VALIDATE_ERROR", "bank account validate error"),

  // ----------------- 渠道账户 -----------------

  // 调用 omc-channel-exchange 远程保存接口异常
  INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALL_ERROR("INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALL_ERROR", "omc-channel-exchange remote save call fail"),

  // 调用 omc-channel-exchange 远程保存回调异常
  INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALLBACK_ERROR("INST_FUNDS_ACCOUNT_REMOTE_SAVE_CALLBACK_ERROR", "omc-channel-exchange remote save callback fail"),

  // ----------------- 合约信息校验 -----------------
  // 合约费用信息校验异常
  INST_CONTRACT_FEE_VALIDATE_ERROR("INST_CONTRACT_FEE_VALIDATE_ERROR", "contract validate error"),

  // 合约fx信息校验异常
  INST_CONTRACT_FX_VALIDATE_ERROR("INST_CONTRACT_FX_VALIDATE_ERROR", "fx contract validate error"),

  // 合约结算信息校验异常
  INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR("INST_CONTRACT_SETTLEMENT_VALIDATE_ERROR", "fx contract validate error"),

  // 合约信息-渠道核对类型查询错误
  INST_CONTRACT_ACCOUNTING_TYPE_QUERY_ERROR("INST_CONTRACT_ACCOUNTING_TYPE_QUERY_ERROR", "contract accounting type query error"),

  // 合约信息-渠道核对方式修改异常
  INST_CONTRACT_ACCOUNTING_TYPE_MODIFY_ERROR("INST_CONTRACT_ACCOUNTING_TYPE_MODIFY_ERROR", "contract accounting type modify error"),

  // 机构中心-日志落库错误
  INST_CENTER_LOG_SAVE_ERROR("INST_CENTER_LOG_SAVE_ERROR", "log save error"),

  // 机构中心-合约模板下载错误
  INST_CENTER_CONTRACT_TEMPLATE_DOWNLOAD_ERR("INST_CENTER_CONTRACT_TEMPLATE_DOWNLOAD_ERR", "contract template download error"),

  // 机构中心-合约查询失败
  INST_CENTER_CONTRACT_QUERY_ERROR("INST_CENTER_CONTRACT_QUERY_ERROR", "contract query error"),

  // 机构中心-合约版本查询失败
  INST_CENTER_CONTRACT_VERSION_QUERY_ERROR("INST_CENTER_CONTRACT_VERSION_QUERY_ERROR", "contract version query error"),

  // 机构中心-合约费用查询策略异常
  INST_CENTER_FEE_FINDER_STRATEGY_ERROR("INST_CENTER_FEE_FINDER_STRATEGY_ERROR", "fee finder strategy error"),

  // 机构中心-合约版本升级异常
  INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR("INST_CENTER_CONTRACT_VERSION_UPGRADE_ERROR", "contract version upgrade error"),

  // 机构中心-流程重试异常
  INST_CENTER_PROCESS_RETRY_ERROR("INST_CENTER_PROCESS_RETRY_ERROR", "inst center process retry error"),

  // 沟通中心通知发起异常
  COMM_CENTER_NOTICE_ERROR("COMM_CENTER_NOTICE_ERROR", "comm center notice error"),
  ;


  private String code;

  private String msg;

  public static String getMsg(String code) {
    for (ErrorCodeEnum errorCodeEnum : values()) {
      if (Objects.equals(errorCodeEnum.getCode(),code)) {
        return errorCodeEnum.getMsg();
      }
    }
    return null;
  }
}
