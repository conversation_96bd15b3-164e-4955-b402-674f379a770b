package com.payermax.channel.inst.center.common.utils;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.property.ExcelReadHeadProperty;
import com.alibaba.excel.util.ConverterUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.StringUtil;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * ExcelUtil
 *
 * <AUTHOR>
 * @desc
 */
public class ExcelUtil {

    private static final ThreadLocal<ThreadLocalData> THREAD_LOCAL_EXCEL_COMMON_DATA = new ThreadLocal<>();

    private static final Map<String, ExcelLangRow> EXCEL_LANG_ROW_MAP = new ConcurrentHashMap<>();

    public static <T extends BaseExcelRow & ExcelLangRow> ExcelParseInfo<T> readExcel(byte[] bytes, Class<T> clazz, String sheetName, String lang) {
        if(Objects.isNull(bytes) || bytes.length == 0 || Objects.isNull(clazz)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "bytes or clazz is empty");
        }
        return readExcel(new ByteArrayInputStream(bytes), clazz, sheetName, lang);
    }

    @SneakyThrows
    public static <T extends BaseExcelRow & ExcelLangRow> ExcelParseInfo<T> readExcel(InputStream inputStream, Class<T> clazz, String sheetName, String lang) {
        if(Objects.isNull(inputStream) || Objects.isNull(clazz)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "inputStream or clazz is empty");
        }
        LangEnum langEnum = LangEnum.parse(lang);

        ExcelParseInfo<T> excelParseInfo;
        try {
            initThreadLocalExcelCommonData();
            THREAD_LOCAL_EXCEL_COMMON_DATA.get().setLang(langEnum);
            excelParseInfo = readExcel(inputStream, ExcelLangRow.langClassImpl(clazz, langEnum), sheetName);
        }finally {
            THREAD_LOCAL_EXCEL_COMMON_DATA.remove();
        }
        excelParseInfo.setLang(langEnum);
        return excelParseInfo;
    }

    public static <T extends BaseExcelRow> ExcelParseInfo<T> readExcel(byte[] bytes, Class<T> clazz, String sheetName) {
        if(Objects.isNull(bytes) || bytes.length == 0 || Objects.isNull(clazz)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "bytes or clazz is empty");
        }
        return readExcel(new ByteArrayInputStream(bytes), clazz, sheetName);
    }

    public static <T extends BaseExcelRow> ExcelParseInfo<T> readExcel(InputStream inputStream, Class<T> clazz, String sheetName) {
        if(Objects.isNull(inputStream) || Objects.isNull(clazz)) {
            throw new BusinessException(ErrorCodeEnum.INNER_ERROR.getCode(), "inputStream or clazz is empty");
        }
        ExcelListener<T> excelListener = new ExcelListener<>();
        ExcelReaderBuilder builder = EasyExcelFactory.read(inputStream, clazz, excelListener);
        ExcelParseInfo<T> excelParseInfo = new ExcelParseInfo<>();
        try {
            initThreadLocalExcelCommonData();
            if (StringUtil.isEmpty(sheetName)) {
                builder.sheet().doRead();
            } else {
                builder.sheet(sheetName).doRead();
            }
            excelParseInfo.setHeaderMap(THREAD_LOCAL_EXCEL_COMMON_DATA.get().getHeaderMap());
            excelParseInfo.setHeaderList(THREAD_LOCAL_EXCEL_COMMON_DATA.get().getHeaderList());
        }finally {
            THREAD_LOCAL_EXCEL_COMMON_DATA.remove();
        }
        excelParseInfo.setDataList(excelListener.getDataList());
        return excelParseInfo;
    }


    private static void initThreadLocalExcelCommonData() {
        ThreadLocalData threadLocalData = THREAD_LOCAL_EXCEL_COMMON_DATA.get();
        if(Objects.isNull(threadLocalData)) {
            THREAD_LOCAL_EXCEL_COMMON_DATA.set(new ThreadLocalData());
        }
    }

    private static class ExcelListener<T extends BaseExcelRow> extends AnalysisEventListener<T> {

        private final List<T> dataList = new ArrayList<>();

        @Override
        public void invoke(T t, AnalysisContext analysisContext) {
            buildExcelHeaderInfo(analysisContext);

            int rowNo = analysisContext.readRowHolder().getRowIndex() + 1;
            t.setExcelRowNo(rowNo);
            t.setLang(THREAD_LOCAL_EXCEL_COMMON_DATA.get().getLang());
            this.dataList.add(t);

            if(t instanceof DefineExcelParser) {
                handleDefineExcelParse((DefineExcelParser) t, rowNo, analysisContext);
            }
        }

        private void handleDefineExcelParse(DefineExcelParser defineExcelParser, int rowNo, AnalysisContext analysisContext) {
            List<ExcelParseInfo.ExcelHeader> excelHeaderList = THREAD_LOCAL_EXCEL_COMMON_DATA.get().getHeaderList();
            Map<Integer, Cell> cellMap = analysisContext.readRowHolder().getCellMap();
            if(CollectionUtils.isEmpty(excelHeaderList) || CollectionUtils.isEmpty(cellMap)) {
                return;
            }
            Map<Integer, ReadCellData<?>> readCellDataMap = cellMap.entrySet().stream().filter(entry -> entry.getValue() instanceof ReadCellData)
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> (ReadCellData<?>) entry.getValue()));
            Map<Integer, String> stringCellMap = ConverterUtils.convertToStringMap(readCellDataMap, analysisContext);
            List<DefineExcelParser.ExcelCellData> excelCellDataList = excelHeaderList.stream().map(header -> {
                DefineExcelParser.ExcelCellData excelCellData = new DefineExcelParser.ExcelCellData();
                excelCellData.setRowNo(rowNo);
                excelCellData.setColumnNo(header.getColumnNo());
                excelCellData.setColumnName(header.getName());
                Integer key = header.getColumnNo() - 1;
                Cell cell = cellMap.get(key);
                if(cell instanceof CellData) {
                    ((CellData<?>) cell).setStringValue(stringCellMap.get(key));
                }
                excelCellData.setCellValue(cell);
                return excelCellData;
            }).collect(Collectors.toList());
            defineExcelParser.rowDataParse(excelCellDataList);
        }

        private void buildExcelHeaderInfo(AnalysisContext analysisContext) {
            if(Objects.nonNull(THREAD_LOCAL_EXCEL_COMMON_DATA.get().getHeaderMap())) {
                return;
            }
            Map<String, ExcelParseInfo.ExcelHeader> excelHeaderMap = new HashMap<>(8);
            THREAD_LOCAL_EXCEL_COMMON_DATA.get().setHeaderMap(excelHeaderMap);
            ExcelReadHeadProperty headProperty = analysisContext.readSheetHolder().getExcelReadHeadProperty();
            Map<Integer, Head> headMap = headProperty.getHeadMap();
            if(CollectionUtils.isEmpty(headMap)) {
                return;
            }
            int headerIndex = headProperty.getHeadRowNumber() - 1;
            headMap.forEach((k, head) -> {
                ExcelParseInfo.ExcelHeader excelHeader = new ExcelParseInfo.ExcelHeader();
                excelHeader.setColumnNo(head.getColumnIndex() + 1);
                excelHeader.setName(head.getHeadNameList().get(headerIndex));
                excelHeader.setFieldName(head.getFieldName());
                excelHeaderMap.put(head.getFieldName(), excelHeader);
            });
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            if(CollectionUtils.isEmpty(headMap)) {
                THREAD_LOCAL_EXCEL_COMMON_DATA.get().setHeaderList(Collections.emptyList());
                return;
            }
            THREAD_LOCAL_EXCEL_COMMON_DATA.get().setHeaderList(headMap.entrySet().stream().filter(entry -> StringUtil.isNotEmpty(entry.getValue())).map(entry -> {
                ExcelParseInfo.ExcelHeader excelHeader = new ExcelParseInfo.ExcelHeader();
                excelHeader.setName(entry.getValue());
                excelHeader.setColumnNo(entry.getKey() + 1);
                return excelHeader;
            }).collect(Collectors.toList()));
        }

        public List<T> getDataList() {
            return this.dataList;
        }
    }

    @Data
    public static class ThreadLocalData {

        private LangEnum lang;

        private Map<String, ExcelParseInfo.ExcelHeader> headerMap;

        private List<ExcelParseInfo.ExcelHeader> headerList;
    }

    @Data
    public static class BaseExcelRow {

        @ExcelIgnore
        private LangEnum lang;

        @ExcelIgnore
        private Integer excelRowNo;
    }

    @Data
    public static class ExcelParseInfo<T extends BaseExcelRow> {

        private LangEnum lang;

        private Map<String, ExcelHeader> headerMap;

        private List<ExcelHeader> headerList;

        private List<T> dataList;

        @Data
        public static class ExcelHeader {

            private String name;

            private String fieldName;

            private Integer columnNo;
        }
    }

    /**
     * 自定义行数据转换
     */
    public interface DefineExcelParser {

        /**
         * 自定义行数据转换
         * @param excelCellDataList
         */
        void rowDataParse(List<ExcelCellData> excelCellDataList);

        @Data
        class ExcelCellData {

            private Integer rowNo;

            private Integer columnNo;

            private String columnName;

            private Cell cellValue;
        }
    }

    public interface ExcelLangRow {

        /**
         * 获取国际化对应的实现类
         * @param langEnum 国际化标识
         * @return 国际化对应的实现类
         */
        Class<? extends ExcelLangRow> langClassImpl(LangEnum langEnum);

        static <T extends ExcelLangRow> Class<T> langClassImpl(Class<T> clazz, LangEnum langEnum) {
            ExcelLangRow excelLangRow = EXCEL_LANG_ROW_MAP.get(clazz.getName());
            if(Objects.nonNull(excelLangRow)) {
                return (Class<T>) excelLangRow.langClassImpl(langEnum);
            }
            Enhancer enhancer = new Enhancer();
            enhancer.setSuperclass(clazz);
            enhancer.setCallback((MethodInterceptor) (object, method, args, methodProxy) -> methodProxy.invokeSuper(object, args));
            excelLangRow = (ExcelLangRow) enhancer.create();
            EXCEL_LANG_ROW_MAP.put(clazz.getName(), excelLangRow);
            return  (Class<T>) excelLangRow.langClassImpl(langEnum);
        }
    }
}
