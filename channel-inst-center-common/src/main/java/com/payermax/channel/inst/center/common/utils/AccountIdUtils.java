package com.payermax.channel.inst.center.common.utils;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR> at 2022/10/9 21:01
 **/
public class AccountIdUtils {

    /**
     * 构建子级资金账户subAccountId
     * <p>
     * 规则：机构标识_机构账号_国家_币种_子级账号号码
     */
    public static String buildSubAccountId(String accountId) {
        if (StringUtils.isBlank(accountId)){
            return null;
        }
        return String.format("%s_%s", accountId, DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"));
    }
}
