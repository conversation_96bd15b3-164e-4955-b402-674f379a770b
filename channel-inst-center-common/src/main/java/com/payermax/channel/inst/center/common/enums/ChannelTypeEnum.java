package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

@Getter
public enum  ChannelTypeEnum {

    RECEIPT((byte)0,"Receipt"),
    PAYOUT((byte)1,"Payout");

    private byte code;
    private String name;

    ChannelTypeEnum(byte code, String name){
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(byte code){
        for(ChannelTypeEnum bizType:values()){
            if(bizType.getCode() == code){
                return bizType.getName();
            }
        }
        return RECEIPT.getName();
    }
}
