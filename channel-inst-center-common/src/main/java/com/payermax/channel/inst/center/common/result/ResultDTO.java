package com.payermax.channel.inst.center.common.result;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultDTO<T> {

    /**
     * 成功标识
     */
    private static final String SUCCESS = "0000";

    /**
     * 业务响应状态码
     */
    private String bizCode;

    /**
     * 返回码描述信息
     */
    private String message;


    /**
     * 需返回的业务数据
     */
    private T data;


    private String channelCode;

    /**
     * 渠道响应码
     */
    private String channelRespCode;

    /**
     * 渠道响应消息
     */
    private String channelRespMsg;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public String getBizCode() {
        if (SUCCESS.equals(bizCode)) {
            return ErrorCodeEnum.SUCCESS.getCode();
        }
        return bizCode;
    }

    public Boolean isRespSuccess() {
        return ErrorCodeEnum.SUCCESS.getCode().equals(getBizCode());
    }

    public static <T> ResultDTO buildSuccess(T data) {
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setBizCode(SUCCESS);
        resultDTO.setData(data);
        return resultDTO;
    }

    public static ResultDTO buildSuccess() {
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setBizCode(SUCCESS);
        return resultDTO;
    }

}
