package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/9 10:53
 **/
public enum FactorKeyEnum {

    NULL(null, "空"),
    SINGLE_TRANS_LIMIT("DIRECT", "单笔限额"),
    AMOUNT_SUM_LIMIT_4_DAY("REDIRECT", "日累计限额"),
    AMOUNT_SUM_LIMIT_4_MONTH("REDIRECT_OTP", "月累计限额"),
    ACCOUNT_TYPE("ACCOUNT_TYPE", "账户类型"),
    IS_NEED_PAN("IS_NEED_PAN", "是否需要PAN"),
    IS_SUPPORT_3DS("IS_SUPPORT_3DS", "是否支持3DS"),
    IS_SUPPORT_PREAUTH("IS_SUPPORT_PREAUTH", "是否支持预授权"),
    MCC("MCC", "MCC"),
    PAYMENT_SCENE("PAYMENT_SCENE", "支付场景"),
    PAY_METHOD_ID("PAY_METHOD_ID", "支付方式ID"),
    SUPPORT_CARD_ISSUE_COUNTRY("SUPPORT_CARD_ISSUE_COUNTRY", "支持发行国家"),
    SUPPORT_CARD_ORG("SUPPORT_CARD_ORG", "支持卡组"),
    SUPPORT_IP_LOCATION("SUPPORT_IP_LOCATION", "支持本地IP"),
    TAX_TYPE("TAX_TYPE", "收费类型"),
    TERMINAL_TYPE("TERMINAL_TYPE", "支持终端类型"),
    TRANS_TIME_LIMIT("TRANS_TIME_LIMIT", "可用时间范围");

    private String type;

    private String desc;

    FactorKeyEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static FactorKeyEnum getByType(String type) {
        for (FactorKeyEnum factorKeyEnum : FactorKeyEnum.values()) {
            if (Objects.equals(factorKeyEnum.getType(),type)) {
                return factorKeyEnum;
            }
        }
        return null;
    }
}
