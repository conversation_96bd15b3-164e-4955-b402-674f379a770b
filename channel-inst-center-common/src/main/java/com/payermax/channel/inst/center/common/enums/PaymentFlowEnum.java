package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/9 10:39
 **/
public enum PaymentFlowEnum {

    NULL(null, "空"),
    DIRECT("DIRECT", "联机"),
    REDIRECT("REDIRECT", "跳转"),
    REDIRECT_OTP("REDIRECT_OTP", "跳转_OTP"),
    DIRECT_COLLECT("DIRECT_COLLECT", "联机_采集"),
    OTP("OTP", "OTP"),
    OFFLINE("OFFLINE", "线下");



    private String type;

    private String desc;

    PaymentFlowEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static PaymentFlowEnum getByType(String type) {
        for (PaymentFlowEnum paymentFlowEnum : PaymentFlowEnum.values()) {
            if (Objects.equals(paymentFlowEnum.getType(),type)) {
                return paymentFlowEnum;
            }
        }
        return PaymentFlowEnum.NULL;
    }
}
