package com.payermax.channel.inst.center.common.constrains;

import com.payermax.channel.inst.center.common.enums.IDescEnum;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/11 10:47 AM
 **/
public class EnumAnnotationValidator implements ConstraintValidator<EnumAnnotation, String> {

    private Set<String> enumNameSet = new HashSet<>();

    @Override
    public void initialize(EnumAnnotation constraintAnnotation) {
        if(!CollectionUtils.isEmpty(enumNameSet)) {
            return;
        }

        Class<? extends IDescEnum> enumClass = constraintAnnotation.values();
        LangEnum langEnum = LangEnum.getLangEnumThreadLocal();

        if (Objects.nonNull(enumClass)) {
            IDescEnum[] enums = enumClass.getEnumConstants();
            this.enumNameSet = Arrays.stream(enums).map(item -> StringUtils.upperCase(item.getDesc(langEnum))).collect(Collectors.toSet());
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtils.isEmpty(value)) {
            return true;
        }
        // 此时value是 枚举类的描述性文字，需要判断是否是合法的枚举值对应的描述
        value = StringUtils.upperCase(value);

        return this.enumNameSet.contains(value);
    }
}
