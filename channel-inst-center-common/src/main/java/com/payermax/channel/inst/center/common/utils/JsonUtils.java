package com.payermax.channel.inst.center.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

public class JsonUtils {

    private static ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }
    
    //对象转字符串
    public static <T> String pojoToJson(T obj){
        if (Objects.isNull(obj)){
            return null;
        }
        try {
            return obj instanceof String ? (String) obj : MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            return null;
        }
    }
    
    //字符串转对象
    @SuppressWarnings("unchecked")
	public static <T> T jsonToPojo(String jsonData,Class<T> clazz){
        if (StringUtils.isBlank(jsonData) || clazz == null){
            return null;
        }
        try {
            return clazz.equals(String.class)? (T) jsonData :MAPPER.readValue(jsonData,clazz);
        } catch (IOException e) {
            return null;
        }
    }

    //字符串转对象集合
    public static <T>List<T> jsonToList(String jsonData, Class<T> clazz) {
        if (StringUtils.isBlank(jsonData) || clazz == null){
            return null;
        }
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
            return MAPPER.readValue(jsonData, javaType);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> T convertValue(Object obj, Class<T> clazz){
        if (Objects.isNull(obj) || clazz == null){
            return null;
        }
        return MAPPER.convertValue(obj, clazz);
    }

    public static <T> T convertValue(Object obj, TypeReference<T> typeReference){
        if (Objects.isNull(obj) || typeReference == null){
            return null;
        }
        return MAPPER.convertValue(obj, typeReference);
    }
}
