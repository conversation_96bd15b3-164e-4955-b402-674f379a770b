package com.payermax.channel.inst.center.common.constrains;

import com.payermax.channel.inst.center.common.enums.LangEnum;

import java.util.*;

/**
 * InterfaceValid
 *
 * <AUTHOR>
 * @desc
 */
public interface InterfaceValid<T, D> {

    /**
     * 线程内校验数据源缓存
     */
    ThreadLocal<Map<String, List<?>>> THREAD_VALID_DATA_CACHE = new ThreadLocal<>();

    /**
     *
     * 数据校验
     * @param value 需要校验的数据
     * @param lang 国际化标识
     * @return 校验结果
     */
    boolean valid(T value, LangEnum lang);

    /**
     * 查询校验时需要的数据
     * @return 校验时需要的数据
     */
    List<D> queryData();

    /**
     * 获取数据集合，优先从缓存中获取，缓存中获取不到正常查询并更新缓存
     * @return 获取数据集合
     */
    default List<D> dataList() {
        List<D> threadValidCache = (List<D>) InterfaceValid.getThreadValidCache(this.getClass());
        if(Objects.nonNull(threadValidCache)) {
            return threadValidCache;
        }
        threadValidCache = queryData();
        InterfaceValid.setThreadValidCache(this.getClass(), threadValidCache);
        if(Objects.isNull(threadValidCache)) {
            return Collections.emptyList();
        }
        return threadValidCache;
    }

    /**
     * 初始化线程校验数据缓存
     */
    static void initThreadValidDataCache() {
        Map<String, List<?>> map = THREAD_VALID_DATA_CACHE.get();
        if(Objects.isNull(map)) {
            THREAD_VALID_DATA_CACHE.set(new HashMap<>(8));
        }
    }

    /**
     * 清除现成校验数据缓存
     */
    static void clearThreadValidDataCache() {
        THREAD_VALID_DATA_CACHE.remove();
    }

    /**
     * 设置现成校验缓存
     * @param clazz
     * @param dataList
     */
    static void setThreadValidCache(Class<? extends InterfaceValid> clazz, List<?> dataList) {
        if(Objects.isNull(clazz)) {
            return;
        }
        Map<String, List<?>> map = THREAD_VALID_DATA_CACHE.get();
        if(Objects.isNull(map)) {
            return;
        }
        map.put(clazz.getName(), dataList);
    }

    /**
     * 获取线程校验缓存
     * @param clazz
     * @return
     */
    static List<?> getThreadValidCache(Class<? extends InterfaceValid> clazz) {
        if(Objects.isNull(clazz)) {
            return null;
        }
        Map<String, List<?>> map = THREAD_VALID_DATA_CACHE.get();
        if(Objects.isNull(map)) {
            return null;
        }
        if(!map.containsKey(clazz.getName())) {
            return null;
        }
        List<?> list = map.get(clazz.getName());
        if(Objects.isNull(list)) {
            return Collections.emptyList();
        }
        return list;
    }
}
