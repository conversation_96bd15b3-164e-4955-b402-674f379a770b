package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FeeCalculateTimingEnumV2 implements IDescOnExcelEnum {

    /**
     * 交易日算费
     */
    CALCULATE_IN_TRADE_DAY("calculate-in-trade-day", "交易日算费","Transaction Day"),

    /**
     * 结算日算费
     */
    CALCULATE_IN_SETTLEMENT_DAY("calculate-in-settlement-day", "结算日算费","Settlement Day"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;
}
