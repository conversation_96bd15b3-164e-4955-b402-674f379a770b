package com.payermax.channel.inst.center.common.result;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseResult<T> implements Serializable {

    private String code;

    private String msg;

    private T data;

    public static BaseResult ok() {
        return ok(null);
    }

    public static <T> BaseResult<T> ok(T data) {
        return new BaseResult<>(ResponseEnum.OK.code, ResponseEnum.OK.msg, data);
    }

    public static BaseResult failed() {
        return failed(ResponseEnum.BAD_REQUEST.code, ResponseEnum.BAD_REQUEST.msg);
    }

    public static BaseResult failed(String msg) {
        return failed(ResponseEnum.BAD_REQUEST.code, msg);
    }

    public static BaseResult failed(ResponseEnum responseEnum) {
        return failed(responseEnum.code, responseEnum.msg);
    }

    public static BaseResult failed(String code, String msg) {
        return failed(code, msg, null);
    }

    public static <T> BaseResult<T> failed(String code, String msg, T data) {
        return new BaseResult<>(code, msg, data);
    }

    @Getter
    public enum ResponseEnum {
        OK("200", "OK"),
        APPLY_SUCCESS("APPLY_SUCCESS", "OK"),
        BAD_REQUEST("400", "Bad request"),
        CHECK_FAILED("401", "Parameter check failed"),
        NOT_FOUND("404", "No corresponding resources were found"),
        DATA_MISSING("405", "Part of the data is still under sychronizing, please retry later"),
        TOO_MUCH_DATA("406", "查询数据量过多，请缩小查询条件范围再次尝试"),
        CHANNEL_NOT_SUPPORT_RETRY("407", "选择的渠道不支持重发"),
        FILE_RECORD_IS_EMPTY("500", "文件记录为空，请检查"),
        FILE_PAYMENT_TYPE_NOT_SUPPORT("501", "支付类型只支持入款或出款，请检查"),
        FILE_ORDER_NO_IS_EMPTY("502", "渠道订单号和三方单号必输，请检查"),
        FILE_ORDER_NO_HAS_REPEAT("503", "渠道订单号有重复，请检查"),
        INST_CENTER_PUSH_RESPONSE_EMPTY("600", "集团合同系统响应为空，请检查");

        private String code;

        private String msg;

        ResponseEnum(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}
