package com.payermax.channel.inst.center.common.constrains;

/**
 * Number
 *
 * <AUTHOR>
 * @desc
 */

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = { NumberAnnotationValidator.class })
public @interface NumberAnnotation {

    String message() default "number not valid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    boolean isInteger() default true;

    boolean allowNull() default true;
}
