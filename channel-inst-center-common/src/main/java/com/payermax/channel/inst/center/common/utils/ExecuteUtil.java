package com.payermax.channel.inst.center.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * ExecuteUtil
 *
 * <AUTHOR>
 * @desc
 */
@Slf4j
public class ExecuteUtil {

    public static void executeNonException(ExecuteItem... executeItems) {
        if(Objects.isNull(executeItems)) {
            return;
        }
        for(ExecuteItem executeItem : executeItems) {
            try {
                executeItem.execute();
            }catch (Exception ex) {
                log.warn("fail to execute code non exception", ex);
            }
        }
    }

    public interface ExecuteItem {

        /**
         * 执行方法
         */
        void execute();
    }
}
