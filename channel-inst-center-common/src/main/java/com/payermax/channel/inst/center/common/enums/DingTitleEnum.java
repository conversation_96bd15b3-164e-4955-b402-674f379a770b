package com.payermax.channel.inst.center.common.enums;

/**
 * 钉钉告警配置key
 *
 * <AUTHOR>
 * @date 2022/5/12 14:44
 */
public enum DingTitleEnum {

    /**
     * 自动路由【预切换】通知
     */
    AUTO_ROUTE_PRE_CHANGE_NOTIFY("preChangeNotify", "【金融交换】路由【预切换】告警", "#000000"),
    /**
     * 自动路由【切换】通知
     */
    AUTO_ROUTE_CHANGE_NOTIFY("changeNotify", "【金融交换】路由【切换】通知", "#ff0000"),
    /**
     * 自动路由【切换】但无可用备用渠道通知
     */
    AUTO_ROUTE_NO_STANDBY_NOTIFY("noStandbyNotify", "【金融交换】路由【无可用备用渠道】告警", "#000000"),

    /**
     * 自动路由【临时切换成功】通知
     */
    AUTO_ROUTE_PROVISIONAL_CHANGE_SUCCESS_NOTIFY("provisionalChangeSuccessNotify", "【金融交换】路由【临时切换成功】通知", "#000000"),
    /**
     * 自动路由【临时切换失败】通知
     */
    AUTO_ROUTE_PROVISIONAL_CHANGE_FAILED_NOTIFY("provisionalChangeFailedNotify", "【金融交换】路由【临时切换失败】通知", "#ff0000");

    private String groupName;

    private String title;

    private String color;

    DingTitleEnum(String groupName, String title, String color) {
        this.groupName = groupName;
        this.title = title;
        this.color = color;
    }

    public String getGroupName() {
        return groupName;
    }

    public String getTitle() {
        return title;
    }

    public String getColor() {
        return color;
    }
}
