package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/27
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum AccumulationCycleEnumV2 implements IDescOnExcelEnum{

    /**
     * 月
     */
    MONTH("month", "Month", "Month"),

    /**
     * 年
     */
    YEAR("year", "Year", "Year"),

    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;

}
