package com.payermax.channel.inst.center.common.utils;

import com.alibaba.fastjson.*;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/3/4
 * @DESC
 */
@Slf4j
public class DiffUtil {


    /**
     * 对数据进行 DIFF 并拼接成字符串，只接受 JSON对象/单元素JSON数组
     */
    public static TreeMap<String, Map<String, Object>> diff2Map(String originDataStr, String modifiedDataStr) {

        // 校验数据
        AssertUtil.isTrue(StringUtils.isNotBlank(originDataStr), ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), "originData is blank");
        AssertUtil.isTrue(jsonStrValid(originDataStr), ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), "originData valid fail");
        AssertUtil.isTrue(StringUtils.isNotBlank(modifiedDataStr), ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), "modifiedData is blank");
        AssertUtil.isTrue(jsonStrValid(modifiedDataStr), ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), "modifiedData valid fail");

        // JSON String 转 Map
        Map<String,Object> originData = JSON.parseObject(originDataStr, new TypeReference<TreeMap<String, Object>>(){});
        Map<String,Object> modifiedData = JSON.parseObject(modifiedDataStr, new TypeReference<TreeMap<String, Object>>(){});
        // 对象转 Map 进行 Diff 操作
        MapDifference<String, Object> difference = Maps.difference(originData, modifiedData);
        TreeMap<String, Map<String, Object>> result = new TreeMap<>();

        // 两个 Map 都存在的值
        difference.entriesDiffering().forEach((key, value) -> result.put(key, new HashMap<String, Object>(4) {{
            put("left", value.leftValue());
            put("right", value.rightValue());
        }}));
        // 只存在于左边 Map 的值
        difference.entriesOnlyOnLeft().forEach((key, value) -> result.put(key, new HashMap<String, Object>(4) {{
            put("left", value);
            put("right", null);
        }}));
        // 只存在于右边 Map 的值
        difference.entriesOnlyOnRight().forEach((key, value) -> result.put(key, new HashMap<String, Object>(4) {{
            put("left", null);
            put("right", value);
        }}));
        return result;
    }

    /**
     * 校验 JSON String 是否合法
     */
    public static boolean jsonStrValid(String jsonStr) {
        JSONValidator validator = JSONValidator.from(jsonStr);
        AssertUtil.isTrue(!JSONValidator.Type.Value.equals(validator.getType()),
                ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), String.format("json must be Object or Array, now is %s", validator.getType()));
        if(JSONValidator.Type.Array.equals(validator.getType())){
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            AssertUtil.isTrue(jsonArray.size() == 1, ErrorCodeEnum.JSON_PARSE_ERROR.getCode(), "json array must only one item");
        }
        return validator.validate();
    }
}
