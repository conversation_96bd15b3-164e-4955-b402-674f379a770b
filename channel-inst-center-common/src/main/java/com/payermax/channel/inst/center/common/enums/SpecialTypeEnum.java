package com.payermax.channel.inst.center.common.enums;

/**
 * 特殊业务类型枚举
 */
public enum SpecialTypeEnum {

    MERCHANT(1, "商户"),
    AMOUNT(2, "金额"),
    MCC(3, "MCC"),
    BIZ_IDENTIFY(4, "业务身份"),
    AGGREGATION(9, "组合类型");

    private Integer type;

    private String desc;

    SpecialTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static SpecialTypeEnum getByType(Integer type) {
        for (SpecialTypeEnum specialTypeEnum : SpecialTypeEnum.values()) {
            if (specialTypeEnum.getType().equals(type)) {
                return specialTypeEnum;
            }
        }
        return null;
    }
}
