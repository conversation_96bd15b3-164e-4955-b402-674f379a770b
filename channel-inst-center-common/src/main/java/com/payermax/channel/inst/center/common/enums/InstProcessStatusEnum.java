package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/1/29
 * @DESC
 */
@Getter
public enum InstProcessStatusEnum {

    /**
     * 审批中
     */
    PROCESSING("ING"),
    /**
     * 通过
     */
    PASS("YES"),
    /**
     * 拒绝
     */
    REJECT("NO"),
    /**
     * 撤回
     */
    REVOKE("REVOKE"),
    /**
     * 终止
     */
    STOP("STOP"),
    /**
     * 等待重试
     */
    WAITING_RETRY("WAITING_RETRY"),
    /**
     * 未知状态
     */
    OTHER("");

    private String ddProcessStatus;

    InstProcessStatusEnum(String ddProcessStatus) {
        this.ddProcessStatus = ddProcessStatus;
    }

    public static InstProcessStatusEnum parse(String value) {
        InstProcessStatusEnum[] values = InstProcessStatusEnum.values();
        for(InstProcessStatusEnum em : values) {
            if(em.name().equalsIgnoreCase(value) || em.getDdProcessStatus().equalsIgnoreCase(value)) {
                return em;
            }
        }
        return OTHER;
    }
}

