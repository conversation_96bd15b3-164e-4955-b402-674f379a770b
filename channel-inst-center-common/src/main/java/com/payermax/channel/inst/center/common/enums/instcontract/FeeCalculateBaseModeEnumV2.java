package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 计费公式
 */
@AllArgsConstructor
@Getter
public enum FeeCalculateBaseModeEnumV2 implements IDescOnExcelEnum {
    /**
     * 交易金额 / (1 + 税率)
     */
    VAT_INCLUDED_IN_TPV("vat-include-in-tpv", "交易金额 / (1 + 税率)", "TransactionAmount/(1+TaxRatio)*Percentage Ratio"),

    /**
     * 交易金额
     */
    FEE_ON_TPV("fee-on-tpv", "交易金额", "TransactionAmount*Percentage Ratio"),

    /**
     * 交易金额 + 税费
     */
    FEE_ON_TPV_AND_TAX("fee-on-tpv-and-tax", "交易金额 + 税费","(TransactionAmount+TaxFee)*Percentage Ratio"),

    /**
     * 交易金额 - 税费
     */
    FEE_ON_TPV_EXCEPT_TAX("fee-on-tpv-except-tax", "交易金额 - 税费", "(TransactionAmount-TaxFee)*Percentage Ratio"),
    ;

    private final String code;

    private final String desc;

    private final String descOnExcel;
}
