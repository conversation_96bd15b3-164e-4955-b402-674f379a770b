package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/1 21:11
 **/
public enum StatusEnumYN {

    NULL(null, "空"),
    N("N", "关闭"),
    Y("Y", "启用");

    private String type;

    private String desc;

    StatusEnumYN(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static StatusEnumYN getByType(String type) {
        for (StatusEnumYN statusEnumYN : StatusEnumYN.values()) {
            if (Objects.equals(statusEnumYN.getType(),type)) {
                return statusEnumYN;
            }
        }
        return null;
    }
}
