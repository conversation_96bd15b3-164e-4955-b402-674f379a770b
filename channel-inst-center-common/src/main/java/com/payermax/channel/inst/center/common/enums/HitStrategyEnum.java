package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/1 21:14
 **/
public enum HitStrategyEnum {

    NULL(null, "空"),
    INIT("INIT", "等待重发"),
    FAILED("FAILED", "交易失败");

    private String type;

    private String desc;

    HitStrategyEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static HitStrategyEnum getByType(String type) {
        for (HitStrategyEnum hitStrategyEnum : HitStrategyEnum.values()) {
            if (Objects.equals(hitStrategyEnum.getType(),type)) {
                return hitStrategyEnum;
            }
        }
        return null;
    }
}
