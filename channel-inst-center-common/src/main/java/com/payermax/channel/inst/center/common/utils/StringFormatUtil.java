package com.payermax.channel.inst.center.common.utils;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> at 2022/10/10 10:55
 **/
public class StringFormatUtil {

    private static final Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}");
    private static Matcher matcher;

    /**
     * 格式化字符串 字符串中使用${key}表示占位符
     *
     * @param sourStr 需要匹配的字符串
     * @param param   参数集
     * @return
     */
    public static String stringFormat(String sourStr, Map<String, Object> param) {
        String tagerStr = sourStr;
        if (StringUtils.isBlank(tagerStr) || param == null) {
            return tagerStr;
        }
        try {
            matcher = pattern.matcher(tagerStr);
            while (matcher.find()) {
                String key = matcher.group();
                String keyclone = key.substring(2, key.length() - 1).trim();
                Object value = param.get(keyclone);
                if (value != null) {
                    tagerStr = tagerStr.replace(key, value.toString());
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("template create sub account stringFormat exception", e);
        }
        return tagerStr;
    }

    /**
     * 检查账号是否格式化完全
     *
     * @param sourStr 需要匹配的字符串
     * @return
     */
    public static boolean checkStringFormat(String sourStr) {
        String tagerStr = sourStr;
        if (StringUtils.isBlank(tagerStr)) {
            return true;
        }
        matcher = pattern.matcher(sourStr);
        if (matcher.find()) {
            return false;
        }
        return true;
    }

    /**
     * 格式化字符串 字符串中使用${key}表示占位符 利用反射 自动获取对象属性值 (必须有get方法)
     *
     * @param sourStr 需要匹配的字符串
     * @return
     */
    public static String stringFormat(String sourStr, Object obj) {
        String tagerStr = sourStr;
        if (StringUtils.isBlank(tagerStr) || obj == null) {
            return sourStr;
        }
        matcher = pattern.matcher(tagerStr);
        PropertyDescriptor pd;
        Method getMethod;
        // 匹配{}中间的内容 包括括号
        while (matcher.find()) {
            String key = matcher.group();
            String keyclone = key.substring(2, key.length() - 1).trim();
            try {
                String value = Objects.toString(ReflectUtil.getFieldValue(obj, keyclone), null);
                if (value != null) {
                    tagerStr = tagerStr.replace(key, value.toString());
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("template create sub account stringFormat exception", e);
            }
        }
        return tagerStr;
    }

    /**
     * 格式化字符串 (替换所有) 字符串中使用${key}表示占位符
     *
     * @param sourStr 需要匹配的字符串
     * @param param   参数集
     * @return
     */
    public static String stringFormatAll(String sourStr, Map<String, Object> param) {
        String tagerStr = sourStr;
        if (param == null || StringUtils.isBlank(tagerStr)) {
            return tagerStr;
        }
        try {
            matcher = pattern.matcher(tagerStr);
            while (matcher.find()) {
                String key = matcher.group();
                String keyclone = key.substring(2, key.length() - 1).trim();
                Object value = param.get(keyclone);
                if (value != null) {
                    tagerStr = tagerStr.replace(key, value.toString());
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("template create sub account stringFormat exception", e);
        }
        return tagerStr;
    }

    /**
     * 格式花字符串，按照占位符名字
     * 输入：sourStr = xxxxx${a}xxxxE{b} ,param = {a:A,b:B}
     * 输出：targetStr = xxxxAxxxxB
     *
     * @param sourStr
     * @param param
     * @return
     */
    public static String stringFormat(String sourStr, JSONObject param) {
        String tagerStr = sourStr;
        if (param == null || StringUtils.isBlank(tagerStr))
            return tagerStr;
        try {
            matcher = pattern.matcher(tagerStr);
            while (matcher.find()) {
                String key = matcher.group();
                String keyclone = key.substring(2, key.length() - 1).trim();
                Object value = param.get(keyclone);
                if (value != null) {
                    tagerStr = tagerStr.replace(key, value.toString());
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("template create sub account stringFormat exception", e);
        }
        return tagerStr;
    }

}




