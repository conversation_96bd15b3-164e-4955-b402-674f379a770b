package com.payermax.channel.inst.center.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import javax.validation.Validation;
import javax.validation.Validator;
import java.lang.ref.SoftReference;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验工具
 *
 * <AUTHOR>
 * @date 2021/12/02 18:42
 */
@Slf4j
public class ValidationUtils {

    private static Map<String, SoftReference<Pattern>> patternMap = new ConcurrentHashMap<>();

    /**
     * 使用hibernate的注解来进行验证
     */
    private static final Validator VALIDATOR = Validation
            .byProvider(HibernateValidator.class)
            .configure()
            .failFast(true)  //快速失败：只要有1个参数校验不通过，就立即停止校验参数
            .buildValidatorFactory()
            .getValidator();

    /**
     * 对象校验
     *
     * @param t
     * @param <T>
     * @return
     */
    public static <T> void validate(T t) {
        notNullCheck(t, "param t is mandatory");

        List<String> validateMsgs = oldValidate(t);
        if (CollectionUtils.isNotEmpty(validateMsgs)) {
            throw new IllegalArgumentException(validateMsgs.get(0));// NO_CHECK
        }
    }

    /**
     * 对象校验并返回校验结果
     *
     * @param t
     * @param <T>
     * @return
     */
    public static <T> String validateAndReturn(T t) {
        notNullCheck(t, "param t is mandatory");

        List<String> validateMsgs = oldValidate(t);
        if (CollectionUtils.isNotEmpty(validateMsgs)) {
            StringBuilder stringBuilder = new StringBuilder();
            validateMsgs.forEach(s -> {
                stringBuilder.append(s).append("; ");
            });
            return stringBuilder.toString();
        }
        return null;
    }

    /**
     * 校验是否为null
     *
     * @param object
     * @param errorMsg
     */
    public static void notNullCheck(Object object, String errorMsg) {
        if (object == null) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验是否为空
     *
     * @param charSequence
     * @param errorMsg
     */
    public static void notEmptyCheck(CharSequence charSequence, String errorMsg) {
        if (charSequence == null || charSequence.length() == 0) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验是否为空
     *
     * @param coll
     * @param errorMsg
     */
    public static void notEmptyCheck(Collection coll, String errorMsg) {
        if (coll == null || coll.size() == 0) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验是否为空
     *
     * @param map
     * @param errorMsg
     */
    public static void notEmptyCheck(Map map, String errorMsg) {
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验是否为空
     *
     * @param charSequence
     * @param errorMsg
     */
    public static void notBlankCheck(CharSequence charSequence, String errorMsg) {
        if (charSequence == null || charSequence.toString().trim().length() == 0) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验长度
     *
     * @param charSequence
     * @param minSize
     * @param maxSize
     * @param errorMsg
     */
    public static void sizeCheck(CharSequence charSequence, Integer minSize, Integer maxSize, String errorMsg) {
        if (charSequence == null) {
            return;
        }
        int size = charSequence.length();
        boolean result = (minSize != null && size < minSize) || (maxSize != null && size > maxSize);
        if (result) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验数字最小值
     *
     * @param number
     * @param min
     * @param inclusive
     * @param errorMsg
     */
    public static void minNumberCheck(Number number, Number min, boolean inclusive, String errorMsg) {
        if (number == null || min == null) {
            return;
        }
        BigDecimal value = BigDecimal.valueOf(number.doubleValue());
        BigDecimal minValue = BigDecimal.valueOf(min.doubleValue());
        boolean result = inclusive ? value.compareTo(minValue) >= 0 : value.compareTo(minValue) > 0;
        if (!result) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 校验数字大小
     *
     * @param number
     * @param min
     * @param max
     * @param errorMsg
     */
    public static void numberCheck(Number number, Number min, Number max, String errorMsg) {
        if (number == null) {
            return;
        }
        BigDecimal value = BigDecimal.valueOf(number.doubleValue());
        if (min != null) {
            BigDecimal minValue = BigDecimal.valueOf(min.doubleValue());
            if (value.compareTo(minValue) < 0) {
                throw new IllegalArgumentException(errorMsg);
            }
        }
        if (max != null) {
            BigDecimal maxValue = BigDecimal.valueOf(max.doubleValue());
            if (value.compareTo(maxValue) > 0) {
                throw new IllegalArgumentException(errorMsg);
            }
        }
    }

    /**
     * 校验正则表达式
     *
     * @param charSequence
     * @param regexp
     * @param errorMsg
     */
    public static void patternCheck(CharSequence charSequence, String regexp, String errorMsg) {
        Pattern pattern;
        SoftReference<Pattern> patternRef = patternMap.get(regexp);
        if (patternRef == null || (pattern = patternRef.get()) == null) {
            pattern = Pattern.compile(regexp);
            patternRef = new SoftReference(pattern);
            patternMap.put(regexp, patternRef);
        }
        Matcher m = pattern.matcher(charSequence);
        if (!m.matches()) {
            throw new IllegalArgumentException(errorMsg);
        }
    }

    /**
     * 老版校验器
     *
     * @param t
     * @param <T>
     * @return
     */
    private static <T> List<String> oldValidate(T t) {
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(t);
        if (!constraintViolations.isEmpty()) {
            List<String> messageList = new ArrayList<>();
            for (ConstraintViolation<T> constraintViolation : constraintViolations) {
                Path path = constraintViolation.getPropertyPath();
                StringBuilder msg = new StringBuilder();
                if (path != null) {
                    msg.append(path);
                    msg.append(" ");
                }
                msg.append(constraintViolation.getMessage());
                messageList.add(msg.toString());
            }
            return messageList;
        }
        return null;
    }
}
