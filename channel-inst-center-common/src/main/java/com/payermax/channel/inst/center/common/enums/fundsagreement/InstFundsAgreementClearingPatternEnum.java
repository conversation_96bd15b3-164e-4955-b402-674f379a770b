package com.payermax.channel.inst.center.common.enums.fundsagreement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @DESC 资金协议清算模式枚举
 */
@AllArgsConstructor
@Getter
public enum InstFundsAgreementClearingPatternEnum {

    /**
     * 实时清算
     */
    REAL_TIME("实时清算-即逐笔清算，内部清算为主"),

    /**
     * 延时清算
     */
    DELAYED("延时清算-即汇总一定周期内交易后，做统一清算，常见于与外部渠道的清算"),
    ;

    private final String description;
}
