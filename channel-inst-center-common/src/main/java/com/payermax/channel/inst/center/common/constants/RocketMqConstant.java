package com.payermax.channel.inst.center.common.constants;

/**
 * <AUTHOR> at 2022/10/16 16:01
 **/
public class RocketMqConstant {

    /**
     * 消费渠道子级资金账号MQ分组
     */
    public static final String CG_CHANNEL_INST_CENTER_ACCOUNT_STATUS_CHANGE= "cg_channel_inst_center_account_status_change";

    /**
     * 消费渠道子级资金账号信息MQ TOPIC
     */
    public static final String TOPIC_CHANNEL_ACCOUNT_STATUS_CHANGE= "topic_channel_account_status_change";

    /**
     * 消费文件服务子级资金账号信息MQ TOPIC
     */
    public static final String TOPIC_FILE_ACCOUNT_STATUS_CHANGE= "topic_file_account_status_change";


    /**
     * 机构合约-版本升级通知
     */
    public static final String TOPIC_INST_CENTER_CONTRACT_VERSION_UPGRADE = "topic_inst_center_contract_version_upgrade";


    /**
     * 机构合约-版本升级通知MQ分组
     */
    public static final String CG_CHANNEL_INST_CENTER_CONTRACT_VERSION_UPGRADE = "cg_channel_inst_center_contract_version_upgrade";
}
