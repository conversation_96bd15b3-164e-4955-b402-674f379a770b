package com.payermax.channel.inst.center.common.enums.fundsagreement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 资金协议类型枚举
 */
@AllArgsConstructor
@Getter
public enum InstFundsAgreementTypeEnum {

    /**
     * 流入协议
     */
    INFLOW("流入协议"),

    /**
     * 流出协议
     */
    OUTFLOW("流出协议"),

    /**
     * 来账协议
     */
    INCOMING("来账协议"),

    /**
     * 外汇协议
     */
    FX("外汇协议"),

    /**
     * 主体间协议
     */
    ENTITY("主体间协议");

    private final String description;
}
