package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/9 10:58
 **/
public enum LogicKeyEnum {

    NULL(null, "空"),
    INCLUDE("INCLUDE", "包含"),
    STRONG_INCLUDE("STRONG_INCLUDE", "明确包含"),
    EXCLUDE("EXCLUDE", "不包含"),
    EQUALS("EQUALS", "相等"),
    AMOUNT_MAX("AMOUNT_MAX", "金额最大值"),
    AMOUNT_INCLUDE("AMOUNT_INCLUDE", "多个固定金额"),
    AMOUNT_MULTIPLE("AMOUNT_MULTIPLE", "金额倍数"),
    AMOUNT_RANGE("AMOUNT_RANGE", "金额范围"),
    TIME_RANGE("TIME_RANGE", "时间范围"),
    UNKNOWN("UNKNOWN", "未知");

    private String type;

    private String desc;

    LogicKeyEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LogicKeyEnum getByType(String type) {
        for (LogicKeyEnum logicKeyEnum : LogicKeyEnum.values()) {
            if (Objects.equals(logicKeyEnum.getType(),type)) {
                return logicKeyEnum;
            }
        }
        return null;
    }
}
