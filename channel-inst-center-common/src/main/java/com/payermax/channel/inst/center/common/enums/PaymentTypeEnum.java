package com.payermax.channel.inst.center.common.enums;
import lombok.Getter;

public enum PaymentTypeEnum {

    /**
     * 支付类型
     */
    PAYMENT("20", "入款"),
    PAYOUT("10", "出款"),
    REFUND("40", "退款"),
    TRANSFER("50", "转账");

    @Getter
    private String value;

    @Getter
    private String desc;

    PaymentTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static PaymentTypeEnum getByValue(String value) {
        for (PaymentTypeEnum paymentTypeEnum : PaymentTypeEnum.values()) {
            if (paymentTypeEnum.getValue().equals(value)) {
                return paymentTypeEnum;
            }
        }
        return null;
    }
    public static PaymentTypeEnum getByDesc(String desc) {
        for (PaymentTypeEnum paymentTypeEnum : PaymentTypeEnum.values()) {
            if (paymentTypeEnum.getDesc().equals(desc)) {
                return paymentTypeEnum;
            }
        }
        return null;
    }
}

