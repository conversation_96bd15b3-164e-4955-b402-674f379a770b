package com.payermax.channel.inst.center.common.enums;

public enum StatusEnum {
    NO((byte)0, "关闭"),
    YES((byte)1, "启用");

    private Byte type;

    private String desc;

    StatusEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static StatusEnum getByType(Byte type) {
        for (StatusEnum statusEnum : StatusEnum.values()) {
            if (statusEnum.getType().equals(type)) {
                return statusEnum;
            }
        }
        return null;
    }
}
