package com.payermax.channel.inst.center.common.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageHitResult<T> implements Serializable {

    private Long total;

    /**
     * 滚表ID
     */
    private String scrollId;

    private Map<String, T> source;
}
