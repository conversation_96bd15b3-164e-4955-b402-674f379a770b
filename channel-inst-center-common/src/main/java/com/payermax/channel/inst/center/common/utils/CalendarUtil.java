package com.payermax.channel.inst.center.common.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/29
 * @DESC
 */
public class CalendarUtil {

    /**
     * 根据年份获取所有日期
     */
    public static List<LocalDate> getDatesOfYear(int year) {
        List<LocalDate> dates = new ArrayList<>();
        // 获取该年的第一天及最后一天
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfYear());

        while (!startDate.isAfter(endDate)) {
            dates.add(startDate);
            startDate = startDate.plusDays(1);
        }
        return dates;
    }

    /**
     * 判断当天是否为周末
     */
    public static Boolean isWeekend(LocalDate date) {
        return DayOfWeek.SATURDAY.equals(date.getDayOfWeek()) || DayOfWeek.SUNDAY.equals(date.getDayOfWeek());
    }

    /**
     * 判断当天是否在周末列表里
     */
    public static Boolean inWeekendList(LocalDate date, Set<DayOfWeek> weekendSet) {
        return weekendSet.contains(date.getDayOfWeek());
    }


    /**
     * 校验日期格式
     */
    public static Boolean isValidDateFormat(String dateStr, String format) {
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format));
            return Boolean.TRUE;
        } catch (DateTimeParseException e) {
            return Boolean.FALSE;
        }
    }
}
