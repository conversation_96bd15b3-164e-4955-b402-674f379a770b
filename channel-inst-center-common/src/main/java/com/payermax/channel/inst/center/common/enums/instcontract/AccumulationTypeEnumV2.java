package com.payermax.channel.inst.center.common.enums.instcontract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/27
 * @DESC
 */
@AllArgsConstructor
@Getter
public enum AccumulationTypeEnumV2 implements IDescOnExcelEnum{


    NUMBER("number", "Accumulated by Number of  Transactions", "Accumulated by Number of  Transactions"),

    AMOUNT("amount", "Accumulated by Transaction Amount", "Accumulated by Transaction Amount"),
    ;
    private final String code;

    private final String desc;

    private final String descOnExcel;
}
