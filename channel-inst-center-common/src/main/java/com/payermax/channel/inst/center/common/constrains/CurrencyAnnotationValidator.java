package com.payermax.channel.inst.center.common.constrains;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Currency;

/**
 * <AUTHOR> at 2023/6/11 9:18 AM
 **/
public class CurrencyAnnotationValidator implements ConstraintValidator<CurrencyAnnotation, String> {

    private static final int CURRENCY_SIZE = 3;

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (StringUtils.isEmpty(value)) {
            return true;
        }
        value = StringUtils.upperCase(value);

        // 币种长度为3
        if (value.length() != CURRENCY_SIZE) {
            buildConstraintViolation(context, "Currency Code is consistent with 3 Characters");
            return false;
        }

        // 币种必须合法，满足Currency
        try {
            Currency.getInstance(value);
        } catch (IllegalArgumentException e) {
            buildConstraintViolation(context, "Currency Code is Illegal");
            return false;
        }
        
        return true;
    }

    private void buildConstraintViolation(ConstraintValidatorContext context, String message) {
        if (context.getDefaultConstraintMessageTemplate().isEmpty()) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
        }
    }
}
