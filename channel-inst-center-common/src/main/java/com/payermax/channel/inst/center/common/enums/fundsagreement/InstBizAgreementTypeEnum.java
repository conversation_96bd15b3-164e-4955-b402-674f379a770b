package com.payermax.channel.inst.center.common.enums.fundsagreement;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 业务协议类型枚举类
 */
@AllArgsConstructor
@Getter
public enum InstBizAgreementTypeEnum {

    /**
     * 渠道收单
     */
    CHANNEL_ACQUIRING("渠道收单"),

    /**
     * 主体间代发
     */
    ENTITY_PAYMENT("主体间代发"),

    /**
     * 渠道代发
     */
    CHANNEL_PAYMENT("渠道代发"),

    /**
     * 渠道 VA
     */
    CHANNEL_VA("渠道 VA"),

    /**
     * 渠道外汇
     */
    CHANNEL_FX("渠道外汇"),

    /**
     * 主体间外汇
     */
    ENTITY_FX("主体间外汇");

    private final String description;

}
