package com.payermax.channel.inst.center.common.utils;

import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/10/11
 * @DESC
 */
@Slf4j
public class ExceptionUtils {

    /**
     * 通用的异常处理方法
     * @param supplier 一个返回结果的 Supplier
     * @param <R> 返回结果的类型
     * @return 如果没有异常，返回 ResultUtils.success(result)，否则返回对应的异常处理结果
     */
    public static <R> Result<R> commonTryCatch(Supplier<R> supplier){
        try {
            R result = supplier.get();
            // 如果没有异常，返回成功的结果
            return ResultUtils.success(result);
        } catch (BizException e) {
            // 业务异常
            log.error("biz Exception", e);
            return ResultUtils.bizExceptionFail(e);
        } catch (BusinessException e) {
            // 业务异常
            log.error("BusinessException Exception", e);
            return ResultUtils.businessException(e);
        } catch (IllegalArgumentException e) {
            // 非法参数异常
            log.error("IllegalArgumentException Exception", e);
            return ResultUtils.parameterInvalidFail(e);
        } catch (Exception e1) {
            // 未知异常
            log.error("unknown Exception", e1);
            return ResultUtils.unknownFail();
        }
    }
}
