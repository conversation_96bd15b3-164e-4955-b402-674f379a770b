package com.payermax.channel.inst.center.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ExportErrorInfo
 *
 * <AUTHOR>
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportErrorInfo {

    private boolean hasErrors;

    /**
     * 导入错误总提示，根据实际情况进行设置即可
     */
    private String errorMsg;

    /**
     * 导入详细提示信息
     */
    private List<ExportErrorInfoItem> exportErrorInfoItems;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExportErrorInfoItem {

        /**
         * 行号
         */
        private Integer rowNo;

        /**
         * 列号
         */
        private Integer columnNo;

        /**
         * 列名
         */
        private String columnName;

        /**
         * 单元格的值
         */
        private Object cellValue;

        /**
         * 单元格数据错误提示信息
         */
        private String errorMsg;

        public String getLetterColumnNo() {
            if(Objects.isNull(getColumnNo())) {
                return null;
            }
            int baseCount = 26;
            int baseStart = 65;
            int num = getColumnNo() - 1;
            StringBuilder builder = new StringBuilder();
            int temp;
            while ((temp = num / baseCount) > 0) {
                builder.append((char) (baseStart + temp - 1));
                num = num % baseCount;
            }
            builder.append((char) (baseStart + num % baseCount));
            return builder.toString();
        }
    }

    public static ExportErrorInfo merge(ExportErrorInfo firstError, ExportErrorInfo secondError) {
        if(Objects.isNull(firstError)) {
            firstError = new ExportErrorInfo();
        }
        if(Objects.isNull(secondError)) {
            secondError = new ExportErrorInfo();
        }
        ExportErrorInfo result = new ExportErrorInfo();
        if(!firstError.isHasErrors() && !secondError.isHasErrors()) {
            return result;
        }
        result.setHasErrors(Boolean.TRUE);
        result.setErrorMsg(firstError.getErrorMsg());
        result.setExportErrorInfoItems(new ArrayList<>());
        if(!CollectionUtils.isEmpty(firstError.getExportErrorInfoItems())) {
            result.getExportErrorInfoItems().addAll(firstError.getExportErrorInfoItems());
        }
        if(!CollectionUtils.isEmpty(secondError.getExportErrorInfoItems())) {
            result.getExportErrorInfoItems().addAll(secondError.getExportErrorInfoItems());
        }
        return result;
    }
}
