package com.payermax.channel.inst.center.common.enums;
import java.util.Objects;

/**
 *  定时任务锁域
 * <AUTHOR>
 * @date 2023/2/6 20:08
 **/
public enum LockRegionEnum {

    NULL("", "空"),
    SUB_ACCOUNT_NO_LOCK("SUB_ACCOUNT_NO_LOCK", "子级账号锁"),
    TASK_LOCK("TASK_LOCK", "定时任务锁");

    private String type;

    private String desc;

    LockRegionEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static LockRegionEnum getByType(Byte type) {
        for (LockRegionEnum lockRegionEnum : LockRegionEnum.values()) {
            if (Objects.equals(lockRegionEnum.getType(),type)) {
                return lockRegionEnum;
            }
        }
        return LockRegionEnum.NULL;
    }
}
