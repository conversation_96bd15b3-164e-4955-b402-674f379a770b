<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.payermax.channel</groupId>
    <artifactId>channel-inst-center-parent</artifactId>
    <version>1.0.0-RELEASE</version>
    <packaging>pom</packaging>
    <name>channel-inst-center</name>
    <description>PayerMax Demo project for Spring Boot system</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
    </parent>

    <properties>
        <revision>1.0.0-20221031-RELEASE</revision>
        <facade-revision>1.0.11-20250424-1-RELEASE</facade-revision>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <springboot.version>2.3.12.RELEASE</springboot.version>

        <spring.cloud.version>Hoxton.SR12</spring.cloud.version>
        <spring-context-support.version>1.0.11</spring-context-support.version>
        <spring-statemachine.version>2.1.3.RELEASE</spring-statemachine.version>
        <dubbo.version>3.0.12.PM1</dubbo.version>
        <nacos-config-spring-boot.version>0.2.10</nacos-config-spring-boot.version>
        <nacos-spring-context.version>1.1.1-PM1-20240718-RELEASE</nacos-spring-context.version>
        <dingtalk.sdk.version>1.2.98</dingtalk.sdk.version>

        <xxl-job-core.version>2.0.1-noglue.PM1</xxl-job-core.version>
        <transmittable.version>2.12.1</transmittable.version>
        <javax.validation-api.version>2.0.1.Final</javax.validation-api.version>
        <hibernate.validator.version>6.0.16.Final</hibernate.validator.version>
        <apache.common.text.version>1.9</apache.common.text.version>

        <paymember.contract.version>2.2.2</paymember.contract.version>


        <fintech-components-tracer.version>1.0.0-20221025-RELEASE</fintech-components-tracer.version>
        <fintech-components-security.version>1.0.0-20221031-RELEASE</fintech-components-security.version>
        <fintech-components-rpc-feign.version>1.0</fintech-components-rpc-feign.version>
        <ionia-tool-distributed-lock.version>0.0.35-20240617-RELEASE</ionia-tool-distributed-lock.version>
        <jackson-datatype-jsr310.version>2.12.5</jackson-datatype-jsr310.version>

        <common-exception-handler.version>1.0.1-20220903-RELEASE</common-exception-handler.version>
        <common-enum.version>1.1.2-20220621-RELEASE</common-enum.version>
        <fintech-components-log.version>1.4.6-20220819-RELEASE</fintech-components-log.version>
        <spring-boot-starter-parent.version>2.3.12.RELEASE</spring-boot-starter-parent.version>
        <mybatis-spring-boot-starter.version>1.3.2</mybatis-spring-boot-starter.version>
        <fintech-components-mq-kafka.version>1.0.1.RELEASE</fintech-components-mq-kafka.version>
        <spring-cloud-starter-task.version>$element.version</spring-cloud-starter-task.version>
        <common-pay-elements-validation.version>1.0.1.RELEASE</common-pay-elements-validation.version>
        <aws-mysql-jdbc.version>1.1.7.2</aws-mysql-jdbc.version>
        <mybatis-plus-boot-starter.version>3.4.3</mybatis-plus-boot-starter.version>
        <guava.version>28.1-jre</guava.version>
        <commons-lang3.version>3.9</commons-lang3.version>
        <fastjson.version>1.2.83</fastjson.version>
        <common-lang.version>2.0.7-20230419-RELEASE</common-lang.version>
        <common-dto.version>2.0.7-20230419-RELEASE</common-dto.version>
        <testng.version>7.4.0</testng.version>
        <hutool-all.version>5.8.3</hutool-all.version>
        <apm-toolkit-log4j-2.x.version>8.5.0</apm-toolkit-log4j-2.x.version>
        <fintech-components-log-dubbo3.version>1.0.10-20220819-RELEASE</fintech-components-log-dubbo3.version>
        <fintech-components-metrics.version>1.0.3-20220621-RELEASE</fintech-components-metrics.version>
        <HikariCP.version>3.4.5</HikariCP.version>
        <lombok.version>1.16.22</lombok.version>
        <mapstruct.version>1.2.0.Final</mapstruct.version>
        <sentry-spring-boot-starter.version>4.3.0</sentry-spring-boot-starter.version>
        <sentry-log4j2.version>4.3.0</sentry-log4j2.version>
        <commons-collections4.version>4.2</commons-collections4.version>
        <operating-log-client.version>1.0.4-RELEASE</operating-log-client.version>
        <fintech-base-manage-api.version>1.0.4</fintech-base-manage-api.version>
        <fintech-common.version>1.0.4</fintech-common.version>
        <aws-java-sdk-s3.version>1.12.26</aws-java-sdk-s3.version>
        <fintech-components-alarm-dingtalk.version>1.0.2-20220630-RELEASE</fintech-components-alarm-dingtalk.version>
        <easyexcel.version>3.3.1</easyexcel.version>
        <fintech-components-mybatis-interceptor.version>1.0.0-20221024-RELEASE</fintech-components-mybatis-interceptor.version>
        <ionia-tool-distributed-id.version>0.0.34-20240614-RELEASE</ionia-tool-distributed-id.version>
        <ionia-rocketMQ.version>0.0.43-20240821-RELEASE</ionia-rocketMQ.version>
        <ionia-config-encrypt.version>0.0.70-20250328-RELEASE</ionia-config-encrypt.version>
        <fintech-components-dubbo-extension-qos.version>1.0.0.RELEASE</fintech-components-dubbo-extension-qos.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.2</dynamic-datasource-spring-boot-starter.version>
        <druid-spring-boot-starter.version>1.2.15</druid-spring-boot-starter.version>
        <omc-crm-facade.version>1.0.5-20221208-RELEASE</omc-crm-facade.version>
        <alibaba.cloud.version>2.2.9.RELEASE</alibaba.cloud.version>
        <log4j.version>2.18.0</log4j.version>
        <fin-file-exchange-facade.version>1.1.4-RELEASE</fin-file-exchange-facade.version>
        <velocity-engine-core.version>2.3</velocity-engine-core.version>
        <freemarker.version>2.3.32</freemarker.version>
        <operating-omc-workflow.version>1.3.9-20250320-RELEASE</operating-omc-workflow.version>
        <channel-gateway-facade.version>1.0.0-20240204-RELEASE</channel-gateway-facade.version>
        <ionia-fs-all-starter.version>0.0.58-20241218-RELEASE</ionia-fs-all-starter.version>
        <omc-channel-exchange-facade.version>0.0.11-20241024-RELEASE</omc-channel-exchange-facade.version>
        <infra-ionia.version>0.0.48-20240924-RELEASE</infra-ionia.version>
        <commcenter-notice-facade.version>1.0.3-20250225-RELEASE</commcenter-notice-facade.version>
    </properties>

    <modules>
        <module>channel-inst-center-common</module>
        <module>channel-inst-center-facade</module>
        <module>channel-inst-center-app</module>
        <module>channel-inst-center-domain</module>
        <module>channel-inst-center-infrastructure</module>
        <module>start</module>
<!--        <module>channel-inst-center-test</module>-->
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-inst-center-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-inst-center-facade</artifactId>
                <version>${facade-revision}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-inst-center-app</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-inst-center-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-inst-center-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>start</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--Project modules End-->

            <!-- ushareit fintech components start -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>${nacos-spring-context.version}</version>
            </dependency>

            <dependency>
                <groupId>com.payermax</groupId>
                <artifactId>fintech-components-metrics</artifactId>
                <version>${fintech-components-metrics.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- ushareit components end -->

            <!--spring-boot start-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springboot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.statemachine</groupId>
                <artifactId>spring-statemachine-core</artifactId>
                <version>${spring-statemachine.version}</version>
            </dependency>
            <!--spring-boot end-->

            <!--spring-cloud start-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--spring-cloud end-->

            <!-- dubbo start -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-bom</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <!-- 新增如下patch用于支持新的QOS命令 -->
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>fintech-components-dubbo-extension-qos</artifactId>
                <version>${fintech-components-dubbo-extension-qos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- dubbo end -->

            <!-- sentry 异常日志组件接入 -->
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-spring-boot-starter</artifactId>
                <version>${sentry-spring-boot-starter.version}</version>
                <exclusions>
                    <!-- 可能会与项目中spring-boot-starter包冲突，所以进行排除 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-log4j2</artifactId>
                <version>${sentry-log4j2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-context-support.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${HikariCP.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${apache.common.text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <!-- 外部依赖 -->
            <dependency>
                <groupId>com.ushareit.pay.member</groupId>
                <artifactId>pay-member-http-api</artifactId>
                <version>${paymember.contract.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.payermax.common</groupId>
                        <artifactId>common-lang</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.payermax.solution</groupId>
                <artifactId>commcenter-notice-facade</artifactId>
                <version>${commcenter-notice-facade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>fintech-components-tracer</artifactId>
                <version>${fintech-components-tracer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.parent</groupId>
                <artifactId>fintech-components-security</artifactId>
                <version>${fintech-components-security.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.parent</groupId>
                <artifactId>fintech-components-rpc-feign</artifactId>
                <version>${fintech-components-rpc-feign.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-tool-distributed-lock</artifactId>
                <version>${ionia-tool-distributed-lock.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-datatype-jsr310.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>common-enum</artifactId>
                <version>${common-enum.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>software.aws.rds</groupId>
                <artifactId>aws-mysql-jdbc</artifactId>
                <version>${aws-mysql-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>common-lang</artifactId>
                <version>${common-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>common-dto</artifactId>
                <version>${common-dto.version}</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-log4j-2.x</artifactId>
                <version>${apm-toolkit-log4j-2.x.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>operating-log-client</artifactId>
                <version>${operating-log-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.base</groupId>
                <artifactId>fintech-base-manage-api</artifactId>
                <version>${fintech-base-manage-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.parent</groupId>
                <artifactId>fintech-common</artifactId>
                <version>${fintech-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.parent</groupId>
                <artifactId>fintech-components-alarm-dingtalk</artifactId>
                <version>${fintech-components-alarm-dingtalk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>fintech-components-mybatis-interceptor</artifactId>
                <version>${fintech-components-mybatis-interceptor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-tool-distributed-id</artifactId>
                <version>${ionia-tool-distributed-id.version}</version>
            </dependency>

            <!--      加密组件      -->
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-config-encrypt-all</artifactId>
                <version>${ionia-config-encrypt.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-registry-nacos</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-rocketMQ</artifactId>
                <version>${ionia-rocketMQ.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating.crm</groupId>
                <artifactId>omc-crm-facade</artifactId>
                <version>${omc-crm-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${alibaba.cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.file</groupId>
                <artifactId>fin-file-exchange-facade</artifactId>
                <version>${fin-file-exchange-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity-engine-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>operating-omc-workflow-facade</artifactId>
                <version>${operating-omc-workflow.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax</groupId>
                <artifactId>channel-gateway-facade</artifactId>
                <version>${channel-gateway-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax</groupId>
                <artifactId>omc-channel-exchange-facade</artifactId>
                <version>${omc-channel-exchange-facade.version}</version>
            </dependency>

            <!-- 文件存储 AWS S3、华为与OBS集合体 -->
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-fs-all-starter</artifactId>
                <version>${ionia-fs-all-starter.version}</version>
            </dependency>
            <!-- 摘要日志 -->
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-log-digest-http</artifactId>
                <version>${infra-ionia.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-log-digest-dubbo3</artifactId>
                <version>${infra-ionia.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-tool-lang</artifactId>
                <version>${infra-ionia.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-tool-notice</artifactId>
                <version>0.0.62-20250106-RELEASE</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <generateBackupPoms>true</generateBackupPoms>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <configFile>${basedir}/src/main/resources/smart-doc.json</configFile>
                    <projectName>机构中心</projectName>
                    <includes>
                        <!-- 使用了mybatis-plus的Page分页需要include所使用的源码包 -->
                        <include>com.baomidou:mybatis-plus-extension</include>
                        <!-- 使用了mybatis-plus的IPage分页需要include mybatis-plus-core-->
                        <include>com.baomidou:mybatis-plus-core</include>
                        <!-- 使用了jpa的分页需要include所使用的源码包 -->
                        <include>org.springframework.data:spring-data-commons</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <!--<phase>compile</phase>-->
                        <goals>
                            <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                            <goal>torna-rest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.6.1</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <annotationProcessorPath>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </annotationProcessorPath>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.payermax.infra</groupId>
                    <artifactId>check-maven-plugin</artifactId>
                    <version>1.0.0-SNAPSHOT</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>attach</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!-- 是否跳过检查，默认为false -->
                        <skip>false</skip>
                        <!-- 是否使用MyBatis(plus)组件，默认为true。如果没有使用设置为false，则不添加SQL检查组件 -->
                        <useMyBatis>true</useMyBatis>
                        <!-- 是否Sharding-Jdbc组件，默认为false。如果使用了设置为true，则不添加SQL检查组件 -->
                        <useShardingJdbc>false</useShardingJdbc>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <!-- 两个ID必须与 setting.xml中的<server><id>Releases</id></server>保持一致 -->
        <repository>
            <id>Releases</id>
            <name>Nexus Release Repository</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>Snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
