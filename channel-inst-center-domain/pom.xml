<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-inst-center-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>

    <artifactId>channel-inst-center-domain</artifactId>
    <packaging>jar</packaging>
    <name>channel-inst-center-domain</name>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hystrix-core</artifactId>
                    <groupId>com.netflix.hystrix</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-rocketMQ</artifactId>
        </dependency>
    </dependencies>

</project>
