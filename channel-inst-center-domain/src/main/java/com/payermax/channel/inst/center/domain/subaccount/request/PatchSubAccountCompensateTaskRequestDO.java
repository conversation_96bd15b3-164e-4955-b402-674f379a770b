package com.payermax.channel.inst.center.domain.subaccount.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> at 2022/10/15 23:11
 **/
@Data
public class PatchSubAccountCompensateTaskRequestDO {

    /**
     * 机构账号
     */
    private List<String> accountIds;

    /**
     * 子级资金账号
     */
    private List<String> subAccountIds;

    /**
     * 商户号
     */
    private List<String> merchantNos;

    /**
     * 补偿子级账号状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer status;

    /**
     * 最近多少小时的数据
     */
    private Integer recentHour;

    /**
     * 分页大小
     */
    private int pageSize;

    /**
     * 是否补偿重发
     */
    private Boolean compensateRetry = Boolean.FALSE;


}
