package com.payermax.channel.inst.center.domain.subaccount.response;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountExtEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> at 2022/10/20 10:39
 **/
@Data
public class BaseFundsAccountResponseDO {

    /**
     * 机构账号标识*
     */
    private String accountId;

    /**
     * 机构标识*
     */
    private String instCode;

    /**
     * 签约主体*
     */
    private String entity;

    /**
     * 国家*
     */
    private String country;

    /**
     * 币种*
     */
    private String currency;

    /**
     * 账号*
     */
    private String accountNo;

    /**
     * 账号名称*
     */
    private String accountName;

    /**
     * 账号类型*
     */
    private String accountType;
    
    /**
     * 申请子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     * 支持多个以,分割
     */
    private String scenes;

    /**
     * 是否支持子级账号 N:否 Y:是*
     */
    private String isSupportSubAccount;

    /**
     * 支持子级账号类型 SupportSubAccountTypeEnum，英文,分割
     */
    private String supportSubAccountType;

    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;

    /**
     * 是否支持自定义子账号名称 N:否 Y:是*
     */
    private String isSupportCustomName;

    /**
     * 银行名称*
     */
    private String bankName;

    /**
     * 银行地址*
     */
    private String bankAddress;

    /**
     * 银行地址 JSON 结构
     * {
     *     "address": "详细地址",
     *     "city": "城市",
     *     "country": "国家",
     *     "postcode": "邮编"
     * }
     */
    private String bankAddressJson;


    /**
     * iban
     */
    private String iban;

    /**
     * swiftCode
     */
    private String swiftCode;



    /**
     * 子级账号名称规则
     */
    private String subAccountNameWebRule;

    /**
     * 扩展属性*
     */
    private List<InstFundsAccountExtEntity> accountExtList;

    /**
     * 账户关联的渠道MID.
     */
    private List<String> relatedMidList;

}
