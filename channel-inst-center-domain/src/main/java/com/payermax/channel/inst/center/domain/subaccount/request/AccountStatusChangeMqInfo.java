package com.payermax.channel.inst.center.domain.subaccount.request;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;

/**
 * <AUTHOR> at 2022/10/12 16:26
 **/
@Data
public class AccountStatusChangeMqInfo extends BaseMqMessage {

    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;


    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
     */
    private String scenes;

    /**
     * 机构开户名称
     */
    private String accountName;

    /**
     * 是否支持子级账号 N:不支持，1:支持
     */
    private String isSupportSubAccount;

    /**
     * 是否支持名称自定义：N：不支持，Y：支持
     */
    private String isSupportCustomName;

    /**
     * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
     */
    private String subAccountMode;


    /**
     * 关联机构MID
     */
    private String instMid;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 子级账号标识
     */
    private String subAccountId;

    /**
     * 子级账号号码
     */
    private String subAccountNo;

    /**
     * 子级账号名称
     */
    private String subAccountName;

    /**
     * 状态 0：待激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer subAccountStatus;

    /**
     * 业务申请唯一键
     */
    private String businessKey;

    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;

    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;
    
    /**
     * 申请子级账号的子商户号
     */
    private String subMerchantNo;

    /**
     * 账号相关拓展信息，json格式
     */
    private String accountJson;

    /**
     * 子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     * 支持多个以,分割
     */
    private String subScenes;

}
