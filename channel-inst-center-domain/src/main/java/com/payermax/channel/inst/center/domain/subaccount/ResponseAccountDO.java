package com.payermax.channel.inst.center.domain.subaccount;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.Data;

/**
 * <AUTHOR> at 2022/10/9 10:42
 **/
@Data
public class ResponseAccountDO {

    /**
     * 生成子账号的可用机构账号
     */
    private InstFundsAccountEntity instFundsAccountEntity;

    /**
     * 子账号对象
     */
    private InstSubFundsAccountEntity instSubFundsAccountEntity;
}
