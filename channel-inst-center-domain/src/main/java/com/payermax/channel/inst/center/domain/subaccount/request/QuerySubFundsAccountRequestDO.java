package com.payermax.channel.inst.center.domain.subaccount.request;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4 11:15
 **/
@Data
public class QuerySubFundsAccountRequestDO extends InstFundsAccountEntity {

    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private List<String> accountIds;
    
    /**
     * 子级账号信息
     */
    private InstFundsAccountAndSubQueryEntity.InstSubFundsAccountDO subAccountQuery;
}
