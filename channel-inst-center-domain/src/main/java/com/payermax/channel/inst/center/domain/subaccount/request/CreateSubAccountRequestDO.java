package com.payermax.channel.inst.center.domain.subaccount.request;

import lombok.Data;

/**
 * <AUTHOR> at 2022/10/20 00:52
 **/
@Data
public class CreateSubAccountRequestDO {

    /**
     * 机构账号标识*
     *
     * 机构账号唯一键（如需要预筛选和创建接口是同一机构账号，此字段必传）
     */
    private String accountId;
    
    /**
     * 业务唯一key，会进行幂等校验
     */
    private String businessKey;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 子账号名称
     */
    private String subAccountName;

    /**
     * 用途
     */
    private String useType;

    /**
     * 子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
     */
    private String scenes;
    
    /**
     * 商户信息
     */
    private InstMerchantInfoDO merchantInfo;

}
