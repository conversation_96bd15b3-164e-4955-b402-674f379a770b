package com.payermax.channel.inst.center.domain.subaccount.request;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/26
 * @DESC
 */
@Data
public class InstSubAccountActivationMqInfo extends BaseMqMessage {


    /**
     * VA账号
     */
    private String vaAccountNo;

    /**
     * 资金账户ID
     */
    private String fundsAccountId;

    /**
     * 激活时间
     */
    private Long activationTime;
    
    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 二级商户号
     */
    private String subMerchantNo;
}
