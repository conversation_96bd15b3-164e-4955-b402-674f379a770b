package com.payermax.channel.inst.center.domain.subaccount.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 机构资金账号表
 *
 * <AUTHOR>
 */
@Data
public class QueryAccountsRequestDO {

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 国家
     */
    private List<String> country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 用途
     */
    private String useType;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 是否支持子级账号
     */
    private String isSupportSubAccount;

    /**
     * 场景
     */
    private String scenes;

}