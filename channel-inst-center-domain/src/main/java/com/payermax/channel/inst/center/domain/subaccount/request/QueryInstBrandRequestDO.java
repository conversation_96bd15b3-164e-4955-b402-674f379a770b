package com.payermax.channel.inst.center.domain.subaccount.request;

import lombok.Data;

/**
 * QueryInstBrandRequestDO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class QueryInstBrandRequestDO {

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * bd shareId
     */
    private String bdId;

    /**
     * 状态，Y：可用、N：不可用
     */
    private String status;
}
