package com.payermax.channel.inst.center.domain.subaccount.response;

import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;

/**
 * <AUTHOR> at 2022/10/12 16:26
 **/
@Data
public class FileAccountStatusChangeMqInfo extends BaseMqMessage {

    /**
     * 机构帐号状态变更MQ
     */
    private AccountStatusChangeMq msgData;

    /**
     * 机构帐号状态变更MQ
     */
    private String data;

    @Data
    public static class AccountStatusChangeMq {
        
        /**
         * 机构帐号标识（机构标识_机构账号_国家_币种）
         */
        private String accountId;

        /**
         * 机构标识
         */
        private String instCode;

        /**
         * 签约主体
         */
        private String entity;

        /**
         * 机构账号
         */
        private String accountNo;

        /**
         * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
         */
        private String accountType;

        /**
         * 机构开户名称
         */
        private String accountName;

        /**
         * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
         */
        private String useType;

        /**
         * 场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、ECOM：电商平台收款
         */
        private String scenes;

        /**
         * 是否支持子级账号 N:不支持，Y:支持
         */
        private String isSupportSubAccount;

        /**
         * 是否支持子级账号预申请 N:不支持，Y:支持
         */
        private String isSupportPreApply;

        /**
         * 子级账号申请模式：号段模式：NUMBER_SEGMENT，API模式：API，线下模式：OFFLINE
         */
        private String subAccountMode;

        /**
         * 国家
         */
        private String country;

        /**
         * 币种
         */
        private String currency;

        /**
         * 是否支持名称自定义：N：不支持，Y：支持
         */
        private String isSupportCustomName;

        /**
         * 是否需要激活：N:不需要，Y:需要
         */
        private String isNeedActivation;

        /**
         * 激活模式 API模式：API，线下模式：OFFLINE
         */
        private String activationMode;

        /**
         * 关联机构MID
         */
        private String instMid;

        /**
         * iban
         */
        private String iban;

        /**
         * swiftCode
         */
        private String swiftCode;

        /**
         * 账户别名
         */
        private String accountAlias;

        /**
         * 状态 N：不可用，Y：可用
         */
        private String status;

        /**
         * 子级账号信息
         */
        private InstFundsAccountAndSubQueryEntity.InstSubFundsAccountDO subAccountQuery;

        /**
         * 子级账号变更信息
         */
        private InstSubFundsAccountChange subAccountChange;

    }
    
    
    @Data
    public static class InstSubFundsAccountChange {

        /**
         * 子级账号号码
         */
        private String subAccountNo;

        /**
         * 子级账号名称
         */
        private String subAccountName;

        /**
         * 子级账号号码BBAN
         */
        private String bSubAccountNo;

        /**
         * 状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
         */
        private Integer status;

        /**
         * 拒绝编码
         */
        private String rejCode;
        
        /**
         * 拒绝描述
         */
        private String rejMessage;
        
        /**
         * 渠道VA唯一编号
         */
        private String channelUniqueNumber;
        
    }
}
