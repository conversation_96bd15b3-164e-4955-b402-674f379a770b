package com.payermax.channel.inst.center.domain.subaccount;

import com.payermax.channel.inst.center.domain.subaccount.request.InstSubFundsAccountRequestDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> at 2022/10/9 10:42
 **/
@Data
public class RequestAccountDO {

    /**
     * 上游传递创建子级资金-请求参数
     */
    private InstSubFundsAccountRequestDO InstSubFundsAccountRequestDO;

    /**
     * 初筛可用机构账号
     */
    private List<InstFundsAccountEntity> instFundsAccountEntityList;

    /**
     * 路由后可用的机构账号
     */
    private InstFundsAccountEntity instFundsAccountEntity;

    /**
     * 生成子级资金号段
     */
    private InstSubNumberSegmentEntity instSubNumberSegmentEntity;

    /**
     * 子级资金账号
     */
    private InstSubFundsAccountEntity instSubFundsAccountEntity;

    /**
     * 子级资金账号-拓展请求参数用于计数
     */
    private List<InstFundsAccountBucketEntity> instSubFundsAccountBucketsIsNullEntityList;

    /**
     * 子级资金账号-拓展请求参数用于使用
     */
    private List<InstFundsAccountBucketEntity> instSubFundsAccountBucketsNotNullEntityList;


}
