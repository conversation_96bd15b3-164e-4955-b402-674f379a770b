package com.payermax.channel.inst.center.service.convertor;

import com.payermax.channel.inst.center.app.manage.contract.settle.DSLResolveProcessor;
import com.payermax.channel.inst.center.facade.dsl.SettleRoundDSLEntity;
import com.payermax.channel.inst.center.facade.request.contract.config.SettlementConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SettleInfoConvertor {


    private final DslEntity2SettleInfoVoConvertor dslEntity2SettleInfoVoConvertor;

    public void convertSettleInfo(SettlementConfig settlementConfig) {
        try {
            List<SettleDate> settleDates = settlementConfig.getSettleDates();
            if (!CollectionUtils.isEmpty(settleDates)) {
                settleDates.forEach(item -> {
                    SettleRoundDSLEntity dslEntity = DSLResolveProcessor.resolveDslToEntity(item);
                    item.setDslEntity(dslEntity);
                });
                // SettleDate LIST 长度永远是 1
                SettleDate settleDate = settleDates.get(0); //NO_CHECK SettleDate LIST 长度永远是 1,所以取第一个无风险
                SettleRoundDSLEntity dslEntity = settleDate.getDslEntity();
                Integer cycleStart = dslEntity.getTransactionCycleStart();
                Integer cycleEnd = dslEntity.getTransactionCycleEnd();
                String timezone = Optional.ofNullable(settleDate.getTimezone()).orElse("GMT");
                settlementConfig.setSettleInfoForCalculate(
                        dslEntity2SettleInfoVoConvertor.convertDslEntity2SettleInfoVo(dslEntity.getBillDateDsl(), cycleStart, cycleEnd, timezone));
                settlementConfig.setFundsPaymentForCalculate(
                        dslEntity2SettleInfoVoConvertor.convertDslEntity2SettleInfoVo(dslEntity.getPaymentDateDsl(), cycleStart, cycleEnd, timezone));
                settlementConfig.setFundsArrivedForCalculate(
                        dslEntity2SettleInfoVoConvertor.convertDslEntity2SettleInfoVo(dslEntity.getArrivedDateDsl(), cycleStart, cycleEnd, timezone));
                settlementConfig.setFundsExchangeForCalculate(
                        dslEntity2SettleInfoVoConvertor.convertDslEntity2SettleInfoVo(dslEntity.getExchangeDateDsl(), cycleStart, cycleEnd, timezone));
            }
        } catch (Exception e) {
            log.error("convert settle error!", e);
        }
    }
}
