package com.payermax.channel.inst.center.facade;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstFundsAccountManage;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountManage;
import com.payermax.channel.inst.center.app.manage.SubAccountCompensateManage;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.domain.subaccount.request.*;
import com.payermax.channel.inst.center.domain.subaccount.response.*;
import com.payermax.channel.inst.center.facade.api.FundsAccountFacade;
import com.payermax.channel.inst.center.facade.request.*;
import com.payermax.channel.inst.center.facade.response.*;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstFundsAccountDao;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 资金账户Facade实现
 *
 * <AUTHOR>
 * @date 2022/10/3 10:01
 */

@DubboService(version = "${dubbo.provider.version}", methods = {
        @Method(name = "queryFundsAccounts", timeout = 10000),
        @Method(name = "createSubAccount", retries = 0, timeout = 10000),
        @Method(name = "closeSubAccount", timeout = 10000),
        @Method(name = "queryAccountDetail", timeout = 10000),
        @Method(name = "queryAccountDetailInfo", timeout = 10000),
        @Method(name = "queryAccountDetailById", timeout = 10000),
        @Method(name = "querySubAccountDetailById", timeout = 10000)
})
@Slf4j
public class FundsAccountFacadeImpl implements FundsAccountFacade {

    @Autowired
    InstFundsAccountManage instFundsAccountManage;

    @Autowired
    InstSubFundsAccountManage instSubFundsAccountManage;
    
    @Autowired
    SubAccountCompensateManage subAccountCompensateManager;
    
    @Autowired
    private ReqDoAssembler reqDoAssembler;

    @Autowired
    private ResDoAssembler resDoAssembler;

    @Resource
    private InstFundsAccountDao instFundsAccountDao;

    @Override
    @DigestLog(isRecord = true)
    public Result<List<QueryAccountsResponse>> queryFundsAccounts(QueryAccountsRequest request) {
        // 请求转换
        QueryAccountsRequestDO accountQueryDO = reqDoAssembler.toInstFundsAccountRequestDO(request);
        // 调用查询机构账户服务
        List<QueryAccountsResponseDO> list = instFundsAccountManage.queryAccount(accountQueryDO);
        // 响应转换
        List<QueryAccountsResponse> resultList = resDoAssembler.toQueryAccountsResponseList(list);
        return ResultUtils.success(resultList);
    }

    @Override
    @DigestLog(isRecord = true)
    public Result<CreateSubAccountResponse> createSubAccount(CreateSubAccountRequest request) {
        // 请求转换
        CreateSubAccountRequestDO createSubAccountRequestDO = reqDoAssembler.toCreateSubAccountRequestDO(request);
        // 调用创建子级账号服务
        CreateSubAccountResponseDO createSubAccountResponseDO = instFundsAccountManage.createSubAccount(createSubAccountRequestDO);
        // 响应转换
        CreateSubAccountResponse createSubAccountResponse = resDoAssembler.toCreateSubAccountResponse(createSubAccountResponseDO);
        return ResultUtils.success(createSubAccountResponse);
    }

    @Override
    public Result<CloseSubAccountResponse> closeSubAccount(String businessKey) {
        // 调用关闭子级账号服务
        CloseSubAccountResponseDO responseDO = instSubFundsAccountManage.closeSubAccountDetailById(businessKey);
        // 响应转换
        CloseSubAccountResponse closeSubAccountResponse = resDoAssembler.toCloseSubAccountResponse(responseDO);
        return ResultUtils.success(closeSubAccountResponse);
    }

    @Override
    public Result<QueryAccountDetailResponse> queryAccountDetail(QueryAccountDetailRequest request) {

        // 请求转换
        QueryAccountDetailRequestDO queryAccountDetailRequestDO = reqDoAssembler.toQueryAccountDetailRequestDO(request);
        // 调用查询机构账户服务
        QueryAccountDetailResponseDO queryAccountDetailResponseDO = instFundsAccountManage.queryAccountDetail(queryAccountDetailRequestDO);
        // 响应转换
        QueryAccountDetailResponse queryAccountDetailResponse = resDoAssembler.toQueryAccountDetailResponse(queryAccountDetailResponseDO);

        return ResultUtils.success(queryAccountDetailResponse);
    }

    @Override
    public Result<QueryAccountDetailResponse> queryAccountDetailInfo(QueryAccountDetailInfoRequest request) {

        // 请求转换
        QueryAccountDetailRequestDO queryAccountDetailRequestDO = reqDoAssembler.toQueryAccountDetailRequestDO(request);
        // 调用查询机构账户服务
        QueryAccountDetailResponseDO queryAccountDetailResponseDO = instFundsAccountManage.queryAccountDetail(queryAccountDetailRequestDO);
        // 响应转换
        QueryAccountDetailResponse queryAccountDetailResponse = resDoAssembler.toQueryAccountDetailResponse(queryAccountDetailResponseDO);

        return ResultUtils.success(queryAccountDetailResponse);
    }

    @Override
    public Result<QueryAccountDetailByIdResponse> queryAccountDetailById(QueryAccountDetailByIdRequest request) {

        // 请求转换
        QueryAccountDetailByIdRequestDO queryAccountDetailByIdRequestDO = reqDoAssembler.toQueryAccountDetailByIdRequestDO(request);
        // 调用查询机构账户服务
        QueryAccountDetailByIdResponseDO queryAccountDetailByIdResponseDO = instFundsAccountManage.queryAccountDetailById(queryAccountDetailByIdRequestDO);
        // 响应转换
        QueryAccountDetailByIdResponse queryAccountDetailByIdResponse = resDoAssembler.toQueryAccountDetailByIdResponse(queryAccountDetailByIdResponseDO);

        return ResultUtils.success(queryAccountDetailByIdResponse);
    }

    @Override
    public Result<List<QueryAccountDetailByIdResponse>> queryAccountByIdListAndMerchant(QueryAccountByIdListRequest request) {
        AssertUtil.notEmpty(request.getAccountIds(), "ERROR", "accountIds must not empty");
        List<QueryAccountDetailByIdResponse> result = new ArrayList<>(request.getAccountIds().size());
        request.getAccountIds().forEach(item -> {
            QueryAccountDetailByIdRequest request1 = new QueryAccountDetailByIdRequest();
            request1.setAccountId(item);
            request1.setMerchantNo(request.getMerchantNo());
            Result<QueryAccountDetailByIdResponse> responseResult = queryAccountDetailById(request1);
            if (Objects.nonNull(responseResult.getData())) {
                result.add(responseResult.getData());
            }
        });
        return ResultUtils.success(result);
    }

    @Override
    public Result<List<QueryAccountDetailByIdResponse>> queryAccountDetailByIdList(List<String> accountIds) {
        AssertUtil.notEmpty(accountIds, "ERROR", "accountIds must not empty");
        // queryAccountDetailById 接口有缓存,可以直接for查询
        List<QueryAccountDetailByIdResponse> result = new ArrayList<>(accountIds.size());
        accountIds.forEach(item -> {
            QueryAccountDetailByIdRequest request = new QueryAccountDetailByIdRequest();
            request.setAccountId(item);
            result.add(queryAccountDetailById(request).getData());
        });
        return ResultUtils.success(result);
    }

    @Override
    public Result<List<InstFundsAccountResponse>> queryInstAccountDetailList(InstFundsAccountRequest request) {
        InstFundsAccountQueryEntity requestEntity = reqDoAssembler.request2InstFundsAccountEntity(request);
        List<InstFundsAccountEntity> accountEntityList = instFundsAccountDao.selectByQueryEntity(requestEntity);
        return ResultUtils.success(reqDoAssembler.instFundsAccount2ResponseEntityList(accountEntityList));
    }

    @Override
    public Result<QuerySubAccountDetailByIdResponse> querySubAccountDetailById(QuerySubAccountDetailByIdRequest request) {
        // 请求转换
        QuerySubAccountDetailByIdRequestDO querySubAccountDetailByIdRequestDO = reqDoAssembler.toQuerySubAccountDetailByIdRequestDO(request);
        // 调用查询机构账户服务
        QuerySubAccountDetailByIdResponseDO querySubAccountDetailByIdResponseDO = instSubFundsAccountManage.querySubAccountDetailById(querySubAccountDetailByIdRequestDO);
        // 响应转换
        QuerySubAccountDetailByIdResponse querySubAccountDetailByIdResponse = resDoAssembler.toQuerySubAccountDetailByIdResponse(querySubAccountDetailByIdResponseDO);

        return ResultUtils.success(querySubAccountDetailByIdResponse);
    }

    @Override
    public Result<Integer> compensateSubAccountNoMq(List<String> subAccountIds) {
        return subAccountCompensateManager.compensateSubAccountNoMq(subAccountIds);
    }
}
