package com.payermax.channel.inst.center.facade.api;


import com.payermax.channel.inst.center.facade.request.InstBankAccountRequest;
import com.payermax.channel.inst.center.facade.request.QueryBrandByChannelCodeRequest;
import com.payermax.channel.inst.center.facade.request.QueryInstBrandRequest;
import com.payermax.channel.inst.center.facade.response.InstBankAccountResponse;
import com.payermax.channel.inst.center.facade.response.InstBaseInfoWithBrandResponse;
import com.payermax.channel.inst.center.facade.response.QueryInstBrandResponse;
import com.payermax.common.lang.model.dto.Result;

import java.util.List;

public interface InstBaseInfoQueryFacade {

    /**
     * 查询全量机构code列表信息
     */
    Result<List<String>> listInstCode();

    /**
     * 全量查询机构品牌接口
     * @param queryInstBrandRequest
     * @return
     */
    Result<List<QueryInstBrandResponse>> listInstBrand(QueryInstBrandRequest queryInstBrandRequest);


    /**
     * 根据 机构标识 & 用途，查询属于机构的银行账户
     * @param req
     * @return
     */
    Result<List<InstBankAccountResponse>> queryInstBankAccount(InstBankAccountRequest req);

    /**
     * 根据渠道编码列表查询机构及品牌
     * @param request 渠道编码列表
     * @return 机构列表
     */
    Result<List<InstBaseInfoWithBrandResponse>> queryByChannelCodeList(QueryBrandByChannelCodeRequest request);

}
