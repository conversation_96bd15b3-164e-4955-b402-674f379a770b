package com.payermax.channel.inst.center.facade.util;

import com.payermax.common.lang.exception.BusinessException;

import java.util.Objects;

/**
 * <AUTHOR> tracy
 * @version 2022-12-09 3:37 PM
 */
public abstract class AssertUtil {
    /**
     * 功能描述: 断言大于零<br>
     *
     * @param number
     */
    public static void greaterThenZero(Number number) {
        greaterThenZero(number,
                "[<PERSON><PERSON><PERSON> failed] - this number must be greater then zero");
    }

    /**
     * 功能描述: 断言大于零<br>
     *
     * @param number
     * @param message 描述信息
     */
    public static void greaterThenZero(Number number, String message) {
        if (number == null || number.longValue() <= 0) {
            throw new IllegalArgumentException(message);
        }
    }

    /**
     * 功能描述: 断言非空<br>
     *
     * @param object
     * @param code
     */
    public static void notNull(Object object, String code) {
        notNull(object, code,
                "[Asser<PERSON> failed] - this argument is required; it must not be null");
    }

    /**
     * 功能描述: 断言非空<br>
     *
     * @param object
     * @param code
     * @param message
     */
    public static void notNull(Object object, String code, String message) {
        if (Objects.isNull(object)) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 功能描述: 断言为true<br>
     *
     * @param expression
     * @param code
     * @param message
     */
    public static void isTrue(boolean expression, String code, String message) {
        if (!expression) {
            throw new BusinessException(code, message);
        }
    }

    /**
     * 功能描述: 断言为true<br>
     *
     * @param expression
     * @param code
     */
    public static void isTrue(boolean expression, String code) {
        isTrue(expression, code, "[Assertion failed] - this expression must be true");
    }

    /**
     * 抛异常接口
     **/
    @FunctionalInterface
    public interface ThrowExceptionFunction {

        /**
         * 抛出异常信息
         *
         * @param ex 错误码
         * @return void
         **/
        void orElseThrow(RuntimeException ex);
    }
}
