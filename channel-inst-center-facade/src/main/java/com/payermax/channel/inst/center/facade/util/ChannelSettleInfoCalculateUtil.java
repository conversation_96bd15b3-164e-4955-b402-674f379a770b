package com.payermax.channel.inst.center.facade.util;

import com.payermax.channel.inst.center.facade.enums.SettleMode;
import com.payermax.channel.inst.center.facade.service.ExchangeCalculator;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.time.ZoneId;
import java.util.Objects;
import java.util.TimeZone;

/**
 * <AUTHOR> tracy
 * @version 2022-10-19 9:13 PM
 */
public class ChannelSettleInfoCalculateUtil {

    /**
     * 根据结算信息&交易日期计算预计换汇日期
     */
    public static long calculateExchangeDate(String transactionType, SettleInfoVo settleInfoVo, long transactionTime) {
        // 当时区不为空，加上时区偏移
        if(!StringUtil.isEmpty(settleInfoVo.getTimezone())){
            transactionTime += TimeZone.getTimeZone(ZoneId.of(settleInfoVo.getTimezone())).getRawOffset();
        }
        long day = transactionTime - ((transactionTime + TimeZone.getTimeZone(ZoneId.of("GMT")).getRawOffset()) % (86400000));
        // 入款交易日换汇和出款日换汇,预计时间都是当日
        if ("TransactionDay".equalsIgnoreCase(transactionType) || "EXCHANGE_IN_TRADE_DAY".equalsIgnoreCase(transactionType)
                || "NoExchange".equalsIgnoreCase(transactionType) || "NO_EXCHANGE".equalsIgnoreCase(transactionType)) {
            return day;
        }
        AssertUtil.notNull(settleInfoVo, "ERROR", "[settleInfoVo] is mandatory");
        ExchangeCalculator exchangeCalculator = SettleMode.getByCode(settleInfoVo.getSettleMode());
        AssertUtil.notNull(exchangeCalculator, "ERROR", "can not recognize settleMode:"
                + settleInfoVo.getSettleMode());
        return Objects.requireNonNull(exchangeCalculator).calculate(settleInfoVo, day);
    }
}

