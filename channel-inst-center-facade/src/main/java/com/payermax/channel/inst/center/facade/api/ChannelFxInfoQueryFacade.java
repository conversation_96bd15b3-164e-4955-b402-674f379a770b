package com.payermax.channel.inst.center.facade.api;

import com.payermax.channel.inst.center.facade.request.FxInfoQueryRequest;
import com.payermax.channel.inst.center.facade.response.FxInfoQueryResponse;
import com.payermax.common.lang.model.dto.Result;

/**
 * <AUTHOR>
 * @version 2022-10-19 8:36 PM
 */
public interface ChannelFxInfoQueryFacade {

    /**
     * 查询交易渠道的换汇信息
     */
    @Deprecated
    Result<FxInfoQueryResponse> queryChanelFxInfo(FxInfoQueryRequest request);
}
