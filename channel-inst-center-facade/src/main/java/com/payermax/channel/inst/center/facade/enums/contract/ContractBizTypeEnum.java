package com.payermax.channel.inst.center.facade.enums.contract;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @DESC 机构合约类型
 * {@link com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum}
 */
@Getter
public enum ContractBizTypeEnum {


    /**
     * 入款
     */
    I,

    /**
     * 出款
     */
    O,

    /**
     * 风控
     */
    R,

    /**
     * 技术服务费
     */
    TS,
    /**
     * VA
     */
    VA,

    /**
     * 争议
     */
    DISP,
    ;
}