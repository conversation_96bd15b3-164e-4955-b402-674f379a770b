package com.payermax.channel.inst.center.facade.dsl;

import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 2023/6/18  6:06 PM
 */
@Data
public class SettleRoundDSLEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 交易周期描述类型
     */
    private RoundTypeEnum transactionCycleType;

    /**
     * 交易周期起始（比如用M-月描述，则起始为1）
     */
    private Integer transactionCycleStart;

    /**
     * 交易周期结束（比如用M-月描述，则结束为-1）
     */
    private Integer transactionCycleEnd;

    /**
     * 账单日dsl
     */
    private DSLEntity billDateDsl;

    /**
     * 付款日
     */
    private DSLEntity paymentDateDsl;

    /**
     * 到账日dsl
     */
    private DSLEntity arrivedDateDsl;

    /**
     * 换汇日dsl
     */
    private DSLEntity exchangeDateDsl;


    /**
     * 结算国家
     */
    private String country;

}