package com.payermax.channel.inst.center.facade.enums;

import com.payermax.channel.inst.center.facade.service.ExchangeCalculator;
import com.payermax.channel.inst.center.facade.service.impl.CyclicalSettleCalculator;
import lombok.Getter;

/**
 * 结算模式
 *
 * <AUTHOR>
 * @version 2022-10-20 2:04 PM
 */
@Getter
public enum SettleMode {

    CYCLICAL_SETTLE("CYCLICAL_SETTLE", new CyclicalSettleCalculator()),

    ;

    /**
     * 结算模式code
     */
    private final String code;

    /**
     * 结算模式对应的计算器
     */
    private final ExchangeCalculator exchangeCalculator;

    SettleMode(String code, ExchangeCalculator exchangeCalculator) {
        this.code = code;
        this.exchangeCalculator = exchangeCalculator;
    }

    public static ExchangeCalculator getByCode(String code) {
        for (SettleMode each : values()) {
            if (each.getCode().equals(code)) {
                return each.getExchangeCalculator();
            }
        }
        return null;
    }
}
