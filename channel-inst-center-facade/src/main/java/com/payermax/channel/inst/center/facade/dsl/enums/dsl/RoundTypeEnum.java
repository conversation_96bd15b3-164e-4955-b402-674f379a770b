package com.payermax.channel.inst.center.facade.dsl.enums.dsl;

import lombok.Getter;

/**
 * 用于描述结算等周期的自定义DSL中
 * <p>
 * 表示相对信息的类型
 * eg:D+1，则相对类型的即为DAY，表示交易日+1日；
 * W+2,则相对类型即为WEEK，表示当前周+2周；
 * M+2,则相对类型即为MONTH，表示当前月+2月；
 * 此外，还有LD表示上个账单日、LW表示上个账单日所在周、LM表示上个账单日所在月
 */
public enum RoundTypeEnum {

    /**
     * 按交易日偏移
     */
    DAY("D"),

    /**
     * 按周偏移
     */
    WEEK("W"),

    /**
     * 按月偏移
     */
    MONTH("M"),

    /**
     * 一周多次
     */
    WEEK_MULTI("WM"),

    /**
     * 一月多次
     */
    MONTH_MULTI("MM"),

    ;

    @Getter
    private final String desc;

    RoundTypeEnum(String desc) {
        this.desc = desc;
    }

    public static RoundTypeEnum getEnumByDesc(String desc) {
        for (RoundTypeEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value;
            }
        }
        return null;
    }

}