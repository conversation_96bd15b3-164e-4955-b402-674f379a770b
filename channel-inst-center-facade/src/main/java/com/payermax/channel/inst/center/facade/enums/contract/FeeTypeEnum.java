package com.payermax.channel.inst.center.facade.enums.contract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC
 * {@link com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum}
 */
@Getter
@AllArgsConstructor
public enum FeeTypeEnum {

    /**
     * 交易手续费
     */
    TRADE,

    /**
     * 退款手续费
     */
    REFUND,

    /**
     * 争议费用
     */
    CHARGEBACK,

    /**
     * 结算费用
     */
    SETTLEMENT,

    /**
     * 税费
     */
    TAX,

    /**
     * VA 账号费
     */
    VA_ACCOUNT,

    /**
     * 调单费
     */
    RETRIEVAL_REQUEST,
    ;
    ;

}
