package com.payermax.channel.inst.center.facade.enums;

import java.util.Objects;

/**
 * 子级资金账号状态
 *
 * <AUTHOR>
 * @date 2022/10/10 22:04
 */
public enum SubAccountStatusEnum {

    /**
     * 子级资金账号状态
     */
    INITIATE(9, "初始化"),
    TO_BE_ACTIVATED(0, "待激活"),
    ACTIVE(1, "激活中"),
    ACTIVATED(2, "已激活"),
    TERMINATED(3, "已停用"),
    APPLY(4, "申请中");

    private Integer status;

    private String desc;

    SubAccountStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 根据订单状态获取枚举
     *
     * @param status
     * @return
     */
    public static SubAccountStatusEnum getSubAccountStatusByStatus(Integer status) {
        for (SubAccountStatusEnum accountStatusEnum : values()) {
            if (Objects.equals(accountStatusEnum.getStatus(), status)) {
                return accountStatusEnum;
            }
        }
        return null;
    }

    /**
     * 是否是终态
     *
     * @param status
     * @return
     */
    public static boolean isFinalStatus(Integer status) {
        if (TERMINATED.getStatus().equals(status) || ACTIVATED.getStatus().equals(status)) {
            return true;
        }
        return false;
    }

    /**
     * 是否是返回子级账号
     *
     * @param status
     * @return
     */
    public static boolean isReturnAccount(Integer status) {
        if (INITIATE.getStatus().equals(status) || APPLY.getStatus().equals(status)) {
            return false;
        }
        return true;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
