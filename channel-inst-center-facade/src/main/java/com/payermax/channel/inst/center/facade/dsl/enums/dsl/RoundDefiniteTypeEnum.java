package com.payermax.channel.inst.center.facade.dsl.enums.dsl;

/**
 * <AUTHOR> 2023/6/18  6:38 PM
 * <p>
 * 周期相对偏移的基础上，使用天/工作日做绝对偏移
 * eg：(M+1).1,其中M+1表示当前月向后一个月，.1就隐式表示用天做绝对偏移，表示第一天，合起来就是下个月第一天；
 * (M+1).1WD，1WD明确表示用工作日做偏移，合起来就是下个月第一个工作日
 */
public enum RoundDefiniteTypeEnum {

    /**
     * 天
     */
    D("DAY"),

    /**
     * 工作日
     */
    WD("WEEKDAY");

    private final String desc;

    RoundDefiniteTypeEnum(String desc) {
        this.desc = desc;
    }
}