package com.payermax.channel.inst.center.facade.dsl.enums.dsl;

import lombok.Getter;

/**
 * <AUTHOR> 2023/6/18  6:28 PM
 */
@Getter
public enum RoundSignTypeEnum {

    ADD("+"),

    MINUS("-");

    private final String sign;

    RoundSignTypeEnum(String sign) {
        this.sign = sign;
    }

    public static RoundSignTypeEnum getRoundSignEnumBySign(String sign) {
        for (RoundSignTypeEnum signTypeEnum : values()) {
            if (signTypeEnum.getSign().equals(sign)) {
                return signTypeEnum;
            }
        }
        return null;
    }
}
