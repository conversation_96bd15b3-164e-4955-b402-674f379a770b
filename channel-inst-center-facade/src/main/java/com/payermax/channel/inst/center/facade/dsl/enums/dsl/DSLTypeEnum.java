package com.payermax.channel.inst.center.facade.dsl.enums.dsl;

import lombok.Getter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2023/6/21  11:48 AM
 */
@Getter
public enum DSLTypeEnum {

    /**
     * 组合形式
     */
    COMPLEX_COMBINE("^\\((?<RelativeInfoType>M|W)(?<RelativeInfoSign>\\+|-)(?<RelativeInfo>[0-9]?\\d*)\\)\\.(?<DefiniteInfoSign>\\-?)(?<DefiniteInfo>(([0-9]?\\d*)|([0-9]?\\d*WD))$)"),

    /**
     * 简单D描述周期 - 交易日
     */
    SIMPLE_DAY("^(?<RelativeInfoType>D)((?<RelativeInfoSign>\\+|-)(?<RelativeInfo>[0-9]?\\d*(WD)?)$)?"),

    /**
     * 简单W描述周期 - 周
     */
    SIMPLE_WEEK("^(?<RelativeInfoType>W)\\.(?<DefiniteInfoSign>\\-?)(?<DefiniteInfo>[1-7]\\d*)"),

    /**
     * 一周多次
     */
    WEEK_MULTI("^(?<RelativeInfoType>WM)\\.(?<DefiniteInfo>.*)"),

    /**
     * 简单M描述周期 - 月
     */
    SIMPLE_MONTH("^(?<RelativeInfoType>M)\\.(?<DefiniteInfoSign>\\-?)(?<DefiniteInfo>(([1-9]|[1-2][0-9]|3[01])|([1-9]\\d*WD))$)"),

    /**
     * 一个月多次
     */
    MONTH_MULTI("^(?<RelativeInfoType>MM)\\.(?<DefiniteInfo>.*)")


    ;

    private final String regexExpression;

    private final Pattern pattern;

    DSLTypeEnum(String regexExpression) {
        this.regexExpression = regexExpression;
        this.pattern = Pattern.compile(regexExpression);
    }

    /**
     * DSL表达式匹配
     */
    public static DSLTypeEnum mateDslType(String dslExpression) {
        for (DSLTypeEnum dslType : DSLTypeEnum.values()) {
            Pattern compileRegex = dslType.getPattern();
            Matcher matcher = compileRegex.matcher(dslExpression);
            if (matcher.matches()) {
                return dslType;
            }
        }
        throw new IllegalArgumentException("settle DSL expression format error");
    }


    /**
     * 判断是否符合某个正则
     *
     * @param dslType       dsl类型
     * @param dslExpression dsl表达式
     * @return true/false
     */
    public static boolean whetherMateDsl(DSLTypeEnum dslType, String dslExpression) {
        Pattern compileRegex = dslType.getPattern();
        Matcher matcher = compileRegex.matcher(dslExpression);
        return matcher.matches();
    }
}
