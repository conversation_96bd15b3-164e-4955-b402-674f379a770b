package com.payermax.channel.inst.center.facade.util;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2022-12-09 3:44 PM
 */
public class DateUtil {
    public static Date addDays(Date date, int days) {
        if (date == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, days);
        return c.getTime();
    }
}
