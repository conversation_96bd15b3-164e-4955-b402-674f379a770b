package com.payermax.channel.inst.center.facade.dsl.enums.dsl;

import lombok.Getter;

/**
 * <AUTHOR> 2023/6/21  3:41 PM
 * <p>
 * eg:(M+1).1WD,
 * RELATIVE_INFO_TYPE为M;
 * RELATIVE_INFO_SIGN为+;
 * RELATIVE_INFO为1;
 * DEFINITE_INFO_SIGN为+;
 * DEFINITE_INFO为1WD
 */
public enum DslKeywordEnum {

    /**
     * 相对信息类型
     */
    RELATIVE_INFO_TYPE("RelativeInfoType"),

    /**
     * 相对信息符号
     */
    RELATIVE_INFO_SIGN("RelativeInfoSign"),

    /**
     * 相对信息
     */
    RELATIVE_INFO("RelativeInfo"),

    /**
     * 绝对信息符号
     */
    DEFINITE_INFO_SIGN("DefiniteInfoSign"),

    /**
     * 绝对信息
     */
    DEFINITE_INFO("DefiniteInfo");

    @Getter
    private final String desc;

    DslKeywordEnum(String desc) {
        this.desc = desc;
    }
}
