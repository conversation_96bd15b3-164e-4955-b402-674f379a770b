package com.payermax.channel.inst.center.facade.dsl;

import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundSignTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 2023/6/26  5:15 PM
 * <p>
 * (M+1).-1WD举例
 * roundRelativeType 为 RoundTypeEnum.MONTH;
 * roundRelativeSignType 为 RoundSignTypeEnum.ADD;
 * roundRelativeOffset 为 1;
 * roundRelativeOffset 为 RoundSignTypeEnum.MINUS;
 * roundDefiniteOffset 为 -1;
 * (M+1).-1WD举例，roundDefiniteOffset 为 true
 */
@Data
public class DSLEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 周期相对部分的类型
     */
    private RoundTypeEnum roundRelativeType;

    /**
     * 周期相对部分的符号
     */
    private RoundSignTypeEnum roundRelativeSignType;

    /**
     * 周期相对部分的偏移量
     */
    private Integer roundRelativeOffset;

    /**
     * 周期绝对部分的符号
     */
    private RoundSignTypeEnum roundDefiniteSignType;

    /**
     * 周期绝对部分偏移量
     */
    private Integer roundDefiniteOffset;

    /**
     * 是否考虑节假日（是否包含WD）
     */
    private Boolean isConsiderHoliday;


    /**
     * 多次结算表达式的周期部分是字符串
     */
    private String definiteOffset;
}