package com.payermax.channel.inst.center.facade.enums;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.service.impl.*;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2022-10-20 2:15 PM
 */
@Getter
public enum CycleUnit {

    DAY("DAY", new DayCycleUnitHandler()),
    WEEK("WEEK", new WeekCycleUnitHandler()),
    WEEK_MULTI("WEEK_MULTI", new WeekMultiHandler()),
    MONTH("MONTH", new MonthCycleUnitHandler()),
    MONTH_MULTI("MONTH_MULTI", new MonthMultiHandler()),
    DOUBLE_MONTH("DOUBLE_MONTH", new DoubleMonthCycleUnitHandler()),
    DOUBLE_WEEK("DOUBLE_WEEK", new DoubleWeekCycleUnitHandler()),

    ;
    /**
     * 周期单位
     */
    private final String unit;

    /**
     * 单位对应的处理器
     */
    private final CycleUnitHandler cycleUnitHandler;


    CycleUnit(String unit, CycleUnitHandler cycleUnitHandler) {
        this.unit = unit;
        this.cycleUnitHandler = cycleUnitHandler;
    }

    public static CycleUnitHandler getByUnit(String unit) {
        for (CycleUnit each : values()) {
            if (each.getUnit().equals(unit)) {
                return each.getCycleUnitHandler();
            }
        }
        return null;
    }
}
