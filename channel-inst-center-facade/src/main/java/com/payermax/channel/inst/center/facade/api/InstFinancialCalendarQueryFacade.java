package com.payermax.channel.inst.center.facade.api;

import com.payermax.channel.inst.center.facade.request.calendar.*;
import com.payermax.channel.inst.center.facade.response.calendar.*;
import com.payermax.common.lang.model.dto.Result;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InstFinancialCalendarQueryFacade {

    /**
     * 检查是否节假日
     */
    Result<HolidayCheckResponse> holidayCheck(HolidayCheckRequest request);


    /**
     * 计算下一个工作日
     */
    Result<List<NextWorkdayCalculationResponse>> calculateNextWorkdays(List<NextWorkdayCalculationRequest> request);


    /**
     * 计算下一个工作日,支持多个国家或币种的并集
     */
    Result<HolidayMultipleCheckResponse> calculateNextWorkdaysOfMultipleLimited(HolidayMultipleCheckRequest request);


    /**
     * 计算特定时间区间有多少工作日
     */
    Result<List<NumOfWorkdaysCalculationResponse>> calculateNumOfWorkdays(List<NumOfWorkdaysCalculationRequest> request);


    /**
     * 查询特定区间内所有节假日
     */
    Result<HolidayQueryResponse> queryAllHolidaysByDate(HolidayQueryRequest request);


}
