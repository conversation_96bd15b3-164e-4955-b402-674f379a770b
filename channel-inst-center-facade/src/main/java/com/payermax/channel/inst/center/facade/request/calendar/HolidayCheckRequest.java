package com.payermax.channel.inst.center.facade.request.calendar;

import com.payermax.channel.inst.center.facade.enums.financialCalendar.CalendarTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @DESC
 */
@Data
public class HolidayCheckRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期(包含)
     */
    private String date;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 日历类型，国家/币种/银行日历
     * {@link CalendarTypeEnum}
     */
    private String calendarType;
}
