package com.payermax.channel.inst.center.facade.enums.contract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC 技术服务费-支付业务类型
 */
@Getter
@AllArgsConstructor
public enum PaymentBusinessTypeEnum {

    /**
     * Payin
     */
    I("Payin"),

    /**
     * Payout
     */
    O("Payout"),

    /**
     * VA
     */
    VA("VA"),

    ;

    /**
     * 描述
     */
    private final String desc;
}
