package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

/**
 * 查询子账号详情响应类
 *
 * <AUTHOR>
 * @date 2022/10/2 20:33
 */
@Data
public class QueryAccountDetailResponse extends BaseFundsAccountResponse {

    /**
     * 机构子账号标识*
     */
    private String subAccountId;

    /**
     * 机构子账号号码*
     */
    private String subAccountNo;

    /**
     * 机构子账号名称*
     */
    private String subAccountName;

    /**
     * 机构子账号号码BBAN*
     */
    private String bSubAccountNo;

    /**
     * 机构子账号状态：0：待激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化*
     */
    private Integer subAccountStatus;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;

    /**
     * 申请子级账号的子商户号
     */
    private String subMerchantNo;

    /**
     * 子级账号使用场景：TRADE：B2B贸易、ADS：广告费收款、COD：COD费用收款、LIVE：直播平台分销、 ECOM：电商平台收款
     * 支持多个以,分割
     */
    private String subScenes;

}
