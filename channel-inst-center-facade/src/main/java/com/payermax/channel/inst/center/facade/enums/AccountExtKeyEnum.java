package com.payermax.channel.inst.center.facade.enums;

/**
 * 资金账号扩展属性key枚举
 *
 * <AUTHOR>
 * @date 2022/10/2 15:56
 */
public enum AccountExtKeyEnum {

    SWIFT_CODE("SwiftCode", "swift code"),
    IBAN("IBAN", "IBAN NO"),
    IFSC("IFSC", "IFSC code"),
    BANK_CODE("BankCode", "bank code"),
    BANK_BRANCH_CODE("BankBranchCode", "bank branch code"),
    BANK_NUMBER("BankNumber", "bank number"),
    ;

    private String code;

    private String desc;


    AccountExtKeyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
